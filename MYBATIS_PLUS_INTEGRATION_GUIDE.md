# MyBatis-Plus 3.5.12 集成指南

## 概述

本项目已成功集成 MyBatis-Plus 3.5.12 版本，与现有的 easyooo-framework DAO 层方案共存。新旧方案可以同时使用，互不影响。

## 架构设计

### 1. 双方案共存架构

- **旧方案（easyooo-framework）**：继续使用现有的 `@Repository` + `@Table` 注解的 Mapper 接口
- **新方案（MyBatis-Plus）**：使用 `@Mapper` 注解的 Mapper 接口，继承 `BaseMapper`

### 2. 包路径隔离

- **旧方案包路径**：`com.tuowan.yeliao.**.persistence.**`
- **新方案包路径**：`com.tuowan.yeliao.**.mapper.plus.**`
- **新方案实体包路径**：`com.tuowan.yeliao.**.entity.plus.**`

### 3. 配置隔离

- **旧方案 SqlSessionFactory**：`sqlSessionFactory`
- **新方案 SqlSessionFactory**：`mybatisPlusSqlSessionFactory`

## 使用方式

### 1. 启用 MyBatis-Plus

在应用配置文件中添加：

```yaml
mybatis-plus:
  enabled: true
```

或者引入专用配置文件：

```yaml
spring:
  profiles:
    include: mybatis-plus
```

### 2. 创建实体类

继承 `BaseEntity` 基础实体类：

```java
@TableName("your_table_name")
public class YourEntity extends BaseEntity {
    
    @TableField("column_name")
    private String fieldName;
    
    // getter and setter
}
```

### 3. 创建 Mapper 接口

```java
@Mapper
public interface YourEntityMapper extends BasePlusMapper<YourEntity> {
    
    // 自定义查询方法
    @Select("SELECT * FROM your_table WHERE field = #{value}")
    List<YourEntity> selectByField(@Param("value") String value);
}
```

### 4. 创建 Service 类

```java
@Service
@ConditionalOnProperty(name = "mybatis-plus.enabled", havingValue = "true")
public class YourEntityService extends ServiceImpl<YourEntityMapper, YourEntity> {
    
    public List<YourEntity> getByField(String value) {
        LambdaQueryWrapper<YourEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(YourEntity::getFieldName, value);
        return list(wrapper);
    }
}
```

## 功能特性

### 1. 基础 CRUD 操作

MyBatis-Plus 提供了丰富的基础 CRUD 操作：

- `save(entity)` - 保存
- `getById(id)` - 根据ID查询
- `updateById(entity)` - 根据ID更新
- `removeById(id)` - 根据ID删除（逻辑删除）
- `list(wrapper)` - 条件查询列表
- `page(page, wrapper)` - 分页查询

### 2. 条件构造器

支持 Lambda 表达式的条件构造：

```java
LambdaQueryWrapper<Entity> wrapper = new LambdaQueryWrapper<>();
wrapper.eq(Entity::getField1, value1)
       .like(Entity::getField2, value2)
       .orderByDesc(Entity::getCreateTime);
```

### 3. 分页查询

```java
Page<Entity> page = new Page<>(current, size);
IPage<Entity> result = mapper.selectPage(page, wrapper);
```

### 4. 自动填充

支持创建时间、更新时间等字段的自动填充：

```java
@TableField(value = "create_time", fill = FieldFill.INSERT)
private Date createTime;

@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
private Date updateTime;
```

### 5. 逻辑删除

支持逻辑删除功能：

```java
@TableLogic
@TableField(value = "deleted", fill = FieldFill.INSERT)
private Integer deleted;
```

## 示例代码

项目中已提供完整的示例代码：

- **实体类**：`SPostPlus.java`
- **Mapper 接口**：`SPostPlusMapper.java`
- **Service 类**：`SPostPlusService.java`
- **Controller 类**：`SPostPlusController.java`

## 注意事项

### 1. 版本兼容性

- MyBatis-Plus 3.5.12 与 Spring Boot 2.4.2 完全兼容
- 与现有的 easyooo-framework 无冲突

### 2. 性能考虑

- MyBatis-Plus 提供了更简洁的 API，减少了样板代码
- 支持多种优化策略，如批量操作、分页优化等

### 3. 迁移策略

- 新业务推荐使用 MyBatis-Plus 方案
- 旧业务可以逐步迁移，也可以继续使用原方案
- 两套方案可以在同一个项目中并存

### 4. 开发建议

- 使用 `@ConditionalOnProperty` 注解确保只在启用时加载相关 Bean
- 遵循包路径命名规范，避免与现有代码冲突
- 充分利用 MyBatis-Plus 的代码生成器提高开发效率

## 配置说明

### 1. 必要依赖

已在父 pom.xml 中配置：

```xml
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.5.12</version>
</dependency>
```

### 2. 配置类

- `MybatisPlusAutoConfiguration` - 主配置类
- `MybatisPlusConfig` - 元数据处理器

### 3. 启用配置

在 `application.yaml` 中添加：

```yaml
mybatis-plus:
  enabled: true
```

## 快速启动

### 1. 启用 MyBatis-Plus

在业务模块的 `application.yaml` 中添加：

```yaml
spring:
  profiles:
    include: mybatis-plus
  config:
    import:
      - classpath:mybatis-plus-common.yaml

mybatis-plus:
  enabled: true
```

### 2. 运行测试

执行测试类验证集成是否成功：

```bash
mvn test -Dtest=PostServiceTest
```

### 3. 访问示例接口

启动应用后，可以访问以下接口：

**基础 CRUD：**
- `GET /api/v2/post/{id}` - 获取动态详情
- `GET /api/v2/post/postId/{postId}` - 根据业务ID获取动态
- `POST /api/v2/post/create` - 创建新动态
- `PUT /api/v2/post/update` - 更新动态
- `DELETE /api/v2/post/{id}` - 删除动态

**查询操作：**
- `GET /api/v2/post/user/{userId}` - 获取用户动态列表
- `GET /api/v2/post/user/{userId}/page` - 分页查询用户动态
- `GET /api/v2/post/hot` - 获取热门动态
- `GET /api/v2/post/search?keyword=关键词` - 搜索动态

**互动操作：**
- `POST /api/v2/post/{id}/praise` - 点赞动态
- `DELETE /api/v2/post/{id}/praise` - 取消点赞
- `POST /api/v2/post/{id}/share` - 分享动态

**管理操作：**
- `PUT /api/v2/post/{id}/approve` - 审核通过
- `PUT /api/v2/post/{id}/reject` - 审核拒绝
- `PUT /api/v2/post/batch/status` - 批量更新状态

**分页查询示例：**
```bash
# 分页查询用户动态
GET /api/v2/post/user/1001/page?current=1&size=10

# 分页查询热门动态
GET /api/v2/post/hot/page?current=1&size=5

# 分页搜索动态
GET /api/v2/post/search/page?keyword=测试&current=1&size=10
```

### 4. 数据库准备

执行 `sql/mybatis_plus_demo.sql` 中的建表语句和测试数据。

### 5. 代码生成

使用 `MybatisPlusGenerator` 快速生成新表的代码：

```java
// 生成指定模块的代码
String[] tableNames = {"s_your_table"};
String[] tablePrefixes = {"s_"};
MybatisPlusGenerator.generateForModule("social", tableNames, tablePrefixes);
```

## 分页组件说明

### MyBatis-Plus 分页插件

本集成使用 MyBatis-Plus 自带的 `PaginationInnerInterceptor` 分页插件，替代了传统的 PageHelper。

#### 分页插件配置

```java
@Bean
public MybatisPlusInterceptor mybatisPlusInterceptor() {
    MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

    // 分页插件
    PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor();
    paginationInterceptor.setDbType(DbType.MYSQL);           // 数据库类型
    paginationInterceptor.setMaxLimit(1000L);                // 最大单页限制
    paginationInterceptor.setOptimizeJoin(true);             // 开启 join 优化
    interceptor.addInnerInterceptor(paginationInterceptor);

    return interceptor;
}
```

#### 分页使用方式

**1. Service 层分页查询：**
```java
public Page<Post> getUserPostPage(Long userId, int current, int size) {
    Page<Post> page = new Page<>(current, size);
    return baseMapper.selectUserPostPage(page, userId);
}
```

**2. Mapper 层分页方法：**
```java
@Select("SELECT * FROM s_post_plus_demo WHERE user_id = #{userId} AND deleted = 0")
Page<Post> selectUserPostPage(Page<Post> page, @Param("userId") Long userId);
```

**3. Controller 层分页接口：**
```java
@GetMapping("/user/{userId}/page")
public Page<Post> getUserPostPage(
        @PathVariable Long userId,
        @RequestParam(defaultValue = "1") int current,
        @RequestParam(defaultValue = "10") int size) {
    return postService.getUserPostPage(userId, current, size);
}
```

#### 分页结果说明

MyBatis-Plus 分页返回的 `Page<T>` 对象包含以下信息：
- `getTotal()` - 总记录数
- `getPages()` - 总页数
- `getCurrent()` - 当前页
- `getSize()` - 每页大小
- `getRecords()` - 当前页数据
- `hasPrevious()` - 是否有上一页
- `hasNext()` - 是否有下一页

#### 优势对比

相比 PageHelper，MyBatis-Plus 分页插件具有以下优势：
1. **性能更优** - 原生支持，无需额外的拦截器处理
2. **类型安全** - 强类型的 Page 对象，避免类型转换
3. **功能丰富** - 提供更多分页相关的便捷方法
4. **集成度高** - 与 MyBatis-Plus 其他功能无缝集成

## 配置说明

### 核心配置类

1. **MybatisPlusAutoConfiguration** - 主配置类，负责条件化加载
2. **MybatisPlusConfig** - 插件配置，包含 MyBatis-Plus 分页插件、乐观锁插件和元数据处理器
3. **BaseEntity** - 基础实体类，包含公共字段
4. **BasePlusMapper** - 基础 Mapper 接口

### 配置文件架构

#### 公共配置文件
- `yl-commons-config/src/main/resources/mybatis-plus-common.yaml` - 公共配置，包含所有通用设置

#### 业务模块配置
- `application-mybatis-plus.yaml` - 业务模块配置，引用公共配置并启用功能

```yaml
# 业务模块配置示例
spring:
  config:
    import:
      - classpath:mybatis-plus-common.yaml

mybatis-plus:
  enabled: true
```

### 配置特性

1. **环境隔离** - 支持 dev/test/prod 不同环境配置
2. **按需启用** - 通过 `mybatis-plus.enabled=true` 控制是否启用
3. **配置复用** - 公共配置避免重复配置
4. **条件加载** - 所有组件都支持条件化加载
5. **分页组件** - 使用 MyBatis-Plus 自带的 PaginationInnerInterceptor，性能更优
6. **插件集成** - 集成分页插件、乐观锁插件，支持多数据库类型

### 实体命名规范

1. **去除表前缀** - 实体类名去掉数据库表的前缀（如 s_、u_、t_）
2. **驼峰命名** - 使用标准的 Java 驼峰命名规范
3. **枚举支持** - 为状态字段提供枚举类型，增强代码可读性
4. **工具方法** - 实体类包含便捷的工具方法

## 文件清单

本次集成新增和更新的文件：

```
# 公共配置和工具类
yl-commons/yl-commons-config/src/main/java/com/tuowan/yeliao/commons/config/mybatis/
├── MybatisPlusAutoConfiguration.java    # MyBatis-Plus 主配置类
├── MybatisPlusConfig.java              # 元数据处理器
└── MybatisPlusGenerator.java           # 代码生成器工具

yl-commons/yl-commons-config/src/main/resources/
└── mybatis-plus-common.yaml            # 公共配置文件

yl-commons/yl-commons-entity/src/main/java/com/tuowan/yeliao/commons/entity/plus/
└── BaseEntity.java                     # 基础实体类

yl-commons/yl-commons-data/src/main/java/com/tuowan/yeliao/commons/data/mapper/plus/
└── BasePlusMapper.java                 # 基础 Mapper 接口

# 业务模块示例（去掉前缀的新实现）
yl-service-social/yl-social-entity/src/main/java/com/tuowan/yeliao/social/entity/plus/
└── Post.java                           # 动态实体类（去掉 s_ 前缀）

yl-service-social/yl-social-data/src/main/java/com/tuowan/yeliao/social/data/mapper/plus/
└── PostMapper.java                     # 动态 Mapper 接口

yl-service-social/yl-social-svr/src/main/java/com/tuowan/yeliao/social/service/plus/
└── PostService.java                    # 动态 Service 类

yl-service-social/yl-social-web/src/main/java/com/tuowan/yeliao/social/web/controller/plus/
└── PostController.java                 # 动态 Controller 类

yl-service-social/yl-social-svr/src/main/resources/
└── application-mybatis-plus.yaml       # 简化的业务模块配置

yl-service-social/yl-social-svr/src/test/java/com/tuowan/yeliao/social/service/plus/
└── PostServiceTest.java                # 动态服务测试类

sql/
└── mybatis_plus_demo.sql               # 数据库建表脚本

MYBATIS_PLUS_INTEGRATION_GUIDE.md       # 本集成指南
```

## 总结

本次 MyBatis-Plus 集成和优化实现了以下目标：

### 第一阶段：基础集成
1. **无侵入集成** - 与现有 easyooo-framework 完全兼容，不影响现有代码
2. **条件化加载** - 通过配置开关控制是否启用 MyBatis-Plus
3. **包隔离策略** - 使用独立的包结构避免冲突
4. **完整功能支持** - 包含分页、乐观锁、逻辑删除等常用功能

### 第二阶段：配置优化和命名规范
1. **公共配置抽取** - 创建 `mybatis-plus-common.yaml` 避免重复配置
2. **环境隔离** - 支持 dev/test/prod 不同环境的配置
3. **命名规范优化** - 去掉数据库表前缀，使用标准 Java 命名
4. **枚举增强** - 为状态字段提供枚举类型，增强代码可读性
5. **工具方法** - 实体类包含便捷的工具方法和状态判断
6. **代码生成器** - 提供自动化代码生成工具

### 核心优势
- **配置简化** - 业务模块只需引用公共配置即可
- **开发效率** - 标准化的命名和丰富的工具方法
- **代码质量** - 枚举类型和工具方法提升代码可读性
- **易于维护** - 统一的配置管理和代码结构

开发者可以在新业务中使用 MyBatis-Plus 的便捷功能，享受简化的配置和标准化的开发体验，同时保持现有系统的稳定性。
