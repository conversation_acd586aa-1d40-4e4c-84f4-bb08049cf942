# MyBatis-Plus 3.5.12 集成指南

## 概述

本项目已成功集成 MyBatis-Plus 3.5.12 版本，与现有的 easyooo-framework DAO 层方案共存。新旧方案可以同时使用，互不影响。

## 架构设计

### 1. 双方案共存架构

- **旧方案（easyooo-framework）**：继续使用现有的 `@Repository` + `@Table` 注解的 Mapper 接口
- **新方案（MyBatis-Plus）**：使用 `@Mapper` 注解的 Mapper 接口，继承 `BaseMapper`

### 2. 包路径隔离

- **旧方案包路径**：`com.tuowan.yeliao.**.persistence.**`
- **新方案包路径**：`com.tuowan.yeliao.**.mapper.plus.**`
- **新方案实体包路径**：`com.tuowan.yeliao.**.entity.plus.**`

### 3. 配置隔离

- **旧方案 SqlSessionFactory**：`sqlSessionFactory`
- **新方案 SqlSessionFactory**：`mybatisPlusSqlSessionFactory`

## 使用方式

### 1. 启用 MyBatis-Plus

在应用配置文件中添加：

```yaml
mybatis-plus:
  enabled: true
```

或者引入专用配置文件：

```yaml
spring:
  profiles:
    include: mybatis-plus
```

### 2. 创建实体类

继承 `BaseEntity` 基础实体类：

```java
@TableName("your_table_name")
public class YourEntity extends BaseEntity {
    
    @TableField("column_name")
    private String fieldName;
    
    // getter and setter
}
```

### 3. 创建 Mapper 接口

```java
@Mapper
public interface YourEntityMapper extends BasePlusMapper<YourEntity> {
    
    // 自定义查询方法
    @Select("SELECT * FROM your_table WHERE field = #{value}")
    List<YourEntity> selectByField(@Param("value") String value);
}
```

### 4. 创建 Service 类

```java
@Service
@ConditionalOnProperty(name = "mybatis-plus.enabled", havingValue = "true")
public class YourEntityService extends ServiceImpl<YourEntityMapper, YourEntity> {
    
    public List<YourEntity> getByField(String value) {
        LambdaQueryWrapper<YourEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(YourEntity::getFieldName, value);
        return list(wrapper);
    }
}
```

## 功能特性

### 1. 基础 CRUD 操作

MyBatis-Plus 提供了丰富的基础 CRUD 操作：

- `save(entity)` - 保存
- `getById(id)` - 根据ID查询
- `updateById(entity)` - 根据ID更新
- `removeById(id)` - 根据ID删除（逻辑删除）
- `list(wrapper)` - 条件查询列表
- `page(page, wrapper)` - 分页查询

### 2. 条件构造器

支持 Lambda 表达式的条件构造：

```java
LambdaQueryWrapper<Entity> wrapper = new LambdaQueryWrapper<>();
wrapper.eq(Entity::getField1, value1)
       .like(Entity::getField2, value2)
       .orderByDesc(Entity::getCreateTime);
```

### 3. 分页查询

```java
Page<Entity> page = new Page<>(current, size);
IPage<Entity> result = mapper.selectPage(page, wrapper);
```

### 4. 自动填充

支持创建时间、更新时间等字段的自动填充：

```java
@TableField(value = "create_time", fill = FieldFill.INSERT)
private Date createTime;

@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
private Date updateTime;
```

### 5. 逻辑删除

支持逻辑删除功能：

```java
@TableLogic
@TableField(value = "deleted", fill = FieldFill.INSERT)
private Integer deleted;
```

## 示例代码

项目中已提供完整的示例代码：

- **实体类**：`SPostPlus.java`
- **Mapper 接口**：`SPostPlusMapper.java`
- **Service 类**：`SPostPlusService.java`
- **Controller 类**：`SPostPlusController.java`

## 注意事项

### 1. 版本兼容性

- MyBatis-Plus 3.5.12 与 Spring Boot 2.4.2 完全兼容
- 与现有的 easyooo-framework 无冲突

### 2. 性能考虑

- MyBatis-Plus 提供了更简洁的 API，减少了样板代码
- 支持多种优化策略，如批量操作、分页优化等

### 3. 迁移策略

- 新业务推荐使用 MyBatis-Plus 方案
- 旧业务可以逐步迁移，也可以继续使用原方案
- 两套方案可以在同一个项目中并存

### 4. 开发建议

- 使用 `@ConditionalOnProperty` 注解确保只在启用时加载相关 Bean
- 遵循包路径命名规范，避免与现有代码冲突
- 充分利用 MyBatis-Plus 的代码生成器提高开发效率

## 配置说明

### 1. 必要依赖

已在父 pom.xml 中配置：

```xml
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.5.12</version>
</dependency>
```

### 2. 配置类

- `MybatisPlusAutoConfiguration` - 主配置类
- `MybatisPlusConfig` - 元数据处理器

### 3. 启用配置

在 `application.yaml` 中添加：

```yaml
mybatis-plus:
  enabled: true
```

## 快速启动

### 1. 启用 MyBatis-Plus

在 `application.yaml` 中添加：

```yaml
spring:
  profiles:
    include: mybatis-plus
```

### 2. 运行测试

执行测试类验证集成是否成功：

```bash
mvn test -Dtest=SPostPlusServiceTest
```

### 3. 访问示例接口

启动应用后，可以访问以下接口：

- `GET /api/v2/post/{id}` - 获取动态详情
- `GET /api/v2/post/user/{userId}` - 获取用户动态列表
- `POST /api/v2/post/create` - 创建新动态
- `GET /api/v2/post/hot` - 获取热门动态

### 4. 数据库准备

执行 `sql/mybatis_plus_demo.sql` 中的建表语句和测试数据。

## 文件清单

本次集成新增的文件：

```
yl-commons/yl-commons-config/src/main/java/com/tuowan/yeliao/commons/config/mybatis/
├── MybatisPlusAutoConfiguration.java    # MyBatis-Plus 主配置类
└── MybatisPlusConfig.java              # 元数据处理器

yl-commons/yl-commons-entity/src/main/java/com/tuowan/yeliao/commons/entity/plus/
└── BaseEntity.java                     # 基础实体类

yl-commons/yl-commons-data/src/main/java/com/tuowan/yeliao/commons/data/mapper/plus/
└── BaseMapper.java                     # 基础 Mapper 接口

yl-service-social/yl-social-entity/src/main/java/com/tuowan/yeliao/social/entity/plus/
└── SPostPlus.java                      # 示例实体类

yl-service-social/yl-social-data/src/main/java/com/tuowan/yeliao/social/data/mapper/plus/
└── SPostPlusMapper.java                # 示例 Mapper 接口

yl-service-social/yl-social-svr/src/main/java/com/tuowan/yeliao/social/service/plus/
└── SPostPlusService.java               # 示例 Service 类

yl-service-social/yl-social-web/src/main/java/com/tuowan/yeliao/social/web/controller/plus/
└── SPostPlusController.java            # 示例 Controller 类

yl-service-social/yl-social-svr/src/main/resources/
└── application-mybatis-plus.yaml       # MyBatis-Plus 配置文件

yl-service-social/yl-social-svr/src/test/java/com/tuowan/yeliao/social/service/plus/
└── SPostPlusServiceTest.java           # 测试类

sql/
└── mybatis_plus_demo.sql               # 数据库建表脚本

MYBATIS_PLUS_INTEGRATION_GUIDE.md       # 本集成指南
```

## 总结

通过以上配置，项目已成功集成 MyBatis-Plus 3.5.12，实现了新旧方案的完美共存。开发人员可以：

1. 继续使用现有的 easyooo-framework 方案开发和维护旧功能
2. 使用新的 MyBatis-Plus 方案开发新功能，享受更简洁的 API 和更强大的功能
3. 根据项目需要，逐步将旧代码迁移到新方案

这种设计确保了项目的平滑过渡和技术栈的现代化升级，降低了上手门槛，提高了开发效率。
