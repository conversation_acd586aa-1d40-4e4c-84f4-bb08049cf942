### easyooo-framework是什么？
一个集成型框架，包含Redis缓存的自动同步模块，业务规则模块，分库分表模块，及一阶段事务、ZK统一配置的支持。很显然它是一个多功能项目，包含很多有用的模块，但各个模块是可相互独立纯在的，目前没有剥离开，所以您可能在POM文件中会看到一些陌生的SCM URL & Maven Proxy URL，请替换这些或者直接删除。

----------

### 具备哪些特性？
* Cache模块支持JVM、Redis两级同步，一阶段事务，集群模式的JVM同步

---------- 


### 开源协议
[Apache License, Version 2.0](http://www.apache.org/licenses/LICENSE-2.0.html) 

----------

### 联系我们
* [Issues&Bugs](https://github.com/leopardoooo/easyooo-framework/issues)
* 欢迎参与项目，只需在Github上fork、pull request即可。

----------
