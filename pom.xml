<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.easyooo</groupId>
	<artifactId>easyooo-framework</artifactId>
	<version>2023.3</version>
	<name>easyooo-framework</name>
	
	<licenses>
		<license>
			<name>${license.name}</name>
			<distribution>empty</distribution>
			<comments> easyooo all rights reserved, don't 
			allow any form of copying and selling</comments>
		</license>
	</licenses>
	
	<developers>
		<developer>
			<name>Killer Huang</name>
			<email><EMAIL></email>
			<url>http://leopardoooo.iteye.com/</url>
			<roles>
				<role>developer</role>
			</roles>
			<timezone>-8</timezone>
		</developer>
	</developers>
	
	<properties>
		<!-- common properties -->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<license.name>easyooo</license.name>
		
		<!-- test scope -->
		<junit-version>4.11</junit-version>
		<hamcrest-version>1.3</hamcrest-version>
		<mockito-version>1.9.5</mockito-version>
		<druid-version>1.0.7</druid-version>
		
		<!-- The third party framework -->
		<commons-pool2-version>2.11.1</commons-pool2-version>
		<redis-clients-version>4.0.1</redis-clients-version>
		<curator-version>2.4.0</curator-version>
		<rhino-version>1.7R4</rhino-version>
		<groovy-version>2.2.1</groovy-version>
		<mybatis-version>3.4.6</mybatis-version>
		<mybatis-spring-version>2.0.7</mybatis-spring-version>
		<spring-version>3.2.8.RELEASE</spring-version>
		<aspectj-version>1.7.4</aspectj-version>
		<slf4j-version>1.7.6</slf4j-version>
		<commons-io-version>2.4</commons-io-version>
		<cglib-nodep-version>3.1</cglib-nodep-version>
		<fastjson-version>1.2.79</fastjson-version>
		<rocketmq-version>3.1.8</rocketmq-version>
		
		<!-- provided scope -->
		<lombok-version>1.12.6</lombok-version>
	</properties>
	
	<dependencies> 
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>${junit-version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.hamcrest</groupId>
					<artifactId>hamcrest-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-all</artifactId>
			<version>${hamcrest-version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-all</artifactId>
			<version>${mockito-version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
	        <groupId>org.springframework</groupId>
	        <artifactId>spring-context</artifactId>
	        <version>${spring-version}</version>
	    </dependency>
	    <dependency>
	        <groupId>org.springframework</groupId>
	        <artifactId>spring-context-support</artifactId>
	        <version>${spring-version}</version>
	    </dependency>
	    <dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
			<version>${aspectj-version}</version>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>${aspectj-version}</version>
		</dependency>
	    <dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>${spring-version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j-version}</version>
		</dependency>
		<!--
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>${slf4j-version}</version>
		</dependency>
		-->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
			<version>${slf4j-version}</version>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>${redis-clients-version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-pool2</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-framework</artifactId>
			<version>${curator-version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-recipes</artifactId>
			<version>${curator-version}</version>
		</dependency>
		-->
		<dependency>
			<groupId>org.mozilla</groupId>
			<artifactId>rhino</artifactId>
			<version>${rhino-version}</version>
		</dependency>
		<dependency> 
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy</artifactId>
			<version>${groovy-version}</version>
		</dependency>
		<dependency> 
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
			<version>${spring-version}</version>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis-spring</artifactId>
			<version>${mybatis-spring-version}</version>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis</artifactId>
			<version>${mybatis-version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>${commons-pool2-version}</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${commons-io-version}</version>
		</dependency>
		<!--
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok-version}</version>
			<scope>provided</scope>
		</dependency>
		-->
		<dependency>
	        <groupId>com.alibaba</groupId>
	        <artifactId>druid</artifactId>
	        <version>${druid-version}</version>
	        <scope>test</scope>
	    </dependency>
	    <dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.29</version>
			 <scope>test</scope>
		</dependency>
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib-nodep</artifactId>
			<version>${cglib-nodep-version}</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson-version}</version>
		</dependency>
		<!--
		<dependency>
		    <groupId>com.alibaba.rocketmq</groupId>
		    <artifactId>rocketmq-client</artifactId>
		    <version>${rocketmq-version}</version>
		</dependency>
		-->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.7</version>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>2.10</version>
		</dependency>
		<dependency>
			<groupId>net.coobird</groupId>
			<artifactId>thumbnailator</artifactId>
			<version>0.4.8</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.4.9</version>
		</dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.5</version>
        </dependency>
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.3</version>
		</dependency>
		<dependency>
			<groupId>com.belerweb</groupId>
			<artifactId>pinyin4j</artifactId>
			<version>2.5.0</version>
		</dependency>
	</dependencies>
	
  	<scm>
		<connection>scm:git:**************:leopardoooo/easyooo-framework.git</connection>
    	<developerConnection>scm:git:**************:leopardoooo/easyooo-framework.git</developerConnection>
  	  <tag>easyooo-framework-1.0.40</tag>
    </scm>
	
	<distributionManagement>
		<repository>
			<!--id的名字可以任意取，但是在setting文件中的属性<server>的ID与这里一致-->
			<id>2545593-release-6J2QSy</id>
			<!--指向仓库类型为host(宿主仓库）的储存类型为Release的仓库-->
			<url>https://packages.aliyun.com/6853744d48e0f3c8a5c3dc3e/maven/2545593-release-6j2qsy</url>
		</repository>
		<snapshotRepository>
			<id>2545593-snapshot-hGEHxB</id>
			<!--指向仓库类型为host(宿主仓库）的储存类型为Snapshot的仓库-->
			<url>https://packages.aliyun.com/6853744d48e0f3c8a5c3dc3e/maven/2545593-snapshot-hgehxb</url>
		</snapshotRepository>
	</distributionManagement>
	
	<build>
		<plugins>
			<plugin>
		        <groupId>org.apache.maven.plugins</groupId>
		        <artifactId>maven-surefire-plugin</artifactId>
		        <configuration>
		          <skip>true</skip>
		        </configuration>
		    </plugin>
			<plugin> 
			    <artifactId>maven-compiler-plugin</artifactId>
			    <configuration>
			        <source>1.7</source>
			        <target>1.7</target>
			        <encoding>UTF-8</encoding>
			    </configuration>
			</plugin>
			<plugin>
			    <artifactId>maven-resources-plugin</artifactId>
			    <configuration>
			        <encoding>UTF-8</encoding>
			    </configuration>
			</plugin>
			<plugin>
		        <groupId>org.apache.maven.plugins</groupId>
		        <artifactId>maven-release-plugin</artifactId>
		        <version>2.4.2</version>
		        <configuration>  
                    <checkModificationExcludes>  
                        <checkModificationExclude>.project</checkModificationExclude>  
                        <checkModificationExclude>.classpath</checkModificationExclude>  
                    </checkModificationExcludes>  
                </configuration>  
		    </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
	</build>
	
</project>
