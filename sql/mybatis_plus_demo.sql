-- MyBatis-Plus 示例表结构
-- 用于演示 MyBatis-Plus 功能

-- 如果表已存在，可以添加 MyBatis-Plus 需要的字段
-- 注意：这里只是示例，实际使用时请根据现有表结构调整

-- 为现有的 s_post 表添加 MyBatis-Plus 支持的字段（如果不存在）
-- ALTER TABLE s_post ADD COLUMN IF NOT EXISTS `deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标识 0-未删除 1-已删除';
-- ALTER TABLE s_post ADD COLUMN IF NOT EXISTS `version` int(11) DEFAULT 0 COMMENT '版本号（乐观锁）';

-- 创建一个新的示例表，完全按照 MyBatis-Plus 规范设计
CREATE TABLE IF NOT EXISTS `s_post_plus_demo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `post_id` bigint(20) NOT NULL COMMENT '动态ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `title` varchar(200) DEFAULT NULL COMMENT '动态标题',
  `content` text COMMENT '动态内容',
  `post_type` tinyint(4) DEFAULT 1 COMMENT '动态类型 1-文字 2-图片 3-视频',
  `audit_status` tinyint(4) DEFAULT 0 COMMENT '审核状态 0-待审核 1-审核通过 2-审核拒绝',
  `status` tinyint(4) DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
  `praise_num` int(11) DEFAULT 0 COMMENT '点赞数',
  `comment_num` int(11) DEFAULT 0 COMMENT '评论数',
  `share_num` int(11) DEFAULT 0 COMMENT '分享数',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标识 0-未删除 1-已删除',
  `version` int(11) DEFAULT 0 COMMENT '版本号（乐观锁）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_post_type` (`post_type`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动态表（MyBatis-Plus 示例）';

-- 插入一些测试数据
INSERT INTO `s_post_plus_demo` (`post_id`, `user_id`, `title`, `content`, `post_type`, `audit_status`, `status`, `praise_num`, `comment_num`, `share_num`) VALUES
(1001, 1001, '第一条动态', '这是使用 MyBatis-Plus 创建的第一条动态', 1, 1, 1, 5, 2, 1),
(1002, 1001, '第二条动态', '这是使用 MyBatis-Plus 创建的第二条动态', 2, 1, 1, 10, 5, 3),
(1003, 1002, '用户1002的动态', '另一个用户的动态内容', 1, 1, 1, 8, 3, 2),
(1004, 1001, '热门动态', '这是一条热门动态，有很多点赞', 1, 1, 1, 50, 20, 10),
(1005, 1003, '待审核动态', '这是一条待审核的动态', 1, 0, 1, 0, 0, 0);

-- 创建用户表示例（如果需要关联查询）
CREATE TABLE IF NOT EXISTS `u_user_plus_demo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(200) DEFAULT NULL COMMENT '头像URL',
  `sex` tinyint(4) DEFAULT 0 COMMENT '性别 0-未知 1-男 2-女',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `signature` varchar(200) DEFAULT NULL COMMENT '个性签名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标识 0-未删除 1-已删除',
  `version` int(11) DEFAULT 0 COMMENT '版本号（乐观锁）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_nickname` (`nickname`),
  KEY `idx_email` (`email`),
  KEY `idx_phone` (`phone`),
  KEY `idx_city` (`city`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表（MyBatis-Plus 示例）';

-- 插入用户测试数据
INSERT INTO `u_user_plus_demo` (`user_id`, `username`, `nickname`, `email`, `phone`, `sex`, `city`, `signature`) VALUES
(1001, 'user1001', '用户1001', '<EMAIL>', '13800138001', 1, '北京', '这是用户1001的个性签名'),
(1002, 'user1002', '用户1002', '<EMAIL>', '13800138002', 2, '上海', '这是用户1002的个性签名'),
(1003, 'user1003', '用户1003', '<EMAIL>', '13800138003', 1, '广州', '这是用户1003的个性签名');

-- 查询示例
-- 1. 基础查询
-- SELECT * FROM s_post_plus_demo WHERE deleted = 0;

-- 2. 分页查询
-- SELECT * FROM s_post_plus_demo WHERE deleted = 0 ORDER BY create_time DESC LIMIT 0, 10;

-- 3. 条件查询
-- SELECT * FROM s_post_plus_demo WHERE user_id = 1001 AND deleted = 0 ORDER BY create_time DESC;

-- 4. 统计查询
-- SELECT COUNT(*) FROM s_post_plus_demo WHERE user_id = 1001 AND deleted = 0;

-- 5. 关联查询
-- SELECT p.*, u.nickname, u.avatar 
-- FROM s_post_plus_demo p 
-- LEFT JOIN u_user_plus_demo u ON p.user_id = u.user_id 
-- WHERE p.deleted = 0 AND u.deleted = 0 
-- ORDER BY p.create_time DESC;
