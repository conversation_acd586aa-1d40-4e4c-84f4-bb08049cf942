package com.easyooo.framework.support.elasticsearch;

import java.lang.annotation.*;

/**
 * 用来标记elasticsearch字段的映射类型
 * <AUTHOR>
 * @date 2020/12/15 9:47
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ESMapping {

    /**
     * 字段类型
     *
     * @return
     */
    MappingType type() default MappingType.Auto;

    /**
     * 索引方式
     *
     * @return
     */
    IndexType index() default IndexType.Not_Analyzed;
}
