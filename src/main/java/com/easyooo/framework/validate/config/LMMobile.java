package com.easyooo.framework.validate.config;

import com.easyooo.framework.validate.LMConstraint;
import com.easyooo.framework.validate.impl.LMMobileValidator;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 可以匹配字符串类型的手机号码，也可以匹配数字类型的手机号码
 *
 * <AUTHOR>
 * @date 2018/6/6 15:13
 */
@LMConstraint(validatedBy = LMMobileValidator.class)
@Documented
@Target({ FIELD })
@Retention(RUNTIME)
public @interface LMMobile {

    String lengthMessage() default "手机号码长度固定为11位数字";

    String patternMessage() default "手机号码格式错误";

}
