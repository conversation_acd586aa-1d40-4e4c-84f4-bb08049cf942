package com.easyooo.framework.validate.config;

import com.easyooo.framework.validate.LMConstraint;
import com.easyooo.framework.validate.impl.LMNotEmptyValidator;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <p>不允许内容为空的验证器，主要验证</p>
 * <ul>
 *     <li>字符串是否有内容</li>
 *     <li>集合至少包含一项元素</li>
 *     <li>Map至少包含一项元素</li>
 *     <li>数组至少包含一项元素</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2018/6/6 13:49
 */
@LMConstraint(validatedBy = LMNotEmptyValidator.class)
@Documented
@Target({ FIELD })
@Retention(RUNTIME)
public @interface LMNotEmpty {

    /**
     * 作为字符串的提示文本
     * @return
     */
    String stringMessage() default "{label}不允许为空";

    /**
     * 当作为集合的时候错误提示文本
     * @return
     */
    String collectionMessage() default "{label}至少需要包含一项";

    /**
     * 该配置会替换验证不合法消息的变量
     */
    String label() default "";

}
