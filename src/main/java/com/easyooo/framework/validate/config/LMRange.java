package com.easyooo.framework.validate.config;

import com.easyooo.framework.validate.LMConstraint;
import com.easyooo.framework.validate.impl.LMLengthValidator;
import com.easyooo.framework.validate.impl.LMRangeValidator;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <p>数值范围验证</p>
 *
 * <AUTHOR>
 * @date 2018/7/20 11:25
 */
@LMConstraint(validatedBy = LMRangeValidator.class)
@Documented
@Target({ FIELD })
@Retention(RUNTIME)
public @interface LMRange {

    int min() default Integer.MIN_VALUE;

    int max() default Integer.MAX_VALUE;

    String label() default "";

    String minMessage() default "{label}的值不能小于{min}";

    String maxMessage() default "{label}的值不能大于{max}";

    String eqMessage() default "{label}的值必须为{min}";
}
