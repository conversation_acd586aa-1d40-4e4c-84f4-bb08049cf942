package com.easyooo.framework.validate.config;

import com.easyooo.framework.validate.LMConstraint;
import com.easyooo.framework.validate.impl.LMUrlValidator;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR>
 * @date 2018/6/6 16:02
 */
@LMConstraint(validatedBy = LMUrlValidator.class)
@Documented
@Target({ FIELD })
@Retention(RUNTIME)
public @interface LMUrl {

    String emptyMessage() default "{label}不能为空";

    String patternMessage() default "{label}不是一个合法的网址";

    String label() default "";

}
