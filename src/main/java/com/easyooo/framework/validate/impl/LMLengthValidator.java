/*!
 * Copyright 2018, Julun, Inc.
 */

package com.easyooo.framework.validate.impl;

import com.easyooo.framework.common.util.MsgUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.easyooo.framework.validate.ConstraintValidator;
import com.easyooo.framework.validate.ValidException;
import com.easyooo.framework.validate.ValidResult;
import com.easyooo.framework.validate.config.LMLength;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/6/6 14:18
 * @see LMLength
 */
public class LMLengthValidator implements ConstraintValidator<LMLength, Object> {

    @Override
    public ValidResult isValid(LMLength config, String fieldName, Object value,
                               Object parent) throws ValidException {
        // 先验证是否为null
        if (value == null) {
            return ValidResult.ok();
        }

        // 最大长度
        int max = config.max() < 0 ? Integer.MAX_VALUE : config.max();
        int min = config.min() < 0 ? 0 : config.min();
        int len = 0;

        if (value instanceof String) {
            // 字符串类型
            len = ((String) value).trim().length();
        } else if (value instanceof Collection) {
            // 集合类型
            len = ((Collection) value).size();
        } else if (value instanceof Map) {
            // Map类型
            len = ((Map) value).size();
        } else if (value.getClass().isArray()) {
            // 数组
            len = ((Object[]) value).length;
        } else {
            throw new ValidException("该注解" + config + "不支持" + value.getClass() + "类型");
        }

        if (len < min || len > max) {
            return newMessageResult(config, fieldName);
        }

        return ValidResult.ok();
    }

    private ValidResult newMessageResult(LMLength config, String fieldName) {
        String msg = null;

        String label = config.label();
        if (StringUtils.isEmpty(label)) {
            label = "参数" + fieldName;
        }

        if (config.min() >= 0 && config.max() >= 0) {
            if (config.min() == config.max()) {
                msg = MsgUtils.formatTpl(config.eqMessage(),
                        "label", label,
                        "min", config.min());
            } else {
                msg = MsgUtils.formatTpl(config.minMaxMessage(),
                        "label", label,
                        "min", config.min(),
                        "max", config.max());
            }
        } else if (config.min() >= 0) {
            msg = MsgUtils.formatTpl(config.minMessage(),
                    "label", label,
                    "min", config.min());
        } else if (config.max() >= 0) {
            msg = MsgUtils.formatTpl(config.maxMessage(),
                    "label",label,
                    "max", config.max());
        }

        return ValidResult.fail(msg);
    }
}
