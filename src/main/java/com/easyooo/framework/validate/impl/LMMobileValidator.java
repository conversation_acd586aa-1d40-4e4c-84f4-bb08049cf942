package com.easyooo.framework.validate.impl;

import com.easyooo.framework.common.util.StringUtils;
import com.easyooo.framework.validate.ConstraintValidator;
import com.easyooo.framework.validate.ValidResult;
import com.easyooo.framework.validate.config.LMMobile;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2018/6/6 15:17
 * @see LMMobile
 */
public class LMMobileValidator implements ConstraintValidator<LMMobile, Object> {

    static final Pattern MOIBILE = Pattern.compile("^\\d{11}$");

    @Override
    public ValidResult isValid(LMMobile config, String fieldName, Object value, Object parent){
        if (value == null || StringUtils.isEmpty(value.toString())) {
            return ValidResult.ok();
        }
        // 根据运营商进行匹配
        String mb = value.toString();
        if (MOIBILE.matcher(mb).matches()) {
            return ValidResult.ok();
        }
        return ValidResult.fail(config.patternMessage());
    }
}
