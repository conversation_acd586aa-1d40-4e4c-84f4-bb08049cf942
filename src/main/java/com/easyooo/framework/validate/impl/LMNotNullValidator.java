package com.easyooo.framework.validate.impl;

import com.easyooo.framework.common.util.MsgUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.easyooo.framework.validate.ConstraintValidator;
import com.easyooo.framework.validate.ValidResult;
import com.easyooo.framework.validate.config.LMNotNull;

/**
 * @see LMNotNull
 */
public class LMNotNullValidator implements ConstraintValidator<LMNotNull, Object> {

    @Override
    public ValidResult isValid(LMNotNull config, String fieldName, Object value, Object parent) {

        String label = config.label();
        if (StringUtils.isEmpty(label)) {
            label = "参数" + fieldName;
        }
        if (value == null) {
            String msg = MsgUtils.formatTpl(config.message(), "label", label);
            return ValidResult.fail(msg);
        }

        return ValidResult.ok();
    }
}
