package com.easyooo.framework.validate.impl;

import com.easyooo.framework.common.util.MsgUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.easyooo.framework.validate.ConstraintValidator;
import com.easyooo.framework.validate.ValidException;
import com.easyooo.framework.validate.ValidResult;
import com.easyooo.framework.validate.config.LMRange;

/**
 * 数值范围验证器
 *
 * <AUTHOR>
 * @date 2018/7/20 11:24
 */
public class LMRangeValidator implements ConstraintValidator<LMRange, Object> {

    @Override
    public ValidResult isValid(LMRange config, String fieldName, Object value, Object parent) throws ValidException {
        if (value == null) {
            return ValidResult.ok();
        }
        String label = config.label();
        if (StringUtils.isEmpty(label)) {
            label = "参数" + fieldName;
        }
        int val = (int)value;
        if (val < config.min()) {
            String msg = MsgUtils.formatTpl(config.minMessage(), "label", label, "min", config.min());
            return ValidResult.fail(msg);
        }
        if (val > config.max()) {
            String msg = MsgUtils.formatTpl(config.maxMessage(), "label", label, "max", config.max());
            return ValidResult.fail(msg);
        }
        if (config.min() == config.max()) {
            if (val != config.min()) {
                String msg = MsgUtils.formatTpl(config.eqMessage(), "label", label, "min", config.min());
                return ValidResult.fail(msg);
            }
        }
        return ValidResult.ok();
    }
}
