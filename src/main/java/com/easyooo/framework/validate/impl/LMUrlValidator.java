/*!
 * Copyright 2018, Julun, Inc.
 */

package com.easyooo.framework.validate.impl;

import com.easyooo.framework.common.util.MsgUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.easyooo.framework.validate.ConstraintValidator;
import com.easyooo.framework.validate.ValidResult;
import com.easyooo.framework.validate.config.LMUrl;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2018/6/6 16:07
 */
public class LMUrlValidator implements ConstraintValidator<LMUrl, String> {

    static final Pattern PATTERN = Pattern.compile("^([hH][tT]{2}[pP]:/*|[hH][tT]{2}[pP][sS]:/*|[fF][tT][pP]:/*)(" +
            "([A-Za-z0-9-~]+).)+" +
            "([A-Za-z0-9-~\\/])+(\\?{0,1}(([A-Za-z0-9-~]+\\={0,1})([A-Za-z0-9-~]*)\\&{0,1})*)$");

    @Override
    public ValidResult isValid(LMUrl config, String fieldName, String url, Object parent) {
        if (StringUtils.isEmpty(url)) {
            return ValidResult.ok();
        }
        String label = config.label();
        if (StringUtils.isEmpty(label)) {
            label = "参数" + fieldName;
        }
        if (!PATTERN.matcher(url).matches()) {
            return ValidResult.fail(MsgUtils.formatTpl(config.patternMessage(), "label", label));
        }

        return ValidResult.ok();
    }
}
