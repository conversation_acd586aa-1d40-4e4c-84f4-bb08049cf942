<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.easyooo.framework.demo.mapper.TUserDetailMapper" >
  <resultMap id="BaseResultMap" type="com.easyooo.framework.demo.domain.TUserDetail" >
    <id column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="sex" property="sex" jdbcType="CHAR" />
    <result column="linkman" property="linkman" jdbcType="VARCHAR" />
    <result column="tel" property="tel" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    user_id, sex, linkman, tel
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_user_detail
    where user_id = #{userId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_user_detail
    where user_id = #{userId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.easyooo.framework.demo.domain.TUserDetail" >
    insert into t_user_detail (user_id, sex, linkman, 
      tel)
    values (#{userId,jdbcType=BIGINT}, #{sex,jdbcType=CHAR}, #{linkman,jdbcType=VARCHAR}, 
      #{tel,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.easyooo.framework.demo.domain.TUserDetail" >
    insert into t_user_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="userId != null" >
        user_id,
      </if>
      <if test="sex != null" >
        sex,
      </if>
      <if test="linkman != null" >
        linkman,
      </if>
      <if test="tel != null" >
        tel,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="sex != null" >
        #{sex,jdbcType=CHAR},
      </if>
      <if test="linkman != null" >
        #{linkman,jdbcType=VARCHAR},
      </if>
      <if test="tel != null" >
        #{tel,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.easyooo.framework.demo.domain.TUserDetail" >
    update t_user_detail
    <set >
      <if test="sex != null" >
        sex = #{sex,jdbcType=CHAR},
      </if>
      <if test="linkman != null" >
        linkman = #{linkman,jdbcType=VARCHAR},
      </if>
      <if test="tel != null" >
        tel = #{tel,jdbcType=INTEGER},
      </if>
    </set>
    where user_id = #{userId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.easyooo.framework.demo.domain.TUserDetail" >
    update t_user_detail
    set sex = #{sex,jdbcType=CHAR},
      linkman = #{linkman,jdbcType=VARCHAR},
      tel = #{tel,jdbcType=INTEGER}
    where user_id = #{userId,jdbcType=BIGINT}
  </update>
</mapper>