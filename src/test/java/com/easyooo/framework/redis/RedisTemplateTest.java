package com.easyooo.framework.redis;

import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.Assert.assertThat;

import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;

import redis.clients.jedis.JedisPool;
import redis.clients.jedis.exceptions.JedisNoScriptException;

import com.easyooo.framework.support.redis.RedisTemplate;
import com.easyooo.framework.support.redis.jedis.JedisOperation;

/**
 * Redis Template Unit Test Case
 *
 * <AUTHOR>
 */
public class RedisTemplateTest {
	
	static JedisPool pool;
	static RedisTemplate redisTemplate;
	
	@BeforeClass
	public static void setUp(){
		pool = new JedisPool("**************", 7379);
		JedisOperation opt = new JedisOperation(pool);

		redisTemplate = new TestRedisTemplate();
		redisTemplate.setRedisOperation(opt);
	}
	
	@AfterClass
	public static void destory(){
		pool.destroy();
	}
	
	@Test
	public void testMoreCmd()throws Exception{
		// 测试Redis更多的命令
		String res = redisTemplate.set("test", "100", "nx", "px", 60 * 1000L);
		System.out.println(res);
		res = redisTemplate.set("test", "100", "nx", "px", 60 * 1000L);
		System.out.println(res);
		System.out.println("ttl: " + redisTemplate.ttl("test"));
		
		// delWhenEqual
		final String delWhenEqualScript = "if redis.call('get',KEYS[1]) == ARGV[1] then  return redis.call('del',KEYS[1])  else return 0 end";
		String delWhenEqualSha = redisTemplate.scriptLoad(delWhenEqualScript);
		System.out.println("sha: " + delWhenEqualSha);
		Object obj = redisTemplate.evalsha(delWhenEqualSha, 1, "1001");
		System.out.println("obj: " + obj);
		try{
			obj = redisTemplate.evalsha(delWhenEqualSha+"111", 1, "1001");
			System.out.println("obj: " + obj);
		}catch(JedisNoScriptException e){
			System.out.println("没有这个脚本哟: " + delWhenEqualSha);
		}
		obj = redisTemplate.evalsha(delWhenEqualSha, 1, "test" , "100");
		System.out.println("obj: " + obj);
	}
	
	@Test
	@Ignore
	public void testExpire()throws Exception{
		redisTemplate.set("aaa", "111");
		redisTemplate.expire("aaa", 3);
		assertThat(redisTemplate.get("aaa"), is("111"));
		Thread.sleep(4 * 1000L);
		assertThat(redisTemplate.get("aaa"), nullValue());
	}
	
	@Test
	@Ignore
	public void testPersist()throws Exception{
		redisTemplate.set("bbb", 3, "222");
		Thread.sleep(4 * 1000L);
		assertThat(redisTemplate.get("bbb"), nullValue());
		
		redisTemplate.set("bbb", 3, "222");
		redisTemplate.persist("bbb");
		Thread.sleep(4 * 1000L);
		assertThat(redisTemplate.get("bbb"), is("222"));
		redisTemplate.expire("bbb", 3);
	}
	
	@Test
	@Ignore
	public void testSmembers()throws Exception{
		System.out.println(redisTemplate.exists("a"));
	}
}
