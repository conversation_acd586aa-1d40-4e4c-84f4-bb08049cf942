<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	default-autowire="byName" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:jee="http://www.springframework.org/schema/jee" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:jdbc="http://www.springframework.org/schema/jdbc"
	xmlns:c="http://www.springframework.org/schema/c"
	xsi:schemaLocation="http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-3.2.xsd
		http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-3.2.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.1.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd">

	<!-- enable transaction demarcation with annotations -->
	<tx:annotation-driven />
	
	<!-- transaction manager, use JtaTransactionManager for global tx -->
	<bean id="transactionManager"
		class="com.easyooo.framework.support.transaction.ResourceAndSimpleDSTransactionManager">
		<property name="dataSource" ref="dataSource" />
	</bean>
	
	<!-- the transactional advice -->
	<tx:advice id="txAdvice" transaction-manager="transactionManager" >
		<!-- the transactional semantics... -->
		<tx:attributes>
			<!-- required  -->
			<tx:method name="save*" propagation="REQUIRED" rollback-for="java.lang.Throwable" />
			<tx:method name="create*" propagation="REQUIRED" rollback-for="java.lang.Throwable" />
			<tx:method name="insert*" propagation="REQUIRED" rollback-for="java.lang.Throwable" />
			<tx:method name="update*" propagation="REQUIRED"  rollback-for="java.lang.Throwable"/>
			<tx:method name="mod*" propagation="REQUIRED" rollback-for="java.lang.Throwable" />
			<tx:method name="del*" propagation="REQUIRED" rollback-for="java.lang.Throwable" />
			<tx:method name="add*" propagation="REQUIRED" rollback-for="java.lang.Throwable" />
			
			<!-- The others are read-only -->
			<tx:method name="*" read-only="true" rollback-for="java.lang.Throwable"/>
		</tx:attributes>
	</tx:advice>
	
	<!-- ensure that the above transactional advice runs for any execution of 
		an operation defined by the FooService interface -->
	<aop:config>
		<aop:pointcut id="serviceOperation"
			expression="execution(* com.easyooo.framework.demo.service..*.*(..))" />
		<aop:advisor advice-ref="txAdvice" pointcut-ref="serviceOperation" />	
	</aop:config>
</beans>
