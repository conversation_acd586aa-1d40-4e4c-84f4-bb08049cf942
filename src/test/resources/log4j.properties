# Copyright © 2014 YAOCHEN Corporation, All Rights Reserved.
       
# Rules reminder:
# DEBUG < INFO < WARN < ERROR < FATAL

# Global logging configuration
log4j.rootLogger=DEBUG, stdout

# jolbox data connection pool
log4j.logger.com.jolbox.bonecp=INFO
# My logging configuration...
log4j.logger.org.mybatis=DEBUG
log4j.logger.org.mybatis.spring=INFO
log4j.logger.org.apache.commons=INFO
#log4j.logger.com.easyooo.framework.cache.storage.LoggingCache=DEBUG
#log4j.logger.com.easyooo.framework.support.transaction.SimpleBufferedTransactionResource=INFO

# MyBatis Generator logging configuration...
log4j.logger.org.mybatis.generator=DEBUG

#Spring Logging configuration
log4j.logger.org.springframework=INFO
log4j.logger.org.springframework.core.io.support.PathMatchingResourcePatternResolver=INFO
# spring junit test debug output
log4j.logger.org.springframework.test.annotation.ProfileValueUtils=INFO
# spring properties 
log4j.logger.org.springframework.core.env.PropertySourcesPropertyResolver=INFO
# load database schema and data 
log4j.logger.org.springframework.jdbc.datasource.init.ResourceDatabasePopulator=INFO

## Console output...
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%5p %d{HH:mm:ss} [%c{1}:%L]- %m%n
