/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.commons.comp.grant;

import com.tuowan.yeliao.commons.comp.grant.dto.GrantDetailDTO;
import com.tuowan.yeliao.commons.data.entity.config.TAward;
import com.tuowan.yeliao.commons.data.entity.config.TAwardDetail;
import com.tuowan.yeliao.commons.data.enums.config.AwardGrantType;
import com.tuowan.yeliao.commons.data.enums.config.AwardType;

/**
 * 发奖接口定义
 *
 * <AUTHOR>
 * @date 2018/7/17 16:19
 */
public interface Grant {

    /**
     * 为该用户发奖，并返回奖励明细字符串，
     * 必须支持奖励类型 {@link #getSupportType()} 才会调用该方法
     *
     * @param grantType  奖励发放类型
     * @param userId     被奖励的用户编号
     * @param d          奖励配置
     * @param awardTimes 发奖次数
     * @param unAward    true待领取业务，false直接发奖业务
     * @param remark     奖励备注
     * @return
     */
    GrantDetailDTO doGrant(AwardGrantType grantType, Long userId, TAward cfg, TAwardDetail d, Integer awardTimes, boolean unAward, String remark);

    /**
     * 是否支持该奖励类型
     *
     * @return
     */
    AwardType getSupportType();

}
