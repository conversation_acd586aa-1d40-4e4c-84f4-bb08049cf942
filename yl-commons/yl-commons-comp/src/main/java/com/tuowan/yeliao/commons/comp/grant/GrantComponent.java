package com.tuowan.yeliao.commons.comp.grant;


import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.comp.grant.dto.*;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.ComponentException;
import com.tuowan.yeliao.commons.data.entity.config.TAward;
import com.tuowan.yeliao.commons.data.entity.config.TAwardDetail;
import com.tuowan.yeliao.commons.data.entity.user.*;
import com.tuowan.yeliao.commons.data.enums.config.AwardGrantType;
import com.tuowan.yeliao.commons.data.enums.config.AwardType;
import com.tuowan.yeliao.commons.data.enums.config.GoodsMergeType;
import com.tuowan.yeliao.commons.data.enums.general.ReviewResultType;
import com.tuowan.yeliao.commons.data.persistence.config.TAwardDetailMapper;
import com.tuowan.yeliao.commons.data.persistence.config.TAwardMapper;
import com.tuowan.yeliao.commons.data.persistence.user.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 发奖组建封装，该类不关心奖品怎么来，只做一件事情，负责根据奖品配置， 发奖到用户背包或业务信息中。并记录奖励明细
 * <AUTHOR>
 * @date 2020/6/30 18:08
 */
@Component
public class GrantComponent implements ApplicationContextAware{

    private Logger logger = LoggerFactory.getLogger(this.getClass());


    @Autowired
    private TAwardMapper tAwardMapper;
    @Autowired
    private TAwardDetailMapper tAwardDetailMapper;
    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private UUserAwardMapper uUserAwardMapper;
    @Autowired
    private UUserAwardDetailMapper uUserAwardDetailMapper;
    @Autowired
    private UUserUnawardDetailMapper uUserUnawardDetailMapper;
    @Autowired
    private UUserUnawardMapper uUserUnawardMapper;
    @Autowired
    private UUserUnawardHisMapper uUserUnawardHisMapper;
    @Autowired
    private UUserUnawardDetailHisMapper uUserUnawardDetailHisMapper;


    /** 发奖的各种实现类 */
    private Map<AwardType, Grant> grantSetImpl = new HashMap<>();

    /**
     * 针对不确定、可变奖励发奖，不需要领取和审核
     */
    public List<GrantDTO> saveCombineAutoGrant(Long userId, VariableAwardDTO variableAwardDTO) {
        // 加锁
        userLockTemplate.acquireTransactionLock(userId);
        List<GrantDTO> grantList = new ArrayList<>();
        List<GrantDetailDTO> grantDetails = new ArrayList<>();
        if (variableAwardDTO == null) {
            return grantList;
        }
        // 手动封装一个 TAward
        TAward awardCfg = new TAward(variableAwardDTO.getAwardCode());
        awardCfg.setName(variableAwardDTO.getAwardName());
        awardCfg.setAboutBeans(variableAwardDTO.getAboutBeans());

        // 手动封装一个 TAwardDetail
        TAwardDetail awardDetail = new TAwardDetail();
        awardDetail.setDetailId(GlobalConstant.SYS_PROD_ID);
        awardDetail.setParentCode(variableAwardDTO.getAwardCode());
        awardDetail.setAwardType(variableAwardDTO.getAwardType());
        awardDetail.setExtJsonCfg(variableAwardDTO.getUrlExtStr());
        awardDetail.setAwardTypeValue(String.valueOf(variableAwardDTO.getAwardValue()));
        awardDetail.setCount(1);
        awardDetail.setEndTime(variableAwardDTO.getEndTime());
        // 金币 & 积分 是按照count字段发奖的
        if(AwardType.Beans == variableAwardDTO.getAwardType() ||
                AwardType.PlatFormBeans == variableAwardDTO.getAwardType() ||
                AwardType.Cash == variableAwardDTO.getAwardType()){
            awardDetail.setCount(variableAwardDTO.getAwardValue().intValue());
        }

        AwardExtendDTO awardDTO = new AwardExtendDTO(variableAwardDTO);
        awardDTO.setAwardCfg(awardCfg);
        awardDTO.setDetails(Collections.singletonList(awardDetail));
        awardDTO.setGrantType(AwardGrantType.AutoGrant);

        grantDetails.addAll(this.doGrant(userId, awardDTO, userId));
        grantList.add(new GrantDTO(awardCfg.getAboutBeans() * variableAwardDTO.getAwardTimes(), grantDetails));
        return grantList;
    }

    /**
     * 自动领取的发奖，比如幸运礼物，不需要领取和审核
     *
     * @param userId 被奖励用户ID
     */
    public List<GrantDTO> saveCombineAutoGrant(Long userId, AwardDTO... awards) {
        // 加锁
        userLockTemplate.acquireTransactionLock(userId);
        List<GrantDTO> grantList = new ArrayList<>();
        List<GrantDetailDTO> grantDetails;
        // 逐个赠送
        for (AwardDTO award : awards) {
            if (award == null) {
                continue;
            }
            TAward awardCfg = tAwardMapper.selectByPrimaryKey(new TAward(award.getAwardCode()));
            if (awardCfg == null) {
                throw new ComponentException("奖励配置{}定义不存在", award.getAwardCode());
            }
            grantDetails = new ArrayList<>();
            List<TAwardDetail> details = tAwardDetailMapper.selectByCode(new TAwardDetail(award.getAwardCode()));
            if (details == null || details.isEmpty()) {
                throw new ComponentException("奖励配置{}不存在明细项目", award.getAwardCode());
            }

            AwardExtendDTO awardDTO = new AwardExtendDTO(award);
            awardDTO.setAwardCfg(awardCfg);
            awardDTO.setDetails(details);
            awardDTO.setGrantType(AwardGrantType.AutoGrant);

            grantDetails.addAll(this.doGrant(userId, awardDTO, userId));

            grantList.add(new GrantDTO(awardCfg.getAboutBeans() * award.getAwardTimes(), grantDetails));
        }
        return grantList;
    }

    /**
     * @see {@link #saveCombineAutoGrant(Long, AwardDTO...)}
     */
    public List<GrantDTO> saveCombineAutoGrant(Long userId, List<AwardDTO> awards) {
        return this.saveCombineAutoGrant(userId, awards.toArray(new AwardDTO[awards.size()]));
    }

    public List<GrantDetailDTO> saveAutoGrant(Long userId, AwardDTO... awards) {
        List<GrantDetailDTO> detailList = new ArrayList<>();
        List<GrantDTO> grantList = this.saveCombineAutoGrant(userId, awards);
        for (GrantDTO grant : grantList) {
            detailList.addAll(grant.getDetailList());
        }
        return detailList;
    }

    public List<GrantDetailDTO> saveAutoGrant(Long userId, List<AwardDTO> awards) {
        return this.saveAutoGrant(userId, awards.toArray(new AwardDTO[awards.size()]));
    }


    /**
     * 系统奖励金币
     * （奖励充值金币）
     * @param awardCode 比如：RedPacket_{redId}
     * @param userId
     * @param beans
     * @param grantRemark 奖励备注
     * @return
     */
    public List<GrantDetailDTO> saveGrantBeans(String awardCode, Long userId, Integer beans, String grantRemark) {
        TAward award = new TAward();
        award.setAboutBeans(beans);
        award.setCode(AwardGrantType.AutoGrant.name());
        award.setName("系统发放");

        List<TAwardDetail> details = new ArrayList<>();
        TAwardDetail detail = new TAwardDetail();
        detail.setDetailId(-1);
        detail.setParentCode(award.getCode());
        detail.setAwardType(AwardType.Beans);
        detail.setAwardTypeValue(AwardType.Beans.getId());
        detail.setCount(beans);
        details.add(detail);

        AwardExtendDTO awardDTO = new AwardExtendDTO(AwardDTO.create(awardCode));
        awardDTO.setGrantType(AwardGrantType.AutoGrant);
        awardDTO.setAwardCfg(award);
        awardDTO.setDetails(details);
        awardDTO.setGrantRemark(grantRemark);

        // 发奖励
        userLockTemplate.acquireTransactionLock(userId);
        return this.doGrant(userId, awardDTO, GlobalConstant.SYS_USER_ID);
    }

    /**
     * 系统奖励金币
     * （奖励平台金币）
     * @param awardCode 比如：RedPacket_{redId}
     * @param userId
     * @param beans
     * @param grantRemark 奖励备注
     * @return
     */
    public List<GrantDetailDTO> saveGrantPlatformBeans(String awardCode, Long userId, Integer beans, String grantRemark) {
        TAward award = new TAward();
        award.setAboutBeans(beans);
        award.setCode(AwardGrantType.AutoGrant.name());
        award.setName("系统发放");

        List<TAwardDetail> details = new ArrayList<>();
        TAwardDetail detail = new TAwardDetail();
        detail.setDetailId(-1);
        detail.setParentCode(award.getCode());
        detail.setAwardType(AwardType.PlatFormBeans);
        detail.setAwardTypeValue(AwardType.PlatFormBeans.getId());
        detail.setCount(beans);
        details.add(detail);

        AwardExtendDTO awardDTO = new AwardExtendDTO(AwardDTO.create(awardCode));
        awardDTO.setGrantType(AwardGrantType.AutoGrant);
        awardDTO.setAwardCfg(award);
        awardDTO.setDetails(details);
        awardDTO.setGrantRemark(grantRemark);

        // 发奖励
        userLockTemplate.acquireTransactionLock(userId);
        return this.doGrant(userId, awardDTO, GlobalConstant.SYS_USER_ID);
    }

    /**
     * 系统奖励现金
     * （奖励平台现金）
     * @param awardCode 比如：RedPacket_{redId}
     * @param userId
     * @param cash
     * @param grantRemark 奖励备注
     * @return
     */
    public List<GrantDetailDTO> saveGrantPlatformCash(String awardCode, Long userId, Integer cash, String grantRemark) {
        TAward award = new TAward();
        // TAward aboutBeans 字段意义为奖励的金币估值，这里暂时不去换算它
        award.setAboutBeans(cash);
        award.setCode(AwardGrantType.AutoGrant.name());
        award.setName("系统发放");

        List<TAwardDetail> details = new ArrayList<>();
        TAwardDetail detail = new TAwardDetail();
        detail.setDetailId(-1);
        detail.setParentCode(award.getCode());
        detail.setAwardType(AwardType.Cash);
        detail.setAwardTypeValue(AwardType.Cash.getId());
        detail.setCount(cash);
        detail.setExtJsonCfg(GlobalUtils.extValue(BusinessDataKey.TAwardDetailExt));
        details.add(detail);

        AwardExtendDTO awardDTO = new AwardExtendDTO(AwardDTO.create(awardCode));
        awardDTO.setGrantType(AwardGrantType.AutoGrant);
        awardDTO.setAwardCfg(award);
        awardDTO.setDetails(details);
        awardDTO.setGrantRemark(grantRemark);

        // 发奖励
        userLockTemplate.acquireTransactionLock(userId);
        return this.doGrant(userId, awardDTO, GlobalConstant.SYS_USER_ID);
    }

    /**
     * 系统奖励银币
     * @param awardCode 比如：RedPacket_{redId}
     * @param userId
     * @param silver
     * @param grantRemark 奖励备注
     * @return
     */
    public List<GrantDetailDTO> saveGrantSilver(String awardCode, Long userId, Integer silver, String grantRemark) {
        TAward award = new TAward();
        award.setAboutBeans(silver);
        award.setCode(AwardGrantType.AutoGrant.name());
        award.setName("系统发放");

        List<TAwardDetail> details = new ArrayList<>();
        TAwardDetail detail = new TAwardDetail();
        detail.setDetailId(-1);
        detail.setParentCode(award.getCode());
        detail.setAwardType(AwardType.Silver);
        detail.setAwardTypeValue(AwardType.Silver.getId());
        detail.setCount(silver);
        details.add(detail);

        AwardExtendDTO awardDTO = new AwardExtendDTO(AwardDTO.create(awardCode));
        awardDTO.setGrantType(AwardGrantType.AutoGrant);
        awardDTO.setAwardCfg(award);
        awardDTO.setDetails(details);
        awardDTO.setGrantRemark(grantRemark);

        // 发奖励
        userLockTemplate.acquireTransactionLock(userId);
        return this.doGrant(userId, awardDTO, GlobalConstant.SYS_USER_ID);
    }


    /**
     * 审核通过待领取记录，用户手工领取，cms后台赠送审核、活动奖励审核
     *
     * @param userId    被奖励用户ID
     * @param unawardId 待领取记录
     */
    public List<TAwardDetail> passUnAwardToGrant(Long userId, Long unawardId, Long optrId) {
        userLockTemplate.acquireTransactionLock(userId);
        // 提取配置
        UUserUnaward unawardRecord = uUserUnawardMapper.selectByPrimaryKeyToLock(new UUserUnaward(unawardId));
        if (unawardRecord == null) {
            throw new ComponentException("奖励已领取或已失效,不能重复领取");
        }
        if (unawardRecord.getExpTime() != null && unawardRecord.getExpTime().getTime() <= System.currentTimeMillis()) {
            throw new ComponentException("奖励已失效,不能领取");
        }
        if(!userId.equals(unawardRecord.getUserId())){
            throw new BusiException("用户匹配不正确");
        }
        AwardExtendDTO awardDTO = new AwardExtendDTO(AwardDTO.create(unawardRecord.getAwardConfigCode()));
        awardDTO.setUnawardId(unawardId);
        if (AwardGrantType.CmsGrant.equals(unawardRecord.getGrantType())) {
            awardDTO.setAwardCfg(UnGrantComponent.analogCmsGrantTAward(unawardRecord.getBeans()));
        } else {
            awardDTO.setAwardCfg(getTAwardCfg(unawardRecord.getAwardConfigCode()));
        }
        List<TAwardDetail> details = getTAwardDetailFromUnaward(unawardRecord);
        awardDTO.setDetails(details);
        awardDTO.setGrantType(unawardRecord.getGrantType());
        awardDTO.setGrantRemark(unawardRecord.getGrantRemark());
        // 发奖励
        List<GrantDetailDTO> grants = this.doGrant(userId, awardDTO, optrId);
        // 删除记录
        delUnAwardRecord(unawardRecord, optrId, ReviewResultType.Pass, null);
        return details;
    }

    /**
     * 待发奖记录审核拒绝,活动审核发放拒绝和cms后台赠送拒绝
     *
     * @param reviewFailReason 拒绝原因
     */
    public void rejectUnAward(Long unawardId, Long optrId, String reviewFailReason) {
        UUserUnaward unawardRecord = uUserUnawardMapper.selectByPrimaryKey(new UUserUnaward(unawardId));
        if (unawardRecord == null) {
            throw new ComponentException("奖励已审核或不存在,不能重复审核");
        }
        if (StringUtils.isBlank(reviewFailReason)) {
            throw new ComponentException("拒绝原因不能为空");
        }
        // 删除记录
        delUnAwardRecord(unawardRecord, optrId, ReviewResultType.Reject, reviewFailReason);
    }

    /**
     * 构建发奖内容
     *
     * @param grantList
     * @return
     */
    public String buildGrantContent(List<GrantDetailDTO> grantList) {
        return buildGrantContent(grantList, true);
    }

    /**
     * 构建发奖内容
     *
     * @param grantList
     * @param appendUnit
     * @return
     */
    public String buildGrantContent(List<GrantDetailDTO> grantList, boolean appendUnit) {
        StringBuilder content = new StringBuilder();
        for (GrantDetailDTO dto : grantList) {
            if (dto.getCount() > 0) {
                content.append(buildGrantContent(dto, appendUnit));
            }
        }
        if (content.length() > 0) {
            content.deleteCharAt(content.length() - 1);
        }
        return content.toString();
    }

    /**
     * 根据待领取记录得到奖励明细项目
     */
    private List<TAwardDetail> getTAwardDetailFromUnaward(UUserUnaward unawardRecord) {
        List<TAwardDetail> list = new ArrayList<>();
        List<UUserUnawardDetail> details = uUserUnawardDetailMapper.selectByUnawardId(unawardRecord.getUnawardId());
        if (ListUtils.isEmpty(details)) {
            throw new ComponentException(MsgUtils.format("待领记录{}找不到明细记录", unawardRecord.getUnawardId()));
        }
        for (UUserUnawardDetail detail : details) {
            TAwardDetail cfg = new TAwardDetail();
            cfg.setAwardType(detail.getAwardType());
            cfg.setAwardTypeValue(detail.getAwardTypeValue());
            cfg.setCount(detail.getCount());
            cfg.setDetailId(detail.getDetailConfigId());
            cfg.setEndTime(detail.getExpTime());
            cfg.setParentCode(unawardRecord.getAwardConfigCode());
            cfg.setExtJsonCfg(detail.getExtJsonCfg());
            list.add(cfg);
        }
        return list;
    }

    /**
     * 提取奖励配置
     */
    private TAward getTAwardCfg(String code) {
        TAward awardCfg = tAwardMapper.selectByPrimaryKey(new TAward(code));
        if (awardCfg == null) {
            logger.error("奖励配置{}不存在", code);
            throw new ComponentException("系统忙，请稍后重试");
        }
        return awardCfg;
    }

    /**
     * 删除待领取记录
     */
    private void delUnAwardRecord(UUserUnaward unawardRecord, Long optrId, ReviewResultType pass,
                                  String reviewFailReason) {
        Long unawardId = unawardRecord.getUnawardId();
        // 移入历史表
        UUserUnawardHis his = new UUserUnawardHis();
        CglibUtils.copy(unawardRecord, his);
        his.setReviewResult(pass);
        his.setReviewOptrId(optrId);
        his.setReviewTime(DateUtils.toString(DateUtils.nowTime()));
        his.setReviewFailReason(reviewFailReason);
        uUserUnawardHisMapper.insert(his);

        for (UUserUnawardDetail detail : uUserUnawardDetailMapper.selectByUnawardId(unawardId)) {
            UUserUnawardDetailHis dhis = new UUserUnawardDetailHis();
            CglibUtils.copy(detail, dhis);
            uUserUnawardDetailHisMapper.insert(dhis);
        }

        if (uUserUnawardMapper.deleteByPrimaryKey(new UUserUnaward(unawardId)) <= 0) {
            throw new ComponentException("奖励已处理,不能重复处理");
        }
        uUserUnawardDetailMapper.deleteByUnawardId(unawardId);
    }

    /**
     * 根据配置发奖,单奖励配置发奖
     *
     * @modifier wangcl 2019-09-18 抽奖活动发放奖品
     */
    public List<GrantDetailDTO> doGrant(Long userId, AwardExtendDTO award, Long optrId) {
        List<GrantDetailDTO> grantDetails = new ArrayList<>();
        // 逐个发奖
        for (TAwardDetail d : award.getDetails()) {
            Grant impl = grantSetImpl.get(d.getAwardType());
            if (impl == null) {
                throw new ComponentException("发奖类型‘{}’找不到实现类。", d.getAwardType().name());
            }
            grantDetails.add(impl.doGrant(award.getGrantType(), userId, award.getAwardCfg(), d, award.getAwardTimes(), false, award.getGrantRemark()));
        }
        // 保存发奖日志
        saveGrantLog(userId, award, grantDetails, optrId);
        return grantDetails;
    }

    /**
     * 自定义奖励配置发放
     *
     * @param userId 被奖励用户ID
     */
    public List<GrantDTO> saveCustomAutoGrant(Long userId, AwardExtendDTO... awards) {
        // 加锁
        userLockTemplate.acquireTransactionLock(userId);
        List<GrantDTO> grantList = new ArrayList<>();
        List<GrantDetailDTO> grantDetails = new ArrayList<>();
        // 逐个赠送
        for (AwardExtendDTO awardExtendDTO : awards) {
            if (awardExtendDTO == null) {
                continue;
            }
            grantDetails.addAll(this.doGrant(userId, awardExtendDTO, userId));
            grantList.add(new GrantDTO(awardExtendDTO.getAwardCfg().getAboutBeans() * awardExtendDTO.getAwardTimes(), grantDetails));
        }
        return grantList;
    }

    /**
     * 获取奖励配置DTO
     *
     * @param awardCode
     * @return
     */
    public List<AwardConfigDTO> getAwardListByAwardCode(String awardCode) {
        List<AwardConfigDTO> awardConfigList = new ArrayList<>();
        TAwardDetail config = new TAwardDetail();
        config.setParentCode(awardCode);
        List<TAwardDetail> detailList = tAwardDetailMapper.selectByCode(config);
        for (TAwardDetail detail : detailList) {
            Map<String, Object> map = JsonUtils.toJsonMap(detail.getExtJsonCfg());
            awardConfigList.add(new AwardConfigDTO(detail, (String) map.get("name"), (String) map.get("pic"), Integer.valueOf((String) map.get("order"))));
        }
        awardConfigList = awardConfigList.stream().sorted(Comparator.comparing(AwardConfigDTO::getOrder)).collect(Collectors.toList());
        return awardConfigList;
    }


    /**
     * 保存发奖日志记录
     */
    private void saveGrantLog(Long userId, AwardExtendDTO award, List<GrantDetailDTO> grants, Long optrId) {
        if (ListUtils.isEmpty(grants)) {
            return;
        }
        StringBuilder content = new StringBuilder();
        for (GrantDetailDTO dto : grants) {
            content.append(buildGrantContent(dto));
        }
        if (logger.isDebugEnabled()) {
            logger.debug("处理发奖 " + content.toString());
        }
        // 保存发奖明细
        Long tid = GlobalUtils.tid();
        UUserAward record = new UUserAward();
        record.setAwardConfigCode(award.getAwardCode());
        record.setBeans(award.getAwardCfg().getAboutBeans() * award.getAwardTimes());
        record.setAwardCount(award.getAwardTimes());
        record.setContent(content.substring(0, content.length() - 1));
        record.setCreateTime(new Date());
        record.setCreator(optrId);
        record.setGrantType(award.getGrantType());
        record.setTargetUserId(userId);
        record.setTid(tid);
        record.setBusiCode(GlobalUtils.busiCode());
        record.setGrantRemark(award.getGrantRemark());
        record.setUnawardId(award.getUnawardId());

        uUserAwardMapper.insert(record);
        for (int i = 0; i < award.getDetails().size(); i++) {
            TAwardDetail detail = award.getDetails().get(i);
            GrantDetailDTO grant = grants.get(i);
            UUserAwardDetail d = new UUserAwardDetail();
            d.setAwardId(record.getAwardId());
            d.setAwardType(detail.getAwardType());
            d.setAwardTypeValue(detail.getAwardTypeValue());
            d.setCount(grant.getCount().intValue());
            d.setCreateTime(record.getCreateTime());
            d.setDetailConfigId(detail.getDetailId().toString());
            if (grant.getProds() == null) {
                uUserAwardDetailMapper.insert(d);
            } else {
                // 金币购买礼物按购买到的礼物记录
                for (GrantProdDTO p : grant.getProds()) {
                    d.setAwardTypeValue(p.getProdId().toString());
                    d.setExpTime(p.getExpTime());
                    d.setCount(p.getAwardCount().intValue());
                    uUserAwardDetailMapper.insert(d);
                }
            }
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ? extends Grant> grantImpl = applicationContext.getBeansOfType(Grant.class);
        for (Grant impl : grantImpl.values()) {
            grantSetImpl.put(impl.getSupportType(), impl);
        }
    }

    private String buildGrantContent(GrantDetailDTO dto) {
        return buildGrantContent(dto, false);
    }

    private String buildGrantContent(GrantDetailDTO dto, boolean appendUnit) {
        StringBuffer content = new StringBuffer();
        if (dto.getProds() != null) {
            for (GrantProdDTO p : dto.getProds()) {
                content.append(p.getProdName()).append("*").append(p.getAwardCount());
                if (appendUnit && GoodsMergeType.AppendTime == p.getMergeType()) {
                    content.append("天");
                }
                content.append("、");
            }
        } else if (dto.getAwardName().contains("现金")) {
            // 现金奖励单位分转成元显示
            String cash = NumberUtils.format(dto.getCount() * 1.0 / 100, 2);
            content.append(dto.getAwardName()).append(":").append(cash).append("元、");
        } else {
            content.append(dto.getAwardName()).append("*").append(dto.getCount()).append("、");
        }
        return content.toString();
    }

}
