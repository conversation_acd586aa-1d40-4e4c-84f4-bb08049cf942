package com.tuowan.yeliao.commons.comp.headline.impl;

import com.easyooo.framework.common.util.MapUtils;
import com.easyooo.framework.common.util.MsgUtils;
import com.tuowan.yeliao.commons.comp.headline.dto.UserNoticeMsgDTO;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.data.enums.social.HeadLineMsgType;
import com.tuowan.yeliao.commons.data.enums.social.HeadLineWebpType;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.tuowan.yeliao.commons.context.GlobalUtils.extValue;


/**
 * 升级头条消息实现
 *
 * <AUTHOR>
 * @date 2021/12/14 14:00
 */
@Component
public class CharmLevelUpMsgImpl extends BaseHeadLineImpl {
    @Override
    public boolean canSend(Long userId) {
        return true;
    }

    @Override
    public UserNoticeMsgDTO getBaseMessage(HeadLineMsgType msgType, Long userId, Long friendId, String paramValue) {
        // 基本信息
        UserNoticeMsgDTO dto = super.getBaseMessage(msgType, userId, friendId, paramValue);
        dto.setUserId(userId);
        Integer charmLevel = extValue(BusinessDataKey.CharmLevel);
        dto.setExtMap(MapUtils.gmap("charmLevel", charmLevel));
        // 普通头条背景图 2.0.0之前
        dto.setBgPic("config/headline/headline-bg03.png");
        dto.setSmallBgPic("config/headline/headline-small-bg03.png");
        // 2.15.0 统一图片背景
        dto.setBgPicV2("config/headline/headline-gift-high_0215.png");
        dto.setSmallBgPicV2("config/headline/headline-gift-high-small.png");
        // V2 前缀图标
        dto.setRunwayPic("config/headline/headline-runway-gift-high.png");
        dto.setHighStyle(true);
        // webp动画样式
        dto.setWebpType(HeadLineWebpType.Flowers);
        // 不置顶处理
        dto.setTop(false);
        return dto;
    }

    @Override
    public Map<String, Object> buildInitContextMap(List<Long> targetUserIds, UserNoticeMsgDTO dto) {
        Map<String, Object> map = super.buildInitContextMap(targetUserIds, dto);
        Integer charmLevel = Integer.parseInt(String.valueOf(dto.getExtMap().get("charmLevel")));
        map.put("text1", "恭喜");
        map.put("text2", MsgUtils.format("魅力等级升级至{}级，魅力无限", charmLevel));
        map.put("charmLevelPic", BusiUtils.getCharmLevelPic(charmLevel));
        extValue(BusinessDataKey.OverwriteBusiCode, HeadLineMsgType.HeadLineCharmLevelUp.getId());
        extValue(BusinessDataKey.CustomMap, map);
        return map;
    }

    @Override
    public HeadLineMsgType getType() {
        return HeadLineMsgType.HeadLineCharmLevelUp;
    }

    @Override
    public boolean supportBusiCode(BusiCodeDefine busiCode) {
        return false;
    }
}
