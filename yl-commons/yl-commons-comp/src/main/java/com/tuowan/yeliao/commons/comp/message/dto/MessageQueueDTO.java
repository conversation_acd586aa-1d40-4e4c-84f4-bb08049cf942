/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.commons.comp.message.dto;


import com.tuowan.yeliao.commons.context.GlobalContext;
import com.tuowan.yeliao.commons.context.GlobalUtils;

/**
 * 缓冲消息数据封装
 *
 * <AUTHOR>
 * @date 2018/8/13 13:03
 */
public class MessageQueueDTO {

    /* 服务信息 */
    private String reqId;
    private Long reqTime;
    private String busiCode;
    private Long tid;
    private Long programId;
    private Long userId;
    private String nickname;

    /* 消息体 */
    private String messageBody;

    public static MessageQueueDTO createByCurrentContext(String messageBody){
        MessageQueueDTO dto = new MessageQueueDTO();

        dto.setReqId(GlobalUtils.reqId());
        dto.setReqTime(GlobalUtils.reqTime());
        dto.setBusiCode(GlobalUtils.busiCode());
        dto.setTid(GlobalUtils.tid());
        dto.setProgramId(GlobalUtils.pid());
        dto.setUserId(GlobalUtils.uid());
        dto.setNickname(GlobalUtils.nickname());

        dto.setMessageBody( messageBody);

        return dto;
    }

    public GlobalContext toGlobalContext(){
        GlobalContext gc = new GlobalContext();

        gc.setReqId(getReqId());
        gc.setReqTime(getReqTime());
        gc.setBusiCode(getBusiCode());
        gc.setTid(getTid());
        gc.setProgramId(getProgramId());
        gc.setUserId(getUserId());
        gc.setNickname(getNickname());

        return gc;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public Long getReqTime() {
        return reqTime;
    }

    public void setReqTime(Long reqTime) {
        this.reqTime = reqTime;
    }

    public String getBusiCode() {
        return busiCode;
    }

    public void setBusiCode(String busiCode) {
        this.busiCode = busiCode;
    }

    public Long getTid() {
        return tid;
    }

    public void setTid(Long tid) {
        this.tid = tid;
    }

    public Long getProgramId() {
        return programId;
    }

    public void setProgramId(Long programId) {
        this.programId = programId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getMessageBody() {
        return messageBody;
    }

    public void setMessageBody(String messageBody) {
        this.messageBody = messageBody;
    }
}
