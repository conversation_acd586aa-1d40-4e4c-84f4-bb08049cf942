/*
 * Copyright © 2014 YAOCHEN Corporation, All Rights Reserved
 */
package com.tuowan.yeliao.commons.comp.message.impl;

import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.comp.message.DataBodyBuilder;
import com.tuowan.yeliao.commons.comp.message.MsgTextTplPaser;
import com.tuowan.yeliao.commons.comp.message.vo.EventMessageVO;
import com.tuowan.yeliao.commons.data.entity.config.TMessage;
import com.tuowan.yeliao.commons.data.enums.config.MessageType;

import java.util.HashMap;
import java.util.Map;

/**
 * 事件消息构造器实现
 * 
 * <AUTHOR>
 */
public class EventDataBodyBuilder implements DataBodyBuilder<EventMessageVO> {
	
	public EventDataBodyBuilder(){
	}
	
	@Override
	public EventMessageVO buildDataBody(TMessage message, Map<String, Object> contextMap) {
		EventMessageVO md = new EventMessageVO();
		// tpl params
		if(StringUtils.isNotBlank(message.getTextTpl())){
			Map<String, Object> tplParams = new HashMap<>();
			String textTpl = MsgTextTplPaser.parse(message.getTextTpl(), contextMap, tplParams);
			md.setTextParams(tplParams);
			md.setTextStyles(message.getTextStyle());
			md.setTextTpl(textTpl);
		}
		md.setEventCode(message.getEventCode());

		return md;
	}
	
	@Override
	public boolean isSupported(TMessage message) {
		return message.getMsgType() == MessageType.Event;
	}
}