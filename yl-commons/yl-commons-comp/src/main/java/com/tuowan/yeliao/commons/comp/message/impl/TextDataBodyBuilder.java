/*
 * Copyright © 2014 YAOCHEN Corporation, All Rights Reserved
 */
package com.tuowan.yeliao.commons.comp.message.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.easyooo.framework.common.util.EnumUtils;
import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.ListUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.comp.message.DataBodyBuilder;
import com.tuowan.yeliao.commons.comp.message.MsgTextTplPaser;
import com.tuowan.yeliao.commons.comp.message.dto.TextStyleDTO;
import com.tuowan.yeliao.commons.comp.message.vo.TextMessageVO;
import com.tuowan.yeliao.commons.data.dto.social.ChatTipsTouchDTO;
import com.tuowan.yeliao.commons.data.entity.config.TMessage;
import com.tuowan.yeliao.commons.data.enums.config.MessageType;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文本消息构造器实现
 *
 * <AUTHOR>
 */
public class TextDataBodyBuilder implements DataBodyBuilder<TextMessageVO> {

    public TextDataBodyBuilder() {
    }

    @Override
    public TextMessageVO buildDataBody(TMessage msg, Map<String, Object> contextMap) throws Throwable {
        TextMessageVO t = new TextMessageVO();

        // tpl params
        Map<String, Object> tplParams = new HashMap<>();
        List<ChatTipsTouchDTO> touchList = new ArrayList<>();
        String textTpl = MsgTextTplPaser.parse(msg.getTextTpl(), contextMap, tplParams);
        // setter
        t.setGroupCode(msg.getGroupCode());
        t.setDisplay(msg.getDisplay());
        t.setTextParams(tplParams);
        t.setTextStyles(msg.getTextStyle());
        t.setTextTouch(msg.getTextTouch());
        // 上下文中touchValue点击值，保持和textTouch同级
        Object contentExtMap = contextMap.get("extMap");
        Map<String, Object> extMap = JSONObject.parseObject(JSON.toJSONString(contentExtMap));
        if (extMap != null) {
            Object touchValue = extMap.get("touchValue");
            if (touchValue != null) {
                t.setTouchValue(touchValue.toString());
            }
        }
        t.setTextTpl(textTpl);
        // 解析所有的点击事件
        if (StringUtils.isEmpty(msg.getTextStyle())) {
            return t;
        }
        List<TextStyleDTO> styleList = JsonUtils.deserializeAsList(msg.getTextStyle(), TextStyleDTO.class);
        if (ListUtils.isEmpty(styleList)) {
            return t;
        }
        // 提取所有样式里面需要替换的参数表达式
        StringBuilder allTouchValueTpl = new StringBuilder();
        for (TextStyleDTO dto : styleList) {
            if (StringUtils.isEmpty(dto.getTouchType()) && StringUtils.isEmpty(dto.getTouchValue())) {
                continue;
            }
            if (dto.getTouchValue().startsWith("$")) {
                allTouchValueTpl.append(dto.getTouchValue());
            } else {
                ChatTipsTouchDTO touch = new ChatTipsTouchDTO();
                touch.setTouchType(EnumUtils.byName(dto.getTouchType(), ClientTouchType.class));
                touch.setTouchValue(dto.getTouchValue());
                touch.setTouchText(tplParams.get(dto.getEl()).toString());
                touchList.add(touch);
            }
        }
        // 获取所有参数表达式的值，重新组装样式
        if (StringUtils.isNotEmpty(allTouchValueTpl.toString())) {
            Map<String, Object> touchValueParams = new HashMap<>();
            MsgTextTplPaser.parse(allTouchValueTpl.toString(), contextMap, touchValueParams);
            for (TextStyleDTO style : styleList) {
                if (StringUtils.isEmpty(style.getTouchType())) {
                    continue;
                }
                // 组装所有的点击事件列表
                ChatTipsTouchDTO touch = new ChatTipsTouchDTO();
                touch.setTouchType(EnumUtils.byName(style.getTouchType(), ClientTouchType.class));
                touch.setTouchValue(style.getTouchValue());
                touch.setTouchText(tplParams.get(style.getEl()).toString());
                touchList.add(touch);
                Object textTouchValue = touchValueParams.get(style.getTouchValue());
                if (textTouchValue != null) {
                    style.setTouchValue(textTouchValue.toString());
                    touch.setTouchValue(textTouchValue.toString());
                }
            }
        }
        t.setTextStyles(JsonUtils.seriazileAsString(styleList));
        t.setTouchList(touchList);
        return t;
    }

    @Override
    public boolean isSupported(TMessage message) {
        return message.getMsgType() == MessageType.Text;
    }
}
