package com.tuowan.yeliao.commons.comp.notice.dto;


import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;

/**
 * 系统通知信息
 *
 * <AUTHOR>
 * @date 2020/7/13 15:35
 */
public abstract class SystemNoticeDTO implements NoticeDTO{

    private Long noticeId;
    private String body;
    private String pic;
    private String title;
    private String subTitle;
    private String detailText;
    private ClientTouchType touchType;
    private Object touchValue;

    public Long getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getDetailText() {
        return detailText;
    }

    public void setDetailText(String detailText) {
        this.detailText = detailText;
    }

    public ClientTouchType getTouchType() {
        return touchType;
    }

    public void setTouchType(ClientTouchType touchType) {
        this.touchType = touchType;
    }

    public Object getTouchValue() {
        return touchValue;
    }

    public void setTouchValue(Object touchValue) {
        this.touchValue = touchValue;
    }
}
