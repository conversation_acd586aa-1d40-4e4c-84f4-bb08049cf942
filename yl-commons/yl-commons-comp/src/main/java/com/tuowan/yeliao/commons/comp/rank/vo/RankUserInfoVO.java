/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.commons.comp.rank.vo;


import com.tuowan.yeliao.commons.comp.rank.RankScore;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.user.UserType;
import com.tuowan.yeliao.commons.data.dto.user.UserBusiDTO;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;

import java.util.List;

/**
 * 用户相关的榜单结果集封装
 *
 * <AUTHOR>
 * @date 2018/8/3 15:05
 */
public class RankUserInfoVO implements RankScore {

    /** 用户编号 */
    private Long userId;
    /** 用户类型 */
    private UserType userType;
    /** 用户昵称 */
    private String nickname;
    /** 头像 */
    private String headPic;
    /** 头像框 */
    private String headFrame;
    /** 用户等级 */
    private Integer userLevel;
    /** 魅力等级 */
    private Integer charmLevel;
    /** 比分 */
    private Long score;
    /** 排名 */
    private String ranking;
    /**
     * 勋章列表
     */
    private List<String> badgesPic;
    /**
     * 用户性别
     */
    private SexType sex;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 用户所属家族ID
     */
    private Integer familyId;
    /**
     * 家族封建图片
     */
    private String familyPic;
    /**
     * 是否为榜一用户
     */
    private BoolType topUser;

    public static RankUserInfoVO create(UserBusiDTO b, Long score) {
        RankUserInfoVO vo = new RankUserInfoVO();
        // 比分
        vo.setScore(score);
        // 用户信息
        vo.setHeadPic(b.getHeadPic());
        vo.setHeadFrame(b.getHeadFrame());
        vo.setNickname(b.getNickname());
        vo.setUserLevel(b.getUserLevel());
        vo.setUserId(b.getUserId());
        vo.setUserType(b.getUserType());
        vo.setSex(b.getSex());
        vo.setCharmLevel(b.getCharmLevel());
        vo.setBadgesPic(b.getBadgesPic());
        return vo;
    }

    public static RankUserInfoVO create(UserBusiDTO b, UUserBasic basic, Long score) {
        RankUserInfoVO vo = new RankUserInfoVO();
        // 比分
        vo.setScore(score);
        // 用户信息
        vo.setHeadPic(b.getHeadPic());
        vo.setHeadFrame(b.getHeadFrame());
        vo.setNickname(b.getNickname());
        vo.setUserLevel(b.getUserLevel());
        vo.setUserId(b.getUserId());
        vo.setUserType(b.getUserType());
        vo.setSex(b.getSex());
        vo.setCharmLevel(b.getCharmLevel());
        vo.setBadgesPic(b.getBadgesPic());
        vo.setAge(BusiUtils.getAgeByDate(basic.getBirthDate()));
        return vo;
    }

    public static RankUserInfoVO create(Long userId, String nickname, String headPic) {
        RankUserInfoVO vo = new RankUserInfoVO();
        vo.setUserId(userId);
        vo.setNickname(nickname);
        vo.setHeadPic(headPic);
        return vo;
    }

    @Override
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public UserType getUserType() {
        return userType;
    }

    public void setUserType(UserType userType) {
        this.userType = userType;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public String getHeadFrame() {
        return headFrame;
    }

    public void setHeadFrame(String headFrame) {
        this.headFrame = headFrame;
    }

    public Integer getUserLevel() {
        return userLevel;
    }

    public void setUserLevel(Integer userLevel) {
        this.userLevel = userLevel;
    }

    public String getRanking() {
        return ranking;
    }

    public void setRanking(String ranking) {
        this.ranking = ranking;
    }

    @Override
    public Long getScore() {
        return score;
    }

    public void setScore(Long score) {
        this.score = score;
    }

    public Integer getCharmLevel() {
        return charmLevel;
    }

    public void setCharmLevel(Integer charmLevel) {
        this.charmLevel = charmLevel;
    }

    public SexType getSex() {
        return sex;
    }

    public void setSex(SexType sex) {
        this.sex = sex;
    }

    public List<String> getBadgesPic() {
        return badgesPic;
    }

    public void setBadgesPic(List<String> badgesPic) {
        this.badgesPic = badgesPic;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }

    public String getFamilyPic() {
        return familyPic;
    }

    public void setFamilyPic(String familyPic) {
        this.familyPic = familyPic;
    }

    public BoolType getTopUser() {
        return topUser;
    }

    public void setTopUser(BoolType topUser) {
        this.topUser = topUser;
    }
}
