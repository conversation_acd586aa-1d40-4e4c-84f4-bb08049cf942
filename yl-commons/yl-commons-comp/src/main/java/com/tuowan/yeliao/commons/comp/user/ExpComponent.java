/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.commons.comp.user;

import com.tuowan.yeliao.commons.comp.change.UserBusiChangeComponent;
import com.tuowan.yeliao.commons.comp.change.dto.BusiChangeDTO;
import com.tuowan.yeliao.commons.comp.grant.GrantComponent;
import com.tuowan.yeliao.commons.comp.headline.UserNoticeComponent;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.config.redis.template.BusiRedisTemplate;
import com.tuowan.yeliao.commons.data.dto.user.UserBusiDTO;
import com.tuowan.yeliao.commons.data.entity.config.TLevel;
import com.tuowan.yeliao.commons.data.enums.config.LevelType;
import com.tuowan.yeliao.commons.data.manager.config.NewsManager;
import com.tuowan.yeliao.commons.data.manager.datacache.UserDataBusiCacheManager;
import com.tuowan.yeliao.commons.data.manager.datacache.column.BusiColumn;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.manager.user.UserLevelManager;
import com.tuowan.yeliao.commons.data.persistence.config.TLevelMapper;
import com.tuowan.yeliao.commons.data.persistence.user.UUserLevelMapper;
import com.tuowan.yeliao.commons.mq.producer.MessageQueueProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 等级相关组件封装，包括用户等级、贵族等级、主播等级
 * 非线程安全，外部需要控制加锁
 *
 * <AUTHOR>
 * @date 2018/7/18 13:30
 */
@Component
public class ExpComponent {

    @Autowired
    private NewsManager newsManager;
    @Autowired
    private UserBusiComponent userBusiComponent;
    @Autowired
    private TLevelMapper tLevelMapper;
    @Autowired
    private UserBusiChangeComponent userBusiChangeComponent;
    @Autowired
    private UserDataBusiCacheManager userDataBusiCacheManager;
    @Autowired
    private UUserLevelMapper userLevelMapper;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private UserLevelManager userLevelManager;
    @Autowired
    private NoticeComponent noticeComponent;
    @Autowired
    private GrantComponent grantComponent;
    @Autowired
    private UserNoticeComponent userNoticeComponent;
    @Autowired
    private BusiRedisTemplate busiRedisTemplate;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private MessageQueueProducer messageQueueProducer;


    /**
     * 获取用户的下个等级
     *
     * @param userLevel
     * @return
     */
    public TLevel getNextUserLevel(Integer userLevel, LevelType levelType) {
        return getUserLevel(userLevel + 1, levelType);
    }

    /**
     * 获取用户当前等级
     *
     * @param userLevel
     * @return
     */
    public TLevel getUserLevel(Integer userLevel, LevelType levelType) {
        // 查找配置的等级列表
        List<TLevel> levels = tLevelMapper.selectByLevelType(new TLevel(levelType));
        levels = levels.stream().sorted(Comparator.comparing(TLevel::getLevelValue)).collect(Collectors.toList());
        for (TLevel level : levels) {
            if (userLevel.equals(level.getLevelValue())) {
                return level;
            }
        }
        // 可能满级的情况
        return null;
    }

    /**
     * 添加用户经验值
     *
     * @param userId    添加的用户编号
     * @param increment 增加的经验值
     */
    public BusiChangeDTO addUserExp(Long userId, Long increment) {
        // 旧得经验值和等级
        UserBusiDTO userDto = userDataBusiCacheManager.getUserBusi(userId, BusiColumn.UserExp, BusiColumn.UserLevel);
        Integer oldLevel = userDto.getUserLevel();
        Long oldExp = userDto.getUserExp();
        // 用户达到顶级 直接返回
        if(oldLevel >= LevelType.User.getTopLevel()){
            return BusiChangeDTO.createUserLevel(userId, oldExp, oldExp, oldLevel, oldLevel);
        }
        // 查询最高等级配置
        TLevel topLevelData = getUserLevel(LevelType.User.getTopLevel(), LevelType.User);
        // 新得经验值和等级
        Long newExp = userBusiComponent.addUserExp(userId, increment, topLevelData.getExpLowerLimit());
        TLevel newLevelObj = null;
        Integer newLevel = null;
        // 非顶级才查询下一级，若最高等级发生变化，请同时更新TSettings表
        newLevelObj = getNewLevel(newExp, LevelType.User);
        newLevel = newLevelObj != null ? newLevelObj.getLevelValue() : null;

        if (newLevel != null && !newLevel.equals(oldLevel)) {
            userBusiComponent.updateUserLevel(userId, newLevel);
        }

        // 保存用户经验等级异动
        BusiChangeDTO dto = BusiChangeDTO.createUserLevel(userId, oldExp, oldExp + increment, oldLevel, newLevel);
        userBusiChangeComponent.addUserLevelChange(dto);

        return dto;
    }

    /**
     * 增加用户魅力值
     *
     * @param userId    添加的用户编号
     * @param increment 增加的经验值
     */
    public BusiChangeDTO addCharmExp(Long userId, Long increment) {
        // 旧得经验值和等级
        UserBusiDTO userDto = userDataBusiCacheManager.getUserBusi(userId, BusiColumn.Sex, BusiColumn.CharmExp, BusiColumn.CharmLevel);
        Integer oldLevel = userDto.getCharmLevel();
        Long oldExp = userDto.getCharmExp();
        // 达到顶级 直接返回
        if(oldLevel >= LevelType.Charm.getTopLevel()){
            return BusiChangeDTO.createCharmLevel(userId, oldExp, oldExp, oldLevel, oldLevel);
        }
        // 查询最高等级配置
        TLevel topLevelData = getUserLevel(LevelType.Charm.getTopLevel(), LevelType.Charm);
        // 新得经验值和等级
        Long newExp = userBusiComponent.addCharmExp(userId, increment, topLevelData.getExpLowerLimit());
        TLevel newLevelObj = null;
        Integer newLevel = null;
        // 非顶级才查询下一级，若最高等级发生变化，请同时更新TSettings表
        newLevelObj = getNewLevel(newExp, LevelType.Charm);
        newLevel = newLevelObj != null ? newLevelObj.getLevelValue() : null;

        if (newLevel != null && !newLevel.equals(oldLevel)) {
            userBusiComponent.updateCharmLevel(userId, newLevel);
        }
        // 保存魅力经验等级异动
        BusiChangeDTO dto = BusiChangeDTO.createCharmLevel(userId, oldExp, oldExp + increment, oldLevel, newLevel);
        userBusiChangeComponent.addCharmLevelChange(dto);

        // 魅力等级发送变更，发送魅力等级变化消息
        /*if (dto.getCharmLevelNew() != null && dto.getCharmLevelNew() > dto.getCharmLevel()) {
            // 对女用户的处理
            if(SexType.Female == userDto.getSex()){
                // 如果女用户私聊单价可选发生改变 那么通知女用户
                if(MessageSettingConfig.getLevels(SexType.Female).contains(dto.getCharmLevelNew())){
                    noticeComponent.sendSystemNotice(userId, NoticeSysType.FemaleUnlockPrice, MapUtils.gmap("level", dto.getCharmLevelNew()));
                }
            }
        }*/
        return dto;
    }

    /**
     * 增加用户壕气值
     *
     * @param userId    添加的用户编号
     * @param increment 增加的经验值
     */
    public BusiChangeDTO addKcExp(Long userId, Long increment) {
        // 旧得经验值和等级
        UserBusiDTO userDto = userDataBusiCacheManager.getUserBusi(userId, BusiColumn.Sex, BusiColumn.KcExp, BusiColumn.KcLevel);
        Integer oldLevel = userDto.getHqLevel();
        Long oldExp = userDto.getHqExp();
        // 达到顶级 直接返回
        if(oldLevel >= LevelType.KuoChuo.getTopLevel()){
            return BusiChangeDTO.createCharmLevel(userId, oldExp, oldExp, oldLevel, oldLevel);
        }
        // 查询最高等级配置
        TLevel topLevelData = getUserLevel(LevelType.KuoChuo.getTopLevel(), LevelType.KuoChuo);
        // 新得经验值和等级
        Long newExp = userBusiComponent.addHqExp(userId, increment, topLevelData.getExpLowerLimit());
        TLevel newLevelObj = null;
        Integer newLevel = null;
        // 非顶级才查询下一级，若最高等级发生变化，请同时更新TSettings表
        newLevelObj = getNewLevel(newExp, LevelType.KuoChuo);
        newLevel = newLevelObj != null ? newLevelObj.getLevelValue() : null;

        if (newLevel != null && !newLevel.equals(oldLevel)) {
            userBusiComponent.updateHqLevel(userId, newLevel);
        }
        // 保存魅力经验等级异动
        BusiChangeDTO dto = BusiChangeDTO.createHqLevel(userId, oldExp, oldExp + increment, oldLevel, newLevel);
        userBusiChangeComponent.addHqLevelChange(dto);

        return dto;
    }

    /**
     * 增加用户成长值
     *
     * @param userId    添加的用户编号
     * @param increment 增加的经验值
     */
    public BusiChangeDTO addGuExp(Long userId, Long increment) {
        // 旧得经验值和等级
        UserBusiDTO userDto = userDataBusiCacheManager.getUserBusi(userId, BusiColumn.Sex, BusiColumn.GuExp, BusiColumn.GuLevel);
        Integer oldLevel = userDto.getGuLevel();
        Long oldExp = userDto.getGuExp();
        // 达到顶级 直接返回
        if(oldLevel >= LevelType.GrowUp.getTopLevel()){
            return BusiChangeDTO.createCharmLevel(userId, oldExp, oldExp, oldLevel, oldLevel);
        }
        // 查询最高等级配置
        TLevel topLevelData = getUserLevel(LevelType.GrowUp.getTopLevel(), LevelType.GrowUp);
        // 新得经验值和等级
        Long newExp = userBusiComponent.addGuExp(userId, increment, topLevelData.getExpLowerLimit());
        TLevel newLevelObj = null;
        Integer newLevel = null;
        // 非顶级才查询下一级，若最高等级发生变化，请同时更新TSettings表
        newLevelObj = getNewLevel(newExp, LevelType.GrowUp);
        newLevel = newLevelObj != null ? newLevelObj.getLevelValue() : null;

        if (newLevel != null && !newLevel.equals(oldLevel)) {
            userBusiComponent.updateGuLevel(userId, newLevel);
        }
        // 保存魅力经验等级异动
        BusiChangeDTO dto = BusiChangeDTO.createGuLevel(userId, oldExp, oldExp + increment, oldLevel, newLevel);
        userBusiChangeComponent.addGuLevelChange(dto);

        return dto;
    }

    /**
     * 返回增加经验值之后的新等级
     *
     * @param newExp    根据新的经验值匹配一个等级出来
     * @param levelType 等级类型
     * @return newLevel
     */
    private TLevel getNewLevel(Long newExp, LevelType levelType) {
        // 查找配置的等级列表
        List<TLevel> levels = tLevelMapper.selectByLevelType(new TLevel(levelType));
        levels = levels.stream().sorted(Comparator.comparing(TLevel::getLevelValue).reversed()).collect(Collectors.toList());
        // 根据经验值匹配新的等级
        for (TLevel level : levels) {
            if (newExp >= level.getExpLowerLimit()) {
                return level;
            }
        }
        return null;
    }
}
