/*!
 * Copyright 2018, Jul<PERSON>, Inc.
 */

package com.tuowan.yeliao.commons.comp.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.easyooo.framework.common.dto.SortFast;
import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.enums.SortType;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.comp.user.dto.ProdGiftDTO;
import com.tuowan.yeliao.commons.comp.user.dto.ProdGoodsDTO;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.BusiRedisTemplate;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.redis.BusiKeyDefine;
import com.tuowan.yeliao.commons.core.exception.ComponentException;
import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.data.dto.user.UserBagDTO;
import com.tuowan.yeliao.commons.data.dto.user.attribute.ChatBubbleDTO;
import com.tuowan.yeliao.commons.data.entity.config.TAnimation;
import com.tuowan.yeliao.commons.data.entity.config.TChatBubble;
import com.tuowan.yeliao.commons.data.entity.config.TProdGoodsType;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGoods;
import com.tuowan.yeliao.commons.data.entity.user.UUserBag;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.config.*;
import com.tuowan.yeliao.commons.data.manager.config.SocialProdManager;
import com.tuowan.yeliao.commons.data.manager.datacache.UserDataGiftCacheManager;
import com.tuowan.yeliao.commons.data.manager.datacache.UserDataGoodsCacheManager;
import com.tuowan.yeliao.commons.data.manager.datacache.UserDataResource;
import com.tuowan.yeliao.commons.data.persistence.config.TProdGoodsTypeMapper;
import com.tuowan.yeliao.commons.data.persistence.user.UUserBagChangeMapper;
import com.tuowan.yeliao.commons.data.persistence.user.UUserBagMapper;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.data.utils.StaticBeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户背包管理类，如果是查询类操作， 推荐使用：GlobalUtils或GlobalDataUtils下的静态方法代替，多次查询效率更高
 *
 * <AUTHOR>
 * @date 2018/7/27 17:43
 */
public abstract class UserBagComponent {

    private final Logger LOG = LoggerFactory.getLogger(getClass());

    @Autowired
    private UserDataGiftCacheManager userDataGiftCacheManager;
    @Autowired
    private UserDataGoodsCacheManager userDataGoodsCacheManager;
    @Autowired
    private UUserBagMapper uUserBagMapper;
    @Autowired
    private TProdGoodsTypeMapper tProdGoodsTypeMapper;
    @Autowired
    private UUserBagChangeMapper uUserBagChangeMapper;
    @Autowired
    private BusiRedisTemplate busiRedisTemplate;
    @Autowired
    private SocialProdManager socialProdManager;

    /**
     * 获取支持的商品分组类型
     *
     * @return
     */
    protected abstract ProdGroupType getSupportGroupType();

    /**
     * 获取当前支持的礼物类型
     *
     * @return
     */
    protected abstract ProdGoodsType getSupportProdGiftType();

    /**
     * 获取物品信息
     *
     * @param goodsId
     * @return
     */
    protected abstract ProdGoodsDTO getProdGoodsInfo(Integer goodsId);

    /**
     * 获取礼物信息
     *
     * @param giftId
     * @return
     */
    protected abstract ProdGiftDTO getProdGiftInfo(Integer giftId);

    /**
     * 获取用户背包直播礼物列表(含已失效的背包记录)
     *
     * @param userId
     * @return
     */
    public List<UUserBag> findUserGifts(Long userId) {
        List<UUserBag> bagList = userDataGiftCacheManager.findUserBag(userId);
        return bagList.stream().filter(bag -> getSupportGroupType() == bag.getProdType().getGroup()).collect(Collectors.toList());
    }

    /**
     * 获取用户有效的直播礼物背包列表
     *
     * @param userId
     * @return
     */
    public List<UUserBag> findUserValidGifts(Long userId) {
        List<UUserBag> bagList = userDataGiftCacheManager.findUserBag(userId);
        bagList = bagList.stream().filter(bag -> getSupportGroupType() == bag.getProdType().getGroup()).collect(Collectors.toList());
        // 过滤失效的礼物
        if (ListUtils.isEmpty(bagList)) {
            return bagList;
        }
        // 有效的礼物
        return getValidGiftBag(bagList, null);
    }

    /**
     * 获取用户背包直播物品列表(含已失效的背包记录)
     *
     * @param userId
     * @return
     */
    public List<UUserBag> findUserGoods(Long userId) {
        List<UUserBag> bagList = userDataGoodsCacheManager.findUserBag(userId);
        return bagList.stream().filter(bag -> getSupportGroupType() == bag.getProdType().getGroup()).collect(Collectors.toList());
    }

    public List<UUserBag> findUserGoods(Long userId, ProdGoodsType goodsType) {
        return this.findUserGoods(userId, goodsType, false);
    }

    public List<UUserBag> findUserGoods(Long userId, ProdGoodsType goodsType, boolean sort) {
        List<UUserBag> bagList = findUserGoods(userId);
        bagList = bagList.stream().filter(bag -> bag.getProdType() == goodsType).collect(Collectors.toList());
        if (sort) {
            for (UUserBag bag : bagList) {
                if (bag.getCreateTime() == null) {
                    bag.setCreateTime(bag.getIsEquipTime());
                }
                Long orderNum = 0L;
//                if (bag.getIsEquip() == EquipStatusType.True) {
//                    orderNum += 1000L;
//                }
                if (bag.getExpTime().after(DateUtils.nowTime())) {
                    orderNum += 100L;
                }
                bag.setOrderNum(orderNum);
            }
            bagList.sort(Comparator.comparing(UUserBag::getOrderNum).thenComparing(UUserBag::getCreateTime).reversed());
        }
        return bagList;
    }

    /**
     * 获取用户背包有效物品列表
     *
     * @param userId
     * @return
     */
    public List<UUserBag> findUserValidGoods(Long userId) {
        List<UUserBag> bagList = userDataGoodsCacheManager.findUserBag(userId);
        if (ListUtils.isEmpty(bagList)) {
            return bagList;
        }
        bagList = bagList.stream().filter(bag -> getSupportGroupType() == bag.getProdType().getGroup()).collect(Collectors.toList());
        // 过滤有效的物品
        return getValidGiftBag(bagList, null);
    }


    /**
     * 获取某个物品有效的背包列表
     *
     * @param userId
     * @param prodType
     * @return
     */
    public List<UUserBag> findUserValidGoods(Long userId, Long targetUserId, ProdGoodsType prodType) {
        return userDataGoodsCacheManager.findValidBags(this.findUserGoods(userId), targetUserId, prodType, null);
    }

    /**
     * 获取背包所有礼物的数量
     *
     * @param userId
     * @return
     */
    public Map<Integer, Integer> findGiftCntMap(Long userId) {
        Map<Integer, Integer> cntMap = new HashMap<>();
        for (UUserBag bag : userDataGiftCacheManager.findValidBagsByNoJudgeProgram(this.findUserGifts(userId))) {
            cntMap.merge(bag.getProdId(), bag.getSurplusCount(), Integer::sum);
        }
        return cntMap;
    }

    /**
     * 获取目标用户有效的商品
     *
     * @param targetUserId
     * @param prodType
     * @param prodId
     * @return
     */
    public List<UUserBag> findUserValidProds(Long targetUserId, ProdGoodsType prodType, Integer prodId) {
        return uUserBagMapper.selectByTargetUserId(targetUserId, prodType, prodId);
    }

    /**
     * 获取礼物、物品背包数量
     *
     * @param userId
     * @return
     */
    public abstract Map<String, Integer> getBagCntMap(Long userId);

    /**
     * 获取背包指定礼物的数量
     *
     * @param userId
     * @param giftId
     * @return
     */
    public int getGiftCnt(Long userId, Integer giftId) {
        int cnt = 0;
        for (UUserBag bag : userDataGiftCacheManager.findValidBagsByNoJudgeProgram(this.findUserGifts(userId), null, giftId)) {
            cnt += bag.getSurplusCount();
        }
        return cnt;
    }

    /**
     * 获取背包指定类型物品类型的数量
     * <p>
     * 如果按时间管理的则返回天数(剩余0.1会表示成1天,剩余1.1天会表示2天)
     *
     * @param userId
     * @param prodType
     * @return
     */
    public Map<Integer, Integer> findGoodsCntMap(Long userId, Long targetUserId, ProdGoodsType prodType) {
        Map<Integer, Integer> cntMap = new HashMap<>();
        boolean isAppendCnt = true;// 是否按数量管理
        TProdGoodsType goodsTypeCfg = getGoodsTypeCfg(prodType);
        if (GoodsMergeType.AppendTime.equals(goodsTypeCfg.getMergeType())) {
            isAppendCnt = false;// 按时间管理
        }
        for (UUserBag bag : userDataGoodsCacheManager.findValidBags(this.findUserGoods(userId), targetUserId, prodType, null)) {
            Integer cnt = cntMap.get(bag.getProdId());
            int incrCnt = bag.getSurplusCount();
            if (!isAppendCnt) {// 时间管理转换天数
                incrCnt = getExpTimeDiffDays(bag.getExpTime());
            }
            if (cnt == null) {
                cntMap.put(bag.getProdId(), incrCnt);
            } else {
                cntMap.put(bag.getProdId(), cnt + incrCnt);
            }
        }
        return cntMap;
    }

    /**
     * 获取背包指定物品的数量
     *
     * @param userId
     * @param prodType
     * @param prodId
     * @return
     */
    public int getGoodsCnt(Long userId, ProdGoodsType prodType, Integer prodId) {
        return getGoodsCnt(userId, null, prodType, prodId);
    }

    /**
     * 获取背包指定物品的数量
     * <p>
     * 如果按时间管理的则返回天数(剩余0.1会表示成1天,剩余1.1天会表示2天)
     *
     * @param userId
     * @param prodType
     * @param prodId
     * @return
     */
    public int getGoodsCnt(Long userId, Long targetUserId, ProdGoodsType prodType, Integer prodId) {
        // 是否按数量管理
        boolean isAppendCnt = true;
        TProdGoodsType goodsTypeCfg = getGoodsTypeCfg(prodType);
        if (GoodsMergeType.AppendTime.equals(goodsTypeCfg.getMergeType())) {
            // 按时间管理
            isAppendCnt = false;
        }
        int cnt = 0;
        for (UUserBag bag : userDataGoodsCacheManager.findValidBags(this.findUserGoods(userId), targetUserId, prodType, prodId)) {
            if (!isAppendCnt) {// 时间管理转换天数
                cnt += getExpTimeDiffDays(bag.getExpTime());
            } else {
                cnt += bag.getSurplusCount();
            }
        }
        return cnt;
    }

    /**
     * 获取背包物品
     *
     * @param userId
     * @param prodType
     * @return
     */
    public Integer findGoodsInfo(Long userId, ProdGoodsType prodType) {
        List<UserBagDTO> infoList = findGoodsInfoList(userId, prodType);
        if(ListUtils.isEmpty(infoList)){
            return null;
        }
        // 属性值降序排列、过期时间升序排列
        return infoList.stream().sorted(Comparator.comparing(UserBagDTO::getOptValue).reversed().thenComparing(UserBagDTO::getExpTime)).map(UserBagDTO::getProdId).findFirst().orElse(null);
    }

    public List<UserBagDTO> findGoodsInfoList(Long userId, ProdGoodsType prodType) {
        List<UserBagDTO> list = new ArrayList<>();
        List<UUserBag> bagList = userDataGoodsCacheManager.findValidBags(this.findUserGoods(userId), null, prodType, null);
        if(ListUtils.isEmpty(bagList)){
            return list;
        }
        bagList.forEach(item -> {
            TProdSocialGoods socialGood = socialProdManager.getSocialGood(item.getProdId());
            if(Objects.isNull(socialGood)) return;
            Integer optValue = 0;
            if(prodType == ProdGoodsType.SocialVoiceDiscountTicket || prodType == ProdGoodsType.SocialVideoDiscountTicket){
                Map<String, Object> map = JsonUtils.toJsonMap(socialGood.getExtJsonCfg());
                optValue = Integer.valueOf((String) map.get("discount"));
            }
            if(prodType == ProdGoodsType.SocialVoiceFreeTicket || prodType == ProdGoodsType.SocialVideoFreeTicket){
                Map<String, Object> map = JsonUtils.toJsonMap(socialGood.getExtJsonCfg());
                optValue = Integer.valueOf((String) map.get("second"));
            }
            list.add(new UserBagDTO(item.getProdId(), item.getSurplusCount(), item.getExpTime(), optValue));
        });
        return list;
    }

    /**
     * 获取背包物品对应数量以及最近过期时间
     *
     * @param userId
     * @param prodType
     * @return
     */
    public Map<Integer, UserBagDTO> findGoodsInfoMap(Long userId, ProdGoodsType prodType) {
        return findGoodsInfoMap(userId, null, prodType, null);
    }

    /**
     * 获取背包物品对应数量以及最近过期时间
     *
     * @param userId
     * @param targetUserId
     * @param prodType
     * @param prodId
     * @return
     */
    public Map<Integer, UserBagDTO> findGoodsInfoMap(Long userId, Long targetUserId, ProdGoodsType prodType, Integer prodId) {
        Map<Integer, UserBagDTO> infoMap = new HashMap<>(1);
        // 是否按数量管理
        boolean isAppendCnt = true;
        // 根据物品类型定义表判断
        TProdGoodsType cfg = getGoodsTypeCfg(prodType);
        if (GoodsMergeType.AppendTime == cfg.getMergeType()) {
            // 按时间管理
            isAppendCnt = false;
        }
        List<UUserBag> bagList = userDataGoodsCacheManager.findValidBags(this.findUserGoods(userId), targetUserId, prodType, prodId);
        UserBagDTO bagDTO;
        for (UUserBag bag : bagList) {
            bagDTO = infoMap.get(bag.getProdId());
            int incrCnt = bag.getSurplusCount();
            if (!isAppendCnt) {
                // 时间管理转换天数
                incrCnt = getExpTimeDiffDays(bag.getExpTime());
            }
            if (bagDTO == null) {
                bagDTO = new UserBagDTO(bag.getProdId());
                infoMap.put(bag.getProdId(), bagDTO);
            }
            bagDTO.setSurplusCount(bagDTO.getSurplusCount() + incrCnt);
            bagDTO.setExpTime(getLatelyExpTime(bagDTO, bag));
            // 同一个物品，有一个佩戴就算佩戴
            if (EquipStatusType.True == bag.getIsEquip() && EquipStatusType.True != bagDTO.getIsEquip()) {
                bagDTO.setIsEquip(EquipStatusType.True);
            }
        }
        return infoMap;
    }


    /**
     * 修改一个物品的佩戴标记(is_support_equip=T的物品类型可以修改) <br>
     * 该方法不能修改已经绑定节目的物品
     *
     * @param targetType 目标修改类型，修改成什么状态
     */
    public void updateGoodsEquip(Long userId, ProdGoodsType prodType, Integer prodId, EquipStatusType targetType) {
        TProdGoodsType cfg = getGoodsTypeCfg(prodType);
        if (!BoolType.True.equals(cfg.getIsSupportEquip())) {
            throw new ComponentException("物品不支持佩戴");
        }
        // 提取背包记录ID
        List<UUserBag> targetBagList = userDataGoodsCacheManager.findUserValidBagsNoGiftWithTypeAndId(userId, prodType, prodId);
        if (ListUtils.isEmpty(targetBagList) || targetBagList.size() > 1) {
            throw new ComponentException("用户ID:{},物品ID:{},在背包中不是唯一的,无法修改佩戴 请联系管理员", userId, prodId);
        }
        UUserBag targetBag = targetBagList.get(0);
        // 判断类型是否一致，一致则不修改
        if (targetBag.getIsEquip().equals(targetType)) {
            return;
        }
        // 如果状态的情况，则判断能否增加状态
        if (EquipStatusType.True.equals(targetType)) {
            EquipStatusType newType = this.getIsEquipByEquipUpperLimit(targetBag, cfg, null, this.findUserGoods(userId));
            if (!EquipStatusType.True.equals(newType)) {
                throw new ComponentException("用户:{},背包:{},类物品佩戴位已满，无法佩戴", userId, cfg.getGoodsType());
            }
        }
        // 修改背包绑定业务缓存
        UserDataResource.getInstance(userId).getNoGiftOperation().bindBusiCacheBeforeBagUpdate(userId, prodType, prodId);
        // 异动记录
        BagChange change = new BagChange(uUserBagChangeMapper);
        // 记录背包异动
        change.recordBefore(targetBag);
        targetBag.setIsEquip(targetType);
        targetBag.setIsEquipTime(new Date());
        uUserBagMapper.updateByPrimaryKeySelective(targetBag);
        change.recordAfter(targetBag.getBagId(), targetBag);
        // 记录异动
        change.saveChange();
        // 标记缓存修改
        UserDataResource.getInstance(userId).getNoGiftOperation().signChange(targetBag);
    }

    /**
     * 修改勋章显示
     */
    public void updateSealEquip(Long bagId, Long userId, BoolType show, EquipShowType showType) {
        EquipStatusType targetType = show == BoolType.True ? EquipStatusType.True : EquipStatusType.False;
        if (show == BoolType.False && showType == EquipShowType.Family) {
            targetType = EquipStatusType.True;
            showType = EquipShowType.Homepage;
        }
        // 提取背包记录ID
        List<UUserBag> targetBagList = userDataGoodsCacheManager.findUserValidBagsNoGiftWithType(userId, ProdGoodsType.SocialSeal);
        UUserBag targetBag = null;
        for (UUserBag bag : targetBagList) {
            if (bag.getBagId().equals(bagId)) {
                targetBag = bag;
                break;
            }
        }
        if (targetBag == null) {
            throw new ComponentException("盖章不存在或已失效，请刷新后重试");
        }
        // 修改背包绑定业务缓存
        UserDataResource.getInstance(userId).getNoGiftOperation().bindBusiCacheBeforeBagUpdate(userId, ProdGoodsType.SocialSeal, targetBag.getProdId());
        // 异动记录
        BagChange change = new BagChange(uUserBagChangeMapper);
        // 记录背包异动
        change.recordBefore(targetBag);
        targetBag.setIsEquip(targetType);
        targetBag.setShowType(showType);
        targetBag.setIsEquipTime(new Date());
        uUserBagMapper.updateByPrimaryKeySelective(targetBag);
        change.recordAfter(targetBag.getBagId(), targetBag);
        // 记录异动
        change.saveChange();
        // 标记缓存修改
        UserDataResource.getInstance(userId).getNoGiftOperation().signChange(targetBag);

        // 如果是家族展示，则取消其他的家族展示
        if (showType == EquipShowType.Family) {

            for (UUserBag bag : targetBagList) {
                if (bag.getBagId().equals(targetBag.getBagId())) {
                    // 不是要修改的盖章
                    continue;
                }
                if (bag.getShowType() != EquipShowType.Family) {
                    continue;
                }
                // 修改背包绑定业务缓存
                UserDataResource.getInstance(userId).getNoGiftOperation().bindBusiCacheBeforeBagUpdate(userId, ProdGoodsType.SocialSeal, bag.getProdId());
                // 异动记录
                BagChange otherChange = new BagChange(uUserBagChangeMapper);
                // 记录背包异动
                otherChange.recordBefore(bag);
                bag.setShowType(EquipShowType.Homepage);
                uUserBagMapper.updateByPrimaryKeySelective(bag);
                otherChange.recordAfter(bag.getBagId(), bag);
                // 记录异动
                otherChange.saveChange();
                // 标记缓存修改
                UserDataResource.getInstance(userId).getNoGiftOperation().signChange(bag);
            }
        }


    }


    /**
     * 背包增加物品(按时间管理的物品)
     *
     * @param days 增加天数
     */
    public UUserBag addGoodsWithTime(Long userId, Long targetUserId, Integer goodsId, ProdGoodsType prodType, Integer days) {
        return addGoodsWithTime(userId, targetUserId, goodsId, null, getGoodsTypeCfg(prodType), days);
    }

    /**
     * 背包增加物品(按时间管理的物品)
     *
     * @param days 增加天数
     */
    public UUserBag addGoodsWithTime(Long userId, Long targetUserId, Integer goodsId, String goodsName, ProdGoodsType prodType, Integer days) {
        return addGoodsWithTime(userId, targetUserId, goodsId, goodsName, getGoodsTypeCfg(prodType), days);
    }

    /**
     * 背包增加物品(按时间管理的物品)
     *
     * @param days 增加天数
     */
    public UUserBag addGoodsWithTime(Long userId, Long targetUserId, Integer goodsId, String goodsName, TProdGoodsType prodGoodsType, Integer days) {
        if (prodGoodsType == null || !GoodsMergeType.AppendTime.equals(prodGoodsType.getMergeType())) {
            throw new DataException("只支持按时间管理的物品");
        }
        return addGoods(userId, targetUserId, prodGoodsType, goodsId, goodsName, days, null);
    }

    /**
     * 背包增加物品(按时间管理的物品)
     *
     * @param userId
     * @param goodsId
     * @param prodType
     * @param expTime  指定截止日
     */
    public UUserBag addGoodsWithTime(Long userId, Long targetUserId, Integer goodsId, ProdGoodsType prodType, Date expTime) {
        return addGoodWithTime(userId, targetUserId, goodsId, null, getGoodsTypeCfg(prodType), expTime);
    }

    /**
     * 背包增加物品(按时间管理的物品)
     *
     * @param userId
     * @param expTime 指定截止日
     */
    public UUserBag addGoodWithTime(Long userId, Long targetUserId, Integer goodsId, String goodsName, TProdGoodsType goodsTypeCfg, Date expTime) {
        if (goodsTypeCfg == null || !GoodsMergeType.AppendTime.equals(goodsTypeCfg.getMergeType())) {
            throw new DataException("只支持按时间管理的物品");
        }
        return addGoods(userId, targetUserId, goodsTypeCfg, goodsId, goodsName, null, expTime);
    }


    /**
     * 背包增加物品(按数量管理的物品)
     *
     * @param cnt     表示增加数量
     * @param expTime 物品失效日(可选)，不填表示2099-1-1失效
     */
    public UUserBag addGoodWithCnt(Long userId, Long targetUserId, Integer goodsId, TProdGoodsType prodGoodsType, Integer cnt, Date expTime) {
        if (prodGoodsType == null || !GoodsMergeType.AppendCount.equals(prodGoodsType.getMergeType())) {
            throw new DataException("只支持按数量管理的物品");
        }
        return addGoods(userId, targetUserId, prodGoodsType, goodsId, null, cnt, expTime);
    }

    /**
     * 背包增加物品(按数量管理的物品)
     *
     * @param cnt     表示增加数量
     * @param expTime 物品失效日(可选)，不填表示2099-1-1失效
     */
    public UUserBag addGoodWithCnt(Long userId, Long targetUserId, Integer goodsId, String goodsName, TProdGoodsType prodGoodsType, Integer cnt, Date expTime) {
        if (prodGoodsType == null || !GoodsMergeType.AppendCount.equals(prodGoodsType.getMergeType())) {
            throw new DataException("只支持按数量管理的物品");
        }
        return addGoods(userId, targetUserId, prodGoodsType, goodsId, goodsName, cnt, expTime);
    }

    /**
     * 背包增加物品
     *
     * @param goodsTypeCfg 物品类型定义
     * @param cnt          如果是物品类型是按数量管理的，cnt表示增加数量；如果按时间管理的，cnt表示增加天数和expTime只能二选一
     * @param expTime      如果是物品类型是按数量管理的，expTime表示失效日(可选)；如果按时间管理的，expTime表示指定截止日,和cnt只能二选一
     * @return
     */
    private UUserBag addGoods(Long userId, Long targetUserId, TProdGoodsType goodsTypeCfg, Integer goodsId, String goodsName, Integer cnt, Date expTime) {
        // 验证闯入数量和失效时间的合法性
        checkAddGoodsParam(goodsTypeCfg, cnt, expTime);
        // 数量管理物品设置默认失效时间
        if (GoodsMergeType.AppendCount.equals(goodsTypeCfg.getMergeType()) && expTime == null) {
            expTime = GlobalConstant.DEFAULT_EXP_TIME;
        }
        //修改背包绑定业务缓存
        UserDataResource.getInstance(userId).getNoGiftOperation().bindBusiCacheBeforeBagUpdate(userId, goodsTypeCfg.getGoodsType(), goodsId);
        List<UUserBag> bagList = this.findUserGoods(userId);
        // 异动记录
        BagChange change = new BagChange(uUserBagChangeMapper);
        // 查询背包中对应物品
        UUserBag goodsBag = this.getSameGoodsBag(bagList, goodsId, goodsTypeCfg, targetUserId, expTime);
        boolean clearBag = false;
        if (goodsBag == null) {
            // 如果cnt < 0 不处理
            if (cnt != null && cnt <= 0) {
                return null;
            }
            goodsBag = createGoodsBag(userId, targetUserId, goodsTypeCfg, goodsId, goodsName, cnt, expTime);
            // 是否装备判断
            goodsBag.setIsEquip(this.getIsEquipByEquipUpperLimit(goodsBag, goodsTypeCfg, targetUserId, bagList));
            if (!EquipStatusType.None.equals(goodsBag.getIsEquip())) {
                goodsBag.setIsEquipTime(new Date());
            }
            // 修改数据库和缓存
            uUserBagMapper.insert(goodsBag);
            bagList.add(goodsBag);
            //记录背包异动
            change.recordAfter(goodsBag.getBagId(), goodsBag);
        } else {
            change.recordBefore(goodsBag);
            if (StringUtils.isNotEmpty(goodsName)) {
                goodsBag.setProdName(goodsName);
            }
            if (EquipStatusType.False.equals(goodsBag.getIsEquip())) {
                //未装备的情况，判断能否装备
                goodsBag.setIsEquip(this.getIsEquipByEquipUpperLimit(goodsBag, goodsTypeCfg, targetUserId, bagList));
            }
            if (GoodsMergeType.AppendCount.equals(goodsTypeCfg.getMergeType())) {
                // 按数量管理，修改数据库数量和缓存
                if (uUserBagMapper.updateCount(goodsBag.getBagId(), cnt) <= 0) {
                    throw new DataException("用户背包数据不一致,请联系管理员处理");
                }
                goodsBag.setSurplusCount(goodsBag.getSurplusCount() + cnt);
            } else {
                // 时间管理，修改数据库时间和缓存
                if (goodsBag.getExpTime().getTime() < System.currentTimeMillis()) {// 当前背包记录已失效，需要重新判断能否状态
                    goodsBag.setIsEquip(this.getIsEquipByEquipUpperLimit(goodsBag, goodsTypeCfg, targetUserId, bagList));
                    if (!EquipStatusType.None.equals(goodsBag.getIsEquip())) {
                        goodsBag.setIsEquipTime(new Date());
                    }
                }
                Date newExpTime = addAppendTimeExpTime(goodsBag, cnt, expTime);
                if (clearBag = newExpTime.before(DateUtils.nowTime())) {
                    // 执行强制过期清理
                    userDataGoodsCacheManager.clearInvalidBags(goodsBag, true);
                } else {
                    goodsBag.setExpTime(newExpTime);
                    goodsBag.setSurplusCount(1);
                    if (uUserBagMapper.updateByPrimaryKeySelective(goodsBag) <= 0) {
                        throw new DataException("用户背包数据不一致,请联系管理员处理");
                    }
                }
            }
            // 记录背包异动
            change.recordAfter(goodsBag.getBagId(), clearBag ? null : goodsBag);
        }
        //记录异动
        change.saveChange();
        // 清除背包
        if (clearBag) {
            uUserBagMapper.deleteByPrimaryKey(goodsBag);
            LOG.info("清理背包失效记录:用户{}背包记录{}", goodsBag.getUserId(), goodsBag.getBagId());
        } else {
            // 标记缓存修改
            UserDataResource.getInstance(userId).getNoGiftOperation().signChange(goodsBag);
            // 标记背包发生变化
            if (goodsBag.getProdType().isShowInBag()) {
                this.markUserBagChange(userId, goodsBag.getBagId());
            }
        }
        return goodsBag;
    }

    /**
     * 背包扣减物品
     *
     * @return 如果goodsId是数量管理的返回剩余数量，否则返回剩余天数(天数是小数直接进位的，0.1表示1天)
     * @cnt 如果goodsId是数量管理的则cnt是扣减数量, 如果goodsId是时间管理的，cnt是扣减天数
     */
    public int reduceGoods(Long userId, Integer goodsId, Integer cnt) {
        if (cnt <= 0) {
            throw new DataException("数量必须大于0");
        }
        ProdGoodsDTO prodGoodsDTO = getProdGoodsInfo(goodsId);
        // 提取背包有效记录
        List<UUserBag> validBagList = userDataGoodsCacheManager.findUserValidBagsNoGiftWithTypeAndId(userId, prodGoodsDTO.getGoodsType(), goodsId);
        if (validBagList.size() == 0) {
            throw new DataException("背包暂无" + prodGoodsDTO.getGoodsName());
        }
        //修改背包绑定业务缓存
        UserDataResource.getInstance(userId).getNoGiftOperation().bindBusiCacheBeforeBagUpdate(userId, prodGoodsDTO.getGoodsType(), goodsId);
        if (GoodsMergeType.AppendCount.equals(prodGoodsDTO.getGoodsTypeCfg().getMergeType())) {
            // 优先扣除最近失效的物品
            sortByExpTime(validBagList);
            return this.reduceBagCnt(userId, validBagList, cnt);
        } else {
            return this.reduceBagTime(userId, validBagList, prodGoodsDTO.getGoodsTypeCfg(), cnt);
        }
    }

    /**
     * 调整背包物品的有效期
     *
     * @param goodsBag
     * @param goodsTypeCfg
     * @param newExpTime
     */
    public void updateGoodBagExpTime(UUserBag goodsBag, TProdGoodsType goodsTypeCfg, Date newExpTime) {
        if (newExpTime != null && newExpTime.getTime() < System.currentTimeMillis()) {
            throw new DataException("expTime必须大于当前时间");
        }
        Long userId = goodsBag.getUserId();
        //修改背包绑定业务缓存
        UserDataResource.getInstance(userId).getNoGiftOperation().bindBusiCacheBeforeBagUpdate(userId, goodsTypeCfg.getGoodsType(), goodsBag.getProdId());
        // 异动记录
        BagChange change = new BagChange(uUserBagChangeMapper);
        change.recordBefore(goodsBag);
        goodsBag.setExpTime(newExpTime);
        uUserBagMapper.updateByPrimaryKeySelective(goodsBag);
        // 记录背包异动
        change.recordAfter(goodsBag.getBagId(), goodsBag);
        //记录异动
        change.saveChange();
        // 标记缓存修改
        UserDataResource.getInstance(userId).getNoGiftOperation().signChange(goodsBag);
    }


    /**
     * 按有效期排序
     *
     * @param bagList
     */
    private void sortByExpTime(List<UUserBag> bagList) {
        SortUtils.sortFast(bagList, new SortFast<UUserBag>() {
            @Override
            public String[] sortField() {
                return new String[]{"expTime"};
            }

            @Override
            public SortType[] sortType() {
                return new SortType[]{SortType.Ascending};
            }

            @Override
            public Object sortValue(String s, UUserBag userBag) {
                if ("expTime".equals(s)) {
                    return userBag.getExpTime();
                }
                return null;
            }
        });
    }

    /**
     * 时间管理的背包记录的剩余天数<br>
     * 小数进位，即0.1表示为1天，1.1表示为2天
     */
    public int getExpTimeDiffDays(Date expTime) {
        return Double.valueOf(Math.ceil(DateUtils.getDiffSeconds(new Date(), expTime) / (24 * 60 * 60.0D))).intValue();
    }

    /**
     * 扣减背包时间
     *
     * @return
     */
    private int reduceBagTime(Long userId, List<UUserBag> validBagList, TProdGoodsType tProdGoodsType, Integer cnt) {
        if (validBagList.size() > 1) {
            throw new DataException("时间管理的背包物品存在多条");
        }
        //记录异动
        BagChange change = new BagChange(uUserBagChangeMapper);
        // 时间管理，只取一条且只有一条
        UUserBag bag = validBagList.get(0);
        change.recordBefore(bag);
        // 判断数量是否足够扣减,取失效时间到当前时间点 剩余多少天，小数点自动进位(例如 1.1天转换成 2天)
        int totalCnt = getExpTimeDiffDays(bag.getExpTime());
        if (totalCnt < cnt) {
            throw new DataException("背包数量不足");
        }
        bag.setExpTime(DateUtils.plusDays(bag.getExpTime(), cnt * -1));
        // 是否需要卸下判断,扣减完自动卸下
        if (totalCnt == cnt && BoolType.True.equals(tProdGoodsType.getIsSupportEquip()) && tProdGoodsType.getEquipUpperLimit() > 0) {
            bag.setIsEquip(EquipStatusType.False);
            bag.setIsEquipTime(new Date());
        }
        if (uUserBagMapper.updateByPrimaryKeySelective(bag) <= 0) {
            throw new DataException("用户背包数据不一致,请联系管理员处理");
        }
        //记录背包异动
        change.recordAfter(bag.getBagId(), bag);
        change.saveChange();
        UserDataResource.getInstance(userId).getNoGiftOperation().signChange(bag);

        return totalCnt - cnt;
    }

    /**
     * 扣减背包数量
     */
    private int reduceBagCnt(Long userId, List<UUserBag> validBagList, int cnt) {
        int totalCnt = 0;
        for (UUserBag bag : validBagList) {
            totalCnt += bag.getSurplusCount();
        }
        if (totalCnt < cnt) {
            throw new DataException("背包数量不足");
        }
        //记录异动
        BagChange change = new BagChange(uUserBagChangeMapper);
        // 扣减数量
        int reduceCnt = cnt;
        for (UUserBag bag : validBagList) {
            int itemReduceCnt = bag.getSurplusCount() >= reduceCnt ? reduceCnt : bag.getSurplusCount();
            // 修改数据库和缓存
            if (uUserBagMapper.updateCount(bag.getBagId(), itemReduceCnt * -1) <= 0) {
                throw new DataException("用户背包数据不一致,请联系管理员处理");
            }
            // 检查数量
            UUserBag uUserBag = uUserBagMapper.selectByPrimaryKey(new UUserBag(bag.getBagId()));
            if (uUserBag.getSurplusCount() < 0) {
                throw new DataException("用户背包数据不一致,请联系管理员处理");
            }
            // 记录背包异动
            change.recordBefore(bag);
            bag.setSurplusCount(bag.getSurplusCount() - itemReduceCnt);
            change.recordAfter(bag.getBagId(), bag);
            change.saveChange();
            // 标记缓存修改
            UserDataResource.getInstance(userId).getNoGiftOperation().signChange(bag);
            if ((reduceCnt -= itemReduceCnt) == 0) {
                break;
            }
        }

        return totalCnt - cnt;
    }

    /**
     * 时间管理的背包记录延长时间
     *
     * @param goodsBag
     * @param cnt
     * @param expTime
     * @return
     */
    private Date addAppendTimeExpTime(UUserBag goodsBag, Integer cnt, Date expTime) {
        if (expTime != null) {
            return expTime;
        }
        if (goodsBag.getExpTime().getTime() <= System.currentTimeMillis()) {
            // 当前背包记录时间已失效
            return DateUtils.plusDays(new Date(), cnt);
        } else {
            return DateUtils.plusDays(goodsBag.getExpTime(), cnt);
        }
    }

    /**
     * 提取物品类型定义
     *
     * @param prodType
     * @return
     */
    public TProdGoodsType getGoodsTypeCfg(ProdGoodsType prodType) {
        return tProdGoodsTypeMapper.selectByPrimaryKey(new TProdGoodsType(prodType));
    }

    /**
     * 创建物品背包记录
     */
    private UUserBag createGoodsBag(Long userId, Long targetUserId, TProdGoodsType tProdGoodsType, Integer goodsId, String goodsName, Integer cnt, Date expTime) {
        UUserBag goodsBag = new UUserBag();
        goodsBag.setProdType(tProdGoodsType.getGoodsType());
        goodsBag.setProdId(goodsId);
        goodsBag.setProdName(goodsName);
        goodsBag.setUserId(userId);
        goodsBag.setTargetUserId(targetUserId);
        if (GoodsMergeType.AppendCount.equals(tProdGoodsType.getMergeType())) {
            // 数量管理
            goodsBag.setSurplusCount(cnt);
            goodsBag.setExpTime(expTime);
        } else {
            // 时间管理
            goodsBag.setSurplusCount(1);
            goodsBag.setExpTime(expTime == null ? DateUtils.plusDays(new Date(), cnt) : expTime);
        }
        goodsBag.setCreateTime(DateUtils.nowTime());
        return goodsBag;
    }


    /**
     * 验证增加背包物品参数合法性 cnt 如果是物品类型是按数量管理的，cnt表示增加数量；如果按时间管理的，cnt表示增加天数和expTime只能二选一
     * expTime 如果是物品类型是按数量管理的，expTime表示失效日(可选)；如果按时间管理的，expTime表示指定截止日,和cnt只能二选一
     * bindProgramId 需要绑定节目物品必须填上该值, 不绑定节目的物品如果填上该值则会绑定节目(目前就房管勋章需要)
     */
    private void checkAddGoodsParam(TProdGoodsType tProdGoodsType, Integer cnt, Date expTime) {
        if (expTime != null && expTime.getTime() < System.currentTimeMillis()) {
            throw new DataException("expTime必须大于当前时间");
        }
        if (GoodsMergeType.AppendCount.equals(tProdGoodsType.getMergeType())) {// 按数量管理
            if (cnt == null || cnt <= 0) {
                throw new DataException("数量必须大于0");
            }
        } else {// 按时间管理
            if ((cnt != null && expTime != null) || (cnt == null && expTime == null)) {
                throw new DataException("按时间管理物品 cnt和expTime只能二选一");
            }
            // cnt 允许小于零，活动勋章自动过期处理
//            if (cnt != null && cnt <= 0) {
//                throw new DataException("数量必须大于0");
//            }
        }
    }

    /**
     * 是否装备物品
     */
    public boolean isEquipGoods(Long userId, Integer goodsId, ProdGoodsType prodType) {
        return userDataGoodsCacheManager.isEquipGoods(userId, goodsId, prodType);
    }

    /**
     * 背包增加礼物
     *
     * @param expTime 礼物失效时间允许为空
     */
    public UUserBag addGift(Long userId, Integer giftId, Integer cnt, Date expTime) {
        if (expTime != null && expTime.getTime() <= System.currentTimeMillis()) {
            throw new DataException("expTime不能小于当前时间");
        }
        if (expTime == null) {// 失效时间设置默认只
            expTime = GlobalConstant.DEFAULT_EXP_TIME;
        }
        if (cnt <= 0) {
            throw new DataException("增加的背包数量必须大于0");
        }
        //记录异动
        BagChange change = new BagChange(uUserBagChangeMapper);

        List<UUserBag> giftList = this.findUserGifts(userId);
        // 查询相同失效日期的礼物记录
        UUserBag bag = getSameGiftBag(giftList, giftId, expTime);
        if (bag == null) {
            bag = createGiftBag(expTime, cnt, userId, giftId);
            // 修改数据库&缓存
            uUserBagMapper.insert(bag);
            giftList.add(bag);
            //记录背包异动
            change.recordAfter(bag.getBagId(), bag);
        } else {
            // 修改数据库
            if (uUserBagMapper.updateCount(bag.getBagId(), cnt) <= 0) {
                throw new DataException("用户背包数据不一致,请联系管理员处理");
            }
            // 记录背包异动
            change.recordBefore(bag);
            // 修改缓存
            bag.setSurplusCount(bag.getSurplusCount() + cnt);
            change.recordAfter(bag.getBagId(), bag);
        }
        // 背包产生新的礼物，标记背包发生变化
        this.markUserBagChange(userId, bag.getBagId());
        change.saveChange();
        // 标记哪个礼物修改
        UserDataResource.getInstance(userId).getGiftOperation().signChange(bag);

        return bag;
    }

    /**
     * 背包扣减礼物 优先快失效的 返回剩余有效背包数量
     */
    public int reduceGift(Long userId, Integer giftId, Integer cnt) {
        if (cnt <= 0) {
            throw new DataException("背包扣减礼物数量必须大于0");
        }
        // 有效的礼物
        List<UUserBag> giftValidList = this.getValidGiftBag(this.findUserGifts(userId), giftId);
        if (ListUtils.isEmpty(giftValidList)) {
            ProdGiftDTO prodGiftDTO = getProdGiftInfo(giftId);
            throw new DataException(MsgUtils.format("回收礼物失败，当前玩家背包中不含有所要回收的礼物【{}】！", prodGiftDTO.getGiftName()));
        }
        // 检查背包数量是否足够
        int totalBagCnt = 0;
        for (UUserBag bag : giftValidList) {
            totalBagCnt += bag.getSurplusCount();
        }
        if (totalBagCnt < cnt) {
            throw new DataException("回收礼物失败，回收数量超限");
        }
        //记录异动
        BagChange change = new BagChange(uUserBagChangeMapper);
        // 扣减数量
        int reduceCnt = cnt;
        for (UUserBag bag : giftValidList) {
            int itemReduceCnt = bag.getSurplusCount() >= reduceCnt ? reduceCnt : bag.getSurplusCount();
            // 更新数据库
            if (uUserBagMapper.updateCount(bag.getBagId(), itemReduceCnt * -1) <= 0) {
                throw new DataException("用户背包数据不一致,请联系管理员处理");
            }
            // 检查数量
            UUserBag uUserBag = uUserBagMapper.selectByPrimaryKey(new UUserBag(bag.getBagId()));
            if (uUserBag.getSurplusCount() < 0) {
                throw new DataException("用户背包数据不一致,请联系管理员处理");
            }
            // 记录背包异动
            change.recordBefore(bag);
            // 标记和修改缓存
            bag.setSurplusCount(bag.getSurplusCount() - itemReduceCnt);
            change.recordAfter(bag.getBagId(), bag);

            UserDataResource.getInstance(userId).getGiftOperation().signChange(bag);
            // 检查是否已经扣减完成
            if ((reduceCnt -= itemReduceCnt) == 0) {
                break;
            }
        }
        change.saveChange();
        // 计算剩余背包数量
        return totalBagCnt - cnt;
    }

    /**
     * 创建一个背包礼物实例
     */
    private UUserBag createGiftBag(Date expTime, Integer cnt, Long userId, Integer giftId) {
        UUserBag b = new UUserBag();
        b.setExpTime(expTime);
        b.setProdType(getSupportProdGiftType());
        b.setSurplusCount(cnt);
        b.setUserId(userId);
        b.setIsEquip(EquipStatusType.None);
        b.setProdId(giftId);
        b.setCreateTime(DateUtils.nowTime());
        return b;
    }

    /**
     * 从礼物背包记录查找礼物记录
     *
     * @param expTime 礼物失效时间允许为空
     */
    private UUserBag getSameGiftBag(List<UUserBag> giftList, Integer giftId, Date expTime) {
        for (UUserBag bag : giftList) {
            if (!bag.getProdId().equals(giftId)) {
                continue;
            }
            if (bag.getExpTime().getTime() != expTime.getTime()) {
                continue;
            }
            return bag;
        }
        return null;
    }

    /**
     * 提取背包有效的礼物, 按背包失效时间排序
     */
    public List<UUserBag> getValidGiftBag(List<UUserBag> giftList, Integer giftId) {
        List<UUserBag> giftValidBags = new ArrayList<>();
        for (UUserBag bag : giftList) {
            if (null != giftId && !bag.getProdId().equals(giftId)) {
                continue;
            }
            if (bag.getSurplusCount() <= 0) {
                continue;
            }
            if (bag.getExpTime().getTime() < System.currentTimeMillis()) {
                continue;
            }
            giftValidBags.add(bag);
        }
        // 需要自定义排序，expTime小的最优先，expTime为空排最后
        if (giftValidBags.size() > 0) {
            SortUtils.sort(giftValidBags, "expTime", SortType.Ascending);
        }
        return giftValidBags;
    }


    /**
     * 从背包提取指定物品记录
     */
    public UUserBag getSameGoodsBag(List<UUserBag> bagList, Integer goodsId, TProdGoodsType tProdGoodsType, Long targetUserId, Date expTime) {
        for (UUserBag bag : bagList) {
            if (!bag.getProdId().equals(goodsId) || bag.getProdType() != tProdGoodsType.getGoodsType()) {
                continue;
            }
            if (bag.getTargetUserId() != null && !bag.getTargetUserId().equals(targetUserId)) {
                continue;
            }
            if (GoodsMergeType.AppendCount.equals(tProdGoodsType.getMergeType()) && expTime.getTime() != bag.getExpTime().getTime()) {
                continue;// 数量管理，失效时间必须一致
            }
            return bag;
        }
        return null;
    }

    /**
     * 根据已装备数量，判断新增物品能不能装备
     */
    private EquipStatusType getIsEquipByEquipUpperLimit(UUserBag currentBag, TProdGoodsType cfg, Long targetUserId, List<UUserBag> bagList) {
        if (BoolType.False.equals(cfg.getIsSupportEquip())) {
            return EquipStatusType.None;
        }
        if (cfg.getEquipUpperLimit() == -1) {
            return EquipStatusType.True;
        }
        // 计算已装备数量
        int total = 0;
        for (UUserBag bag : bagList) {
            if (bag.getSurplusCount() <= 0) {
                continue;// 已失效
            }
            if (bag.getExpTime().getTime() <= System.currentTimeMillis()) {
                continue;// 已失效
            }
            if (bag.getTargetUserId() != null && !bag.getTargetUserId().equals(targetUserId)) {
                continue;
            }
            if (bag.getIsEquip() != EquipStatusType.True) {
                continue; // 是否佩戴
            }
            if (cfg.getGoodsType() != null && !cfg.getGoodsType().name().equals(bag.getProdType().name())) {
                continue; // 物品类型一致
            }
            total++;
        }
        if (cfg.getEquipUpperLimit() > total) {
            return EquipStatusType.True;
        } else {
            return EquipStatusType.False;
        }
    }


    /**
     * 清理一个失效的礼物背包记录
     */
    public void clearBag(UUserBag invalidBag) {
        clearBag(invalidBag, false);
    }

    /**
     * 清理背包记录
     * 如果背包仍有效，需强制清理
     */
    public void clearBag(UUserBag bag, boolean isForce) {
        BagChange change = new BagChange(uUserBagChangeMapper);
        change.recordBefore(bag);
        if (ProdGoodsType.SocialGift.equals(bag.getProdType())) {
            userDataGiftCacheManager.clearInvalidBags(bag, isForce);
        } else {
            userDataGoodsCacheManager.clearInvalidBags(bag, isForce);
        }
        change.recordAfter(bag.getBagId(), null);
        change.saveChange();
        if (bag.getProdType() != ProdGoodsType.SocialBadge && bag.getProdType() != ProdGoodsType.SocialCar) {
            uUserBagMapper.deleteByPrimaryKey(bag);
        }
        LOG.info("清理背包失效记录:用户{}背包记录{}", bag.getUserId(), bag.getBagId());
    }

    /**
     * 获取背包最近的失效时间
     *
     * @param bagDTO
     * @param bag
     * @return
     */
    private Date getLatelyExpTime(UserBagDTO bagDTO, UUserBag bag) {
        if (bagDTO.getExpTime() == null) {
            return bag.getExpTime();
        }
        if (bag.getExpTime().before(bagDTO.getExpTime())) {
            return bag.getExpTime();
        }
        return bagDTO.getExpTime();
    }


    /**
     * 获取背包最近的失效时间
     *
     * @param subBagList 同一个物品的背包合集
     * @return
     */
    public String getLatelyExpTimeStr(List<UUserBag> subBagList, boolean giftBag) {
        Date expTime = getLatelyExpTime(subBagList, giftBag);
        return formatExpTime(expTime);

    }

    /**
     * 格式化失效时间
     *
     * @param expTime
     * @return
     */
    public String formatExpTime(Date expTime) {
        // 背包没有查询到 或者已失效
        if (null == expTime) {
            return null;
        }
        // 永久有效时间
        if (DateUtils.isSameDay(GlobalConstant.DEFAULT_EXP_TIME, expTime)) {
            return "永久有效";
        }
        // 同天显示 时分
        if (DateUtils.isSameDay(expTime)) {
            return DateUtils.toString(expTime, DatePattern.HM) + "失效";
        }
        // 不同天显示月日
        return DateUtils.toString(expTime, DatePattern.MD2) + "失效";
    }

    /**
     * 格式化失效时间
     *
     * @param expTime
     * @return
     */
    public String formatExpTimeForDressUp(Date expTime) {
        // 背包没有查询到 或者已失效
        if (null == expTime) {
            return null;
        }
        // 永久有效时间
        if (expTime.getTime() >= GlobalConstant.DEFAULT_EXP_TIME.getTime()) {
            return "永久";
        }
        if (DateUtils.isSameDay(expTime)) {
            return MsgUtils.format("剩余{}天", 1);
        }
        Long diffHours = DateUtils.getDiffHours(new Date(), expTime);
        if (diffHours < 0) {
            return MsgUtils.format("剩余{}天", 1);
        }
        // 多少天
        Long diffDays = diffHours / 24L;
        // 不足一天按照一天算
        Long remainder = diffHours % 24L;
        if (remainder > 0) {
            diffDays += 1;
        }
        return MsgUtils.format("剩余{}天", diffDays);
    }

    /**
     * 获取背包最近的失效时间
     *
     * @param subBagList 同一个物品的背包合集
     * @return
     */
    public Date getLatelyExpTime(List<UUserBag> subBagList, boolean giftBag) {
        if (ListUtils.isEmpty(subBagList)) {
            return null;
        }
        subBagList.sort(Comparator.comparing(UUserBag::getExpTime));
        UUserBag bag = subBagList.get(0);
        // 没有失效时间
        if (bag.getExpTime() == null) {
            if (giftBag) {
                return null;
            }
            return GlobalConstant.DEFAULT_EXP_TIME;
        }
        // 永久有效时间
        if (DateUtils.isSameDay(GlobalConstant.DEFAULT_EXP_TIME, bag.getExpTime())) {
            if (giftBag) {
                return null;
            }
            return GlobalConstant.DEFAULT_EXP_TIME;
        }
        Date now = new Date();
        // 失效时间在当前时间之前(已经失效)
        if (bag.getExpTime().before(now)) {
            return null;
        }
        return bag.getExpTime();
    }

    /**
     * 指定背包物品扣减数量
     *
     * @param bagId 指定背包物品
     */
    public int reduceByBagId(Long userId, Long bagId, Integer goodsId, Integer cnt) {
        if (cnt <= 0) {
            throw new DataException("数量必须大于0");
        }
        ProdGoodsDTO prodGoodsDTO = getProdGoodsInfo(goodsId);
        // 提取背包有效记录
        List<UUserBag> validBagList = userDataGoodsCacheManager.findUserValidBagsNoGiftWithTypeAndId(userId, ProdGoodsType.valueOf(prodGoodsDTO.getGoodsType().name()), goodsId);
        if (validBagList.size() == 0) {
            throw new DataException("背包暂无" + prodGoodsDTO.getGoodsName());
        }
        //修改背包绑定业务缓存
        UserDataResource.getInstance(userId).getNoGiftOperation().bindBusiCacheBeforeBagUpdate(userId, prodGoodsDTO.getGoodsType(), goodsId);

        //扣除指定背包数量
        return this.reduceBagCntByBagId(userId, validBagList, bagId, cnt);
    }

    /**
     * 扣减背包数量
     */
    private Integer reduceBagCntByBagId(Long userId, List<UUserBag> validBagList, Long bagId, int cnt) {
        //记录异动
        BagChange change = new BagChange(uUserBagChangeMapper);
        // 扣减数量
        for (UUserBag bag : validBagList) {
            //判断是否是此bagId
            if (!bagId.equals(bag.getBagId())) {
                continue;
            }
            if (bag.getSurplusCount() < cnt) {
                throw new DataException("用户背包数量不足");
            }
            // 修改数据库和缓存
            if (uUserBagMapper.updateCount(bag.getBagId(), cnt * -1) <= 0) {
                throw new DataException("用户背包数据不一致,请联系管理员处理");
            }
            // 检查数量
            UUserBag uUserBag = uUserBagMapper.selectByPrimaryKey(new UUserBag(bag.getBagId()));
            if (uUserBag.getSurplusCount() < 0) {
                throw new DataException("用户背包数据不一致,请联系管理员处理");
            }
            // 记录背包异动
            change.recordBefore(bag);
            bag.setSurplusCount(bag.getSurplusCount() - cnt);
            change.recordAfter(bag.getBagId(), bag);
            change.saveChange();
            // 标记缓存修改
            UserDataResource.getInstance(userId).getNoGiftOperation().signChange(bag);
            return uUserBag.getSurplusCount();
        }
        return null;
    }

    /**
     * 标记用户背包异动
     * 仅在用户背包新增，且是从无到有时，标记背包变化
     *
     * @param uid
     */
    public void markUserBagChange(Long uid, Long bagId) {
        // 增加用户背包异动标记
        RedisKey key = buildBagChangeKey(uid);
        busiRedisTemplate.sadd(key, bagId.toString());
        busiRedisTemplate.expire(key);
    }

    /**
     * 获取用户佩戴的聊天气泡
     *
     * @param userId
     * @return
     */
    public ChatBubbleDTO getWearChatBubble(Long userId) {
        List<UUserBag> bagList = findUserValidGoods(userId, null, ProdGoodsType.SocialChatBubble);
        for (UUserBag bag : bagList) {
            if (bag.getIsEquip() == EquipStatusType.True) {
                ProdGoodsDTO prodGoodsDTO = getProdGoodsInfo(bag.getProdId());
                if (null != prodGoodsDTO) {
                    TChatBubble chatBubble = socialProdManager.getChatBubble(bag.getProdId());
                    return new ChatBubbleDTO(chatBubble);
                }
            }
        }
        return null;
    }

    /**
     * 获取用户的聊天气泡
     *
     * @param user
     * @return
     */
    public ChatBubbleDTO getWearChatBubble(UUserBasic user) {
        if (!BusiUtils.isVip(user)) {
            return null;
        }
        return ChatBubbleDTO.buildDefault();
    }

    /**
     * 获取用户佩戴的头像框
     *
     * @param userId
     * @return
     */
    public String getWearHeadFrame(Long userId) {
        List<UUserBag> bagList = findUserValidGoods(userId, null, ProdGoodsType.SocialHeadFrame);
        for (UUserBag bag : bagList) {
            if (bag.getIsEquip() == EquipStatusType.True) {
                ProdGoodsDTO prodGoodsDTO = getProdGoodsInfo(bag.getProdId());
                if (null != prodGoodsDTO) {
                    return prodGoodsDTO.getGoodPic();
                }
            }
        }
        return null;
    }


    /**
     * 获取用户佩戴的座驾
     *
     * @param userId
     * @return
     */
    public Map<String, Object> getWearCar(Long userId) {
        // 读取活动座驾
        List<UUserBag> bagList = findUserValidGoods(userId, null, ProdGoodsType.SocialCar);
        for (UUserBag bag : bagList) {
            if (bag.getIsEquip() == EquipStatusType.True) {
                Integer prodId = bag.getProdId();
                TProdSocialGoods goods = socialProdManager.getSocialGood(prodId);
                if (StringUtils.isEmpty(goods.getAnimateCode())) {
                    LOG.error("座驾: {} 没有配置animateCode动画.", prodId);
                }
                Map<String, Object> carMap = getCarAnimationParams(goods.getAnimateCode());
                if (carMap != null) {
                    return carMap;
                }
            }
        }
        return null;
    }

    /**
     * 封装动画参数
     *
     * @param animationCode
     * @return
     */
    private Map<String, Object> getCarAnimationParams(String animationCode) {
        if (StringUtils.isEmpty(animationCode)) {
            return null;
        }
        TAnimation a = StaticBeanUtils.animationMapper().selectByPrimaryKey(new TAnimation(animationCode));
        if (a != null) {
            JSONObject extJsonCfg = JSON.parseObject(a.getExtJsonCfg());
            if (extJsonCfg == null) {
                extJsonCfg = new JSONObject();
                extJsonCfg.put("type", "mini");
            }
            Map<String, Object> returnMap = new HashMap<>(4);
            returnMap.put("type", extJsonCfg.get("type"));
            returnMap.put("theme", extJsonCfg.get("theme"));
            returnMap.put("svga", a.getSvgaUrl());
            returnMap.put("webp", a.getWebpUrl());
            return returnMap;
        }
        return null;
    }

    /**
     * 背包异动标记缓存
     */
    private RedisKey buildBagChangeKey(Long uid) {
        return RedisKey.create(BusiKeyDefine.UserBagChangeSet, uid);
    }

    /**
     * 移除用户背包异动标记
     *
     * @param uid
     */
    public void removeBagChangeMark(Long uid) {
        busiRedisTemplate.del(buildBagChangeKey(uid));
    }

    /**
     * 用户背包是有异动标记
     *
     * @param uid
     * @return
     */
    public boolean bagChanged(Long uid) {
        return busiRedisTemplate.exists(buildBagChangeKey(uid));
    }

    public boolean bagGiftChanged(Long userId, Long bagId) {
        return busiRedisTemplate.sismember(buildBagChangeKey(userId), bagId.toString());
    }

    /**
     * 修改背包物品名称
     *
     * @param bag
     * @param prodName
     */
    public void updateProdName(UUserBag bag, String prodName) {
        Long userId = bag.getUserId();
        //修改背包绑定业务缓存
        UserDataResource.getInstance(userId).getNoGiftOperation().bindBusiCacheBeforeBagUpdate(userId, bag.getProdType(), bag.getProdId());
        // 初始化背包缓存
        findUserGoods(bag.getUserId());
        // 异动记录
        BagChange change = new BagChange(uUserBagChangeMapper);
        change.recordBefore(bag);
        bag.setProdName(prodName);
        uUserBagMapper.updateByPrimaryKeySelective(bag);
        // 记录背包异动
        change.recordAfter(bag.getBagId(), bag);
        change.saveChange();
        // 标记缓存修改
        UserDataResource.getInstance(userId).getNoGiftOperation().signChange(bag);
    }

}
