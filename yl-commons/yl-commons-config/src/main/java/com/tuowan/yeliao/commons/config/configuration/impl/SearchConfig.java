package com.tuowan.yeliao.commons.config.configuration.impl;

import com.easyooo.framework.common.util.PropUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * 全文搜索参数配置
 *
 * <AUTHOR>
 * @date 2021/11/20 16:38
 */
public class SearchConfig {

    private static Logger LOG = LoggerFactory.getLogger(SearchConfig.class);

    /** 集群地址 */
    public static final HttpHost[] HTTP_HOSTS;
    /** 请求凭证 */
    public static final CredentialsProvider CREDENTIALS_PROVIDER;
    /** 整个连接池的大小 */
    public static final Integer MAX_CONN_TOTAL;
    /** 单个路由连接的最大数 */
    public static final Integer MAX_CONN_PER_ROUTE;
    /** 客户端和服务器建立连接超时时间 */
    public static final Integer CONNECT_TIMEOUT = 3000;
    /** 客户端从服务器读取数据超时时间 */
    public static final Integer SOCKET_TIMEOUT = 15000;
    /** 从连接池获取连接超时时间 */
    public static final Integer CONNECTION_REQUEST_TIMEOUT = 3000;

    static {
        Properties props = UnifiedConfig.getProperties();
//        Properties props = new Properties();
//        props.put("search.address", "http://**************:9200");
//        props.put("search.maxConnTotal", 100);
//        props.put("search.maxConnPerRoute", 50);
        HTTP_HOSTS = SearchConfig.getHttpHosts(props);
        CREDENTIALS_PROVIDER = SearchConfig.getCredentialsProvider(props);
        MAX_CONN_TOTAL = PropUtils.getInt(props, "search.maxConnTotal");
        MAX_CONN_PER_ROUTE = PropUtils.getInt(props, "search.maxConnPerRoute");
    }

    private static HttpHost[] getHttpHosts(Properties props) {
        String hosts = PropUtils.getString(props, "search.address");
        try {
            List<HttpHost> httpHosts = new ArrayList<>();
            for (String host : hosts.split(",")) {
                URL hostUrl = new URL(host);
                httpHosts.add(new HttpHost(hostUrl.getHost(), hostUrl.getPort(), hostUrl.getProtocol()));
            }
            return httpHosts.toArray(new HttpHost[0]);
        } catch (Exception e) {
            LOG.error("获取全文搜索集群地址出错，原因：", e);
            return null;
        }
    }

    private static CredentialsProvider getCredentialsProvider(Properties props) {
        String username = PropUtils.getString(props, "search.username");
        String password = PropUtils.getString(props, "search.password");
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            return null;
        }
        CredentialsProvider provider = new BasicCredentialsProvider();
        provider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
        return provider;
    }
}
