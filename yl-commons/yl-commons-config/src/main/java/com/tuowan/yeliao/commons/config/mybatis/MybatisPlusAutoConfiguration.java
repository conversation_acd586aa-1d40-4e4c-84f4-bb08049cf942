package com.tuowan.yeliao.commons.config.mybatis;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * MyBatis-Plus 配置类
 * 与现有的 easyooo-framework 配置隔离，用于新业务开发
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Configuration
@ConditionalOnProperty(name = "mybatis-plus.enabled", havingValue = "true", matchIfMissing = false)
public class MybatisPlusAutoConfiguration {

    /**
     * MyBatis-Plus 专用的 SqlSessionFactory
     * 使用不同的 bean 名称避免与现有配置冲突
     */
    @Bean("mybatisPlusSqlSessionFactory")
    public SqlSessionFactory mybatisPlusSqlSessionFactory(DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean factory = new MybatisSqlSessionFactoryBean();
        factory.setDataSource(dataSource);
        
        // 设置 mapper xml 文件位置
        factory.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources("classpath*:mapper/plus/**/*Mapper.xml"));
        
        // 设置实体类包路径
        factory.setTypeAliasesPackage("com.tuowan.yeliao.**.entity.plus");
        
        // 全局配置
        GlobalConfig globalConfig = new GlobalConfig();
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();
        dbConfig.setLogicDeleteField("deleted"); // 逻辑删除字段
        dbConfig.setLogicDeleteValue("1"); // 逻辑删除值
        dbConfig.setLogicNotDeleteValue("0"); // 逻辑未删除值
        globalConfig.setDbConfig(dbConfig);
        factory.setGlobalConfig(globalConfig);
        
        // 添加插件
        factory.setPlugins(mybatisPlusInterceptor());
        
        return factory.getObject();
    }

    /**
     * MyBatis-Plus 拦截器配置
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

    /**
     * MyBatis-Plus 专用的 Mapper 扫描配置
     * 扫描特定包路径，避免与现有 Mapper 冲突
     */
    @Bean("mybatisPlusMapperScannerConfigurer")
    public static MapperScannerConfigurer mybatisPlusMapperScannerConfigurer() {
        MapperScannerConfigurer configurer = new MapperScannerConfigurer();
        // 只扫描 plus 包下的 Mapper
        configurer.setBasePackage("com.tuowan.yeliao.**.mapper.plus");
        // 使用 MyBatis-Plus 专用的 SqlSessionFactory
        configurer.setSqlSessionFactoryBeanName("mybatisPlusSqlSessionFactory");
        return configurer;
    }
}
