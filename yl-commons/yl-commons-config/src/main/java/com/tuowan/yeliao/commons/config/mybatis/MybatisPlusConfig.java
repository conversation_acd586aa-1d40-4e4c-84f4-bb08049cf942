package com.tuowan.yeliao.commons.config.mybatis;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * MyBatis-Plus 元数据处理器
 * 自动填充创建时间、更新时间等字段
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Component
@ConditionalOnProperty(name = "mybatis-plus.enabled", havingValue = "true", matchIfMissing = false)
public class MybatisPlusConfig implements MetaObjectHandler {

    /**
     * 插入时自动填充
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        Date now = new Date();
        
        // 自动填充创建时间
        this.strictInsertFill(metaObject, "createTime", Date.class, now);
        this.strictInsertFill(metaObject, "createdTime", Date.class, now);
        this.strictInsertFill(metaObject, "gmtCreate", Date.class, now);
        
        // 自动填充更新时间
        this.strictInsertFill(metaObject, "updateTime", Date.class, now);
        this.strictInsertFill(metaObject, "updatedTime", Date.class, now);
        this.strictInsertFill(metaObject, "gmtModified", Date.class, now);
        
        // 自动填充逻辑删除字段
        this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
        this.strictInsertFill(metaObject, "isDeleted", Integer.class, 0);
        
        // 支持 LocalDateTime
        LocalDateTime localNow = LocalDateTime.now();
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, localNow);
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, localNow);
    }

    /**
     * 更新时自动填充
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        Date now = new Date();
        
        // 自动填充更新时间
        this.strictUpdateFill(metaObject, "updateTime", Date.class, now);
        this.strictUpdateFill(metaObject, "updatedTime", Date.class, now);
        this.strictUpdateFill(metaObject, "gmtModified", Date.class, now);
        
        // 支持 LocalDateTime
        LocalDateTime localNow = LocalDateTime.now();
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, localNow);
    }
}
