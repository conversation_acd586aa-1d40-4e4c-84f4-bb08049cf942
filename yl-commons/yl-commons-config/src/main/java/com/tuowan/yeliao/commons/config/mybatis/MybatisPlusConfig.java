package com.tuowan.yeliao.commons.config.mybatis;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * MyBatis-Plus 配置类
 * 包含分页插件、乐观锁插件和元数据处理器
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Configuration
@ConditionalOnProperty(name = "mybatis-plus.enabled", havingValue = "true", matchIfMissing = false)
public class MybatisPlusConfig {

    /**
     * MyBatis-Plus 拦截器配置
     * 包含分页插件和乐观锁插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 分页插件
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor();
        // 设置数据库类型为 MySQL
        paginationInterceptor.setDbType(DbType.MYSQL);
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInterceptor.setMaxLimit(1000L);
        // 开启 count 的 join 优化，只针对部分 left join
        paginationInterceptor.setOptimizeJoin(true);
        interceptor.addInnerInterceptor(paginationInterceptor);

        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        return interceptor;
    }

    /**
     * 元数据处理器
     * 自动填充创建时间、更新时间等字段
     */
    @Component
    @ConditionalOnProperty(name = "mybatis-plus.enabled", havingValue = "true", matchIfMissing = false)
    public static class MetaObjectHandlerConfig implements MetaObjectHandler {

        /**
         * 插入时自动填充
         */
        @Override
        public void insertFill(MetaObject metaObject) {
            Date now = new Date();

            // 自动填充创建时间
            this.strictInsertFill(metaObject, "createTime", Date.class, now);
            this.strictInsertFill(metaObject, "createdTime", Date.class, now);
            this.strictInsertFill(metaObject, "gmtCreate", Date.class, now);

            // 自动填充更新时间
            this.strictInsertFill(metaObject, "updateTime", Date.class, now);
            this.strictInsertFill(metaObject, "updatedTime", Date.class, now);
            this.strictInsertFill(metaObject, "gmtModified", Date.class, now);

            // 自动填充逻辑删除字段
            this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
            this.strictInsertFill(metaObject, "isDeleted", Integer.class, 0);

            // 支持 LocalDateTime
            LocalDateTime localNow = LocalDateTime.now();
            this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, localNow);
            this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, localNow);
        }

        /**
         * 更新时自动填充
         */
        @Override
        public void updateFill(MetaObject metaObject) {
            Date now = new Date();

            // 自动填充更新时间
            this.strictUpdateFill(metaObject, "updateTime", Date.class, now);
            this.strictUpdateFill(metaObject, "updatedTime", Date.class, now);
            this.strictUpdateFill(metaObject, "gmtModified", Date.class, now);

            // 支持 LocalDateTime
            LocalDateTime localNow = LocalDateTime.now();
            this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, localNow);
        }
    }
}
