package com.tuowan.yeliao.commons.config.mybatis;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.fill.Column;

import java.util.Collections;

/**
 * MyBatis-Plus 代码生成器
 * 用于快速生成实体类、Mapper、Service、Controller
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public class MybatisPlusGenerator {

    /**
     * 数据库连接配置
     */
    private static final String DB_URL = "******************************************************************************************************************************************************";
    private static final String DB_USERNAME = "root";
    private static final String DB_PASSWORD = "password";

    /**
     * 包配置
     */
    private static final String PARENT_PACKAGE = "com.tuowan.yeliao";
    private static final String MODULE_NAME = "social"; // 模块名，如：social、user、acct
    
    /**
     * 路径配置
     */
    private static final String PROJECT_PATH = System.getProperty("user.dir");
    private static final String ENTITY_PATH = PROJECT_PATH + "/yl-service-" + MODULE_NAME + "/yl-" + MODULE_NAME + "-entity/src/main/java";
    private static final String MAPPER_PATH = PROJECT_PATH + "/yl-service-" + MODULE_NAME + "/yl-" + MODULE_NAME + "-data/src/main/java";
    private static final String SERVICE_PATH = PROJECT_PATH + "/yl-service-" + MODULE_NAME + "/yl-" + MODULE_NAME + "-svr/src/main/java";
    private static final String CONTROLLER_PATH = PROJECT_PATH + "/yl-service-" + MODULE_NAME + "/yl-" + MODULE_NAME + "-web/src/main/java";
    private static final String MAPPER_XML_PATH = PROJECT_PATH + "/yl-service-" + MODULE_NAME + "/yl-" + MODULE_NAME + "-data/src/main/resources/mapper/plus";

    public static void main(String[] args) {
        // 需要生成的表名（去掉前缀）
        String[] tableNames = {"s_post_plus_demo"}; // 可以添加多个表
        
        // 表前缀（生成时会去掉）
        String[] tablePrefixes = {"s_", "u_", "t_"};
        
        generateCode(tableNames, tablePrefixes);
    }

    /**
     * 生成代码
     */
    public static void generateCode(String[] tableNames, String[] tablePrefixes) {
        FastAutoGenerator.create(DB_URL, DB_USERNAME, DB_PASSWORD)
                // 全局配置
                .globalConfig(builder -> {
                    builder.author("System") // 设置作者
                           .enableSwagger() // 开启 swagger 模式
                           .fileOverride() // 覆盖已生成文件
                           .dateType(DateType.ONLY_DATE) // 时间类型对应策略
                           .commentDate("yyyy-MM-dd") // 注释日期
                           .outputDir(ENTITY_PATH); // 指定输出目录
                })
                // 包配置
                .packageConfig(builder -> {
                    builder.parent(PARENT_PACKAGE) // 设置父包名
                           .moduleName(MODULE_NAME) // 设置父包模块名
                           .entity("entity.plus") // Entity 包名
                           .mapper("data.mapper.plus") // Mapper 包名
                           .service("service.plus") // Service 包名
                           .serviceImpl("service.plus.impl") // ServiceImpl 包名
                           .controller("web.controller.plus") // Controller 包名
                           .pathInfo(Collections.singletonMap(OutputFile.mapperXml, MAPPER_XML_PATH)); // 设置mapperXml生成路径
                })
                // 策略配置
                .strategyConfig(builder -> {
                    builder.addInclude(tableNames) // 设置需要生成的表名
                           .addTablePrefix(tablePrefixes) // 设置过滤表前缀
                           
                           // Entity 策略配置
                           .entityBuilder()
                           .enableLombok() // 开启 lombok 模型
                           .enableTableFieldAnnotation() // 开启生成实体时生成字段注解
                           .enableChainModel() // 开启链式模型
                           .naming(NamingStrategy.underline_to_camel) // 数据库表映射到实体的命名策略
                           .columnNaming(NamingStrategy.underline_to_camel) // 数据库表字段映射到实体的命名策略
                           .idType(IdType.AUTO) // 主键类型
                           .formatFileName("%s") // 格式化文件名称
                           .addSuperEntityClass("com.tuowan.yeliao.commons.entity.plus.BaseEntity") // 设置父类
                           .addSuperEntityColumns("id", "create_time", "update_time", "deleted", "version") // 设置父类公共字段
                           .addTableFills(new Column("create_time", FieldFill.INSERT)) // 基于数据库字段填充
                           .addTableFills(new Column("update_time", FieldFill.INSERT_UPDATE))
                           .addTableFills(new Column("deleted", FieldFill.INSERT))
                           
                           // Mapper 策略配置
                           .mapperBuilder()
                           .enableMapperAnnotation() // 开启 @Mapper 注解
                           .enableBaseResultMap() // 启用 BaseResultMap 生成
                           .enableBaseColumnList() // 启用 BaseColumnList
                           .superClass("com.tuowan.yeliao.commons.data.mapper.plus.BasePlusMapper") // 设置父类
                           .formatMapperFileName("%sMapper") // 格式化 mapper 文件名称
                           .formatXmlFileName("%sMapper") // 格式化 xml 实现类文件名称
                           
                           // Service 策略配置
                           .serviceBuilder()
                           .formatServiceFileName("%sService") // 格式化 service 接口文件名称
                           .formatServiceImplFileName("%sServiceImpl") // 格式化 service 实现类文件名称
                           .superServiceClass("com.baomidou.mybatisplus.extension.service.IService") // 设置 service 接口父类
                           .superServiceImplClass("com.baomidou.mybatisplus.extension.service.impl.ServiceImpl") // 设置 service 实现类父类
                           
                           // Controller 策略配置
                           .controllerBuilder()
                           .enableHyphenStyle() // 开启驼峰转连字符
                           .enableRestStyle() // 开启生成@RestController 控制器
                           .formatFileName("%sController") // 格式化文件名称
                           .requestMappingBuilder()
                           .requestMapping("/api/v2/{table}"); // 设置请求映射
                })
                // 模板引擎配置，默认 Velocity 可选模板引擎 Beetl 或 Freemarker
                //.templateEngine(new BeetlTemplateEngine())
                //.templateEngine(new FreemarkerTemplateEngine())
                .execute();
        
        System.out.println("代码生成完成！");
        System.out.println("Entity 路径: " + ENTITY_PATH);
        System.out.println("Mapper 路径: " + MAPPER_PATH);
        System.out.println("Service 路径: " + SERVICE_PATH);
        System.out.println("Controller 路径: " + CONTROLLER_PATH);
        System.out.println("Mapper XML 路径: " + MAPPER_XML_PATH);
    }

    /**
     * 生成单个模块的代码
     */
    public static void generateForModule(String moduleName, String[] tableNames, String[] tablePrefixes) {
        // 动态设置模块路径
        String entityPath = PROJECT_PATH + "/yl-service-" + moduleName + "/yl-" + moduleName + "-entity/src/main/java";
        String mapperPath = PROJECT_PATH + "/yl-service-" + moduleName + "/yl-" + moduleName + "-data/src/main/java";
        String servicePath = PROJECT_PATH + "/yl-service-" + moduleName + "/yl-" + moduleName + "-svr/src/main/java";
        String controllerPath = PROJECT_PATH + "/yl-service-" + moduleName + "/yl-" + moduleName + "-web/src/main/java";
        String mapperXmlPath = PROJECT_PATH + "/yl-service-" + moduleName + "/yl-" + moduleName + "-data/src/main/resources/mapper/plus";

        FastAutoGenerator.create(DB_URL, DB_USERNAME, DB_PASSWORD)
                .globalConfig(builder -> {
                    builder.author("System")
                           .enableSwagger()
                           .fileOverride()
                           .dateType(DateType.ONLY_DATE)
                           .commentDate("yyyy-MM-dd")
                           .outputDir(entityPath);
                })
                .packageConfig(builder -> {
                    builder.parent(PARENT_PACKAGE)
                           .moduleName(moduleName)
                           .entity("entity.plus")
                           .mapper("data.mapper.plus")
                           .service("service.plus")
                           .serviceImpl("service.plus.impl")
                           .controller("web.controller.plus")
                           .pathInfo(Collections.singletonMap(OutputFile.mapperXml, mapperXmlPath));
                })
                .strategyConfig(builder -> {
                    builder.addInclude(tableNames)
                           .addTablePrefix(tablePrefixes)
                           .entityBuilder()
                           .enableLombok()
                           .enableTableFieldAnnotation()
                           .enableChainModel()
                           .naming(NamingStrategy.underline_to_camel)
                           .columnNaming(NamingStrategy.underline_to_camel)
                           .idType(IdType.AUTO)
                           .formatFileName("%s")
                           .addSuperEntityClass("com.tuowan.yeliao.commons.entity.plus.BaseEntity")
                           .addSuperEntityColumns("id", "create_time", "update_time", "deleted", "version")
                           .addTableFills(new Column("create_time", FieldFill.INSERT))
                           .addTableFills(new Column("update_time", FieldFill.INSERT_UPDATE))
                           .addTableFills(new Column("deleted", FieldFill.INSERT))
                           .mapperBuilder()
                           .enableMapperAnnotation()
                           .enableBaseResultMap()
                           .enableBaseColumnList()
                           .superClass("com.tuowan.yeliao.commons.data.mapper.plus.BasePlusMapper")
                           .formatMapperFileName("%sMapper")
                           .formatXmlFileName("%sMapper")
                           .serviceBuilder()
                           .formatServiceFileName("%sService")
                           .formatServiceImplFileName("%sServiceImpl")
                           .superServiceClass("com.baomidou.mybatisplus.extension.service.IService")
                           .superServiceImplClass("com.baomidou.mybatisplus.extension.service.impl.ServiceImpl")
                           .controllerBuilder()
                           .enableHyphenStyle()
                           .enableRestStyle()
                           .formatFileName("%sController");
                })
                .execute();
        
        System.out.println("模块 " + moduleName + " 代码生成完成！");
    }

    /**
     * 使用示例
     */
    public static void example() {
        // 示例1：为 social 模块生成 Post 相关代码
        String[] socialTables = {"s_post_plus_demo", "s_comment", "s_like"};
        String[] prefixes = {"s_"};
        generateForModule("social", socialTables, prefixes);
        
        // 示例2：为 user 模块生成 User 相关代码
        String[] userTables = {"u_user", "u_profile", "u_setting"};
        String[] userPrefixes = {"u_"};
        generateForModule("user", userTables, userPrefixes);
    }
}
