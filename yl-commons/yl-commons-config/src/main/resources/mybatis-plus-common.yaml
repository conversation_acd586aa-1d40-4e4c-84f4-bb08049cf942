# MyBatis-Plus 公共配置
# 所有使用 MyBatis-Plus 的模块都可以引用此配置

# MyBatis-Plus 全局配置
mybatis-plus:
  # 启用 MyBatis-Plus（默认关闭，需要在具体模块中开启）
  enabled: false
  
  # Mapper XML 文件位置
  mapper-locations: 
    - classpath*:mapper/plus/**/*Mapper.xml
    - classpath*:com/tuowan/yeliao/**/mapper/plus/**/*Mapper.xml
  
  # 实体类别名包路径
  type-aliases-package: 
    - com.tuowan.yeliao.**.entity.plus
    - com.tuowan.yeliao.**.domain.plus
  
  # 全局配置
  global-config:
    # 数据库配置
    db-config:
      # 主键类型（AUTO 自增）
      id-type: auto
      # 表名前缀（可以根据需要配置）
      table-prefix: ""
      # 字段策略
      insert-strategy: not_null
      update-strategy: not_null
      select-strategy: not_empty
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      # 乐观锁字段
      version-field: version
    
    # 元数据处理器
    meta-object-handler: com.tuowan.yeliao.commons.config.mybatis.MybatisPlusConfig
  
  # MyBatis 配置
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: true
    # 懒加载
    lazy-loading-enabled: true
    # 积极懒加载
    aggressive-lazy-loading: false
    # 允许多结果集
    multiple-result-sets-enabled: true
    # 使用列标签
    use-column-label: true
    # 使用生成的键
    use-generated-keys: true
    # 自动映射行为
    auto-mapping-behavior: partial
    # 自动映射未知列行为
    auto-mapping-unknown-column-behavior: none
    # 默认执行器类型
    default-executor-type: simple
    # 默认语句超时时间
    default-statement-timeout: 25000
    # 默认获取数据大小
    default-fetch-size: 100
    # 是否开启自动刷新参数
    safe-row-bounds-enabled: false
    # 是否开启自动生成主键
    safe-result-handler-enabled: true
    # 日志实现（开发环境可开启，生产环境建议关闭）
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 分页插件配置
pagehelper:
  # 数据库方言
  helper-dialect: mysql
  # 分页合理化
  reasonable: true
  # 支持通过 Mapper 接口参数来传递分页参数
  support-methods-arguments: true
  # 分页参数
  params: count=countSql
  # 自动识别数据库类型
  auto-dialect: true
  # 关闭合理化时，如果pageNum<1会查询第一页，如果pageNum>pages会查询最后一页
  reasonable: false

# 性能分析插件配置（仅开发环境使用）
---
spring:
  profiles: dev

mybatis-plus:
  configuration:
    # 开发环境开启 SQL 日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

---
spring:
  profiles: test

mybatis-plus:
  configuration:
    # 测试环境使用 SLF4J 日志
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

---
spring:
  profiles: prod

mybatis-plus:
  configuration:
    # 生产环境关闭 SQL 日志
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
