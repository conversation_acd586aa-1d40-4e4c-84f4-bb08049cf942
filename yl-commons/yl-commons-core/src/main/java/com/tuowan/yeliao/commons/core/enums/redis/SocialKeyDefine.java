package com.tuowan.yeliao.commons.core.enums.redis;

import com.easyooo.framework.support.redis.key.DataMode;
import com.easyooo.framework.support.redis.key.KeyDefine;
import com.easyooo.framework.support.redis.key.KeyMode;
import com.easyooo.framework.support.redis.key.TTLMode;

/**
 * Recom缓存Key定义
 *
 * <AUTHOR>
 * @date 2020/6/16 21:13
 */
public enum SocialKeyDefine implements KeyDefine {

    // 亲密度下降消息用户
    IntimateDownMsgUser(KeyMode.Fixed, DataMode.Set, 3 * 3600),
    // 搭讪用户
    ChatUpUser(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),
    // 女用户今日搭讪次数
    FemaleChatUpDayTimes(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),
    // 女用户每分钟搭讪次数
    FemaleChatUpMinTimes(KeyMode.Params, DataMode.Incr, 60),
    // 女用户今日邀请音视频通话次数
    FemaleInviteCallDayTimes(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),
    // 女用户小时邀请音视频通话次数
    FemaleInviteCallHourTimes(KeyMode.Params, DataMode.Incr, 3600),
    // 用户邀请通话记录
    UserInviteCallRecord(KeyMode.Params, DataMode.String, 60),
    // 在线A，B聊主
    OnlineChatMaster(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 用户送出的魔法礼物数量缓存（目前只有魔法书，每天的数量）
    MagicGiftSendCount(KeyMode.Params, DataMode.Hash, 30 * 24 * 3600),
    // 礼物普通电视墙
    GiftCommonTvWall(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 礼物大额电视墙
    GiftBaTvWall(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 用户查看大额礼物的最近时间
    UserLookBaGiftTime(KeyMode.Params, DataMode.String, 3 * 24 * 3600),
    // 流光消息
    StreamMsg(KeyMode.Params, DataMode.Hash, TTLMode.DYNAMIC),
    // 家族文本消息队列
    RoomTextMsgQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 家族文本消息上下文
    RoomTextMsgContext(KeyMode.Params, DataMode.String, 5 * 60),

    // 男性用户私信礼物24小时内的消费总额缓存
    PrivateGiftConsumeBeans(KeyMode.Params, DataMode.Hash, 24 * 3600),

    // 用户待回复消息记录 key<uid> tid-timestamp, 每次更新的时候，移除过期的
    UserWaitReply(KeyMode.Params, DataMode.Sortedset, 8 * 3600),

    RealHeadRemind(KeyMode.Fixed, DataMode.Set, TTLMode.END_OF_DAY),

    // 等会融云回调验证的消息标识
    MsgCallbackNew(KeyMode.Params, DataMode.String, 60),
    MsgCallbackOld(KeyMode.Params, DataMode.List, 60),

    // 批量搭讪标记
    BatchChatUpMark(KeyMode.Params, DataMode.String, TTLMode.END_OF_DAY),
    // 未充值男用户当日私聊女用户次数
    NrMaleChatFemaleTimes(KeyMode.Params, DataMode.Hash, TTLMode.END_OF_DAY),
    // 搭讪分发队列
    ChatUpDispatchQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 搭讪任务生成标记，一段时间内不会重复生成搭讪任务
    ChatUpStartMark(KeyMode.Params, DataMode.String, 30 * 60),
    // 最近匹配过的用户ID缓存
    ChatUpMatchedUserIds(KeyMode.Params, DataMode.Sortedset, 2 * 24 * 3600),
    // 每日搭讪基础上限
    ChatUpBaseLimit(KeyMode.Params, DataMode.Hash, 2 * 24 * 3600),
    // 每日搭讪未达标用户，达到搭讪上限的用户
    ChatUpLimitedUserIds(KeyMode.Params, DataMode.Hash, TTLMode.END_OF_DAY),
    // 每日搭讪休息状态的用户ID
    ChatUpIdleUserIds(KeyMode.Params, DataMode.Hash, TTLMode.END_OF_DAY),
    // 停止自动搭讪的用户
    ChatUpStoppedUserIds(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),
    // 用户每日社交收益金额（用来判断每日搭讪分配的流量上线）
    UserDayIncomeCash(KeyMode.Params, DataMode.Hash, 3 * 24 * 3600),
    // 用户每日分发的搭讪次数
    UserDayChatUpTimes(KeyMode.Params, DataMode.Hash, 3 * 24 * 3600),
    // 用户每小时分发的搭讪次数
    UserHourChatUpTimes(KeyMode.Params, DataMode.Hash, 3600),
    // 用户日私信次数
    UserChatTimes(KeyMode.Params, DataMode.String, 25 * 3600),
    // 弹出输入框Banner 用户
    ChatInputBannerUser(KeyMode.Fixed, DataMode.Set, TTLMode.END_OF_DAY),
    // 用户
    ChatChargeTips(KeyMode.Fixed, DataMode.String, TTLMode.NONE),
    // 女用户每天首次跟男用户建立私信交互记录缓存
    UserFirstMsgForDayRecord(KeyMode.Params, DataMode.Sortedset, TTLMode.END_OF_DAY),
    // 首页礼物
    HomePageGifts(KeyMode.Params, DataMode.Set, 3 * 24 * 3600),
    // 男用户视频缘分队列
    MaleVideoFateQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 用户消息回复数据
    UserMsgReplyData(KeyMode.Params, DataMode.Sortedset, TTLMode.END_OF_DAY),
    // 社交行为交互时间记录
    SocialLastlyInteractive(KeyMode.Params, DataMode.Sortedset, 24 * 3600),
    // 礼物索要频率缓存
    RqGiftFrequency(KeyMode.Params, DataMode.Incr, 120),
    // 女用户一键搭讪一小时满额池子
    FemaleFastCuHourFullPond(KeyMode.Params, DataMode.Set, 3600),
    // 女用户一键搭讪一天满额池子
    FemaleFastCuDayFullPond(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),
    // 女用户接收一键搭讪次数
    FemaleFastCuTimes(KeyMode.Params, DataMode.Hash, TTLMode.END_OF_DAY),
    /************************  家族相关缓存 *******************/
    // 家族禁言列表（params：FamilyId）
    FamilyShutupList(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    FamilyRemoveTime(KeyMode.Params, DataMode.String, TTLMode.DYNAMIC),
    FamilyKickOutTime(KeyMode.Params, DataMode.String, 2 * 3600),
    // 报下麦后不能上麦的时间限制缓存
    FamilyKickMicTime(KeyMode.Params, DataMode.String, 15 * 60),
    FamilyLastVisitIds(KeyMode.Params, DataMode.Hash, TTLMode.NONE),
    // 用户当前正在访问的家族
    FamilyLastVisitTime(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    FamilyLastJoinExpire(KeyMode.Params, DataMode.String, 3600),
    FamilyLastEnterExpire(KeyMode.Params, DataMode.String, 3600),
    // 聊天室入场时效
    FamilyLastEnterAdmissionExpire(KeyMode.Params, DataMode.String, 60),


    // 家族成员贡献总榜
    FamilyMemberTotalRank(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 家族成员贡献值周榜
    FamilyMemberWeekRank(KeyMode.Params, DataMode.Sortedset, 15 * 24 * 3600),
    // 家族成员贡献值日榜
    FamilyMemberDayRank(KeyMode.Params, DataMode.Sortedset, 2 * 24 * 3600),
    // 家族成员贡献月榜
    FamilyMemberMonthRank(KeyMode.Params, DataMode.Sortedset, 45 * 24 * 3600),

    // 家族个人土豪榜（周榜）
    FamilyWealthRank(KeyMode.Params, DataMode.Sortedset, 15 * 24 * 3600),
    // 家族个人魅力榜（周榜）
    FamilyCharmRank(KeyMode.Params, DataMode.Sortedset, 15 * 24 * 3600),

    // 家族贡献总榜
    FamilyTotalRank(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 家族周贡献总榜
    FamilyWeekRank(KeyMode.Params, DataMode.Sortedset, 15 * 24 * 3600),
    // 家族日贡献总榜
    FamilyDayRank(KeyMode.Params, DataMode.Sortedset, 2 * 24 * 3600),
    // 家族成员每日私信获得的贡献值
    FamilyChatDayContribution(KeyMode.Params, DataMode.Hash, 24 * 3600),
    // 家族每日签到记录缓存
    FamilySignIn(KeyMode.Params, DataMode.Set, 24 * 3600),
    // 家族分钟贡献值
    FamilyMinuteRank(KeyMode.Params, DataMode.Hash, 30 * 60),
    // 家族火箭记录
    FamilyRocket(KeyMode.Params, DataMode.Sortedset, TTLMode.DYNAMIC),
    // 家族贡献日榜奖励
    FamilyDayRankMark(KeyMode.Params, DataMode.String, TTLMode.END_OF_DAY),
    // 家族贡献日榜奖励用户
    FamilyDayRankMarkUsers(KeyMode.Params, DataMode.Set, 2 * 24 * 3600),
    // 榜一tips队列
    FamilyDayMemberTipsQueue(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),
    // 榜一tips内容
    FamilyDayMemberTipsContent(KeyMode.Params, DataMode.String, 2 * 24 * 3600),
    // 榜一tips 发送用户
    FamilyDayMemberTipsLastUser(KeyMode.Params, DataMode.String, 2 * 24 * 3600),
    // 榜一炫彩发言用户
    FamilyDayMemberColorfulUser(KeyMode.Params, DataMode.String, TTLMode.END_OF_DAY),

    // 今日已经发放过家族奖励的ID缓存
    FamilyAwardGrantIds(KeyMode.Fixed, DataMode.Set, TTLMode.END_OF_DAY),

    // 家族信息修改次数
    FamilyUpdateTimes(KeyMode.Params, DataMode.Hash, TTLMode.END_OF_WEEK),
    // 家族解散时间（是否允许下次创建家族）
    FamilyDisbandTime(KeyMode.Params, DataMode.String, 2 * 24 * 3600),

    // 心意礼物昨日榜单
    YesterdayMgRank(KeyMode.Params, DataMode.Sortedset, TTLMode.END_OF_DAY),
    // 心意礼物历史榜单
    HisMgRank(KeyMode.Params, DataMode.Sortedset, TTLMode.END_OF_DAY),
    // 我的前三榜单用户
    MyMgRankThreeFriend(KeyMode.Params, DataMode.Sortedset, 5 * 24 * 3600),
    // 心意榜大哥（送礼方）
    MyMgRankEldestBrother(KeyMode.Params, DataMode.String, TTLMode.END_OF_DAY),

    // 聊天室幸运礼物连续赠送标识（缓存过期时间：3秒）
    GroupChatLgContinuousSendGiftSign(KeyMode.Params, DataMode.String, 3),
    // 聊天室幸运礼物连续赠送信息（缓存过期时间：10秒）
    GroupChatLgContinuousSendInfo(KeyMode.Params, DataMode.String, 10),
    // 聊天室幸运礼物礼物消息队列
    GroupChatLgWaitSendGiftMsgQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),

    /***********************  语聊房相关缓存 ******************/
    AudioRoomMicInfo(KeyMode.Params, DataMode.Hash, TTLMode.NONE),
    AudioLivingFamilyIds(KeyMode.Fixed, DataMode.Hash, TTLMode.NONE),
    AudioLiveMuteStatus(KeyMode.Params, DataMode.String, 24 * 3600),
    AudioLiveMusic(KeyMode.Params, DataMode.Hash, 2 * 24 * 3600),
    // 锁麦信息
    AudioRoomMicLockInfo(KeyMode.Params, DataMode.Set, TTLMode.NONE),


    // 家族列表
    FamilyTypeList(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 家族成员列表 角色分排序
    FamilyMember(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 家族群聊用户列表 最近上线时间
    FamilyChatUser(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 家族上次群聊人数
    FamilyLastChatUserNum(KeyMode.Params, DataMode.String, 3600),
    // 家族上次群聊人数文本
    FamilyLastNumText(KeyMode.Params, DataMode.String, 3600),
    // 邀请加入加入的人数
    FamilyInviteUserIds(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),
    // 邀请上麦时效
    FamilyInviteOnMic(KeyMode.Params, DataMode.String, 2 * 60),
    // 推荐家族时效
    FamilyRecommend(KeyMode.Params, DataMode.String, TTLMode.END_OF_DAY),
    // 家族提醒用户
    FamilyRemind(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),
    // 家族提醒队列
    FamilyRemindQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 家族待归档队列
    FamilyDisbandQueue(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),
    // 家族满员时效
    FamilyFullExpire(KeyMode.Params, DataMode.String, TTLMode.END_OF_WEEK),
    // 公告未读用户
    FamilyAnnouncementUsers(KeyMode.Params, DataMode.Set, 3 * 24 * 3600),

    // 家族@ 用户消息列表
    FamilyAtUserMsgList(KeyMode.Params, DataMode.Sortedset, 24 * 3600),
    // 家族@ 消息内容
    FamilyAtMsgContent(KeyMode.Params, DataMode.String, 24 * 3600),
    // 用户@ 消息未读
    FamilyAtUserUnRead(KeyMode.Params, DataMode.Hash, 24 * 3600),
    // @ 消息未读异动
    FamilyAtMarkChange(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),

    /************************  情侣空间相关缓存 *******************/
    // 情侣打卡信息
    CpClockInfo(KeyMode.Params, DataMode.Hash, TTLMode.NONE),
    // 情侣连续打卡
    CpClockContinuous(KeyMode.Params, DataMode.List, TTLMode.NONE),
    // 纪念日引导标记
    CpAnniversaryGuideMark(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),

    // 亲密度今日变化
    IntimateTodayChange(KeyMode.Params, DataMode.String, TTLMode.END_OF_DAY),
    // 用户亲密度增加标记(3天内)
    IntimateIncrMark(KeyMode.Params, DataMode.String, 3 * 24 * 3600),
    // 挽留用户信息
    UserStayInfo(KeyMode.Params, DataMode.String, 6 * 3600),


    /************************ 红包相关缓存 *******************/
    // 红包信息
    RedPacketInfo(KeyMode.Params, DataMode.Hash, 2 * 24 * 3600),
    // 抢红包用户 userId - robBeans
    RedPacketRobUser(KeyMode.Params, DataMode.Hash, 2 * 24 * 3600),
    // 定时红包队列
    RedPacketTimeQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 所有红包缓存
    AllRedPacket(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 家族红包缓存
    FamilyRedPacket(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 待失效队列
    RedPacketInvalidQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 抢红包详情 userId_beans time
    RedPacketDetail(KeyMode.Params, DataMode.Sortedset, 2 * 24 * 3600),

    /***********************  临时的活动缓存 ******************/
    // 圣诞节CP日榜
    ChristmasCpDayRank(KeyMode.Params, DataMode.Sortedset, 15 * 24 * 3600),
    // 圣诞节个人CP榜
    ChristmasCpRank(KeyMode.Params, DataMode.Sortedset, 15 * 24 * 3600),
    // 圣诞节家族日榜
    ChristmasFamilyDayRank(KeyMode.Params, DataMode.Sortedset, 15 * 24 * 3600),
    // 圣诞节家族成员榜单
    ChristmasFamilyUserDayRank(KeyMode.Params, DataMode.Sortedset, 15 * 24 * 3600),
    // 活动轮播消息列表
    ActMsgList(KeyMode.Fixed, DataMode.List, 15 * 24 * 3600),

    /************************** 抽奖转盘业务缓存 **************************/
    // 转盘抽奖列表缓存
    TurntableLotteryAwardConfigList(KeyMode.Fixed, DataMode.String, 30 * 24 * 3600),
    // 最近中奖消息缓存
    TurntableLotteryAwardMsg(KeyMode.Fixed, DataMode.List, TTLMode.NONE),


    /*********************** 音视频通话缓存 **********************/
    // 音视频通话扣费队列
    NetCallDeductQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),

    // 音视频通话扣费信息
    NetCallDeductInfo(KeyMode.Params, DataMode.Hash, 6 * 3600),

    // 音视频通话余额提醒队列
    NetCallBalanceRemindQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),

    // 音视频通话余额提醒信息
    NetCallBalanceRemindInfo(KeyMode.Params, DataMode.String, 6 * 3600),

    // 音视频通话强制中断队列
    NetCallDisconnectQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),

    // 音视频通话返还金币队列
    NetCallReturnBeansQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),

    // 音视频通话剔除频道队列
    NetCallKickChannelQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),

    // 音视频通话超时队列
    NetCallTimeoutQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),

    // 音视频通话消息通知标记
    NetCallNoticeMark(KeyMode.Params, DataMode.String, 1 * 60),

    // 等待开始录制的语音通话队列
    NetCallRecordingQueue(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),

    // 音视频通话等待结束录制的队列
    NetCallRecordingEndQueue(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),

    // 云录制信息缓存
    NetCallRecordingInfo(KeyMode.Fixed, DataMode.Hash, TTLMode.NONE),

    // 音频使用折扣券信息
    NetCallDiscountInfo(KeyMode.Params, DataMode.String, 3 * 24 * 3600),

    // 音视频通话遮罩信息
    NetCallMask(KeyMode.Params, DataMode.Set, 3600),

    // 待接听电话用户
    NetCallWait(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),

    // 音频通话记录 2个月缓存
    UserCallLog(KeyMode.Params, DataMode.Sortedset, 30 * 24 * 3600),

    // 好友之间最近的一次音频记录 2个月缓存
    UserCallFriendRecentLog(KeyMode.Params, DataMode.String, 30 * 24 * 3600),

    // 用户有最新的通话记录
    UserCallMark(KeyMode.Params, DataMode.String, 30 * 24 * 3600),

    // 用户按照日统计的信息缓存
    UserCallDayInfo(KeyMode.Params, DataMode.Hash, TTLMode.END_OF_DAY),

    // 用户按照周统计的信息缓存
    UserCallWeekInfo(KeyMode.Params, DataMode.Hash, TTLMode.END_OF_DAY),

    // 用户月充值累计
    UserMonthRecharge(KeyMode.Params, DataMode.String, 32 * 24 * 3600),

    // 每天通知用户缓存
    DayRemindUsers(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),

    // 女用户 拒接、漏接 次数缓存
    FemaleRefuseOrMissCallTimes(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),

    /************************  姻缘测算相关缓存 *******************/
    // 匹配到用户的 key 用户ID
    MarriageMatchUser(KeyMode.Params, DataMode.Sortedset, 7 * 24 * 3600),
    // 今日匹配次数
    MarriageMatchTimes(KeyMode.Params, DataMode.String, TTLMode.END_OF_DAY),
    // 总测算次数
    MarriageTotalTimes(KeyMode.Fixed, DataMode.String, TTLMode.NONE),
    // 用户婚配指数
    MarriageToIndex(KeyMode.Params, DataMode.String, 30 * 24 * 3600),
    // 测算概率
    MarriageProbability(KeyMode.Fixed, DataMode.String, 5 * 60),
    // 等待触发的私信提醒
    ChatTips(KeyMode.Params, DataMode.String, TTLMode.END_OF_DAY),
    // 每日私聊增加的亲密度 备注：不包括送礼 通话
    ChatIntimate(KeyMode.Fixed, DataMode.Hash, TTLMode.END_OF_DAY),
    // 官方提示标识
    ChatManagerTipSign(KeyMode.Params, DataMode.String, 24 * 3600),
    // 今日已获得的免费礼物数量
    ReceivedFreeGiftCount(KeyMode.Fixed, DataMode.Hash, TTLMode.END_OF_DAY),
    // 今日已送出的免费礼物数量
    SendFreeGiftCount(KeyMode.Fixed, DataMode.Hash, TTLMode.END_OF_DAY),


    // 数美语音识别requestId，Param：channelId
    SMAudioCheckRequestId(KeyMode.Params, DataMode.String, 3 * 24 * 3600),
    // 数美语音识别channelId，Param：requestId
    SMAudioCheckChannelId(KeyMode.Params, DataMode.String, 3 * 24 * 3600),
    // 数美语音识别违规次数
    SMAudioCheckViolation(KeyMode.Params, DataMode.String, 3 * 24 * 3600),
    // 数美语音识别违规通知用户间隔
    SMAudioCheckUserMsg(KeyMode.Params, DataMode.String, 30),
    // 数美语音识别违规通知钉钉客服间隔
    SMAudioCheckDdKfMsg(KeyMode.Params, DataMode.String, 30),
    // 数美语音识别特定场景违规次数
    SMAudioCheckTimes(KeyMode.Params, DataMode.String, 24 * 3600),


    // 在线充当机器人的真实用户列表，用来循环使用用户
    OnlineMaleRobotList(KeyMode.Fixed, DataMode.List, TTLMode.NONE),
    // 高等级在线男用户，用来给新女用户派发红包
    OnlineHighLevelMale(KeyMode.Fixed, DataMode.List, TTLMode.NONE),
    // 可用在线机器人集合
    OnlineMaleRobotSet(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),

    // 在线充当机器人的真实用户列表，用来循环使用用户
    OnlineFemaleRobotList(KeyMode.Fixed, DataMode.List, TTLMode.NONE),
    // 在线聊主队列，用户聊天红包匹配，根据聊主等级区分队列
    OnlineChatMasterList(KeyMode.Params, DataMode.List, TTLMode.NONE),
    // 可用在线机器人集合
    OnlineFemaleRobotSet(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),
    // 首页新人排序队列
    HomePageNewUserRecomQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 首页视频聊排序队列
    HomePageVideoChatRecomQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),

    // 首页白名单用户
    HomeWhitelistUsers(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),

    // 首页用户离线队列
    HomeUserOfflineQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),

    // 用户访客记录
    UserVisitRank(KeyMode.Params, DataMode.Sortedset, 24 * 3600),
    // 待插入访客队列
    WaitAddVisitorQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 上次模拟插入访客的时间
    AddVisitorLastTime(KeyMode.Params, DataMode.String, 5 * 60),

    // 删除OSS文件队列
    OSSDeleteObjectsQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),


    // 视频通话鉴黄通知
    VideoCallGreenNotify(KeyMode.Params, DataMode.String, TTLMode.DYNAMIC),


    /************** 好友关系推荐相关 ************************/
    // 密友列表缓存，根据在线状态 + 亲密度等级排序
    IntimateFriend(KeyMode.Params, DataMode.Sortedset, 2 * 24 * 3600),


    /***********  红包订单匹配业务 *******************/
    // 缘分匹配红包派发队列
    FateMatchRedPacketDispatchQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 等待用户回复领取的红包信息
    FateMatchRedPacketWaitReceiveInfo(KeyMode.Params, DataMode.Hash, TTLMode.NONE),
    // 缘分匹配红包待领取队列, 检查红包过期扫描该队列
    FateMatchRedPacketWaitReceiveQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 缘分匹配有待领取红包的标记(有效期3分钟)
    FateMatchRedPacketMark(KeyMode.Params, DataMode.String, 3 * 60),
    // 缘分匹配红包派发标记，有效期45分钟，有效期内不重复派发红包
    FateMatchRedPacketStartMark(KeyMode.Params, DataMode.String, 45 * 60),
    // 已经互相派过单的缓存,两天内不重复派发红包
    FateMatchedUser(KeyMode.Params, DataMode.String, 2 * 24 * 3600),
    // 单日红包数量限制
    FateMatchRedPacketCount(KeyMode.Fixed, DataMode.Hash, TTLMode.END_OF_DAY),

    // 系统消息 - 待发送队列
    SystemNoticeQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 系统消息 - 通知次数
    SystemNoticeTimes(KeyMode.Params, DataMode.String, TTLMode.DYNAMIC),
    // 用户每日红包流量检查提醒时效
    CheckRedPacketLevelUp(KeyMode.Params, DataMode.String, 24 * 3600),


    /****************** 社交推荐相关 ***********************/
    /**
     * 用户密友动态列表，通过密友用户和用户动态列表聚合生成
     */
    IntimatePost(KeyMode.Params, DataMode.Sortedset, 12 * 3600),
    /**
     * 用户关注动态列表，通过关注用户和用户动态列表聚合生成
     */
    FollowPost(KeyMode.Params, DataMode.Sortedset, 12 * 3600),
    /**
     * 最新动态列表发布时间倒序
     */
    NewPost(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 删除动态的用户，成员=删除动态用户ID，分数=删除时间
     */
    RemovePostUsers(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 推荐动态列表，按热度值排序
     */
    RecomPost(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 动态已读人数，定时刷新到数据库
     */
    PostReadNum(KeyMode.Fixed, DataMode.Hash, TTLMode.NONE),
    /**
     * 动态热度发生变化的动态缓存,参数：分片ID
     */
    PostHeatChange(KeyMode.Params, DataMode.Set, TTLMode.NONE),
    /**
     * 动态基础热度递减队列,参数：分片ID
     */
    PostBaseHeatDecrement(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 动态-热度待刷新动态
     */
    WaitRefreshHotPost(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 话题动态列表缓存 ，根据热度排序
     */
    GroupPostByHeat(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 话题动态列表，根据时间排序
     */
    GroupPostByTime(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 动态消息限制
     */
    FriendPostOptMsgLimit(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),
    /**
     * 机器人互动任务列表缓存, 参数：分片ID, 成员：动态ID#用户ID#任务类型
     */
    RobotTask(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 动态发布标记 用来同步粉丝 关注状态
     */
    PostPubChange(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 动态-最近参加某个动态话题的用户列表
     */
    PostGroupUserJoin(KeyMode.Params, DataMode.Sortedset, 30 * 24 * 3600),
    /**
     * 动态-话题参与者头像保存
     */
    PostGroupJoinUserHeadPic(KeyMode.Params, DataMode.List, 3600),
    /**
     * 动态-用户动态点赞postId保存
     */
    PostUserPraisePostIds(KeyMode.Params, DataMode.Set, 24 * 3600),
    /**
     * 动态-用户动态投票logId保存
     */
    PostUserVoteLogIds(KeyMode.Params, DataMode.Set, 24 * 3600),
    /**
     * 动态-用户动态上热门postId保存
     */
    PostUserUpHotPostIds(KeyMode.Params, DataMode.Sortedset, 24 * 3600),
    /**
     * 动态-热门动态
     */
    HotPost(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 动态-置顶
     */
    TopUpPost(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 用户系统消息队列
     */
    UserSystemNoticeQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 动态-评论-用户评论点赞commentId保存
     */
    PostCommentPraiseCommentIds(KeyMode.Params, DataMode.Set, 24 * 3600),

    /****************** 通话匹配相关 ***********************/
    /**
     * 通话匹配 - 聊主队列
     */
    NetCallMatchFemaleQueue(KeyMode.Fixed, DataMode.List, TTLMode.NONE),
    /**
     * 通话匹配 - 超时队列
     */
    NetCallMatchTimeoutQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 通话匹配 - 匹配次数
     */
    NetCallMatchTimes(KeyMode.Params, DataMode.Incr, 10 * 60),
    /**
     * 通话匹配 - 男性队列
     */
    NetCallMatchMaleQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 通话匹配 - 24小时内已匹配标记
     */
    NetCallMatchMark(KeyMode.Params, DataMode.String, 24 * 3600),
    /**
     * 通话匹配 - 邀请标识
     */
    NetCallMatchInviteId(KeyMode.Params, DataMode.String, 5 * 60),

    /****************** 视频匹配相关 ***********************/
    /**
     * 视频匹配队列
     */
    VideoMatchQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 视频匹配次数
     */
    VideoMatchTimes(KeyMode.Params, DataMode.String, TTLMode.END_OF_DAY),
    /**
     * 视频匹配信息
     */
    VideoMatchInfo(KeyMode.Params, DataMode.String, 2 * 3600),
    /**
     * 视频速配信息
     * 备注：存储1分钟
     */
    VideoMatchInfoPlus(KeyMode.Params, DataMode.Hash, 60),

    /****************** 游戏相关 ***********************/
    /**
     * 邀请用户游戏队列
     */
    InviteUserGameQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    /**
     * 邀请用户临时冻结
     */
    InviteUserGameFrost(KeyMode.Params, DataMode.List, 3600),

    ESNearByUserLastId(KeyMode.Fixed, DataMode.String, TTLMode.NONE),

    RebuildLatePostsPicUserId(KeyMode.Fixed, DataMode.String, TTLMode.NONE),

    /**
     * 乐斗西游勇气值
     */
    JourneyCourage(KeyMode.Params, DataMode.Hash, 180 * 3600),
    /**
     * 乐斗西游中奖消息
     */
    JourneyAwardText(KeyMode.Fixed, DataMode.List, TTLMode.NONE),
    /**
     * 乐斗西游连续击败次数
     */
    JourneyMonsterFail(KeyMode.Fixed, DataMode.Hash, TTLMode.NONE),
    /**
     * 乐斗西游怪物虚弱记录
     */
    JourneyMonsterWeak(KeyMode.Params, DataMode.String, TTLMode.NONE),
    /**
     * 乐斗西游当前在玩玩家
     */
    JourneyMonsterUser(KeyMode.Params, DataMode.Sortedset, 90 * 3600),
    /**
     * 乐斗西游用户待兑换列表
     */
    JourneyWaitExchange(KeyMode.Params, DataMode.Hash, TTLMode.NONE),
    /**
     * 乐斗西游上次成功击败时间
     */
    JourneyLastSuccessTime(KeyMode.Params, DataMode.Hash, TTLMode.NONE),

    /**
     * 进入聊天室的访问日志， 异步批量插入
     */
    FamilyEnterLogQueue(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),

    /******　用户行为相关缓存 **********/
    // 私信用户列表，用于广告风险检测
    ChatUsersForAdRisk(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),
    // C级及以下聊主每天新私信的用户数量
    ChatNewUsersEveryDay(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),
    // 当天私信人数达到上限的用户缓存
    ChatUserCountLimitUsers(KeyMode.Fixed, DataMode.Set, TTLMode.END_OF_DAY),
    // 新注册女性48小时内文字发言次数
    NewFemaleChatMsgCount(KeyMode.Params, DataMode.Incr, 48 * 3600),
    // 新注册女性48小时内命中风险标识次数
    NewFemaleChatRiskModelCount(KeyMode.Params, DataMode.Incr, 48 * 3600),
    // 新注册女性用户48小时内送礼人数
    NewFemaleSendGiftUsers(KeyMode.Params, DataMode.Set, 48 * 3600),
    // 用户资料风险触发次数
    UserInfoRiskTimes(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),
    // 用户资料修改限制缓存
    UserInfoRiskLimit(KeyMode.Params, DataMode.Incr, 24 * 3600),

    // 家族每分钟发言次数缓存，用于计算热度
    FamilyMsgCount(KeyMode.Params, DataMode.Hash, 20 * 60),
    // 家族每分钟消费金币缓存，用于计算热度
    FamilyGiftBeans(KeyMode.Params, DataMode.Hash, 20 * 60),
    // 家族每分钟红包个数缓存，用于计算热度
    FamilyRedPacketCount(KeyMode.Params, DataMode.Hash, 20 * 60),

    // 家族在线用户列表
    FamilyOnlineUsers(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    FamilyCurrVisitIds(KeyMode.Fixed, DataMode.Hash, TTLMode.NONE),
    // 家族在线用户快照缓存, 包换用户在线信息的一个大对象
    FamilyOnlineUsersSnapshot(KeyMode.Params, DataMode.String, 7 * 24 * 3600),
    // 家族在线异动队列
    FamilyOnlineChangeQueue(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),
    // 家族在线时长
    FamilyOnlineTime(KeyMode.Params, DataMode.Hash, 3 * 24 * 3600),

    // 用户推送通知时间间隔控制
    UserNoticeInterval(KeyMode.Fixed, DataMode.String, TTLMode.DYNAMIC),

    // 春节用户活动任务进度缓存
    SpringTaskProgress(KeyMode.Params, DataMode.Hash, 15 * 24 * 3600),
    // 春节活动用户任务领取记录缓存
    SpringTaskReceiveCount(KeyMode.Params, DataMode.Hash, 15 * 24 * 3600),
    // 春节活动用户红包数量
    SpringTaskRedPacketCount(KeyMode.Fixed, DataMode.Hash, 15 * 24 * 3600),
    // 春节活动用户新春卡数量
    SpringTaskCardCount(KeyMode.Fixed, DataMode.Hash, 15 * 24 * 3600),

    // 国庆大转盘抽奖活动人数统计key
    LwaDrawPersonStat(KeyMode.Fixed, DataMode.Incr, 10 * 24 * 3600),
    // 国庆大转盘当日任务已领取
    LwaDrawDayTaskReceive(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),
    // 国庆大转盘活动期间-女性用户日通话时长记录
    LwaFemaleVideoDuration(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),
    // 国庆幸运大转盘抽奖活动测试缓存
    LwaTestTime(KeyMode.Fixed, DataMode.String, TTLMode.END_OF_DAY),

    // 大转盘抽奖中奖banner
    DzpAwardBanner(KeyMode.Fixed, DataMode.List, TTLMode.NONE),

    // 男用户等级缓存
    MaleNumLevel(KeyMode.Params, DataMode.String, TTLMode.DYNAMIC),
    /*************** 通话风控相关缓存 ****************/
    NetCallDayTimes(KeyMode.Params, DataMode.Hash, TTLMode.END_OF_DAY),


    // 数据统计 - 访问令牌
    StatisticsAccessToken(KeyMode.Params, DataMode.String, 12 * 3600),
    // 数据统计 - 付费统计
    StatisticsPaid(KeyMode.Params, DataMode.Hash, 7 * 24 * 3600),

    /*************** 嘉宾相关缓存 ****************/
    // 嘉宾 - 等级0点重置标记
    MasterResetMark(KeyMode.Fixed, DataMode.String, TTLMode.END_OF_DAY),
    // 嘉宾 - 当天的最高等级
    MasterMaxLevel(KeyMode.Params, DataMode.String, TTLMode.END_OF_DAY),
    // 嘉宾 - 1小时内曝光次数
    MasterExposureTimes(KeyMode.Params, DataMode.Incr, 1 * 24 * 3600),
    // 嘉宾 - 新人推荐列表
    MasterNewRecom(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),
    // 嘉宾 - 新人推荐在线列表
    MasterNewRecomOnline(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),
    // 嘉宾 - S级推荐列表
    MasterTopRecom(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),
    // 嘉宾 - S级推荐在线列表
    MasterTopRecomOnline(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),
    // 嘉宾 - S级推荐白名单列表
    MasterTopRecomWhitelist(KeyMode.Fixed, DataMode.Set, TTLMode.NONE),
    // 嘉宾 - 新人自动升级队列
    MasterNewUpQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 嘉宾 - 新人自动降级队列
    MasterNewDownQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 嘉宾 - 新人扶持晋升队列
    MasterNewSupportQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 嘉宾 - 新人扶持期标记
    MasterNewSupportMark(KeyMode.Params, DataMode.String, 24 * 3600),
    // 嘉宾 - 晋升至S级嘉宾标记
    MasterTopUpMoney(KeyMode.Fixed, DataMode.String, TTLMode.NONE),
    // 嘉宾 - 已加入晋升至S级队列标记
    MasterTopUpMark(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),
    // 嘉宾 - 已加入新人晋升队列标记
    MasterNewUpMark(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),
    // 嘉宾 - 待晋升至S级的嘉宾队列(S级有效期只算当天)
    MasterTopUpQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.END_OF_DAY),
    // 嘉宾 - 嘉宾最后一次赚取时间
    MasterEarnLastTime(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 嘉宾 - 嘉宾近3日有赚取的人数
    MasterEarnUserNum(KeyMode.Params, DataMode.Sortedset, 3 * 24 * 3600),
    // 嘉宾 - 嘉宾当日有赚取的用户标记
    MasterEarnUserMark(KeyMode.Fixed, DataMode.String, TTLMode.END_OF_DAY),
    // 嘉宾 - 嘉宾近3日赚取的金额
    MasterEarnMoney(KeyMode.Params, DataMode.Sortedset, 3 * 24 * 3600),
    // 嘉宾 - 嘉宾每日赚取榜单
    MasterTodayEarnMoneyRank(KeyMode.Params, DataMode.Sortedset, 3 * 24 * 3600),
    // 嘉宾 - 嘉宾每日赚取分类
    MasterTodayEarnMoneyCategory(KeyMode.Params, DataMode.Hash, 10 * 24 * 3600),
    // 嘉宾 - 嘉宾每周赚取榜单
    MasterWeekEarnMoneyRank(KeyMode.Params, DataMode.Sortedset, 15 * 24 * 3600),
    // 付费用户最近一次有效社交关系人
    PaidUserLastSocialLink(KeyMode.Params, DataMode.String, 12 * 3600),
    // 女用户今日匹配状态缓存KEY
    FemaleTodayMatchStatusKey(KeyMode.Params, DataMode.String, TTLMode.END_OF_DAY),
    /*************** 邀请人相关 ****************/
    // 邀请人日收益榜 保存2天
    InvitorDayCashRank(KeyMode.Params, DataMode.Sortedset, 2 * 24 * 3600),
    // 邀请人日收益
    InvitorDayCash(KeyMode.Params, DataMode.String, 2 * 24 * 3600),
    // 邀请人今日邀请榜
    InvitorDayFriendRank(KeyMode.Params, DataMode.Sortedset, 2 * 24 * 3600),
    // 邀请人日邀请
    InvitorDayFriend(KeyMode.Params, DataMode.String, 2 * 24 * 3600),

    /*************** 缘分匹配相关缓存 ****************/
    // 缘分匹配 - 女用户队列
    FateMatchFemaleQueue(KeyMode.Params, DataMode.List, TTLMode.NONE),
    // 缘分匹配 - 红包派发队列
    FateMatchDispatchQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 缘分匹配 - 红包派发次数
    FateMatchDispatchTimes(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),
    // 缘分匹配 - 红包派发受限标记
    FateMatchDispatchLimit(KeyMode.Params, DataMode.String, 30 * 60),
    // 缘分匹配 - 24小时互动标记
    FateMatchInteractMark(KeyMode.Params, DataMode.String, 24 * 3600),
    // 缘分匹配 - 派发统计(存储派单数和回复数)
    FateMatchDispatchStat(KeyMode.Params, DataMode.Hash, 24 * 3600),
    // 缘分匹配 - 红包信息
    FateMatchRedPacket(KeyMode.Params, DataMode.String, 180),
    // 缘分匹配 - 红包待领取队列
    FateMatchRedPacketReceiveQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.END_OF_DAY),
    // 每日匹配用户记录缓存
    FateMatchUserDay(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),
    // 每日上传专属搭讪次数记录
    UserUploadEcuTimesSign(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),
    // 缘分搭讪 - 搭讪发送次数
    ChatUpMatchTimes(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),
    // 缘分匹配次数
    FeteMatchTimes(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),

    /*************** 流量匹配相关 ****************/
    // 男用户匹配队列（备注：被分割成了10个）
    MaleMatchQueue(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 男用户今日消息匹配曝光池
    MaleMatchDataForDay(KeyMode.Params, DataMode.Sortedset, TTLMode.END_OF_DAY),
    // 男用户今日消息匹配次数
    MaleMatchTimesForDay(KeyMode.Params, DataMode.Hash, TTLMode.END_OF_DAY),
    // 男用户今日视频匹配曝光池
    MaleMatchDataForBg(KeyMode.Params, DataMode.Sortedset, TTLMode.END_OF_DAY),
    // 女用户数字等级队列（分为 1、2、3、4、5、0）
    FemaleNumLevelQueue(KeyMode.Params, DataMode.Set, TTLMode.NONE),
    // 女用户字符等级队列（分为 S、A、B、C、D、U）
    FemaleCharLevelQueue(KeyMode.Params, DataMode.Set, TTLMode.NONE),
    // 女用户今日匹配数据记录（缓存保存时间：3天）
    FemaleMatchDataForDay(KeyMode.Params, DataMode.Hash, TTLMode.END_OF_DAY),
    // 女用户2分钟匹配数据记录（缓存保存时间：2分钟）
    FemaleMinuMatchData(KeyMode.Params, DataMode.Incr, 120),
    // 女用户冷静池（分为 今日限制、15分钟限制、休息期限制）
    FemaleSoberPond(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    /*************** 宇宙相关 ****************/
    // 宇宙数据保存key
    CosmosCollect(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 用户当日查看数量
    UserWatchCount(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),
    // 宇宙查看数量
    CosmosWatchCount(KeyMode.Params, DataMode.Incr, 5 * 24 * 3600),
    // 用户当日发送数量
    UserSendCount(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),
    // 用户7日查看的数据合集
    UserWatchDataOfSeven(KeyMode.Params, DataMode.Sortedset, 7 * 24 * 3600),

    /************** 活动相关 **************/

    // 存储罐活动用户领取缓存
    UserPbReceiveCache(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),
    // 用户存储罐活动中 收益达到可领取 发送通知缓存
    UserPbArriveRcNotice(KeyMode.Params, DataMode.String, TTLMode.END_OF_DAY),
    // 存钱罐测试缓存
    UserPbActTestCache(KeyMode.Params, DataMode.String, TTLMode.END_OF_DAY),
    //女用户聊天用户集合（用于做私信限制）
    FemaleChatUserSet(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),
    //  活动弹窗仅展示一次set
    PopUpOpenOnceSet(KeyMode.Params, DataMode.Set, TTLMode.DYNAMIC),
    //  活动弹窗一天仅展示一次set
    PopUpOpenOnceADaySet(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),
    // 音视频通话语音违规次数记录
    CallAudioIllegalRecord(KeyMode.Params, DataMode.String, 60 * 60),
    // 视频通话画面违规次数记录
    VideoCallFrameIllegalRecord(KeyMode.Params, DataMode.String, 60 * 60),
    // 视频通话摄像头配置缓存
    VideoCallCameraCfg(KeyMode.Params, DataMode.String, 60),
    // 正在通话保存队列
    CurrentCalling(KeyMode.Params, DataMode.Sortedset, TTLMode.NONE),
    // 女用户主动拨打数据存储队列
    FemaleActCallRecord(KeyMode.Params, DataMode.Set, TTLMode.END_OF_DAY),
    // 女用户主动拨打次数
    FemaleActCallTimes(KeyMode.Params, DataMode.Incr, TTLMode.END_OF_DAY),
    // 视频通话画面违规复查队列
    VideoCallFrameViolationQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    // 今日通话评价次数缓存记录
    TodayCallEvaluateTimes(KeyMode.Params, DataMode.Hash, TTLMode.END_OF_DAY),
    // 人脸检测不合规次数记录
    VideoCallNoFaceTimesRecord(KeyMode.Params, DataMode.Incr, 300),
    // 视频通话画面违规摄像头关闭记录
    VideoViolationCameraOffRecord(KeyMode.Params, DataMode.Incr, 15),
    // 女用户主动拨打频率限制
    FemaleActCallFrequencyLimit(KeyMode.Params, DataMode.Incr, 180),
    /** --------------------- TaoAi 女神服务分 ---------------------- */
    // 女用户天服务分储存 备注：保存15天
    FemaleDayScore(KeyMode.Params, DataMode.Hash, 15 * 24 * 3600),
    // 字符等级待评级用户
    CharLevelWaitConfirmUser(KeyMode.Params, DataMode.Sortedset, 3600),
    // 数字等级待评级用户
    NumLevelWaitConfirmUser(KeyMode.Params, DataMode.Sortedset, 3600),

    /*********************** 游戏 **********************/
    // 1、动物园
    // 1.1、正在进行的动物园游戏轮次 备注：缓存时间2分钟
    GameAnimalRunningId(KeyMode.Params, DataMode.String, 120),
    // 1.2、动物园历史中奖动物记录
    GameAnimalHis(KeyMode.Fixed, DataMode.List, TTLMode.NONE),
    // 1.3、动物园参与用户记录 备注：1、缓存的score我们记录用户下注数额；2、缓存我们存储1分钟
    GameAnimalJoinUsers(KeyMode.Params, DataMode.Sortedset, 60),
    // 1.4、动物园下注池
    GameAnimalBettingPond(KeyMode.Params, DataMode.Incr, 60),
    // 1.5、动物园收益排行榜
    GameAnimalIncomeRank(KeyMode.Params, DataMode.Sortedset, 3 * 24 * 3600),
    // 1.6、动物园每局游戏曝光人数记录
    GameAnimalBgUsers(KeyMode.Params, DataMode.Set, 30),
    // 1.7、动物园消息通知队列
    AnimalMessageNoticeQueue(KeyMode.Fixed, DataMode.Sortedset, TTLMode.NONE),
    ;

    /**
     * 有效期，如果secondMode设置为 TTLMode.NOW_ADD
     */
    private Integer seconds;
    private TTLMode ttlMode;
    private DataMode dataType;
    private KeyMode keyMode;
    private String keyPrefix;


    /**
     * 创建指定过期模式的缓存Key
     *
     * @param keyPrefix 指定key前缀
     * @param keyMode   Key生成模式
     * @param dataType  Redis储数据类型
     * @param mode      过期时间模式
     */
    SocialKeyDefine(String keyPrefix, KeyMode keyMode, DataMode dataType, TTLMode mode) {
        this(keyMode, dataType, mode);
        this.keyPrefix = keyPrefix;
    }

    /**
     * 创建指定过期时间的Key，过期模式SecondMode.NOW_ADD
     *
     * @param keyPrefix 指定key前缀
     * @param keyMode   Key生成模式
     * @param dataType  数据类型
     * @param seconds   过期秒数
     */
    SocialKeyDefine(String keyPrefix, KeyMode keyMode, DataMode dataType, Integer seconds) {
        this(keyMode, dataType, seconds);
        this.keyPrefix = keyPrefix;
    }

    SocialKeyDefine(KeyMode keyMode, DataMode dataType, Integer seconds) {
        this.keyMode = keyMode;
        this.dataType = dataType;
        this.seconds = seconds;
        this.ttlMode = TTLMode.NOW_ADD;
    }

    SocialKeyDefine(KeyMode keyMode, DataMode dataType, TTLMode mode) {
        this.keyMode = keyMode;
        this.dataType = dataType;
        this.ttlMode = mode;

        if (ttlMode == TTLMode.NOW_ADD) {
            throw new IllegalArgumentException("请使用带有seconds参数的构造方法！");
        }

        if (ttlMode == TTLMode.TARGET_DATE) {
            throw new IllegalArgumentException("业务缓存不允许调用带有截止日期的缓存，" +
                    "如果时活动缓存请调用ActivityKeyDefine定义！");
        }
    }

    @Override
    public TTLMode getTTLMode() {
        return this.ttlMode;
    }

    @Override
    public Integer getSecondValue() {
        return seconds;
    }

    @Override
    public KeyMode getKeyMode() {
        return this.keyMode;
    }

    @Override
    public String getKeyPrefix() {
        return this.keyPrefix == null ? this.name() : this.keyPrefix;
    }

    @Override
    public DataMode getDataType() {
        return this.dataType;
    }

    @Override
    public String getTTLDateStr() {
        return null;
    }
}
