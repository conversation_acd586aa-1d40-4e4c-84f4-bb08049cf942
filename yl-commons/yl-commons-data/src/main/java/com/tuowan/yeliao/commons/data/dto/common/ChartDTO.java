package com.tuowan.yeliao.commons.data.dto.common;

import java.util.List;

/**
 * 图表信息
 *
 * <AUTHOR>
 * @date 2022/3/30 17:50
 */
public class ChartDTO {

    /** X轴 */
    private List<String> xAxis;
    /** Y轴 */
    private List<Double> yAxis;

    public static ChartDTO build(List<String> xAxis, List<Double> yAxis) {
        ChartDTO dto = new ChartDTO();
        dto.setXAxis(xAxis);
        dto.setYAxis(yAxis);
        return dto;
    }

    public List<String> getXAxis() {
        return xAxis;
    }

    public void setXAxis(List<String> xAxis) {
        this.xAxis = xAxis;
    }

    public List<Double> getYAxis() {
        return yAxis;
    }

    public void setYAxis(List<Double> yAxis) {
        this.yAxis = yAxis;
    }
}
