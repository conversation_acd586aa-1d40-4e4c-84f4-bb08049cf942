package com.tuowan.yeliao.commons.data.manager.config;

import com.easyooo.framework.common.enums.SortType;
import com.easyooo.framework.common.util.ListUtils;
import com.easyooo.framework.common.util.SortUtils;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.entity.config.*;
import com.tuowan.yeliao.commons.data.enums.config.ProdGoodsType;
import com.tuowan.yeliao.commons.data.enums.config.SocialGiftGroup;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.commons.data.persistence.config.*;
import com.tuowan.yeliao.commons.data.utils.DataFilterUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 社交商品配置数据封装
 *
 * <AUTHOR>
 * @date 2021/11/3 09:33
 */
@Component
public class SocialProdManager {


    @Autowired
    private TProdGiftCountMapper prodGiftCountMapper;
    @Autowired
    private TProdSocialGiftMapper socialGiftMapper;
    @Autowired
    private TProdSocialGoodsMapper socialGoodsMapper;
    @Autowired
    private TChatBubbleMapper chatBubbleMapper;
    @Autowired
    private TProdGoodsTypeMapper prodGoodsTypeMapper;
    @Autowired
    private TProdActualGoodsMapper prodActualGoodsMapper;

    /**
     * 获取真实商品
     */
    public TProdActualGoods getProdActualGoods(Long goodsId){
        return prodActualGoodsMapper.selectByPrimaryKey(new TProdActualGoods(goodsId));
    }

    /**
     * 获取礼物面板的礼物
     *
     * @return
     */
    public List<TProdSocialGift> getGiftPanel(SocialGiftGroup giftGroup) {
        List<TProdSocialGift> gifts = socialGiftMapper.selectAll();
        return DataFilterUtils.filter(gifts, dataItem -> {
            if(giftGroup != dataItem.getGroupCode()){
                return true;
            }
            // 背包礼物不在礼物面板出现
            if (BoolType.True == dataItem.getIsBagOnly()) {
                return true;
            }
            if (Status.Disable == dataItem.getStatus()) {
                // 礼物已禁用，不再显示
                return true;
            }
            if (dataItem.getExpTime() != null) {
                // 礼物已失效，不再显示
                if (System.currentTimeMillis() > dataItem.getExpTime().getTime()) {
                    return true;
                }
            }
            return false;
        });
    }

    /**
     * 根据tplCode获取模版列表
     *
     * @param tplCode
     * @return
     */
    public List<TProdGiftCount> getTplCountList(String tplCode) {
        List<TProdGiftCount> giftCounts = prodGiftCountMapper.selectByTplCode(new TProdGiftCount(tplCode));
        // 数量升序
        return giftCounts.stream().sorted(Comparator.comparing(TProdGiftCount::getCount)).collect(Collectors.toList());
    }

    /**
     * 根据tplId获取模版
     *
     * @param tplId
     * @return
     */
    public TProdGiftCount getTplCount(Integer tplId) {
        return prodGiftCountMapper.selectByPrimaryKey(new TProdGiftCount(tplId));
    }

    /**
     * 获取礼物数量模板
     */
    public Map<String, List<TProdGiftCount>> getGiftCountMap() {
        List<TProdGiftCount> countTplList = prodGiftCountMapper.selectAll();
        SortUtils.sort(countTplList, "count", SortType.Descending);
        return ListUtils.grouping(countTplList, TProdGiftCount::getTplCode);
    }

    /**
     * 获取社交礼物
     *
     * @param giftId
     * @return
     */
    public TProdSocialGift getSocialGift(Integer giftId) {
        return socialGiftMapper.selectByPrimaryKey(new TProdSocialGift(giftId));
    }

    /**
     * 获取社交物品
     *
     * @param goodId
     * @return
     */
    public TProdSocialGoods getSocialGood(Integer goodId) {
        return socialGoodsMapper.selectByPrimaryKey(new TProdSocialGoods(goodId));
    }


    /**
     * 获取所有社交礼物
     *
     * @return
     */
    public List<TProdSocialGift> getSocialAllGift() {
        return socialGiftMapper.selectAll();
    }

    /**
     * 物品ID获取气泡样式
     *
     * @param goodId
     * @return
     */
    public TChatBubble getChatBubble(Integer goodId) {
        return chatBubbleMapper.selectByPrimaryKey(new TChatBubble(goodId));
    }

    /**
     * 获取goodType配置
     *
     * @param goodsType
     * @return
     */
    public TProdGoodsType getGoodsTypeCfg(ProdGoodsType goodsType){
       return prodGoodsTypeMapper.selectByPrimaryKey(new TProdGoodsType(goodsType));
    }


}
