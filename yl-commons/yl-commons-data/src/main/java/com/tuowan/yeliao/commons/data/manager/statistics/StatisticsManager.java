package com.tuowan.yeliao.commons.data.manager.statistics;

import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.ListUtils;
import com.easyooo.framework.common.util.NumberUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.data.dto.common.ChartDTO;
import com.tuowan.yeliao.commons.data.dto.common.MemberScoreDTO;
import com.tuowan.yeliao.commons.data.dto.common.OptionDTO;
import com.tuowan.yeliao.commons.data.dto.statistics.ChannelAdItemDTO;
import com.tuowan.yeliao.commons.data.dto.statistics.ChannelAdStatDTO;
import com.tuowan.yeliao.commons.data.dto.statistics.ChannelRoiDTO;
import com.tuowan.yeliao.commons.data.dto.statistics.PaidStatDTO;
import com.tuowan.yeliao.commons.data.persistence.query.QueryCmsMapper;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 数据统计数据封装
 *
 * <AUTHOR>
 * @date 2022/3/23 10:55
 */
@Component
public class StatisticsManager {

    @Autowired(required = false)
    private SocialRedisTemplate socialRedisTemplate;
    @Autowired
    private QueryCmsMapper queryCmsMapper;

    /**
     * 生成访问令牌
     *
     * @return
     */
    public String getAccessToken() {
        String accessToken = StringUtils.getUUID();
        RedisKey redisKey = buildStatisticsAccessTokenKey(accessToken);
        socialRedisTemplate.set(redisKey, "T");
        return accessToken;
    }

    /**
     * 判断令牌是否已失效
     *
     * @param accessToken
     * @return
     */
    public boolean isInValidAccessToken(String accessToken) {
        RedisKey redisKey = buildStatisticsAccessTokenKey(accessToken);
        return !socialRedisTemplate.exists(redisKey);
    }

    /**
     * 累计渠道付费金额
     *
     * @param nowDate
     * @param channelCode 归因渠道
     * @param payAmount   付费金额(单位分)
     */
    public void addPaidStat(Date nowDate, String channelCode, Long payAmount) {
        RedisKey redisKey = buildStatisticsPaidKey(nowDate);
        channelCode = StringUtils.defaultString(channelCode, GlobalConstant.NONE_CHANNEL);
        socialRedisTemplate.hincrBy(redisKey, channelCode, payAmount);
        socialRedisTemplate.expire(redisKey);
    }

    /**
     * 获取付费统计信息
     *
     * @return
     */
    public PaidStatDTO getPaidStatInfo() {
        Date nowDate = new Date();
        Date yesterdayDate = DateUtils.plusDays(nowDate, -1);
        Map<String, String> todayMap = getPaidStatMap(nowDate);
        Map<String, String> yesterdayMap = getPaidStatMap(yesterdayDate);
        List<MemberScoreDTO> todayList = new ArrayList<>();
        todayMap.forEach((k, v) -> {
            String score = BusiUtils.formatNumberStyle2(NumberUtils.toDouble(v) / 100);
            todayList.add(MemberScoreDTO.build(k, score));
        });
        // 如果昨日存在的渠道，今日不存在，也加入列表中，方便对比
        yesterdayMap.forEach((k, v) -> {
            if (!todayMap.containsKey(k)) {
                todayList.add(MemberScoreDTO.build(k, 0D));
            }
        });
        // 按付费金额倒序
        todayList.sort(Comparator.comparing(MemberScoreDTO::getScore).reversed());
        // 取付费前10渠道
        List<MemberScoreDTO> resultList = ListUtils.subList(todayList, 0, 10);
        List<String> channelCodes = new ArrayList<>();
        List<Double> todayMoneys = new ArrayList<>();
        List<Double> yesterdayMoneys = new ArrayList<>();
        for (MemberScoreDTO dto : resultList) {
            channelCodes.add(dto.getMember());
            todayMoneys.add(dto.getScore());
            String score = BusiUtils.formatNumberStyle2(NumberUtils.toDouble(yesterdayMap.get(dto.getMember())) / 100);
            yesterdayMoneys.add(NumberUtils.toDouble(score));
        }
        return PaidStatDTO.build(channelCodes, todayMoneys, yesterdayMoneys);
    }

    /**
     * 获取渠道计划统计信息
     *
     * @param channelId
     * @param dateType
     * @param dataType
     * @return
     */
    public ChannelAdStatDTO getChannelAdStat(Integer channelId, Integer dateType, Integer dataType) {
        Date statDate = DateUtils.trunc(DateUtils.plusDays(new Date(), dateType));
        Map<String, Object> params = new HashMap<>();
        params.put("statDate", statDate);
        params.put("channelId", channelId);
        params.put("dataType", dataType);
        List<ChannelAdItemDTO> itemList = queryCmsMapper.queryChannelAdItemList(params);
        Double totalCost = 0D;
        Double totalPaid = 0D;
        for (ChannelAdItemDTO item : itemList) {
            totalCost += item.getCostAmount();
            totalPaid += item.getPaidAmount();

        }
        List<OptionDTO> channelList = new ArrayList<>();
        channelList.add(0, OptionDTO.build(null, "全部渠道"));
        channelList.addAll(queryCmsMapper.queryChannelList());
        ChannelAdStatDTO dto = new ChannelAdStatDTO();
        dto.setChannelList(channelList);
        dto.setTotalCost(BusiUtils.formatNumberStyle2(totalCost));
        dto.setTotalPaid(BusiUtils.formatNumberStyle2(totalPaid));
        dto.setItemList(itemList);
        return dto;
    }

    /**
     * 获取渠道计划图表数据
     *
     * @param adId
     * @param dateType
     * @param timeType
     * @return
     */
    public ChartDTO getChannelAdChart(String adId, Integer dataType, Integer dateType, Integer timeType) {
        Date[] timeRange = getChannelAdTimeRange(dateType, timeType);
        Date beginTime = timeRange[0];
        Date endTime = timeRange[1];
        List<String> xAxis = buildChartXAxis(beginTime, timeType);
        List<Double> yAxis = buildChartYAxis(xAxis, adId, beginTime, endTime, dataType, timeType);
        return ChartDTO.build(xAxis, yAxis);
    }

    private Date[] getChannelAdTimeRange(Integer dateType, Integer timeType) {
        DateTime dt = new DateTime().withSecondOfMinute(0);
        Date startTime;
        Date endTime;
        if (dateType == 0) {
            // 今日
            if (timeType == 0) {
                // 分钟
                Integer nowMinute = dt.getMinuteOfHour();
                Integer minute = nowMinute % 5;
                if (minute != 0) {
                    endTime = dt.withMinuteOfHour(nowMinute - minute).toDate();
                } else {
                    endTime = dt.toDate();
                }
                startTime = DateUtils.plusMinutes(endTime, -45);
            } else if (timeType == 1) {
                // 小时
                endTime = dt.withMinuteOfHour(0).toDate();
                startTime = DateUtils.plusHours(endTime, -9);
            } else {
                endTime = dt.toDate();
                startTime = DateUtils.plusDays(endTime, -9);
            }
        } else {
            // 昨日
            endTime = dt.withHourOfDay(0).withMinuteOfHour(0).toDate();
            if (timeType == 0) {
                // 分钟
                startTime = DateUtils.plusMinutes(endTime, -45);
            } else {
                // 小时
                startTime = DateUtils.plusHours(endTime, -9);
            }
        }
        return new Date[]{startTime, endTime};
    }

    private List<String> buildChartXAxis(Date startTime, Integer timeType) {
        DateTime dt = new DateTime(startTime);
        List<String> xAxis = new ArrayList<>();
        int i = 0;
        if (timeType < 2) {
            // 分或小时
            Integer minutes = timeType == 0 ? 5 : 60;
            do {
                xAxis.add(dt.toString("HH:mm"));
                dt = dt.plusMinutes(minutes);
            } while (++i < 10);
        } else {
            // 天
            do {
                xAxis.add(dt.toString("M-d"));
                dt = dt.plusDays(1);
            } while (++i < 10);
        }
        return xAxis;
    }

    private List<Double> buildChartYAxis(List<String> xAxis, String adId, Date beginTime, Date endTime, Integer dataType, Integer timeType) {
        Map<String, Object> params = new HashMap<>();
        params.put("adId", adId);
        params.put("beginTime", beginTime);
        params.put("endTime", endTime);
        Map<String, Double> resultMap = new HashMap<>();
        DatePattern pattern;
        List<ChannelRoiDTO> resultList;
        if (timeType == 0 || timeType == 1) {
            pattern = DatePattern.HM;
            resultList = queryCmsMapper.queryChannelAdRoiList(params);
        } else {
            pattern = DatePattern.MD3;
            resultList = queryCmsMapper.queryChannelAdList(params);
        }
        for (ChannelRoiDTO dto : resultList) {
            String key = DateUtils.toString(dto.getCreateTime(), pattern);
            if (!resultMap.containsKey(key)) {
                if (dataType == 0) {
                    resultMap.put(key, dto.getCostAmount());
                } else if (dataType == 1) {
                    resultMap.put(key, dto.getRoiValue());
                } else {
                    resultMap.put(key, dto.getPaidAmount());
                }
            }
        }
        List<Double> yAxis = new ArrayList<>();
        for (String x : xAxis) {
            yAxis.add(NumberUtils.toDouble(resultMap.get(x)));
        }
        return yAxis;
    }

    private Map<String, String> getPaidStatMap(Date targetDate) {
        RedisKey redisKey = buildStatisticsPaidKey(targetDate);
        Map<String, String> statMap = socialRedisTemplate.hgetAll(redisKey);
        if (statMap == null) {
            return new HashMap<>();
        }
        return statMap;
    }

    private RedisKey buildStatisticsAccessTokenKey(String accessToken) {
        return RedisKey.create(SocialKeyDefine.StatisticsAccessToken, accessToken);
    }

    private RedisKey buildStatisticsPaidKey(Date nowDate) {
        String dateStr = DateUtils.toString(nowDate, DatePattern.YMD2);
        return RedisKey.create(SocialKeyDefine.StatisticsPaid, dateStr);
    }
}
