package com.tuowan.yeliao.commons.data.manager.user;

import com.alibaba.nacos.common.utils.Pair;
import com.easyooo.framework.common.extend.SimpleMap;
import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.ListUtils;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.data.entity.config.TFemaleLevelNum;
import com.tuowan.yeliao.commons.data.entity.user.*;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.enums.user.ChatMasterLevel;
import com.tuowan.yeliao.commons.data.enums.user.FemaleLevel;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.persistence.user.UChatMasterChangeMapper;
import com.tuowan.yeliao.commons.data.persistence.user.UChatMasterMapper;
import com.tuowan.yeliao.commons.data.persistence.user.UChatMasterRatingMapper;
import com.tuowan.yeliao.commons.data.persistence.user.UUserBasicMapper;
import com.tuowan.yeliao.commons.data.support.config.impl.FemaleLevelNumConfig;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 聊主数据封装
 *
 * <AUTHOR>
 * @date 2020/9/21 20:55
 */
@Component
public class ChatMasterManager {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private UChatMasterMapper uChatMasterMapper;
    @Autowired
    private UChatMasterChangeMapper uChatMasterChangeMapper;
    @Autowired
    private UUserBasicMapper uUserBasicMapper;
    @Autowired
    private ActionStatManager actionStatManager;
    @Autowired
    private UChatMasterRatingMapper uChatMasterRatingMapper;
    @Autowired
    private UserInfoManager userInfoManager;

    /**
     * 获取女用户每日主动私聊总数
     * 备注：区分包
     */
    public Integer getActMsgLimit(Long femaleUserId){
        TFemaleLevelNum config = FemaleLevelNumConfig.getConfig(getFemaleLevel(femaleUserId));
        return Objects.isNull(config.getActMsgLimit()) ? Integer.MAX_VALUE : config.getActMsgLimit();
    }

    /**
     * 获取女用户每日系统推送总数
     */
    public Integer getScuDayLimit(Long femaleUserId){
        TFemaleLevelNum config = FemaleLevelNumConfig.getConfig(getFemaleLevel(femaleUserId));
        return Objects.isNull(config.getDayLimit()) ? 0 : config.getDayLimit();
    }

    /**
     * 获取女用户每日主动搭讪总数
     * 备注：区分包
     */
    public Integer getActiveLimit(Long femaleUserId){
        TFemaleLevelNum config = FemaleLevelNumConfig.getConfig(getFemaleLevel(femaleUserId));
        return Objects.isNull(config.getActiveLimit()) ? Integer.MAX_VALUE : config.getActiveLimit();
    }

    /**
     * 获取女用户主动拨打所需亲密度
     */
    public Integer getActCallIntimate(Long femaleUserId){
        TFemaleLevelNum config = FemaleLevelNumConfig.getConfig(getFemaleLevel(femaleUserId));
        return Objects.isNull(config.getActCallIntimate()) ? 0 : config.getActCallIntimate();
    }

    /**
     * 获取女用户每日主动拨打总数
     * 备注：区分包
     */
    public Integer getActCallLimit(Long femaleUserId){
        TFemaleLevelNum config = FemaleLevelNumConfig.getConfig(getFemaleLevel(femaleUserId));
        return Objects.isNull(config.getActCallLimit()) ? Integer.MAX_VALUE : config.getActCallLimit();
    }

    /**
     * 获取待刷新嘉宾字符等级数据
     */
    public List<UChatMaster> getWaitRefreshCharLevelMaster(ChatMasterLevel level, int pageNo, int pageSize){
        return uChatMasterMapper.getWaitRefreshCharLevelMaster(level.getId(), pageNo, pageSize);
    }

    /**
     * 获取待刷新嘉宾数字等级数据
     */
    public List<UChatMaster> getWaitRefreshNumLevelMaster(FemaleLevel level, int pageNo, int pageSize){
        return uChatMasterMapper.getWaitRefreshNumLevelMaster(level.getId(), pageNo, pageSize);
    }

    /**
     * 刷新男嘉宾等级信息
     * 1、新人注册24小时为N级。
     * 2、超过24小时分配为D级。
     * 3、未完成双认证分配为C级。
     * 4、完成双认证的分配为B级。
     * 5、财富等级＞=10级，分配为A级。（达到即可不考虑衰减）
     * 6、财富等级＞=20级，分配为S及。（达到即可不考虑衰减）
     */
    public void refreshMaleMasterLevel(Long userId){
        try {
            UUserBasic userBasic = userInfoManager.getUserBasic(userId);
            if(SexType.Male != userBasic.getSex()){
                return;
            }
            UChatMaster uChatMaster = uChatMasterMapper.selectByPrimaryKey(new UChatMaster(userId));
            if(Objects.isNull(uChatMaster)){
                return;
            }
            ChatMasterLevel nowLevel = uChatMaster.getChatMasterLevel();
            // 计算男嘉宾当前等级信息
            UUserLevel userLevel = userInfoManager.getUserLevel(userId);
            if(userLevel.getUserLevel() >= 10){
                nowLevel = ChatMasterLevel.A;
            }else if(BoolType.True == userBasic.getRealName() && BoolType.True == userBasic.getRealPerson()){
                nowLevel = ChatMasterLevel.B;
            }else if(BoolType.True == userBasic.getRealName() || BoolType.True == userBasic.getRealPerson()){
                nowLevel = ChatMasterLevel.C;
            }else{
                nowLevel = ChatMasterLevel.D;
            }
            if(uChatMaster.getChatMasterLevel() != nowLevel){
                // 更新
                UChatMaster update = new UChatMaster(userId);
                update.setChatMasterLevel(nowLevel);
                uChatMasterMapper.updateByPrimaryKeySelective(update);
            }
        }catch (Exception e){
            LOG.error("ChatMasterManager-refreshMaleMasterLevel-error reason:", e);
        }
    }

    /**
     * 批量插入评分定级记录
     *
     * @param ratingList
     */
    public void batchInsertRating(List<UChatMasterRating> ratingList) {
        if (ListUtils.isEmpty(ratingList)) {
            return;
        }
        uChatMasterRatingMapper.batchInsert(ratingList);
    }

    /**
     * 获取嘉宾等级
     *
     * @param userId
     * @return
     */
    public ChatMasterLevel getChatMasterLevel(Long userId) {
        return getChatMaster(userId).getChatMasterLevel();
    }

    /**
     * 获取数字等级
     */
    public FemaleLevel getFemaleLevel(Long userId){
        return getChatMaster(userId).getFemaleLevel();
    }

    /**
     * 获取聊主信息
     *
     * @param userId
     * @return
     */
    public UChatMaster getChatMaster(Long userId) {
        return uChatMasterMapper.selectByPrimaryKey(new UChatMaster(userId));
    }

    /**
     * 删除聊主信息
     */
    public void deleteChatMaster(Long userId){
        uChatMasterMapper.deleteByPrimaryKey(new UChatMaster(userId));
    }

    /**
     * 保存用户聊主信息
     */
    public void saveChatMaster(UChatMaster chatMaster) {
        if (Objects.isNull(chatMaster)) {
            return;
        }
        uChatMasterMapper.insert(chatMaster);
    }

    /**
     * 更新嘉宾信息
     */
    public void updateChatMaster(UChatMaster master){
        uChatMasterMapper.updateByPrimaryKeySelective(master);
    }

    /**
     * 更新嘉宾字符等级
     *
     * @param exist
     * @param chatMasterLevel
     */
    public ChatMasterLevel updateMasterLevel(UChatMaster exist, ChatMasterLevel chatMasterLevel, String remark, String busiCode, Long create) {
        Long userId = exist.getUserId();
        UChatMaster chatMaster = new UChatMaster(userId);
        if (chatMasterLevel == exist.getChatMasterLevel()) {
            return exist.getChatMasterLevel();
        }
        // 异动记录
        saveMasterChange(userId, "chatMasterCharLevel", exist.getChatMasterLevel().getId(), chatMasterLevel.getId(), remark, busiCode, create);

        chatMaster.setChatMasterLevel(chatMasterLevel);
        uChatMasterMapper.updateByPrimaryKeySelective(chatMaster);

        // 同步到basic表
        UUserBasic basic = new UUserBasic(userId);
        basic.setChatMasterLevel(chatMasterLevel);
        uUserBasicMapper.updateByPrimaryKeySelective(basic);
        return exist.getChatMasterLevel();
    }

    /**
     * 更新嘉宾数字等级
     *
     * @param exist
     * @param femaleLevel
     *
     * @return 返回旧的等级数据
     */
    public FemaleLevel updateMasterNumLevel(UChatMaster exist, FemaleLevel femaleLevel, String remark, String busiCode, Long create) {
        Long userId = exist.getUserId();
        UChatMaster chatMaster = new UChatMaster(userId);
        if (femaleLevel == exist.getFemaleLevel()) {
            return exist.getFemaleLevel();
        }
        // 异动记录
        saveMasterChange(userId, "chatMasterNumLevel", exist.getFemaleLevel().getId(), femaleLevel.getId(), remark, busiCode, create);

        chatMaster.setFemaleLevel(femaleLevel);
        uChatMasterMapper.updateByPrimaryKeySelective(chatMaster);

        return exist.getFemaleLevel();
    }

    /**
     * 聊主异动记录
     *
     * @param userId
     * @param propertyName
     * @param oldValue
     * @param newValue
     * @param remark
     * @param busiCode
     * @param create
     */
    public void saveMasterChange(Long userId, String propertyName, Object oldValue, Object newValue, String remark, String busiCode, Long create) {
        UChatMasterChange change = new UChatMasterChange();
        change.setUserId(userId);
        change.setPropertyName(propertyName);
        if (oldValue != null) {
            change.setOldValue(oldValue.toString());
        }
        change.setBusiCode(busiCode);
        change.setNewValue(newValue.toString());
        change.setRemark(remark);
        change.setCreator(create);
        change.setCreateTime(new Date());
        uChatMasterChangeMapper.insert(change);
    }

}
