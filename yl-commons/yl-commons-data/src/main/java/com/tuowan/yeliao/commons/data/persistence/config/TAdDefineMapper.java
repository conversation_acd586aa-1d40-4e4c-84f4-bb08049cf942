/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.cache.annotations.GroupStrategy;
import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TAdDefine;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "T_AD_DEFINE", schema = "YL_CONFIG")
public interface TAdDefineMapper {
    int deleteByPrimaryKey(TAdDefine record);

    int insert(TAdDefine record);

    TAdDefine selectByPrimaryKey(TAdDefine record);

    int updateByPrimaryKeySelective(TAdDefine record);

    int updateByPrimaryKey(TAdDefine record);

    @GroupStrategy
    List<TAdDefine> selectByType(TAdDefine type);
}