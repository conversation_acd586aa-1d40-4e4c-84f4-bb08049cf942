/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TAnimalMenu;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "t_animal_menu", schema = "yl_config")
public interface TAnimalMenuMapper {
    int deleteByPrimaryKey(TAnimalMenu record);

    int insert(TAnimalMenu record);

    TAnimalMenu selectByPrimaryKey(TAnimalMenu record);

    int updateByPrimaryKeySelective(TAnimalMenu record);

    int updateByPrimaryKey(TAnimalMenu record);

    List<TAnimalMenu> selectAll();
}