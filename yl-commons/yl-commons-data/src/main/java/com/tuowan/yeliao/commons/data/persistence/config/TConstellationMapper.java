/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TConstellation;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "T_CONSTELLATION", schema = "YL_CONFIG")
public interface TConstellationMapper {
    int deleteByPrimaryKey(TConstellation record);

    int insert(TConstellation record);

    TConstellation selectByPrimaryKey(TConstellation record);

    int updateByPrimaryKeySelective(TConstellation record);

    int updateByPrimaryKey(TConstellation record);

    List<TConstellation> selectAll();
}