/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TEmoticon;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "T_EMOTICON", schema = "YL_CONFIG")
public interface TEmoticonMapper {
    int deleteByPrimaryKey(TEmoticon record);

    int insert(TEmoticon record);

    TEmoticon selectByPrimaryKey(TEmoticon record);

    int updateByPrimaryKeySelective(TEmoticon record);

    int updateByPrimaryKey(TEmoticon record);
}