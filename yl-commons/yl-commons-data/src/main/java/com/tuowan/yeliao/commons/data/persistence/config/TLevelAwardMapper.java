/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TLevelAward;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "T_LEVEL_AWARD", schema = "YL_CONFIG")
public interface TLevelAwardMapper {
    int deleteByPrimaryKey(TLevelAward record);

    int insert(TLevelAward record);

    TLevelAward selectByPrimaryKey(TLevelAward record);

    int updateByPrimaryKeySelective(TLevelAward record);

    int updateByPrimaryKey(TLevelAward record);
}