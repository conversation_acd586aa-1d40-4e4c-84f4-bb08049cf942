/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.cache.annotations.GroupStrategy;
import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TMessage;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "T_MESSAGE", schema = "YL_CONFIG")
public interface TMessageMapper {
    int deleteByPrimaryKey(TMessage record);

    int insert(TMessage record);

    TMessage selectByPrimaryKey(TMessage record);

    int updateByPrimaryKeySelective(TMessage record);

    int updateByPrimaryKey(TMessage record);

    /**
     * 根据业务代码查询消息列表
     *
     * @param message
     * @return
     */
    @GroupStrategy
    List<TMessage> selectByBusiCode(TMessage message);
}