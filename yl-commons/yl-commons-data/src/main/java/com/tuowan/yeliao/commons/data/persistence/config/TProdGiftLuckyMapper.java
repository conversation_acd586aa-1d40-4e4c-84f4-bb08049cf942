/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.cache.annotations.GroupStrategy;
import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TProdGiftLucky;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "T_PROD_GIFT_LUCKY", schema = "YL_CONFIG")
public interface TProdGiftLuckyMapper {
    int deleteByPrimaryKey(TProdGiftLucky record);

    int insert(TProdGiftLucky record);

    TProdGiftLucky selectByPrimaryKey(TProdGiftLucky record);

    int updateByPrimaryKeySelective(TProdGiftLucky record);

    int updateByPrimaryKey(TProdGiftLucky record);

    @GroupStrategy
    List<TProdGiftLucky> selectByGiftId(TProdGiftLucky tp);
}