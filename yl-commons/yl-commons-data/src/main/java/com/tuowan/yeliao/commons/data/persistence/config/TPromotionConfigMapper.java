/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TPromotionConfig;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "T_PROMOTION_CONFIG", schema = "YL_CONFIG")
public interface TPromotionConfigMapper {
    int deleteByPrimaryKey(TPromotionConfig record);

    int insert(TPromotionConfig record);

    TPromotionConfig selectByPrimaryKey(TPromotionConfig record);

    int updateByPrimaryKeySelective(TPromotionConfig record);

    int updateByPrimaryKey(TPromotionConfig record);

    List<TPromotionConfig> selectAll();
}