/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TSocialTag;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "T_SOCIAL_TAG", schema = "YL_CONFIG")
public interface TSocialTagMapper {
    int deleteByPrimaryKey(TSocialTag record);

    int insert(TSocialTag record);

    TSocialTag selectByPrimaryKey(TSocialTag record);

    int updateByPrimaryKeySelective(TSocialTag record);

    int updateByPrimaryKey(TSocialTag record);

    List<TSocialTag> selectAll();
}