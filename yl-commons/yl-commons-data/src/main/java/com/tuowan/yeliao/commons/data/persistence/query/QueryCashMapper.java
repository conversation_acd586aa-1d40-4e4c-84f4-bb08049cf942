package com.tuowan.yeliao.commons.data.persistence.query;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.dto.query.UCashDTO;
import com.tuowan.yeliao.commons.data.dto.query.WdAmountDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * 零钱相关业务查询定义
 *
 * <AUTHOR>
 * @date 2020/7/16 22:06
 */
@Repository
@Table(schema = "YL_QUERY")
public interface QueryCashMapper {

    /**
     * 根据用户ID获取零钱信息
     *
     * @param userId
     * @return
     */
    UCashDTO selectByUserId(Long userId);

    /**
     * 统计每日提现金额数据
     */
    WdAmountDTO statWdDayInfo(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
