/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.user;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.user.UMessageSetting;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "U_MESSAGE_SETTING", schema = "YL_BUSI")
public interface UMessageSettingMapper {
    int deleteByPrimaryKey(UMessageSetting record);

    int insert(UMessageSetting record);

    UMessageSetting selectByPrimaryKey(UMessageSetting record);

    int updateByPrimaryKeySelective(UMessageSetting record);

    int updateByPrimaryKey(UMessageSetting record);
}