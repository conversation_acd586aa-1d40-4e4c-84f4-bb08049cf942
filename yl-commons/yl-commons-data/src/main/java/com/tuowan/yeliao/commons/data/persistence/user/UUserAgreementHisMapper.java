/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.user;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.user.UUserAgreementHis;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "U_USER_AGREEMENT_HIS", schema = "YL_BUSI")
public interface UUserAgreementHisMapper {
    int deleteByPrimaryKey(UUserAgreementHis record);

    int insert(UUserAgreementHis record);

    UUserAgreementHis selectByPrimaryKey(UUserAgreementHis record);

    int updateByPrimaryKeySelective(UUserAgreementHis record);

    int updateByPrimaryKey(UUserAgreementHis record);
}