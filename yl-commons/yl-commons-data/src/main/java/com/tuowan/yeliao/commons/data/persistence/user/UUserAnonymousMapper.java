/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.user;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.user.UUserAnonymous;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "U_USER_ANONYMOUS", schema = "YL_BUSI")
public interface UUserAnonymousMapper {
    int deleteByPrimaryKey(UUserAnonymous record);

    int insert(UUserAnonymous record);

    UUserAnonymous selectByPrimaryKey(UUserAnonymous record);

    int updateByPrimaryKeySelective(UUserAnonymous record);

    int updateByPrimaryKey(UUserAnonymous record);
}