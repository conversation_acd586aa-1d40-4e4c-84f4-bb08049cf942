/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.user;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.user.UUserDeviceMark;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "U_USER_DEVICE_MARK", schema = "YL_BUSI")
public interface UUserDeviceMarkMapper {
    int deleteByPrimaryKey(UUserDeviceMark record);

    int insert(UUserDeviceMark record);

    UUserDeviceMark selectByPrimaryKey(UUserDeviceMark record);

    int updateByPrimaryKeySelective(UUserDeviceMark record);

    int updateByPrimaryKey(UUserDeviceMark record);
}