/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.user;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.user.UUserPortray;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "U_USER_PORTRAY", schema = "YL_BUSI")
public interface UUserPortrayMapper {
    int deleteByPrimaryKey(UUserPortray record);

    int insert(UUserPortray record);

    UUserPortray selectByPrimaryKey(UUserPortray record);

    int updateByPrimaryKeySelective(UUserPortray record);

    int updateByPrimaryKey(UUserPortray record);
}