/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.user;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.user.UUserTaskNew;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "U_USER_TASK_NEW", schema = "YL_BUSI")
public interface UUserTaskNewMapper {
    int deleteByPrimaryKey(UUserTaskNew record);

    int insert(UUserTaskNew record);

    UUserTaskNew selectByPrimaryKey(UUserTaskNew record);

    int updateByPrimaryKeySelective(UUserTaskNew record);

    int updateByPrimaryKey(UUserTaskNew record);
}