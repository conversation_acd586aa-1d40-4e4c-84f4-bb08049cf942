<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TAwardDetailMapper">
    <sql id="Base_Column_List">
        detail_id, parent_code, award_type, award_type_value, count, exp_days, end_time,
        ext_json_cfg
    </sql>
    <select id="selectByPrimaryKey" parameterType="TAwardDetail" resultType="TAwardDetail">
        select
        <include refid="Base_Column_List"/>
        from t_award_detail
        where detail_id = #{detailId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="TAwardDetail">
        delete from t_award_detail
        where detail_id = #{detailId}
    </delete>
    <insert id="insert" parameterType="TAwardDetail">
        insert into t_award_detail (detail_id, parent_code, award_type, award_type_value, count, exp_days,
        end_time, ext_json_cfg)
        values (#{detailId}, #{parentCode}, #{awardType}, #{awardTypeValue}, #{count}, #{expDays},
        #{endTime}, #{extJsonCfg})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="TAwardDetail">
        update t_award_detail
        <set>
            <if test="parentCode != null">
                parent_code = #{parentCode},
            </if>
            <if test="awardType != null">
                award_type = #{awardType},
            </if>
            <if test="awardTypeValue != null">
                award_type_value = #{awardTypeValue},
            </if>
            <if test="count != null">
                count = #{count},
            </if>
            <if test="expDays != null">
                exp_days = #{expDays},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="extJsonCfg != null">
                ext_json_cfg = #{extJsonCfg},
            </if>
        </set>
        where detail_id = #{detailId}
    </update>
    <update id="updateByPrimaryKey" parameterType="TAwardDetail">
        update t_award_detail
        set parent_code = #{parentCode},
        award_type = #{awardType},
        award_type_value = #{awardTypeValue},
        count = #{count},
        exp_days = #{expDays},
        end_time = #{endTime},
        ext_json_cfg = #{extJsonCfg}
        where detail_id = #{detailId}
    </update>

    <!-- 查询奖励明细 -->
    <select id="selectByCode" parameterType="TAwardDetail" resultType="TAwardDetail">
        select
        <include refid="Base_Column_List"/>
        from t_award_detail
        where parent_code = #{parentCode}
    </select>

    <select id="selectLikeCode" parameterType="map" resultType="TAwardDetail">
        select
        <include refid="Base_Column_List"/>
        from t_award_detail
        where parent_code like concat(#{parentCode},'%')
    </select>

    <select id="selectByParentCodes" resultType="TAwardDetail">
        select
        <include refid="Base_Column_List"/>
        from t_award_detail
        where parent_code in
        <foreach close=")" collection="parentCodes" item="parentCode" open="(" separator=",">
            #{parentCode}
        </foreach>
    </select>
</mapper>