<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TCityCultureMapper">
  <sql id="Base_Column_List">
    log_id, city_id, culture_type, name, cover_pic, praise_num, status, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="TCityCulture" resultType="TCityCulture">
    select 
    <include refid="Base_Column_List" />
    from t_city_culture
    where log_id = #{logId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="TCityCulture">
    delete from t_city_culture
    where log_id = #{logId}
  </delete>
  <insert id="insert" parameterType="TCityCulture">
    insert into t_city_culture (log_id, city_id, culture_type, name, cover_pic, praise_num,
      status, create_time)
    values (#{logId}, #{cityId}, #{cultureType}, #{name}, #{coverPic}, #{praiseNum},
      #{status}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="TCityCulture">
    update t_city_culture
    <set>
      <if test="cityId != null">
        city_id = #{cityId},
      </if>
      <if test="cultureType != null">
        culture_type = #{cultureType},
      </if>
      <if test="name != null">
        name = #{name},
      </if>
      <if test="coverPic != null">
        cover_pic = #{coverPic},
      </if>
      <if test="praiseNum != null">
        praise_num = #{praiseNum},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where log_id = #{logId}
  </update>
  <update id="updateByPrimaryKey" parameterType="TCityCulture">
    update t_city_culture
    set city_id = #{cityId},
      culture_type = #{cultureType},
      name = #{name},
      cover_pic = #{coverPic},
      praise_num = #{praiseNum},
      status = #{status},
      create_time = #{createTime}
    where log_id = #{logId}
  </update>

  <select id="selectByCityId" parameterType="TCityCulture" resultType="TCityCulture">
    select
    <include refid="Base_Column_List" />
    from t_city_culture
    where city_id = #{cityId}
  </select>

  <select id="selectAll"  resultType="TCityCulture">
    select
    <include refid="Base_Column_List" />
    from t_city_culture
  </select>
</mapper>