<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TFemaleLevelNumMapper">
  <sql id="Base_Column_List">
    id, `level`, min_income1, min_income2, c_time_l, c_time_g, is_rp, msg_rate, rest_it, day_limit,
    minu_limit, active_limit, act_call_limit, act_call_intimate, act_msg_limit, no_reply_limit, fcu_hour_limit,
    fcu_day_limit, rp_rate_seconds, rp_rate_people, `desc`, `order`, is_def, operator, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="TFemaleLevelNum" resultType="TFemaleLevelNum">
    select
    <include refid="Base_Column_List" />
    from t_female_level_num
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="TFemaleLevelNum">
    delete from t_female_level_num
    where id = #{id}
  </delete>
  <insert id="insert" parameterType="TFemaleLevelNum">
    insert into t_female_level_num (id, level, min_income1, min_income2, c_time_l, c_time_g, is_rp, msg_rate,
    rest_it, day_limit, minu_limit, active_limit, act_call_limit, act_call_intimate, act_msg_limit, no_reply_limit, fcu_hour_limit, fcu_day_limit,
    rp_rate_seconds, rp_rate_people, desc, order, is_def, operator, update_time, create_time)
    values (#{id}, #{level}, #{minIncome1}, #{minIncome2}, #{cTimeL}, #{cTimeG}, #{isRp}, #{msgRate},
    #{restIt}, #{dayLimit}, #{minuLimit}, #{activeLimit}, #{actCallLimit}, #{actCallIntimate}, #{actMsgLimit}, #{noReplyLimit}, #{fcuHourLimit}, #{fcuDayLimit},
    #{rpRateSeconds}, #{rpRatePeople}, #{desc}, #{order}, #{isDef}, #{operator}, #{updateTime}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="TFemaleLevelNum">
    update t_female_level_num
    <set>
      <if test="level != null">
        level = #{level},
      </if>
      <if test="minIncome1 != null">
        min_income1 = #{minIncome1},
      </if>
      <if test="minIncome2 != null">
        min_income2 = #{minIncome2},
      </if>
      <if test="cTimeL != null">
        c_time_l = #{cTimeL},
      </if>
      <if test="cTimeG != null">
        c_time_g = #{cTimeG},
      </if>
      <if test="isRp != null">
        is_rp = #{isRp},
      </if>
      <if test="msgRate != null">
        msg_rate = #{msgRate},
      </if>
      <if test="restIt != null">
        rest_it = #{restIt},
      </if>
      <if test="dayLimit != null">
        day_limit = #{dayLimit},
      </if>
      <if test="minuLimit != null">
        minu_limit = #{minuLimit},
      </if>
      <if test="activeLimit != null">
        active_limit = #{activeLimit},
      </if>
      <if test="actCallLimit != null">
        act_call_limit = #{actCallLimit},
      </if>
      <if test="actCallIntimate != null">
        act_call_intimate = #{actCallIntimate},
      </if>
      <if test="actMsgLimit != null">
        act_msg_limit = #{actMsgLimit},
      </if>
      <if test="noReplyLimit != null">
        no_reply_limit = #{noReplyLimit},
      </if>
      <if test="fcuHourLimit != null">
        fcu_hour_limit = #{fcuHourLimit},
      </if>
      <if test="fcuDayLimit != null">
        fcu_day_limit = #{fcuDayLimit},
      </if>
      <if test="rpRateSeconds != null">
        rp_rate_seconds = #{rpRateSeconds},
      </if>
      <if test="rpRatePeople != null">
        rp_rate_people = #{rpRatePeople},
      </if>
      <if test="desc != null">
        desc = #{desc},
      </if>
      <if test="order != null">
        order = #{order},
      </if>
      <if test="isDef != null">
        is_def = #{isDef},
      </if>
      <if test="operator != null">
        operator = #{operator},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="TFemaleLevelNum">
    update t_female_level_num
    set level = #{level},
    min_income1 = #{minIncome1},
    min_income2 = #{minIncome2},
    c_time_l = #{cTimeL},
    c_time_g = #{cTimeG},
    is_rp = #{isRp},
    msg_rate = #{msgRate},
    rest_it = #{restIt},
    day_limit = #{dayLimit},
    minu_limit = #{minuLimit},
    active_limit = #{activeLimit},
    act_call_limit = #{actCallLimit},
    act_call_intimate = #{actCallIntimate},
    act_msg_limit = #{actMsgLimit},
    no_reply_limit = #{noReplyLimit},
    fcu_hour_limit = #{fcuHourLimit},
    fcu_day_limit = #{fcuDayLimit},
    rp_rate_seconds = #{rpRateSeconds},
    rp_rate_people = #{rpRatePeople},
    desc = #{desc},
    order = #{order},
    is_def = #{isDef},
    operator = #{operator},
    update_time = #{updateTime},
    create_time = #{createTime}
    where id = #{id}
  </update>
  <select id="selectAll" resultType="TFemaleLevelNum">
    select
    <include refid="Base_Column_List" />
    from t_female_level_num
  </select>
</mapper>