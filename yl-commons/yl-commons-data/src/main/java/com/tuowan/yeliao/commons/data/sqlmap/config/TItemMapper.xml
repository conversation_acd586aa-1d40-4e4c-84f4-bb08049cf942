<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TItemMapper">
    <sql id="Base_Column_List">
        item_key, group_code, group_order_num, is_edit, remark, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="TItem" resultType="TItem">
        select
        <include refid="Base_Column_List"/>
        from t_item
        where item_key = #{itemKey}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="TItem">
        delete from t_item
        where item_key = #{itemKey}
    </delete>
    <insert id="insert" parameterType="TItem">
        insert into t_item (item_key, group_code, group_order_num, is_edit, remark, create_time
        )
        values (#{itemKey}, #{groupCode}, #{groupOrderNum}, #{isEdit}, #{remark}, #{createTime}
        )
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="TItem">
        update t_item
        <set>
            <if test="groupCode != null">
                group_code = #{groupCode},
            </if>
            <if test="groupOrderNum != null">
                group_order_num = #{groupOrderNum},
            </if>
            <if test="isEdit != null">
                is_edit = #{isEdit},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where item_key = #{itemKey}
    </update>
    <update id="updateByPrimaryKey" parameterType="TItem">
        update t_item
        set group_code = #{groupCode},
        group_order_num = #{groupOrderNum},
        is_edit = #{isEdit},
        remark = #{remark},
        create_time = #{createTime}
        where item_key = #{itemKey}
    </update>

    <select id="selectByGroupCode" parameterType="TItem" resultType="TItem">
        select
        <include refid="Base_Column_List"/>
        from t_item
        where group_code = #{groupCode}
        order by group_order_num asc
    </select>
</mapper>