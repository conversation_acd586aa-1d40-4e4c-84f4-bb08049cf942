<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TMessageMapper">
  <sql id="Base_Column_List">
    msg_id, client_type, busi_code, match_rule, msg_type, display, param_init_rule, param_return_rule, 
    target_rule, room_types, event_code, group_code, text_tpl, text_style, text_touch, 
    animat_find_type, animat_find_value, exp_time, eff_time, status, create_time,
    remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="TMessage" resultType="TMessage">
    select 
    <include refid="Base_Column_List" />
    from t_message
    where msg_id = #{msgId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="TMessage">
    delete from t_message
    where msg_id = #{msgId}
  </delete>
  <insert id="insert" parameterType="TMessage">
    insert into t_message (msg_id, client_type, busi_code, match_rule, msg_type, display, 
      param_init_rule, param_return_rule, target_rule, room_types, event_code, 
      group_code, text_tpl, text_style, text_touch, animat_find_type, animat_find_value, 
      exp_time, eff_time, status, create_time, remark)
    values (#{msgId}, #{clientType}, #{busiCode}, #{matchRule}, #{msgType}, #{display}, 
      #{paramInitRule}, #{paramReturnRule}, #{targetRule}, #{roomTypes}, #{eventCode}, 
      #{groupCode}, #{textTpl}, #{textStyle}, #{textTouch}, #{animatFindType}, #{animatFindValue}, 
      #{expTime}, #{effTime}, #{status}, #{createTime}, #{remark})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="TMessage">
    update t_message
    <set>
      <if test="clientType != null">
        client_type = #{clientType},
      </if>
      <if test="busiCode != null">
        busi_code = #{busiCode},
      </if>
      <if test="matchRule != null">
        match_rule = #{matchRule},
      </if>
      <if test="msgType != null">
        msg_type = #{msgType},
      </if>
      <if test="display != null">
        display = #{display},
      </if>
      <if test="paramInitRule != null">
        param_init_rule = #{paramInitRule},
      </if>
      <if test="paramReturnRule != null">
        param_return_rule = #{paramReturnRule},
      </if>
      <if test="targetRule != null">
        target_rule = #{targetRule},
      </if>
      <if test="roomTypes != null">
        room_types = #{roomTypes},
      </if>
      <if test="eventCode != null">
        event_code = #{eventCode},
      </if>
      <if test="groupCode != null">
        group_code = #{groupCode},
      </if>
      <if test="textTpl != null">
        text_tpl = #{textTpl},
      </if>
      <if test="textStyle != null">
        text_style = #{textStyle},
      </if>
      <if test="textTouch != null">
        text_touch = #{textTouch},
      </if>
      <if test="animatFindType != null">
        animat_find_type = #{animatFindType},
      </if>
      <if test="animatFindValue != null">
        animat_find_value = #{animatFindValue},
      </if>
      <if test="expTime != null">
        exp_time = #{expTime},
      </if>
      <if test="effTime != null">
        eff_time = #{effTime},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="remark != null">
        remark = #{remark},
      </if>
    </set>
    where msg_id = #{msgId}
  </update>
  <update id="updateByPrimaryKey" parameterType="TMessage">
    update t_message
    set client_type = #{clientType},
      busi_code = #{busiCode},
      match_rule = #{matchRule},
      msg_type = #{msgType},
      display = #{display},
      param_init_rule = #{paramInitRule},
      param_return_rule = #{paramReturnRule},
      target_rule = #{targetRule},
      room_types = #{roomTypes},
      event_code = #{eventCode},
      group_code = #{groupCode},
      text_tpl = #{textTpl},
      text_style = #{textStyle},
      text_touch = #{textTouch},
      animat_find_type = #{animatFindType},
      animat_find_value = #{animatFindValue},
      exp_time = #{expTime},
      eff_time = #{effTime},
      status = #{status},
      create_time = #{createTime},
      remark = #{remark}
    where msg_id = #{msgId}
  </update>

  <select id="selectByBusiCode" parameterType="TMessage" resultType="TMessage">
    select
    <include refid="Base_Column_List" />
    from t_message t
    where t.busi_code = #{busiCode}
    and t.`status` = 'E'
  </select>
</mapper>