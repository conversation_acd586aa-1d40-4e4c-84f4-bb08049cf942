<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TProdActualGoodsMapper">
  <sql id="Base_Column_List">
    goods_id, goods_name, goods_pic, goods_unit, goods_money, beans, status, goods_desc, 
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="TProdActualGoods" resultType="TProdActualGoods">
    select 
    <include refid="Base_Column_List" />
    from t_prod_actual_goods
    where goods_id = #{goodsId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="TProdActualGoods">
    delete from t_prod_actual_goods
    where goods_id = #{goodsId}
  </delete>
  <insert id="insert" parameterType="TProdActualGoods">
    insert into t_prod_actual_goods (goods_id, goods_name, goods_pic, goods_unit, goods_money, beans, 
      status, goods_desc, create_time)
    values (#{goodsId}, #{goodsName}, #{goodsPic}, #{goodsUnit}, #{goodsMoney}, #{beans}, 
      #{status}, #{goodsDesc}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="TProdActualGoods">
    update t_prod_actual_goods
    <set>
      <if test="goodsName != null">
        goods_name = #{goodsName},
      </if>
      <if test="goodsPic != null">
        goods_pic = #{goodsPic},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit},
      </if>
      <if test="goodsMoney != null">
        goods_money = #{goodsMoney},
      </if>
      <if test="beans != null">
        beans = #{beans},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="goodsDesc != null">
        goods_desc = #{goodsDesc},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where goods_id = #{goodsId}
  </update>
  <update id="updateByPrimaryKey" parameterType="TProdActualGoods">
    update t_prod_actual_goods
    set goods_name = #{goodsName},
      goods_pic = #{goodsPic},
      goods_unit = #{goodsUnit},
      goods_money = #{goodsMoney},
      beans = #{beans},
      status = #{status},
      goods_desc = #{goodsDesc},
      create_time = #{createTime}
    where goods_id = #{goodsId}
  </update>
</mapper>