<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TProfessionMapper">
  <sql id="Base_Column_List">
    profession_id, profession_name, profession_type, status, order_num, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="TProfession" resultType="TProfession">
    select 
    <include refid="Base_Column_List" />
    from t_profession
    where profession_id = #{professionId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="TProfession">
    delete from t_profession
    where profession_id = #{professionId}
  </delete>
  <insert id="insert" parameterType="TProfession">
    insert into t_profession (profession_id, profession_name, profession_type, status, order_num, 
      create_time)
    values (#{professionId}, #{professionName}, #{professionType}, #{status}, #{orderNum}, 
      #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="TProfession">
    update t_profession
    <set>
      <if test="professionName != null">
        profession_name = #{professionName},
      </if>
      <if test="professionType != null">
        profession_type = #{professionType},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where profession_id = #{professionId}
  </update>
  <update id="updateByPrimaryKey" parameterType="TProfession">
    update t_profession
    set profession_name = #{professionName},
      profession_type = #{professionType},
      status = #{status},
      order_num = #{orderNum},
      create_time = #{createTime}
    where profession_id = #{professionId}
  </update>

  <select id="selectAll"  resultType="TProfession">
    select
    <include refid="Base_Column_List" />
    from t_profession
    where  status = 'E'
  </select>

</mapper>