<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TRelationTagMapper">
    <sql id="Base_Column_List">
    tag_id, tag_name, display_color, status, tag_class,sex, order_num, create_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="TRelationTag" resultType="TRelationTag">
        select
        <include refid="Base_Column_List"/>
        from t_relation_tag
        where tag_id = #{tagId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="TRelationTag">
    delete from t_relation_tag
    where tag_id = #{tagId}
  </delete>
    <insert id="insert" parameterType="TRelationTag">
    insert into t_relation_tag (tag_id, tag_name, display_color, status, tag_class, sex, order_num,
      create_time)
    values (#{tagId}, #{tagName}, #{displayColor}, #{status}, #{tagClass},#{sex}, #{orderNum},
      #{createTime})
  </insert>
    <update id="updateByPrimaryKeySelective" parameterType="TRelationTag">
        update t_relation_tag
        <set>
            <if test="tagName != null">
                tag_name = #{tagName},
            </if>
            <if test="displayColor != null">
                display_color = #{displayColor},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="tagClass != null">
                tag_class = #{tagClass},
            </if>
            <if test="sex != null">
                sex = #{sex},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where tag_id = #{tagId}
    </update>
    <update id="updateByPrimaryKey" parameterType="TRelationTag">
    update t_relation_tag
    set tag_name = #{tagName},
      display_color = #{displayColor},
      status = #{status},
      tag_class = #{tagClass},
      sex = #{sex},
      order_num = #{orderNum},
      create_time = #{createTime}
    where tag_id = #{tagId}
  </update>
    <select id="selectByTagClassAndSex" resultType="TRelationTag">
        select
        <include refid="Base_Column_List"/>
        from t_relation_tag where tag_class = #{tagClass} and sex = #{sex} and status = 'E'
        order by order_num
    </select>
</mapper>