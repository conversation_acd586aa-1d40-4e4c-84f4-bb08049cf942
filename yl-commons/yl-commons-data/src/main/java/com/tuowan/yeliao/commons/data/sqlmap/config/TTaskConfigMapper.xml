<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TTaskConfigMapper">
    <sql id="Base_Column_List">
        task_code, task_type, sex_type,friend_sex_type, busi_code, match_rule, task_name, task_desc, task_pic,
        order_num, status, eff_date, exp_date, finish_total_count, award_cash, award_beans, award_info, award_code,
        touch_type, touch_value, notice
    </sql>
    <select id="selectByPrimaryKey" parameterType="TTaskConfig" resultType="TTaskConfig">
        select
        <include refid="Base_Column_List"/>
        from t_task_config
        where task_code = #{taskCode}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="TTaskConfig">
        delete from t_task_config
        where task_code = #{taskCode}
    </delete>
    <insert id="insert" parameterType="TTaskConfig">
        insert into t_task_config (task_code, task_type, sex_type,friend_sex_type, busi_code, match_rule, task_name,
        task_desc, task_pic, order_num, status, eff_date, exp_date, finish_total_count,
        award_cash, award_beans, award_info, award_code, touch_type, touch_value, notice)
        values (#{taskCode}, #{taskType}, #{sexType},#{friendSexType}, #{busiCode}, #{matchRule}, #{taskName},
        #{taskDesc}, #{taskPic}, #{orderNum}, #{status}, #{effDate}, #{expDate}, #{finishTotalCount},
        #{awardCash}, #{awardBeans}, #{awardInfo}, #{awardCode}, #{touchType}, #{touchValue}, #{notice})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="TTaskConfig">
        update t_task_config
        <set>
            <if test="taskType != null">
                task_type = #{taskType},
            </if>
            <if test="sexType != null">
                sex_type = #{sexType},
            </if>
            <if test="friendSexType != null">
                friend_sex_type = #{friendSexType},
            </if>
            <if test="busiCode != null">
                busi_code = #{busiCode},
            </if>
            <if test="matchRule != null">
                match_rule = #{matchRule},
            </if>
            <if test="taskName != null">
                task_name = #{taskName},
            </if>
            <if test="taskDesc != null">
                task_desc = #{taskDesc},
            </if>
            <if test="taskPic != null">
                task_pic = #{taskPic},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="effDate != null">
                eff_date = #{effDate},
            </if>
            <if test="expDate != null">
                exp_date = #{expDate},
            </if>
            <if test="finishTotalCount != null">
                finish_total_count = #{finishTotalCount},
            </if>
            <if test="awardCash != null">
                award_cash = #{awardCash},
            </if>
            <if test="awardBeans != null">
                award_beans = #{awardBeans},
            </if>
            <if test="awardInfo != null">
                award_info = #{awardInfo},
            </if>
            <if test="awardCode != null">
                award_code = #{awardCode},
            </if>
            <if test="touchType != null">
                touch_type = #{touchType},
            </if>
            <if test="touchValue != null">
                touch_value = #{touchValue},
            </if>
            <if test="notice != null">
                notice = #{notice},
            </if>
        </set>
        where task_code = #{taskCode}
    </update>
    <update id="updateByPrimaryKey" parameterType="TTaskConfig">
        update t_task_config
        set task_type = #{taskType},
        sex_type = #{sexType},
        friend_sex_type = #{friendSexType},
        busi_code = #{busiCode},
        match_rule = #{matchRule},
        task_name = #{taskName},
        task_desc = #{taskDesc},
        task_pic = #{taskPic},
        order_num = #{orderNum},
        status = #{status},
        eff_date = #{effDate},
        exp_date = #{expDate},
        finish_total_count = #{finishTotalCount},
        award_cash = #{awardCash},
        award_beans = #{awardBeans},
        award_info = #{awardInfo},
        award_code = #{awardCode},
        touch_type = #{touchType},
        touch_value = #{touchValue},
        notice = #{notice}
        where task_code = #{taskCode}
    </update>

    <select id="selectAll" resultType="TTaskConfig">
        select
        <include refid="Base_Column_List"/>
        from t_task_config
        where status = 'E'
    </select>
</mapper>