<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TVoiceMapper">
    <sql id="Base_Column_List">
    log_id, voice_title, voice_content
  </sql>
    <select id="selectByPrimaryKey" parameterType="TVoice" resultType="TVoice">
        select
        <include refid="Base_Column_List"/>
        from t_voice
        where log_id = #{logId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="TVoice">
    delete from t_voice
    where log_id = #{logId}
  </delete>
    <insert id="insert" parameterType="TVoice">
    insert into t_voice (log_id, voice_title, voice_content)
    values (#{logId}, #{voiceTitle}, #{voiceContent})
  </insert>
    <update id="updateByPrimaryKeySelective" parameterType="TVoice">
        update t_voice
        <set>
            <if test="voiceTitle != null">
                voice_title = #{voiceTitle},
            </if>
            <if test="voiceContent != null">
                voice_content = #{voiceContent},
            </if>
        </set>
        where log_id = #{logId}
    </update>
    <update id="updateByPrimaryKey" parameterType="TVoice">
    update t_voice
    set voice_title = #{voiceTitle},
      voice_content = #{voiceContent}
    where log_id = #{logId}
  </update>
    <select id="selectAll" parameterType="TVoice" resultType="TVoice">
        select
        <include refid="Base_Column_List"/>
        from t_voice
    </select>
</mapper>