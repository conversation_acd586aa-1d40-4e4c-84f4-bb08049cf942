/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.commons.data.support.config;


import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.EnumUtils;
import com.easyooo.framework.common.util.IPUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.AppConfig;
import com.tuowan.yeliao.commons.config.enums.RedisType;
import com.tuowan.yeliao.commons.data.enums.general.RedisChannelDefine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPubSub;

import java.net.URI;
import java.util.Date;
import java.util.concurrent.*;

/**
 * 表配置核心数据同步频道订阅者，CMS修改核心配置会通过Redis的频道推送过来，从而完成JVM内存实时生效
 *
 * <AUTHOR>
 * @date 2018/9/19 19:54
 */
@Component
public class ConfigDataSyncJedisPubSub extends JedisPubSub implements InitializingBean, DisposableBean {

    /** 订阅标志：是否已经初始化了 */
    private static volatile boolean initSubscribeMark = false;
    private final Logger LOG = LoggerFactory.getLogger(getClass());

    private ExecutorService subscribePool;

    /**
     * 推送频道数据变化的通知
     */
    public boolean publish(RedisChannelDefine rcd) {
        if (rcd == null) {
            return false;
        }

        // 创建独立的Redis实例去发送消息发送频道消息
        Jedis jedis = null;
        try {
            jedis = createAloneRedisClient();
            Long subscribedChannels = jedis.publish(rcd.getId(), DateUtils.toString(new Date()));
            if (LOG.isInfoEnabled()) {
                LOG.info("发送频道消息，共有{}个订阅者收到消息。", subscribedChannels);
            }

            // 接收到信息的订阅者数量
            return subscribedChannels > 0;
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 接收消息事件
     */
    @Override
    public void onMessage(String channel, String message) {
        RedisChannelDefine rst = EnumUtils.byId(channel, RedisChannelDefine.class);
        if (rst == null) {
            LOG.warn("JVM配置数据同步：接收到无法识别的频道[{}]，已丢弃", channel);
            return;
        }
        ConfigReloadBase config = ConfigFactory.getInstance(rst);
        if (config == null) {
            LOG.warn("未找到频道对应的配置[{}]，已丢弃", channel);
            return;
        }
        config.reload();
        // 记录数据同步的日志
        LOG.info("JVM表配置数据同步日志：Project: {}, Channel: {}, IP: {}, Pid: {}",
                AppConfig.APP_TYPE, channel, IPUtils.getHostAddress(),
                IPUtils.getCurrentPid());
    }

    /**
     * 订阅
     */
    @Override
    public void onSubscribe(String channel, int subscribedChannels) {
        // 输出订阅日志
        if (LOG.isDebugEnabled()) {
            LOG.debug("JVM表配置数据同步订阅事件：Channel: {}, 订阅频道数量: {}", channel, subscribedChannels);
        }
    }

    /**
     * 开始订阅JVM配置同步消息
     */
    private void startSubscribe() {
        ThreadFactory namedFactory = new ThreadFactoryBuilder()
                .setNameFormat("jvm-config-pool-%d").build();

        // Common Thread Pool
        subscribePool = new ThreadPoolExecutor(1, 1, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1),
                namedFactory, new ThreadPoolExecutor.AbortPolicy());

        // 通过独立的线程去订阅
        final ConfigDataSyncJedisPubSub pubSub = this;
        subscribePool.execute(new Runnable() {
            @Override
            public void run() {
                // 订阅全部频道
                RedisChannelDefine[] defines = RedisChannelDefine.values();
                String[] channels = new String[defines.length];
                for (int i = 0; i < defines.length; i++) {
                    channels[i] = defines[i].getId();
                }
                // 独自创建一个连接去订阅配置同步
                createAloneRedisClient().subscribe(pubSub, channels);
            }
        });
    }

    /**
     * 创建一个单独的订阅者客户端实例，
     * <note>为了修复ERR only (P)SUBSCRIBE / (P)UNSUBSCRIBE / QUIT allowed in this context这个BUG
     * https://redis.io/topics/pubsub </note>
     *
     * @return
     */
    private Jedis createAloneRedisClient() {
        String connectionString = UnifiedConfig.getRedisSourceConfig().getConnectionString(RedisType.Sys);
        return new Jedis(URI.create(connectionString));
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 提取枚举定义的所有频道
        if (!initSubscribeMark) {
            initSubscribeMark = true;
            startSubscribe();
        }
    }

    @Override
    public void destroy() throws Exception {
        if (subscribePool != null) {
            subscribePool.shutdown();
        }
        this.unsubscribe();
    }
}
