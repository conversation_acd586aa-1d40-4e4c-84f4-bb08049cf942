package com.tuowan.yeliao.commons.data.support.config.impl;

import com.easyooo.framework.common.util.SpringContextUtils;
import com.tuowan.yeliao.commons.data.dto.config.MerchantDTO;
import com.tuowan.yeliao.commons.data.entity.config.TFemaleLevelNum;
import com.tuowan.yeliao.commons.data.enums.user.FemaleLevel;
import com.tuowan.yeliao.commons.data.persistence.config.TFemaleLevelNumMapper;
import com.tuowan.yeliao.commons.data.support.config.ConfigFactory;
import com.tuowan.yeliao.commons.data.support.config.ConfigReloadBase;

import java.util.List;

/**
 *  女用户数字等级配置配置
 *
 * <AUTHOR>
 * @date 2022/8/8 17:42
 */
public class FemaleLevelNumConfig extends ConfigReloadBase<TFemaleLevelNum> {
    @Override
    protected List<TFemaleLevelNum> selectAll() {
        return SpringContextUtils.getBean(TFemaleLevelNumMapper.class).selectAll();
    }

    @Override
    protected String getKey(TFemaleLevelNum config) {
        return config.getLevel().name();
    }

    public static TFemaleLevelNum getConfig(FemaleLevel level) {
        return ConfigFactory.getFemaleLevelNum().get(level.name());
    }

    public static List<TFemaleLevelNum> getAllConfig() {
        return ConfigFactory.getFemaleLevelNum().getAll();
    }
}
