/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.KeyProperty;
import java.math.BigDecimal;
import java.util.Date;

import com.tuowan.yeliao.commons.data.enums.game.AnimalMenuType;
import com.tuowan.yeliao.commons.data.enums.game.AnimalType;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import org.apache.ibatis.type.Alias;

@Alias("TAnimalMenu")
public class TAnimalMenu {
    /** 套餐类型 */
    @KeyProperty
    private AnimalMenuType menuType;

    /** 动物类型 */
    @KeyProperty
    private AnimalType animalType;

    /** 概率 */
    private Double probability;

    /** 赔率 */
    private Integer odds;

    /** 状态 */
    private Status status;

    /** 顺序 */
    private Integer sq;

    /** 修改时间 */
    private Date updateTime;

    /** 操作人 */
    private Long operator;

    public TAnimalMenu() {
        
    }

    /** 根据主键初始化实例 **/
    public TAnimalMenu(AnimalMenuType menuType, AnimalType animalType) {
        this.menuType = menuType;
        this.animalType = animalType;
    }

    public AnimalMenuType getMenuType() {
        return menuType;
    }

    public void setMenuType(AnimalMenuType menuType) {
        this.menuType = menuType;
    }

    public AnimalType getAnimalType() {
        return animalType;
    }

    public void setAnimalType(AnimalType animalType) {
        this.animalType = animalType;
    }

    public Double getProbability() {
        return probability;
    }

    public void setProbability(Double probability) {
        this.probability = probability;
    }

    public Integer getOdds() {
        return odds;
    }

    public void setOdds(Integer odds) {
        this.odds = odds;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Integer getSq() {
        return sq;
    }

    public void setSq(Integer sq) {
        this.sq = sq;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getOperator() {
        return operator;
    }

    public void setOperator(Long operator) {
        this.operator = operator;
    }
}