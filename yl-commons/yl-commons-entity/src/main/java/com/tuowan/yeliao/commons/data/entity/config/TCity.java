/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.easyooo.framework.cache.annotations.MiniTable;
import com.tuowan.yeliao.commons.data.enums.config.CityConfigType;
import org.apache.ibatis.type.Alias;

@Alias("TCity")
@Cache(expire = 30 * 24 * 3600)
@MiniTable
public class TCity {
    /** 城市ID */
    @KeyProperty
    private Integer cityId;

    /** 城市 */
    private String city;

    /** 城市拼音 */
    private String cityPinyin;

    /** 城市分类 */
    private CityConfigType cityType;

    /** 省份 */
    private String province;

    /** 国家 */
    private String country;

    /** 排序值 */
    private Integer provinceOrderNum;

    /** 排序值 */
    private Integer cityOrderNum;

    /** 经度值 */
    private String lng;

    /** 纬度值 */
    private String lat;

    /** 城市简介 */
    private String introduce;


    public TCity() {

    }

    /** 根据主键初始化实例 **/
    public TCity(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCityPinyin() {
        return cityPinyin;
    }

    public void setCityPinyin(String cityPinyin) {
        this.cityPinyin = cityPinyin;
    }

    public CityConfigType getCityType() {
        return cityType;
    }

    public void setCityType(CityConfigType cityType) {
        this.cityType = cityType;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getProvinceOrderNum() {
        return provinceOrderNum;
    }

    public void setProvinceOrderNum(Integer provinceOrderNum) {
        this.provinceOrderNum = provinceOrderNum;
    }

    public Integer getCityOrderNum() {
        return cityOrderNum;
    }

    public void setCityOrderNum(Integer cityOrderNum) {
        this.cityOrderNum = cityOrderNum;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getIntroduce() {
        return introduce;
    }

    public void setIntroduce(String introduce) {
        this.introduce = introduce;
    }
}