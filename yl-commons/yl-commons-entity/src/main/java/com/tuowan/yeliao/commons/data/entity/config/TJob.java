/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.easyooo.framework.cache.annotations.MiniTable;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("TJob")
@Cache
@MiniTable
public class TJob {
    /** 职业定义ID */
    @KeyProperty
    private Integer jobId;

    /** 职业名称 */
    private String jobName;

    /** 职业分类 */
    private String jobClass;

    /** 状态 */
    private Status status;

    /** 排序值 */
    private Integer orderNum;

    /**  */
    private Date createTime;

    public TJob() {

    }

    /** 根据主键初始化实例 **/
    public TJob(Integer jobId) {
        this.jobId = jobId;
    }

    public Integer getJobId() {
        return jobId;
    }

    public void setJobId(Integer jobId) {
        this.jobId = jobId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName == null ? null : jobName.trim();
    }

    public String getJobClass() {
        return jobClass;
    }

    public void setJobClass(String jobClass) {
        this.jobClass = jobClass == null ? null : jobClass.trim();
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}