/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("TNoticeSms")
public class TNoticeSms {
    /** 短信编号 */
    @KeyProperty
    private Long smsId;

    /** 短信类型，暂时只适用于营销短信   充值A类用户RechargeA(RA）：
            充值B类用户RechargeB(RB)、
            充值C类用户RechargeC(RC)、
            非充值用户NotRecharge(NR)、
 */
    private String smsType;

    /** 匹配规则 */
    private String matchRule;

    /** 短信内容 */
    private String contentTpl;

    /** 短信签名 */
    private String smsSign;

    /** 状态(Status)：启用(E)，禁用(D) */
    private String status;

    /** 上次更新时间 */
    private Date lastUpdateTime;

    /** 创建时间 */
    private Date createTime;

    public TNoticeSms() {
        
    }

    /** 根据主键初始化实例 **/
    public TNoticeSms(Long smsId) {
        this.smsId = smsId;
    }

    public Long getSmsId() {
        return smsId;
    }

    public void setSmsId(Long smsId) {
        this.smsId = smsId;
    }

    public String getSmsType() {
        return smsType;
    }

    public void setSmsType(String smsType) {
        this.smsType = smsType == null ? null : smsType.trim();
    }

    public String getMatchRule() {
        return matchRule;
    }

    public void setMatchRule(String matchRule) {
        this.matchRule = matchRule == null ? null : matchRule.trim();
    }

    public String getContentTpl() {
        return contentTpl;
    }

    public void setContentTpl(String contentTpl) {
        this.contentTpl = contentTpl == null ? null : contentTpl.trim();
    }

    public String getSmsSign() {
        return smsSign;
    }

    public void setSmsSign(String smsSign) {
        this.smsSign = smsSign == null ? null : smsSign.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}