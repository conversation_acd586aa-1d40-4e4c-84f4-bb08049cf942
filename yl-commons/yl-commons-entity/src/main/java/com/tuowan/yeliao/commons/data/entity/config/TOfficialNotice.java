/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.KeyProperty;
import java.util.Date;

import com.tuowan.yeliao.commons.data.enums.config.OfficialNoticeType;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import org.apache.ibatis.type.Alias;

@Alias("TOfficialNotice")
public class TOfficialNotice {
    /** 标识 */
    @KeyProperty
    private String sign;

    /** 类型 */
    private OfficialNoticeType type;

    /** 标题 */
    private String title;

    /** 简单描述 */
    private String desc;

    /** 内容 */
    private String content;

    /** 操作人 */
    private Long operator;

    /** 状态 */
    private Status status;

    /** 创建时间 */
    private Date createTime;

    public TOfficialNotice() {
        
    }

    /** 根据主键初始化实例 **/
    public TOfficialNotice(String sign) {
        this.sign = sign;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign == null ? null : sign.trim();
    }

    public OfficialNoticeType getType() {
        return type;
    }

    public void setType(OfficialNoticeType type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc == null ? null : desc.trim();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public Long getOperator() {
        return operator;
    }

    public void setOperator(Long operator) {
        this.operator = operator;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}