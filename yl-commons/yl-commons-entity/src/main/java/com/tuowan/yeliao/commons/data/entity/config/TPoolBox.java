/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.Group;
import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("TPoolBox")
@Cache(expire = 3 * 24 * 3600)
public class TPoolBox {
    /** 抽奖ID */
    @KeyProperty
    private Integer poolId;

    /** 抽奖Code：
            剑龙蛋Egg18，
            翼龙蛋Egg188，
            霸王龙蛋Egg888，
            幸运黄瓜ID
            棒棒糖ID
            
            CMS取字典的中文 */
    @Group
    private String poolCode;

    /** 奖励内容配置代码：奖励内容表的Code，可为空，为空意味着这是一个空箱子 */
    private String awardConfigCode;

    /** 奖励盒子数量 */
    private Integer count;

    /** 奖励金币估值 */
    private Integer aboutBeans;

    /** 备注：可填写奖励内容的说明 */
    private String remark;

    /** 创建人用户编号 */
    private Long creator;

    /** 创建时间 */
    private Date createTime;

    public TPoolBox() {
        
    }

    /** 根据主键初始化实例 **/
    public TPoolBox(Integer poolId) {
        this.poolId = poolId;
    }

    public TPoolBox(String poolCode) {
        this.poolCode = poolCode;
    }

    public Integer getPoolId() {
        return poolId;
    }

    public void setPoolId(Integer poolId) {
        this.poolId = poolId;
    }

    public String getPoolCode() {
        return poolCode;
    }

    public void setPoolCode(String poolCode) {
        this.poolCode = poolCode == null ? null : poolCode.trim();
    }

    public String getAwardConfigCode() {
        return awardConfigCode;
    }

    public void setAwardConfigCode(String awardConfigCode) {
        this.awardConfigCode = awardConfigCode == null ? null : awardConfigCode.trim();
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getAboutBeans() {
        return aboutBeans;
    }

    public void setAboutBeans(Integer aboutBeans) {
        this.aboutBeans = aboutBeans;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}