/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.Group;
import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("TProdGiftAnimation")
@Cache(expire = 30 * 24 * 3600)
public class TProdGiftAnimation {
    /** 主键ID，没什么业务用途 */
    @KeyProperty
    private Integer keyId;

    /** 礼物ID */
    @Group
    private Integer giftId;

    /** 数量 */
    private Integer count;

    /** 数量最大值：不填表示是固定数量，填了表示和上一个数量组成的范围值 <= 操作 */
    private Integer countUpperLimit;

    /** 动画代码 */
    private String animateCode;

    /** 生效时间 */
    private Date effTime;

    /** 失效时间 */
    private Date expTime;

    public TProdGiftAnimation() {
        
    }

    /** 根据主键初始化实例 **/
    public TProdGiftAnimation(Integer keyId) {
        this.keyId = keyId;
    }

    public TProdGiftAnimation(Integer keyId, Integer giftId) {
        this.keyId = keyId;
        this.giftId = giftId;
    }

    public Integer getKeyId() {
        return keyId;
    }

    public void setKeyId(Integer keyId) {
        this.keyId = keyId;
    }

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getCountUpperLimit() {
        return countUpperLimit;
    }

    public void setCountUpperLimit(Integer countUpperLimit) {
        this.countUpperLimit = countUpperLimit;
    }

    public String getAnimateCode() {
        return animateCode;
    }

    public void setAnimateCode(String animateCode) {
        this.animateCode = animateCode == null ? null : animateCode.trim();
    }

    public Date getEffTime() {
        return effTime;
    }

    public void setEffTime(Date effTime) {
        this.effTime = effTime;
    }

    public Date getExpTime() {
        return expTime;
    }

    public void setExpTime(Date expTime) {
        this.expTime = expTime;
    }
}