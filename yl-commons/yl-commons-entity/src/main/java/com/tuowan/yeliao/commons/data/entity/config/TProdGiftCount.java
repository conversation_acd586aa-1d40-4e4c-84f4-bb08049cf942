/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.Group;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.easyooo.framework.cache.annotations.MiniTable;
import org.apache.ibatis.type.Alias;

@<PERSON>as("TProdGiftCount")
@Cache(expire = 3 * 24 * 3600)
@MiniTable
public class TProdGiftCount {
    /** 自动增长 */
    @KeyProperty
    private Integer tplId;

    /** 模板分组Code */
    @Group
    private String tplCode;

    /** 实际数量 */
    private Integer count;

    /** 数量简称 */
    private String name;

    /** 不同模板的售价 */
    private Long beans;

    public TProdGiftCount() {
        
    }

    /** 根据主键初始化实例 **/
    public TProdGiftCount(Integer tplId) {
        this.tplId = tplId;
    }

    public TProdGiftCount(String tplCode) {
        this.tplCode = tplCode;
    }

    public Integer getTplId() {
        return tplId;
    }

    public void setTplId(Integer tplId) {
        this.tplId = tplId;
    }

    public String getTplCode() {
        return tplCode;
    }

    public void setTplCode(String tplCode) {
        this.tplCode = tplCode == null ? null : tplCode.trim();
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Long getBeans() {
        return beans;
    }

    public void setBeans(Long beans) {
        this.beans = beans;
    }
}