/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.Group;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("TSendGiftLimit")
@Cache(expire = 30 * 24 * 3600)
public class TSendGiftLimit {
    /** 配置表主键 */
    @KeyProperty
    private Long cfgId;

    /** 省 */
    @Group
    private String province;

    /** 市 */
    private String city;

    /** 限制金额 */
    private Double limitMoney;

    /** 触发女用户数 */
    private Integer femaleNum;

    /** 触发男用户数 */
    private Integer maleNum;

    /** 状态 */
    private Status status;

    /** 创建人 */
    private Long creator;

    /** 创建时间 */
    private Date createTime;

    public TSendGiftLimit() {
        
    }

    /** 根据主键初始化实例 **/
    public TSendGiftLimit(Long cfgId) {
        this.cfgId = cfgId;
    }

    /** 根据分组键初始化实例 **/
    public TSendGiftLimit(String province) {
        this.province = province;
    }

    public Long getCfgId() {
        return cfgId;
    }

    public void setCfgId(Long cfgId) {
        this.cfgId = cfgId;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    public Double getLimitMoney() {
        return limitMoney;
    }

    public void setLimitMoney(Double limitMoney) {
        this.limitMoney = limitMoney;
    }

    public Integer getFemaleNum() {
        return femaleNum;
    }

    public void setFemaleNum(Integer femaleNum) {
        this.femaleNum = femaleNum;
    }

    public Integer getMaleNum() {
        return maleNum;
    }

    public void setMaleNum(Integer maleNum) {
        this.maleNum = maleNum;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}