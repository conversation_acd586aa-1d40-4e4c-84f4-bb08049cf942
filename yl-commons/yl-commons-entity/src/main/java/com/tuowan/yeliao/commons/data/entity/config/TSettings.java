/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("TSettings")
public class TSettings {
    /** 参数名 */
    @KeyProperty
    private String paramName;

    /** 参数值 */
    private String paramValue;

    /**
     * 适用客户端类型(ClientType)：
     * 微信公众号：H
     * Android客户端：A
     * Ios客户端：I
     * 直播联盟：U
     * Cms：C
     * Site网站：S
     * Assistent助手：T
     * All: F
     */
    private ClientType clientType;

    /** 是否允许编辑：T/F */
    private BoolType isEdit;

    /** 备注信息 */
    private String remark;

    /** 创建时间 */
    private Date createTime;

    public TSettings() {

    }

    /** 根据主键初始化实例 **/
    public TSettings(String paramName) {
        this.paramName = paramName;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName == null ? null : paramName.trim();
    }

    public String getParamValue() {
        return paramValue;
    }

    public void setParamValue(String paramValue) {
        this.paramValue = paramValue == null ? null : paramValue.trim();
    }

    public ClientType getClientType() {
        return clientType;
    }

    public void setClientType(ClientType clientType) {
        this.clientType = clientType;
    }

    public BoolType getIsEdit() {
        return isEdit;
    }

    public void setIsEdit(BoolType isEdit) {
        this.isEdit = isEdit;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}