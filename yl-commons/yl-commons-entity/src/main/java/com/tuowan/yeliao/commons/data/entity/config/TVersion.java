/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.Group;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.easyooo.framework.cache.annotations.MiniTable;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.data.enums.config.AppUpdateGrayType;
import com.tuowan.yeliao.commons.data.enums.config.AppUpdateSexType;
import com.tuowan.yeliao.commons.data.enums.config.AppUpdateUserType;
import com.tuowan.yeliao.commons.data.enums.config.AppVersionType;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("TVersion")
@Cache
@MiniTable
public class TVersion {
    /** 客户端类型(ClientType)：
            微信公众号：H
            Android客户端：A
            Ios客户端：I
            直播联盟：U
            Cms：C
            Site网站：S
            Assistent助手：T
            All: F */
    @KeyProperty
    @Group
    private ClientType clientType;

    /** 版本类型(AppVersionType)：公测版Beta(B)、标准版Release(R)、审核版Review(V)
            
            公测版：需要设置公测投放时间和失效时间。
            
            当公测结束之后，需要自动或者手动切换位标准版本 */
    @KeyProperty
    private AppVersionType versionType;

    /** 马甲包类型 */
    @KeyProperty
    private PackageType packageType;

    /** 状态(Status)：正常(E)、禁用(D) */
    private Status status;

    /** 仅当公测版本才需要填 */
    private Date otStartTime;

    /** 仅当公测版本才需要填 */
    private Date otExpTime;

    /** 最新版本号，如2.5.0 */
    private String latestVersion;

    /** BuilderNumber */
    private Integer builderNumber;

    /** 兼容最小版本，如2.4.0 */
    private String compatibleVersion;

    /** 下载地址1：exe、ipa、apk等安装包 */
    private String installUrl;

    /** 下载地址2：在线更新内容包 */
    private String pkgUrl;

    /** 下载文件md5值 */
    private String installFileMd5;

    /** 更新内容 */
    private String updateContent;

    /** 指定用户类型 */
    private AppUpdateUserType userType;

    /** 指定用户值 */
    private String pointUsers;

    /** 指定性别 */
    private AppUpdateSexType sexType;

    /** 灰度比例 */
    private AppUpdateGrayType grayType;

    /** 上次变更用户ID */
    private Long lastCreator;

    /** 上次变更时间 */
    private Date lastChangeTime;

    public TVersion() {
        
    }

    public TVersion(ClientType clientType) {
        this.clientType = clientType;
    }

    /** 根据主键初始化实例 **/
    public TVersion(ClientType clientType, AppVersionType versionType) {
        this.clientType = clientType;
        this.versionType = versionType;
    }

    public TVersion(ClientType clientType, AppVersionType versionType, PackageType packageType) {
        this.clientType = clientType;
        this.versionType = versionType;
        this.packageType = packageType;
    }

    public PackageType getPackageType() {
        return packageType;
    }

    public void setPackageType(PackageType packageType) {
        this.packageType = packageType;
    }

    public ClientType getClientType() {
        return clientType;
    }

    public void setClientType(ClientType clientType) {
        this.clientType = clientType;
    }

    public AppVersionType getVersionType() {
        return versionType;
    }

    public void setVersionType(AppVersionType versionType) {
        this.versionType = versionType;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Date getOtStartTime() {
        return otStartTime;
    }

    public void setOtStartTime(Date otStartTime) {
        this.otStartTime = otStartTime;
    }

    public Date getOtExpTime() {
        return otExpTime;
    }

    public void setOtExpTime(Date otExpTime) {
        this.otExpTime = otExpTime;
    }

    public String getLatestVersion() {
        return latestVersion;
    }

    public void setLatestVersion(String latestVersion) {
        this.latestVersion = latestVersion == null ? null : latestVersion.trim();
    }

    public Integer getBuilderNumber() {
        return builderNumber;
    }

    public void setBuilderNumber(Integer builderNumber) {
        this.builderNumber = builderNumber;
    }

    public String getCompatibleVersion() {
        return compatibleVersion;
    }

    public void setCompatibleVersion(String compatibleVersion) {
        this.compatibleVersion = compatibleVersion == null ? null : compatibleVersion.trim();
    }

    public String getInstallUrl() {
        return installUrl;
    }

    public void setInstallUrl(String installUrl) {
        this.installUrl = installUrl == null ? null : installUrl.trim();
    }

    public String getPkgUrl() {
        return pkgUrl;
    }

    public void setPkgUrl(String pkgUrl) {
        this.pkgUrl = pkgUrl == null ? null : pkgUrl.trim();
    }

    public String getInstallFileMd5() {
        return installFileMd5;
    }

    public void setInstallFileMd5(String installFileMd5) {
        this.installFileMd5 = installFileMd5 == null ? null : installFileMd5.trim();
    }

    public String getUpdateContent() {
        return updateContent;
    }

    public void setUpdateContent(String updateContent) {
        this.updateContent = updateContent == null ? null : updateContent.trim();
    }

    public AppUpdateUserType getUserType() {
        return userType;
    }

    public void setUserType(AppUpdateUserType userType) {
        this.userType = userType;
    }

    public String getPointUsers() {
        return pointUsers;
    }

    public void setPointUsers(String pointUsers) {
        this.pointUsers = pointUsers;
    }

    public AppUpdateSexType getSexType() {
        return sexType;
    }

    public void setSexType(AppUpdateSexType sexType) {
        this.sexType = sexType;
    }

    public AppUpdateGrayType getGrayType() {
        return grayType;
    }

    public void setGrayType(AppUpdateGrayType grayType) {
        this.grayType = grayType;
    }

    public Long getLastCreator() {
        return lastCreator;
    }

    public void setLastCreator(Long lastCreator) {
        this.lastCreator = lastCreator;
    }

    public Date getLastChangeTime() {
        return lastChangeTime;
    }

    public void setLastChangeTime(Date lastChangeTime) {
        this.lastChangeTime = lastChangeTime;
    }
}