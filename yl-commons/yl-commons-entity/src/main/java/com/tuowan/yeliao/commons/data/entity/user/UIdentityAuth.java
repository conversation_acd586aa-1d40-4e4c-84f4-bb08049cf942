/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.user;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.core.enums.general.ReviewStatus;
import com.tuowan.yeliao.commons.data.enums.general.AuditStatus;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.commons.data.enums.user.AuthEntry;
import com.tuowan.yeliao.commons.data.enums.user.MediaApplyStatus;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("UIdentityAuth")
@Cache(expire = 1 * 24 * 3600)
public class UIdentityAuth {
    /**  */
    @KeyProperty
    private Long userId;

    /** 真实姓名 */
    private String realname;

    /** 性别 */
    private SexType sex;

    /** 手机号 */
    private String mobile;

    /** 手机号绑定时间 */
    private Date mobileBindTime;

    /** 身份证号码 */
    private String identityNo;

    /** 身份证照片列表 正面，反面，手持 */
    private String identityCardPics;

    /** 支付宝用户ID */
    private String alipayUserId;

    /** 支付宝昵称 */
    private String alipayNickname;

    /** 支付宝绑定时间 */
    private Date alipayBindTime;

    /** 支付宝首次提现时间 */
    private Date alipayWithdrawTime;

    /** 微信应用标识 */
    private String weixinAppId;

    /** 微信用户ID(unionId) */
    private String weixinUserId;

    /** 微信用户昵称 */
    private String weixinNickname;

    /** 微信绑定时间 */
    private Date weixinBindTime;

    /**
     * 微信首次提现时间
     */
    private Date weixinWithdrawTime;

    /**
     * 状态 此条信息可用 不可用
     */
    private Status status;

    /**
     * 真人对比留档信息
     */
    private String realPeopleInfo;

    /**
     * 真人认证状态
     */
    private ReviewStatus realPeopleStatus;

    /**
     * 实名认证对比留档信息
     */
    private String realNameInfo;

    /**
     * 实名认证时间
     */
    private Date realNameTime;

    /**
     * 真人认证时间
     */
    private Date realPersonTime;

    /**
     * 实名认证入口
     */
    private AuthEntry realNameEntry;

    /**
     * 真人认证入口
     */
    private AuthEntry realPersonEntry;

    /**
     * 解绑次数
     */
    private Integer untieTimes;

    /**
     * 创建时间
     */
    private Date createTime;

    public UIdentityAuth() {

    }

    /** 根据主键初始化实例 **/
    public String getRealNameInfo() {
        return realNameInfo;
    }

    public void setRealNameInfo(String realNameInfo) {
        this.realNameInfo = realNameInfo;
    }

    public UIdentityAuth(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    public SexType getSex() {
        return sex;
    }

    public void setSex(SexType sex) {
        this.sex = sex;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Date getMobileBindTime() {
        return mobileBindTime;
    }

    public void setMobileBindTime(Date mobileBindTime) {
        this.mobileBindTime = mobileBindTime;
    }

    public String getIdentityNo() {
        return identityNo;
    }

    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo;
    }

    public String getIdentityCardPics() {
        return identityCardPics;
    }

    public void setIdentityCardPics(String identityCardPics) {
        this.identityCardPics = identityCardPics;
    }

    public String getAlipayUserId() {
        return alipayUserId;
    }

    public void setAlipayUserId(String alipayUserId) {
        this.alipayUserId = alipayUserId;
    }

    public String getAlipayNickname() {
        return alipayNickname;
    }

    public void setAlipayNickname(String alipayNickname) {
        this.alipayNickname = alipayNickname;
    }

    public Date getAlipayBindTime() {
        return alipayBindTime;
    }

    public void setAlipayBindTime(Date alipayBindTime) {
        this.alipayBindTime = alipayBindTime;
    }

    public Date getAlipayWithdrawTime() {
        return alipayWithdrawTime;
    }

    public void setAlipayWithdrawTime(Date alipayWithdrawTime) {
        this.alipayWithdrawTime = alipayWithdrawTime;
    }

    public String getWeixinAppId() {
        return weixinAppId;
    }

    public void setWeixinAppId(String weixinAppId) {
        this.weixinAppId = weixinAppId;
    }

    public String getWeixinUserId() {
        return weixinUserId;
    }

    public void setWeixinUserId(String weixinUserId) {
        this.weixinUserId = weixinUserId;
    }

    public String getWeixinNickname() {
        return weixinNickname;
    }

    public void setWeixinNickname(String weixinNickname) {
        this.weixinNickname = weixinNickname;
    }

    public Date getWeixinBindTime() {
        return weixinBindTime;
    }

    public void setWeixinBindTime(Date weixinBindTime) {
        this.weixinBindTime = weixinBindTime;
    }

    public Date getWeixinWithdrawTime() {
        return weixinWithdrawTime;
    }

    public void setWeixinWithdrawTime(Date weixinWithdrawTime) {
        this.weixinWithdrawTime = weixinWithdrawTime;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public String getRealPeopleInfo() {
        return realPeopleInfo;
    }

    public void setRealPeopleInfo(String realPeopleInfo) {
        this.realPeopleInfo = realPeopleInfo;
    }

    public ReviewStatus getRealPeopleStatus() {
        return realPeopleStatus;
    }

    public void setRealPeopleStatus(ReviewStatus realPeopleStatus) {
        this.realPeopleStatus = realPeopleStatus;
    }

    public Date getRealNameTime() {
        return realNameTime;
    }

    public void setRealNameTime(Date realNameTime) {
        this.realNameTime = realNameTime;
    }

    public Date getRealPersonTime() {
        return realPersonTime;
    }

    public void setRealPersonTime(Date realPersonTime) {
        this.realPersonTime = realPersonTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUntieTimes() {
        return untieTimes;
    }

    public void setUntieTimes(Integer untieTimes) {
        this.untieTimes = untieTimes;
    }

    public AuthEntry getRealNameEntry() {
        return realNameEntry;
    }

    public void setRealNameEntry(AuthEntry realNameEntry) {
        this.realNameEntry = realNameEntry;
    }

    public AuthEntry getRealPersonEntry() {
        return realPersonEntry;
    }

    public void setRealPersonEntry(AuthEntry realPersonEntry) {
        this.realPersonEntry = realPersonEntry;
    }
}