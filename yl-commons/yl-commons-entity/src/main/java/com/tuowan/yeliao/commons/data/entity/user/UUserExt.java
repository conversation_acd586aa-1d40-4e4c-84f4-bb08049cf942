/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.user;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.general.LoginType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.data.enums.common.TargetPresentType;
import com.tuowan.yeliao.commons.data.enums.config.AppVersionType;
import com.tuowan.yeliao.commons.data.enums.general.AuditStatus;
import com.tuowan.yeliao.commons.data.enums.user.InvitePsType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("UUserExt")
@Cache(expire = 3 * 24 * 3600)
public class UUserExt {
    /** 用户ID */
    @KeyProperty
    private Long userId;

    /**
     * 首次登录类型(LoginType)：
     * 手机注册Moblie：M
     * 注册账号Account：A
     * QQ登录：Q
     * 微信：W
     */
    private LoginType regType;

    /** 应用渠道 */
    private String channelId;

    /** 归因渠道 */
    private String channelCode;

    /** 注册IP地址 */
    private String regIp;

    /** 注册ip地址城市 */
    private String ipCity;

    /** 注册ip城市 */
    private String city;

    /**
     * 注册客户端类型
     */
    private ClientType regClientType;

    /**
     * 注册版本号
     */
    private String regClientVersion;

    /**
     * 上次应用渠道
     */
    private String lastChannelId;

    /**
     * 上次打开时间
     */
    private Date lastOpenTime;

    /**
     * 上次打开版本号
     */
    private String lastOpenVersion;

    /**
     * 上次客户端类型(ClientType)
     */
    private ClientType lastClientType;

    /**
     * 上次应用版本(AppVersionType)
     */
    private AppVersionType lastVersionType;

    /**
     * 上次设备型号
     */
    private String lastDeviceModel;

    /**
     * 上次设备系统
     */
    private String lastDeviceOs;

    /**
     * 上次登录用户ID（分身账号ID）
     */
    private Long lastUserId;

    /**
     * 首次登陆时间
     */
    private Date firstLoginTime;

    /**
     * 首次充值时间
     */
    private Date firstRechargeTime;

    /**
     * 上次充值时间
     */
    private Date lastRechargeTime;

    /**
     * 上次发送动态时间
     */
    private Date lastPostTime;

    /** 我的邀请人用户ID */
    private Long inviteUserId;

    /** 我邀请的女用户并且真人认证的用户数 */
    private Integer totalRpInvite;

    /** 我的邀请提成固定比例 */
    private Integer inviteFixScale;

    /** 分成模式 */
    private TargetPresentType presentType;

    /** 我的邀请提成模式 */
    private InvitePsType invitePsType;

    /** 是否有效邀请人 */
    private BoolType validInvitor;

    /** 支持查看聊天室类型 */
    private String lookChatRoomType;

    /** 包体 */
    private PackageType packageType;

    /** 注册时间 */
    private Date createTime;

    public UUserExt() {

    }

    public UUserExt(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LoginType getRegType() {
        return regType;
    }

    public void setRegType(LoginType regType) {
        this.regType = regType;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getRegIp() {
        return regIp;
    }

    public void setRegIp(String regIp) {
        this.regIp = regIp;
    }

    public String getIpCity() {
        return ipCity;
    }

    public void setIpCity(String ipCity) {
        this.ipCity = ipCity;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public ClientType getRegClientType() {
        return regClientType;
    }

    public void setRegClientType(ClientType regClientType) {
        this.regClientType = regClientType;
    }

    public String getRegClientVersion() {
        return regClientVersion;
    }

    public void setRegClientVersion(String regClientVersion) {
        this.regClientVersion = regClientVersion;
    }

    public String getLastChannelId() {
        return lastChannelId;
    }

    public void setLastChannelId(String lastChannelId) {
        this.lastChannelId = lastChannelId;
    }

    public Date getLastOpenTime() {
        return lastOpenTime;
    }

    public void setLastOpenTime(Date lastOpenTime) {
        this.lastOpenTime = lastOpenTime;
    }

    public String getLastOpenVersion() {
        return lastOpenVersion;
    }

    public void setLastOpenVersion(String lastOpenVersion) {
        this.lastOpenVersion = lastOpenVersion;
    }

    public ClientType getLastClientType() {
        return lastClientType;
    }

    public void setLastClientType(ClientType lastClientType) {
        this.lastClientType = lastClientType;
    }

    public AppVersionType getLastVersionType() {
        return lastVersionType;
    }

    public String getLastDeviceModel() {
        return lastDeviceModel;
    }

    public void setLastDeviceModel(String lastDeviceModel) {
        this.lastDeviceModel = lastDeviceModel;
    }

    public String getLastDeviceOs() {
        return lastDeviceOs;
    }

    public void setLastDeviceOs(String lastDeviceOs) {
        this.lastDeviceOs = lastDeviceOs;
    }

    public void setLastVersionType(AppVersionType lastVersionType) {
        this.lastVersionType = lastVersionType;
    }

    public Long getLastUserId() {
        return lastUserId;
    }

    public void setLastUserId(Long lastUserId) {
        this.lastUserId = lastUserId;
    }

    public Date getFirstLoginTime() {
        return firstLoginTime;
    }

    public void setFirstLoginTime(Date firstLoginTime) {
        this.firstLoginTime = firstLoginTime;
    }

    public Date getFirstRechargeTime() {
        return firstRechargeTime;
    }

    public void setFirstRechargeTime(Date firstRechargeTime) {
        this.firstRechargeTime = firstRechargeTime;
    }

    public Date getLastRechargeTime() {
        return lastRechargeTime;
    }

    public void setLastRechargeTime(Date lastRechargeTime) {
        this.lastRechargeTime = lastRechargeTime;
    }

    public Date getLastPostTime() {
        return lastPostTime;
    }

    public void setLastPostTime(Date lastPostTime) {
        this.lastPostTime = lastPostTime;
    }

    public Long getInviteUserId() {
        return inviteUserId;
    }

    public void setInviteUserId(Long inviteUserId) {
        this.inviteUserId = inviteUserId;
    }

    public Integer getTotalRpInvite() {
        return totalRpInvite;
    }

    public void setTotalRpInvite(Integer totalRpInvite) {
        this.totalRpInvite = totalRpInvite;
    }

    public Integer getInviteFixScale() {
        return inviteFixScale;
    }

    public void setInviteFixScale(Integer inviteFixScale) {
        this.inviteFixScale = inviteFixScale;
    }

    public TargetPresentType getPresentType() {
        return presentType;
    }

    public void setPresentType(TargetPresentType presentType) {
        this.presentType = presentType;
    }

    public InvitePsType getInvitePsType() {
        return invitePsType;
    }

    public void setInvitePsType(InvitePsType invitePsType) {
        this.invitePsType = invitePsType;
    }

    public BoolType getValidInvitor() {
        return validInvitor;
    }

    public void setValidInvitor(BoolType validInvitor) {
        this.validInvitor = validInvitor;
    }

    public String getLookChatRoomType() {
        return lookChatRoomType;
    }

    public void setLookChatRoomType(String lookChatRoomType) {
        this.lookChatRoomType = lookChatRoomType;
    }

    public PackageType getPackageType() {
        return packageType;
    }

    public void setPackageType(PackageType packageType) {
        this.packageType = packageType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}