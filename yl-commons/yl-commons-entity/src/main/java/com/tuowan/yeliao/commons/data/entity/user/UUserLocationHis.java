/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.user;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("UUserLocationHis")
public class UUserLocationHis {
    /** 记录ID */
    @KeyProperty
    private Long logId;

    /** 用户id */
    private Long userId;

    /** 纬度值 */
    private String lat;

    /** 经度值 */
    private String lng;

    /** 省 */
    private String province;

    /** 市 */
    private String city;

    /** 区县 */
    private String district;

    /** 创建时间 */
    private Date createTime;

    public UUserLocationHis() {
        
    }

    public UUserLocationHis(UUserLocation location) {
        this.userId = location.getUserId();
        this.lat = location.getLat();
        this.lng = location.getLng();
        this.province = location.getProvince();
        this.city = location.getCity();
        this.district = location.getDistrict();
        this.createTime = new Date();
    }

    /** 根据主键初始化实例 **/
    public UUserLocationHis(Long logId) {
        this.logId = logId;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat == null ? null : lat.trim();
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng == null ? null : lng.trim();
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district == null ? null : district.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}