/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.user;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("UUserTaskNew")
@Cache
public class UUserTaskNew {
    /**
     * 用户id
     */
    @KeyProperty
    private Long userId;

    /**
     * 任务code
     */
    @KeyProperty
    private String taskCode;

    /**
     * 奖励物品内容
     */
    private String awardValue;

    /**
     * 任务进度
     */
    private Long progress;

    /**
     * 完成任务时间
     */
    private Date taskFinishTime;

    /**
     * 奖励领取时间
     */
    private Date taskReceiveTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public UUserTaskNew() {

    }

    /**
     * 根据主键初始化实例
     **/
    public UUserTaskNew(Long userId, String taskCode) {
        this.userId = userId;
        this.taskCode = taskCode;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode == null ? null : taskCode.trim();
    }

    public String getAwardValue() {
        return awardValue;
    }

    public void setAwardValue(String awardValue) {
        this.awardValue = awardValue == null ? null : awardValue.trim();
    }

    public Long getProgress() {
        return progress;
    }

    public void setProgress(Long progress) {
        this.progress = progress;
    }

    public Date getTaskFinishTime() {
        return taskFinishTime;
    }

    public void setTaskFinishTime(Date taskFinishTime) {
        this.taskFinishTime = taskFinishTime;
    }

    public Date getTaskReceiveTime() {
        return taskReceiveTime;
    }

    public void setTaskReceiveTime(Date taskReceiveTime) {
        this.taskReceiveTime = taskReceiveTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}