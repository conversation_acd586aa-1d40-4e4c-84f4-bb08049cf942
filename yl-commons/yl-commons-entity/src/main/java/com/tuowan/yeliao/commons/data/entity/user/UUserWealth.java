/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.user;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

@Alias("UUserWealth")
public class UUserWealth {
    /** 用户ID */
    @KeyProperty
    private Long userId;

    /** 用户当前充值金币余额  */
    private Long rechargeBeans;

    /** 用户当前赠送金币余额 */
    private Long platformBeans;

    /** 用户当前银币余额 */
    private Long silver;

    /** 累计充值现金 */
    private Long totalRecharge;

    /** 历史总充值金币 */
    private Long totalRechargeBeans;

    /** 历史总赠送金币 */
    private Long totalPlatformBeans;

    /** 历史总银币 */
    private Long totalSilver;

    public UUserWealth() {
        
    }

    /** 根据主键初始化实例 **/
    public UUserWealth(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getRechargeBeans() {
        return rechargeBeans;
    }

    public void setRechargeBeans(Long rechargeBeans) {
        this.rechargeBeans = rechargeBeans;
    }

    public Long getPlatformBeans() {
        return platformBeans;
    }

    public void setPlatformBeans(Long platformBeans) {
        this.platformBeans = platformBeans;
    }

    public Long getSilver() {
        return silver;
    }

    public void setSilver(Long silver) {
        this.silver = silver;
    }

    public Long getTotalRecharge() {
        return totalRecharge;
    }

    public void setTotalRecharge(Long totalRecharge) {
        this.totalRecharge = totalRecharge;
    }

    public Long getTotalRechargeBeans() {
        return totalRechargeBeans;
    }

    public void setTotalRechargeBeans(Long totalRechargeBeans) {
        this.totalRechargeBeans = totalRechargeBeans;
    }

    public Long getTotalPlatformBeans() {
        return totalPlatformBeans;
    }

    public void setTotalPlatformBeans(Long totalPlatformBeans) {
        this.totalPlatformBeans = totalPlatformBeans;
    }

    public Long getTotalSilver() {
        return totalSilver;
    }

    public void setTotalSilver(Long totalSilver) {
        this.totalSilver = totalSilver;
    }
}