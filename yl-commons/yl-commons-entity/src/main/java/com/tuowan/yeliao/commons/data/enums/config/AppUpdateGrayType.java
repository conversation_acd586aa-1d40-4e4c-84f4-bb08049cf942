package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

public enum AppUpdateGrayType implements EnumUtils.IDEnum {

    All("A", "全部更新", 1),
    Two("T", "更新2分之1", 2),
    Three("H", "更新3分之1", 3),
    Four("F", "更新4分之1", 4),
    Five("V", "更新5分之1", 5),
    ;

    private String id;
    private String desc;
    private Integer rate;

    AppUpdateGrayType(String id, String desc, Integer rate) {
        this.id = id;
        this.desc = desc;
        this.rate = rate;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public Integer getRate() {
        return rate;
    }
}
