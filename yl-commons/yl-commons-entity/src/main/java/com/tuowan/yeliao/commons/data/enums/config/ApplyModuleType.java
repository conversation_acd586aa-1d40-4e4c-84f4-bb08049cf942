package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 适用模块枚举
 *
 * <AUTHOR>
 */
public enum ApplyModuleType implements EnumUtils.IDEnum {

    Invite("I", "邀友"),
    <PERSON><PERSON><PERSON>("M", "养鹊"),
    ;

    private final String id;
    private final String desc;

    ApplyModuleType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
