package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

public enum BlockType implements EnumUtils.IDEnum {

    Id("I", "身份证号"),

    Mobile("M", "手机号");

    private String id;
    private String desc;

    BlockType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}
