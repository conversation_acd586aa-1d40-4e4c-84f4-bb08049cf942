package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 装备状态枚举定义
 *
 * <AUTHOR>
 * @date 2018/7/27 10:57
 */
public enum EquipStatusType implements EnumUtils.IDEnum {

    True("T", "是"),

    False("F", "否"),

    None("N", "空");

    private String id;
    private String desc;

    EquipStatusType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
