package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

public enum GoodsGroupCode implements EnumUtils.IDEnum {

	HomePage("首页标签"),
	Rocket("置顶道具"),
	Room("直播间"),
	Ticket("消费券"),
	Badge("勋章"),
	Car("座驾"),
	Game("游戏"),
	Vip("VIP"),
	Discount("折扣券"),
    RoyalCard("贵族卡"),
    <PERSON><PERSON>("私信道具"),
    <PERSON><PERSON>ble("气泡"),
    Frame("头像框"),
	;

    private String desc;

    GoodsGroupCode(String desc) {

        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.name();
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
