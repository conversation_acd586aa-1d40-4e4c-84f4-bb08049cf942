package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 幸运抽奖规则
 * 定义一些特殊的抽奖方式
 *
 * <AUTHOR>
 * @date 2019/11/28 21:04
 */
public enum LuckyRule implements EnumUtils.IDEnum{

    LastedLess1("Less1","最近30天送礼小于1次使用的坑位规则", 3),
    LastedLess10("Less10", "最近30天送礼小于10次使用的坑位规则", 3),
    LastedMore10("More10", "最近30天送礼大于等于10次使用的坑位规则", 3)
    ;

    private String id;
    private String desc;
    /** 适用礼物ID */
    private Integer giftId;

    LuckyRule(String id, String desc, Integer giftId) {
        this.id = id;
        this.desc = desc;
    }
    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
