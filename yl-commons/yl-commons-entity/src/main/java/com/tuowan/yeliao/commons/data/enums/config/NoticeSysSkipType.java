package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * app端支持的跳转类型
 *
 * <AUTHOR>
 * @date 2019/7/5 14:34
 */
public enum NoticeSysSkipType implements EnumUtils.IDEnum {

    // 消息中心
    RoyalDownWarning("RDW", "贵族降级预警消息"),
    TempRoyalAvailable("TRA", "贵族体验卡可用"),
    GuardExpireWarning("GEW", "守护过期预警消息"),
    GetBadge("GB", "获得勋章"),
    GetCar("GC", "获得座驾"),
    GetPrettyNum("GP", "获得靓号"),
    GetExpBuff("GEB", "获得用户经验值加X倍效果"),
    NewMonthCard("NMC", "新版月卡"),
    GetCoupon("GCP", "获取优惠券消息"),
    GetDiscount("GDC", "获取折扣券消息"),
    ShowRecharge("SR", "跳转充值中心"),
    Mini("MI", "拉起小程序"),
    ;

    private String id;
    private String desc;

    NoticeSysSkipType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
