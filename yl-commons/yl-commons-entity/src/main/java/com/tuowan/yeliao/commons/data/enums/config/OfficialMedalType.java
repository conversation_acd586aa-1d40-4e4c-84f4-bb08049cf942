package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 官方勋章分类
 *
 * <AUTHOR>
 * @date 2020/7/11 13:42
 */
public enum OfficialMedalType implements EnumUtils.IDEnum {

    LiveImage("L", "真人形象", "config/home/<USER>", 1, true, true, 1),

    AttractivePart("A", "魅力部位", "config/home/<USER>", 3, true, false, 2),

    <PERSON><PERSON>("H", "兴趣爱好", "config/home/<USER>", Integer.MAX_VALUE, true, false, 3),

    Professional("P", "职业认证", "config/home/<USER>", 1, true, false, 4),;


    private String id;
    private String desc;
    private String icon;
    private Integer maxCount;
    private Boolean realName;
    private Boolean realPerson;
    private Integer order;

    OfficialMedalType(String id, String desc, String icon, Integer maxCount,
                      Boolean realName, Boolean realPerson, Integer order) {
        this.id = id;
        this.desc = desc;
        this.icon = icon;
        this.maxCount = maxCount;
        this.realName = realName;
        this.realPerson = realPerson;
        this.order = order;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    public String getIcon() {
        return icon;
    }

    public Integer getMaxCount() {
        return maxCount;
    }

    public Boolean getRealName() {
        return realName;
    }

    public Boolean getRealPerson() {
        return realPerson;
    }

    public Integer getOrder() {
        return order;
    }

}
