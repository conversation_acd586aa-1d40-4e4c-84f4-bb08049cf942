package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;
import com.easyooo.framework.common.util.StringUtils;

import java.util.Objects;

/**
 * 推广类型枚举定义
 * <p>
 * 该类只定义在应用商店推广的类型，同时也需在数据库配置中定义(mp_config#t_promotion_config)
 *
 * <AUTHOR>
 * @date 2022/2/8 18:02
 */
public enum PromotionType implements EnumUtils.IDEnum {

    VivoStore("VOSTORE", "VIVO商店", "VIVO"),
    XiaoMiStore("XMSTORE", "XIAOMI商店", "XIAOMI"),
    ;

    /** 该值与yl_config#t_promotion_config#type_code一致，请使用大写字母 */
    private String id;
    private String desc;
    private String channel;

    PromotionType(String id, String desc, String channel) {
        this.id = id;
        this.desc = desc;
        this.channel = channel;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public String getChannel() {
        return channel;
    }

    /**
     * 根据渠道获取当前推广类型
     * <p>
     * 如果是应用商店推广的渠道才有值
     *
     * @param channel
     * @return
     */
    public static PromotionType getPromotionType(String channel) {
        if (StringUtils.isEmpty(channel)) {
            return null;
        }
        for (PromotionType type : PromotionType.values()) {
            if (Objects.equals(type.getChannel(), channel)) {
                return type;
            }
        }
        return null;
    }
}
