package com.tuowan.yeliao.commons.data.enums.config;

/**
 * 全局参数名枚举定义
 *
 * <AUTHOR>
 * @date 2018/7/10 17:58
 */
public enum SettingsType {

    /**
     * 开启之后所有的Service执行都将会先执行服务状态检查
     */
    LatestStartAdsVersion("最新启动广告版本"),
    MobileRegex("手机号正则表达式"),
    RequestExecTimeThreshold("请求执行时间阈值"),
    RequestExternalExecTimeThreshold("包含外部调用的请求执行时间阈值"),
    TopRoyalLevel("贵族最高等级"),
    IosReviewUserIds("苹果应用审核用户ID"),
    IAPVerifyTimeout("IAP收据验证超时时间"),
    LiveDefaultSdkProvider("直播默认SDK厂商"),
    LiveDefaultCdnProvider("直播默认CDN厂商"),
    LiveMicProviderWeight("直播连麦厂商权重"),
    LiveToggleMicWhitelist("允许切换连麦厂商的节目白名单"),
    LiveGreenWhitelist("直播间鉴黄白名单"),
    OfflineProgramDays("节目指定天数未开播自动下线"),
    CityConfigVersion("城市设置变化的版本"),
    NoCheckUpgradeVersions("不需要检测版本更新的版本"),
    LimitRechargeWhitelist("限制充值的用户名单列表"),
    jobConfigVersion("职业设置变化的版本"),
    CustomerServiceUrl("联系客服地址"),
    ServiceUnavailableTime("服务不可用时间范围"),
    DefaultSmsCode("默认登录短信验证码，如果开启，则登录需要需要输入指定验证码"),
    OpenGeeTest("极验开关"),

    OpenSystemChatUp("是否开启系统搭讪"),

    OpenSystemChatUpMsg("是否可以发送系统搭讪消息"),

    OpenRedPacketChatUp("是否开启红包搭讪"),

    OpenChatSearch("是否开启聊天搜索功能"),

    OpenBanUserIllegalWord("是否开启用户关键词违禁封禁"),
    OpenBanUserIllegalWordCheck("是否开启用户关键词违禁封禁前置判断检查"),
    IllegalWordBanUserLevel("违禁词封禁可以封禁的用户等级"),

    OpenPrivateSearch("是否开启私聊搜索功能"),

    OpenTransferRedis("开启redis迁移"),

    FateMatchNewFirst("缘分匹配新用户首次匹配参数"),
    FateMatchNewNoFirst("缘分匹配新用户非首次匹配参数"),
    FateMatchActFirst("缘分匹配活跃用户首次匹配参数"),
    FateMatchActNoFirst("缘分匹配活跃用户非首次匹配参数"),
    FateMatchFixFirst("缘分匹配固定对象用户首次匹配参数"),
    FateMatchFixNoFirst("缘分匹配固定对象用户非首次匹配参数"),

    AssistantUpgradeWhitelist("助手升级设备白名单"),
    DefaultSvcUserId("默认客服ID"),
    ChatBlacklist("聊天黑名单"),
    PostWhitelist("发动态白名单，不用做图片机器审核"),
    AndroidReviewAccount("安卓审核账号"),
    AndroidReviewVersion("安卓审核版本号"),
    AndroidReviewAccountSy("安卓审核账号-拾缘"),
    AndroidReviewVersionSy("安卓审核版本号-拾缘"),
    AndroidPureModeChannel("安卓纯净模式渠道"),
    IOSReviewVersion("ios审核版本号"),

    WhitelistChannel("加白渠道包"),
    HideTaskChannel("隐藏任务中心的渠道"),
    HiedInviteChannel("隐藏邀友渠道"),


    FirstRechargeChannel("首充礼物测试渠道"),


    AppleReviewPhone("苹果审核手机号和验证码"),
    AndroidReviewPhone("安卓审核手机号和验证码"),

    RobotExeInterval("机器人规则执行间隔json"),
    RobotProgramWhitelist("机器人节目白名单"),

    LatestHomeCategoryVersion("最新首页分栏版本"),

    LocationClosedUserIds("关闭位置更新的用户白名单"),
    RechargeRebateTimeRange("充值返利时间范围"),
    SoftYellowFilterHoursRange("文本软色情过滤范围"),
    PicPronFilterHoursRange("图片软色情过滤范围"),

    JL01MchChannel("商户号JL01分流渠道"),

    FemaleUpdateInfoSwitch("女用户禁止修改信息时间范围"),
    MaleUpdateInfoSwitch("男用户禁止修改信息时间范围"),
    FlowDistributionRule("流量分配规则"),
    RegBlackNumberFragment("注册黑名单号码片段"),
    TransferRightUser("具备转账功能的用户"),
    OnlineStatusDpCfg("在线状态显示配置"),
    ChatMasterLevelCfg("女用户等级配置"),
    SubmitYd("是否提交易盾审核"),
    VideoCallFaceCheckSwitch("视频通话人脸检测开关"),
    VideoCallFaceCheckWhiteList("视频通话人脸检测白名单"),
    InviteCodeUsers("指定邀请码的用户"),
    RegMinVersion("注册支持最小版本"),
    AutoPassRpWhiteInvitor("自动审核通过白名单邀请人"),
    /**********************  用户登录配置 *****************************/
    MobileCodeLoginFreeUser("手机验证码登录白名单用户"),
    RegYdRiskControl("注册易盾智能风控开关控制"),

    /**********************  任务相关配置 *****************************/
    UserNewTask("新手任务开关"),
    PrimaryWeekIncome("初级收益配置,例如：条件收益,奖励收益"),
    AdvancedWeekIncome("高级收益配置,例如：条件收益,奖励收益"),
    RedPacketFlowConfig("红包流量等级比例配置"),
    RedPacketFlowRegConfig("首次注册红包流量等级比例配置"),
    WeekCashRewardTime("周收益奖励时间"),

    VideoHighSexyRate("视频通话允许的更高的性感指数"),

    /**********************  交友相关配置 *****************************/

    H5RecomChatMaster("H5推荐的聊主ID,多个以英文逗号分隔"),
    RegNoticeOfficialUserId("注册通知的官方ID, 不支持多个"),

    MaleRecomTimesEveryDay("男性用户每日推荐次数"),
    MaleFirstRecomTimes("男性用户首次打开APP推荐次数"),
    MaleSecondRecomTimes("男性用户每日第二次打开APP推荐次数"),

    MinRobotTaskInterval("机器人任务最小执行时间间隔，单位：分钟"),
    ChatUpMatchTriggerFrequency("缘分搭讪触发频率"),


    WithdrawDailyLimitMoney("提现每日限制金额"),
    AnonyCallIncrHeatRange("匿名语音每次递增热度范围"),
    HomeRecomChannels("首页推荐特殊渠道"),
    FirstChargeChannels("首充渠道配置"),
    FirstChargeOpen("首充活动开关"),
    NewUserChargeOpen("新人充值活动开关"),
    PostBaseHeatValue("动态基础热度值"),
    CheckRealFace("是否做人脸检测"),
    VideoCallGreenInterval("视频通话鉴黄频率"),
    VideoCallGreenNotifyInterval("直播鉴黄通知频率"),
    ChatInviteLimit("邀请频率限制开关"),
    IOSRechargeChannel("苹果可以H5充值的渠道"),
    ShowResponse("是否显示响应结果"),
    MaxChatUserCount("最大私聊人数"),
    RedPacketWaitRobMinute("红包允许可领取的时间"),
    FamilyBaseHeatValue("家族基础热度值"),
    ChatMsgFee("私信金币单价"),
    MaxChatUserNum("每日最多主动私信人数，仅限制C级及以下聊主和没有亲密度的用户"),
    CustomerUserId("客服私信ID"),
    SensitiveContent("特定义敏感词"),
    PostAuditFreeSwitch("动态免审核开关"),
    CosmosAuditFreeSwitch("宇宙免审核开关"),
    WithDrawBlackList("提现黑名单，在此名单不允许提现"),
    IpCitySwitch("IP城市显示/更新开关"),
    RegisterBindMobileSwitch("注册绑定手机号开关控制"),
    FeMaleChatUpTimesLimit("女用户搭讪次数限制"),
    MaleChatUpTTL("男用户搭讪状态重置时间"),
    MaleChatUpOfflineTTL("男用户离线搭讪状态重置时间"),
    PopTeenagersSwitch("是否弹出青少年弹窗开关控制"),
    NearUserHideLocSwitch("附近用户隐藏位置开关"),
    SendGiftAreaLimitSwitch("送礼地域限制开关"),
    SendGiftWhiteList("送礼白名单《不受送礼地域限制的控制》"),
    SendGiftLimitPopSwitch("送礼限制弹窗控制"),
    FirstChatSendTips("第一次聊天发送付费明示"),
    InviteAllSwitch("邀友总开关"),
    InviteTimeSwitch("邀友时间开关"),
    SignInActTime("签到活动时间区间"),
    RechargeActTime("充值活动时间区间"),
    PigBankActTime("存钱罐活动开始时间"),
    LuckyWheelActTime("大转盘抽奖活动时间"),
    LuckWheelNoticeSwitch("大转盘抽奖活动通知开关"),
    FemaleMaxChatUserNum("等级为S、A、B嘉宾每日聊天上限"),
    OpenFemaleChatLimit("女用户每日聊天人数上限开关"),
    MaliceRegisterIp("恶意注册IP,该IP下注册男用户不发注册奖励"),
    VideoChatMaleInitSwitch("视频聊那用户初始化开关"),
    SendUserWaterPopUpConfig("灌水弹窗信息"),
    FallbackFemaleUsers("保底女用户"),

    MaleSystemChatUpInterval("男用户系统搭讪的间隔"),

    VideoCallCameraCfg("男用户视频通话摄像头配置"),
    VideoCallFrameViolationCfg("视频通话画面违规配置"),

    SystemPushTime("系统推送给A池B池女用户的次数"),

    MaleSystemMatchScaleCfg("男用户系统匹配频率配置"),

    InviteShareIncomeDetail("邀请分享收益明细"),

    AuthWhiteUsers("认证白名单"),
    /**
     * --------------------- 客服相关 -------------------
     */
    CustomerServiceTime("客服服务时间"),
    AdminTotpSecret("管理员TOTP密钥"),
    VipWeekWhitelist("周会员白名单用户"),

    RongImCallbackCheck("是否开题融云IM消息回调校验"),
    InvalidRyMsgType("非法融云消息类型"),

    PreheatSessionId("程序预热专用会话ID"),

    MerchantWarnNeedUserNum("商户开始预警最小用户数"),
    MerchantDisableNeedUserNum("商户直接禁用所需用户数"),

    IsGroupBlackUserCheck("是否开启集团黑名单校验 T开启、F不开启"),

    /** ----------------------------- 活动相关 --------------- */
    ActInviteTimes("邀友活动开始结束时间"),
    ActInvitePeopleAward("邀友活动人头奖励"),
    ActInviteSettleAward("邀友活动结算奖励配置"),

    SignInAwardActTimes("签到抽奖活动时间范围"),
    EcuGiftActTimes("专属礼物活动有效时间"),

    /** ----------------------- 通话相关 ------------------*/

    CallIntimateValueCheck("通话亲密度校验"),
    CallVoiceFirstRechargeCheck("语音通话首充校验开关"),
    CallVideoFirstRechargeCheck("视频通话首充校验开关"),
    CallAudioIllegalTimes("音视频通话音频违规次数"),
    FemaleIncomeDataOpen("女用户数收益数据入口开关"),

    UserVideoPopRule("用户视频弹窗规则配置"),
    ;
    private String desc;

    SettingsType(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
