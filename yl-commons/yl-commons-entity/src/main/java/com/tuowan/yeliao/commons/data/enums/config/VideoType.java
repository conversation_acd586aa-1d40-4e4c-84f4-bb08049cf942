package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 配置的视频类型
 *
 * <AUTHOR>
 * @date 2020/7/29 17:30
 */
public enum VideoType implements EnumUtils.IDEnum {
    Teach("T", "教学视频"),;

    private String id;
    private String desc;

    VideoType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
