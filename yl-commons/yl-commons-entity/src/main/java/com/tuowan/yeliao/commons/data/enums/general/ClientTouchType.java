package com.tuowan.yeliao.commons.data.enums.general;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 客户端打开类型枚举定义
 *
 * <AUTHOR>
 * @date 2018/7/4 19:47
 */
public enum ClientTouchType implements EnumUtils.IDEnum {

    Url("Url", "网页地址"),

    Recharge("Recharge", "充值首页"),

    MineHomePage("MineHomePage", "我的主页"),

    OtherHomePage("OtherHomePage", "他人主页"),

    EditMineHomePage("EditMineHomePage", "编辑资料界面"),

    UserCenter("UserCenter", "用户中心"),

    RealName("RealName", "实名认证"),

    RealHead("RealHead", "真人头像认证<客户端会先调用校验接口>"),

    RealPerson("RealPerson", "真人认证"),

    UpdateInfo("UpdateInfo", "编辑资料"),

    FriendHome("FriendHome", "交友首页"),

    Message("Message", "消息"),

    InviteFriend("InviteFriend", "邀友赚钱"),

    SystemNotice("SystemNotice", "系统消息"),

    FriendNotice("FriendNotice", "好友消息"),

    PrivateChat("PrivateChat", "私聊"),

    MySign("MySign", "我的签名页面"),

    Voice("Voice", "语音签名页面"),

    VoiceCall("VoiceCall", "语音通话"),

    VideoCall("VideoCall", "视频通话"),

    Report("Report", "举报"),

    ReportRecordMyReport("ReportRecordMyReport", "举报记录-我的举报"),

    ApplyVideoAuth("ApplyVideoAuth", "视频聊推荐专区申请"),

    QuickVoice("QuickVoice", "快捷语音页面"),

    FriendAuth("FriendAuth", "我的交友"),

    LoveSpace("LoveSpace", "情侣空间"),

    MessageSetting("MessageSetting", "消息设置"),

    Recommend("Recommend", "首页推荐"),

    OpenFamilyCard("OpenFamilyCard", "打开家族名片（在家族聊天室中才能点击）"),

    WelcomeNewMember("WelcomeNewMember", "欢迎新成员"),

    WelcomeVisitor("WelcomeVisitor", "欢迎游客"),

    FamilyList("FamilyList", "家族列表"),

    EnterFamily("EnterFamily", "进入家族"),

    JoinFamily("JoinFamily", "加入家族"),

    OpenRedPacket("OpenRedPacket", "打开红包详情"),

    WeChat("WeChat", "复制微信"),

    PostDetail("PostDetail", "动态详情"),

    MoodDetail("MoodDetail", "心情详情"),

    PubPost("PubPost", "发布动态"),

    Post("Post", "动态页面"),

    Nearby("Nearby", "首页附近"),

    TaskCenter("TaskCenter", "任务中心"),

    DressUpCenter("DressUpCenter", "装扮中心"),

    ValuePackage("ValuePackage", "超值礼包"),

    FamilyAction("FamilyAction", "进入家族并执行下一步操作"),

    Level("Level", "等级页面"),

    QuitFamily("QuitFamily", "退出家族"),

    MessagePopup("MessagePopup", "弹出提示框"),

    FamilyHome("FamilyHome", "家族主页"),

    FamilyTab("FamilyTab", "家族底部Tab"),

    Marriage("Marriage", "姻缘测算"),

    VideoMatch("VideoMatch", "视频匹配"),

    VideoDating("VideoDating", "视频约会"),

    OpenVip("OpenVip", "开通Vip快捷弹窗"),

    VipCenter("VipCenter", "会员中心"),

    Test("Test", "测试使用"),

    AuthCentre("AuthCentre", "认证中心"),

    MyInfo("MyInfo", "我的页面《最底下table最右边的一个》"),

    SignInDialog("SignInDialog", "我的页面签到弹窗"),

    ChatUpSet("ChatUpSet","专属搭讪设置页面"),

    InvitorHome("InvitorHome", "邀请首页"),

    EcuReply("EcuReply", "专属回复"),

    FastRecharge("FastRecharge", "快捷充值"),

    CustomerPage("CustomerPage", "客服中心"),

    MsgFeeSetPage("MsgFeeSetPage", "消息价格设置页面"),

    ChatRoom("ChatRoom", "聊天室"),

    MainPage("MainPage", "APP首页"),

    ChatRoomHome("ChatRoomHome", "聊天室首页"),

    JumpToChatRoom("JumpToChatRoom", "聊天室聊天页面"),

    ZooGame("ZooGame", "动物园游戏"),
    ;

    private String id;
    private String desc;

    ClientTouchType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

}
