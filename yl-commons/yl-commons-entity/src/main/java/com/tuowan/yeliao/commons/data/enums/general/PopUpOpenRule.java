package com.tuowan.yeliao.commons.data.enums.general;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 弹窗打开方式
 *
 * <AUTHOR>
 * @date 2019/1/22 10:54
 */
public enum PopUpOpenRule implements EnumUtils.IDEnum{

    E("E", "每日一次"),
    O("O", "仅弹一次");

    private String id;
    private String desc;

    PopUpOpenRule(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

}
