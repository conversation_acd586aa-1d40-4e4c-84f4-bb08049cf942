package com.tuowan.yeliao.commons.data.enums.general;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 审核结果枚举定义
 *
 * <AUTHOR>
 * @date 2018/7/5 14:26
 */
public enum ReviewResultType implements EnumUtils.IDEnum {

    Wait("W", "待审核", 2),

    Reject("J", "被拒绝", 1),

    Pass("P", "通过", 3),
    ;

    private String id;
    private String desc;
    /** 审核状态做排序 */
    private Integer order;

    ReviewResultType(String id, String desc, Integer order) {
        this.id = id;
        this.desc = desc;
        this.order = order;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    public Integer getOrder() {
        return order;
    }
}
