package com.tuowan.yeliao.commons.data.enums.social;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 聊天内容类型枚举
 *
 * <AUTHOR>
 * @date 2020/7/4 10:51
 */
public enum ChatContentType implements EnumUtils.IDEnum {

    ChatUp("C", "搭讪消息"),
    Text("T", "文本"),
    Pic("P", "图片", null, "[图片]"),
    Audio("A", "语音消息"),
    Voice("V", "语音通话"),
    Video("O", "视频通话"),
    Gift("G", "礼物"),
    RedPacket("RP", "红包"),
    UnlockMedia("CM", "解锁媒资"),
    ChargePic("CP", "付费照片"),
    ChargeVideo("CV", "付费视频"),
    DiceGame("D", "骰子游戏", "1,2,3,4,5,6", "[动态表情]"),
    FingerGame("F", "石头剪刀布", "rock,paper,scissors", "[动态表情]"),
    MessageInvite("MI", "邀请消息"),
    Location("L", "位置信息"),
    ;

    private String id;
    private String desc;
    private String content;
    private String showText;

    ChatContentType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    ChatContentType(String id, String desc, String content) {
        this.id = id;
        this.desc = desc;
        this.content = content;
    }

    ChatContentType(String id, String desc, String content, String showText) {
        this.id = id;
        this.desc = desc;
        this.content = content;
        this.showText = showText;
    }


    public String getContent() {
        return content;
    }

    public String getShowText() {
        return showText;
    }

    public void setShowText(String showText) {
        this.showText = showText;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
