package com.tuowan.yeliao.commons.data.enums.social;

import com.easyooo.framework.common.util.EnumUtils;

public enum EcuType implements EnumUtils.IDEnum {
    Words("W", "文字", "EcuWords", 20, 10),
    Voice("V", "语音", "EcuVoice", 10, 5),
    Pic("P", "图片", "EcuPic", 10, 5),
    ;

    private String id;
    private String desc;
    private String yidunReviewTypeName;
    private Integer numLimit;
    private Integer addLimitDay;

    EcuType(String id, String desc, String yidunReviewTypeName, Integer numLimit, Integer addLimitDay) {
        this.id = id;
        this.desc = desc;
        this.yidunReviewTypeName = yidunReviewTypeName;
        this.numLimit = numLimit;
        this.addLimitDay = addLimitDay;
    }

    /**
     * 根据 yidunReviewTypeName 获取实例
     * @return
     */
    public static EcuType getByType(String yidunReviewTypeName){
        for(EcuType item : EcuType.values()){
            if (item.yidunReviewTypeName.equals(yidunReviewTypeName)){
                return item;
            }
        }
        return null;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public String getYidunReviewTypeName() {
        return yidunReviewTypeName;
    }

    public Integer getNumLimit() {
        return numLimit;
    }

    public Integer getAddLimitDay() {
        return addLimitDay;
    }
}
