package com.tuowan.yeliao.commons.data.enums.user;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 头像年龄枚举
 *
 * <AUTHOR>
 * @date 2021/3/1 13:14
 */
public enum HeadAgeType implements EnumUtils.IDEnum {

    /** 青年 */
    Youth("Y", "18-30", 18, 30),
    /** 壮年 */
    Prime("P", "30-40", 30, 40),
    /** 中年 */
    Middle("M", "40-50", 40, 50),
    /** 老年 */
    Elderly("E", "50以上", 50, 200);

    private String id;
    private String desc;
    private Integer min;
    private Integer max;

    HeadAgeType(String id, String desc, Integer min, Integer max) {
        this.id = id;
        this.desc = desc;
        this.min = min;
        this.max = max;
    }


    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    public Integer getMin() {
        return min;
    }

    public Integer getMax() {
        return max;
    }

    /**
     * 年龄获取用户年龄段类型
     *
     * @param age
     * @return
     */
    public static HeadAgeType getTypeByAge(Integer age) {
        if (null == age || age <= HeadAgeType.Youth.getMin()) {
            return HeadAgeType.Youth;
        }
        HeadAgeType types[] = HeadAgeType.values();
        for (HeadAgeType type : types) {
            if (age > type.getMin() && age <= type.getMax()) {
                return type;
            }
        }
        return HeadAgeType.Youth;
    }
}
