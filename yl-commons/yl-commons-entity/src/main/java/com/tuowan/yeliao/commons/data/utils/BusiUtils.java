package com.tuowan.yeliao.commons.data.utils;

import com.alibaba.nacos.common.utils.MD5Utils;
import com.alibaba.nacos.common.utils.Pair;
import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.*;
import com.google.common.base.CharMatcher;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.AppConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.DataKeyConfig;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.constant.IconConstant;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.core.utils.InviteCodeUtils;
import com.tuowan.yeliao.commons.data.entity.config.TCity;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserLocation;
import com.tuowan.yeliao.commons.data.enums.config.BmiLevelType;
import com.tuowan.yeliao.commons.data.enums.config.IncomeType;
import com.tuowan.yeliao.commons.data.enums.config.ProvinceSimpleType;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.enums.user.ConstellationType;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.enums.user.VipType;
import com.tuowan.yeliao.commons.data.utils.dto.AdDefineDTO;
import com.tuowan.yeliao.commons.data.utils.dto.UserDistanceDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.geom.Point2D;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 业务工具类
 *
 * <AUTHOR>
 * @date 2021/11/19 14:55
 */
public class BusiUtils {

    private final static Logger LOG = LoggerFactory.getLogger(BusiUtils.class);


    /**
     * 特殊国家字符的正则匹配
     */
    private static final Pattern CHARS_SPECIAL_PATTERN = Pattern.compile("[\u202E|\ufb00-\ufb4f\u0590-\u05FF\u07C0-\u07FF\u0700-\u074F\u0780-\u07BF\u0600-\u06FF\u0750-\u077F\uFB50-\uFDFF\uFE70-\uFEFF]");

    /**
     * 纯数字匹配
     */
    private static final Pattern ALL_NUMBER = Pattern.compile("^[0-9]+$");

    /**
     * 纯汉字+数字匹配
     */
    private static final Pattern ALL_CHINA_NUMBER = Pattern.compile("^[\u4e00-\u9fa50-9]+$");

    /**
     * 微信号正则匹配
     */
    private static final Pattern WE_CHAT_PATTERN = Pattern.compile("^[a-zA-Z]{1}[-_a-zA-Z0-9]{4,19}+$");

    /**
     * 手机号正则匹配
     */
    private static final Pattern MOBILE_PATTERN = Pattern.compile("^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(16[0-9])|(17[01235678])|(18[0-9])|(19[0-9]))\\d{8}$");


    /**
     * 特殊字符正则匹配
     */
    private static final Pattern NAME_PATTERN = Pattern.compile("[`~!@#$%^&*()+=|{}':;',\\\\[\\\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]");

    /**
     * 邀请码正则匹配
     */
    private static final Pattern INVITE_CODE_PATTERN = Pattern.compile("^[a-zA-Z0-9]+$");

    /**
     * 特殊字符替换模板
     */
    private static Map<String, String> CHARS_EXCHANGE_MAPPING = new HashMap<>();

    /**
     * 用户贵族等级图标
     */
    private static final String ROYAL_LEVEL_PIC = "level/royal/L{}.png";

    /**
     * 财富等级图标
     */
    private static final String USER_LEVEL_PIC = "cfg/level/user/user_{}.png";

    /**
     * 魅力等级图标
     */
    private static final String CHARM_LEVEL_PIC = "cfg/level/charm/charm_{}.png";


    /**
     * 家族等级图标
     */
    private static final String FAMILY_LEVEL_PIC = "config/level/family/L{}.png";

    /**
     * 不是用户贵族灰色图标
     */
    private static final String NO_ROYAL_LEVEL_PIC = "config/medal/no_royal.png";

    /**
     * 真人尺寸小图标
     */
    private static final String SMALL_REAL_PEOPLE_AUTH_MARK = "config/medal/real_people_small.png";

    /**
     * 未认证真人图标
     */
    private static final String NO_REAL_PEOPLE = "config/medal/no_real_people.png";


    /**
     * 零钱放大倍数
     */
    private static final Double CASH_SCALE = 10000D;

    /**
     * 星座的起始天集合
     */
    private static final Integer[] DAY_ARR = new Integer[]{22, 20, 19, 21, 20, 21, 22, 23, 23, 23, 24, 23, 22};

    /**
     * 15位身份证号
     */
    private static final Integer FIFTEEN_ID_CARD = 15;
    /**
     * 18位身份证号
     */
    private static final Integer EIGHTEEN_ID_CARD = 18;

    /**
     * 地球半径 单位 km
     */
    private static final double EARTH_RADIUS = 6378137;

    /**
     * 图片水印参数
     */
    private static final String WATER_MARK_PARAM = "?x-oss-process=image/watermark,type_d3F5LXplbmhlaQ,size_30,text_{},color_FFFFFF,shadow_50,t_100,g_se,x_10,y_10";

    /**
     * 昵称敏感词
     */
    public static final Set<String> NICKNAME_SENSITIVE_WORDS = new HashSet<>();

    /**
     * 真人头像随机文案
     */
    public static final List<String> REAL_PEOPLE_TEXT = new ArrayList<>();

    /**
     * 心动随机文案
     */
    public static final List<String> HEART_TOUCH_TEXT = new ArrayList<>();

    /**
     * 互相心动随机文案
     */
    public static final List<String> MUTUAL_HEART_TOUCH_TEXT = new ArrayList<>();

    /**
     * 亲密度提升5级随机文案
     */
    public static final List<String> INTIMATE_UP_5_TEXT = new ArrayList<>();
    /**
     * 亲密度提升6级随机文案
     */
    public static final List<String> INTIMATE_UP_6_TEXT = new ArrayList<>();
    /**
     * 亲密度提升7级随机文案
     */
    public static final List<String> INTIMATE_UP_7_TEXT = new ArrayList<>();

    /**
     * 私信礼物随机文案
     */
    public static final List<String> CHAT_GIFT_TEXT = new ArrayList<>();

    /**
     * 通话10分钟随机文案
     */
    public static final List<String> HANG_UP_10_TEXT = new ArrayList<>();
    /**
     * 通话30分钟随机文案
     */
    public static final List<String> HANG_UP_30_TEXT = new ArrayList<>();
    /**
     * 通话60分钟随机文案
     */
    public static final List<String> HANG_UP_60_TEXT = new ArrayList<>();

    /**
     * 审核标签随机文案
     */
    public static final List<String> AUDIT_TAG_PASS_TEXT = new ArrayList<>();

    /**
     * 点赞标签随机文案
     */
    public static final List<String> PRAISE_TAG_TEXT = new ArrayList<>();

    /**
     * 特殊日期
     */
    public static final List<String> SPECIAL_DATE_STR = new ArrayList<>();

    /**
     * 微信前缀
     */
    private static final List<String> WE_CHAT_PREFIX_LIST = new ArrayList<>();

    /**
     * 家族自定义标签
     */
    private static final List<String> FAMILY_CUSTOM_TAG = new ArrayList<>();


    /**
     * 家族九宫格图片
     */
    public static final List<String> FAMILY_GRID = new ArrayList<>();

    /**
     * 家族九宫格图片新版
     */
    public static final List<String> NEW_FAMILY_GRID = new ArrayList<>();

    /**
     * 家族随机文案
     */
    public static final List<String> FAMILY_RANDOM_TEXT = new ArrayList<>();

    /**
     * 家族红包随机文案
     */
    public static final List<String> FAMILY_REDPACKET_RANDOM_TEXT = new ArrayList<>();

    /**
     * 十天干
     */
    private static final String[] GAN_ARRY = {"庚", "辛", "壬", "癸", "甲", "乙", "丙", "丁", "戊", "己",};
    /**
     * 十二地支
     */
    private static final String[] ZHI_ARRY = {"申", "酉", "戌", "亥", "子", "丑", "寅", "卯", "辰", "巳", "午", "未"};

    static {
        CHARS_EXCHANGE_MAPPING.put("<", "&lt;");
        CHARS_EXCHANGE_MAPPING.put(">", "&gt;");
        CHARS_EXCHANGE_MAPPING.put("\n", "");
        CHARS_EXCHANGE_MAPPING.put("\r", "");
        CHARS_EXCHANGE_MAPPING.put("'", "");
        CHARS_EXCHANGE_MAPPING.put("\"", "");
        CHARS_EXCHANGE_MAPPING.put("\\\\", "");
        CHARS_EXCHANGE_MAPPING.put("\\{", "");
        CHARS_EXCHANGE_MAPPING.put("\\}", "");
        CHARS_EXCHANGE_MAPPING.put("\\$", "");

        // 昵称敏感词
        NICKNAME_SENSITIVE_WORDS.add("64");
        NICKNAME_SENSITIVE_WORDS.add("89");

        REAL_PEOPLE_TEXT.add("快去看看Ta的庐山真面目吧");
        REAL_PEOPLE_TEXT.add("真实不做作~5星好评！！！");
        REAL_PEOPLE_TEXT.add("真实交友不来虚的");

        HEART_TOUCH_TEXT.add("被Ta的魅力征服");
        HEART_TOUCH_TEXT.add("真是人气爆棚");
        HEART_TOUCH_TEXT.add("走红指日可待");

        MUTUAL_HEART_TOUCH_TEXT.add("互相心动，祝有情人终成眷属");
        MUTUAL_HEART_TOUCH_TEXT.add("互相心动啦~");
        MUTUAL_HEART_TOUCH_TEXT.add("互相心动啦~果然平均2.5小时就能找到真爱");

        INTIMATE_UP_5_TEXT.add("亲密度上升至5级，祝愿双方感情持续升温");
        INTIMATE_UP_5_TEXT.add("亲密度上升至5级，成了彼此心尖尖上的人");
        INTIMATE_UP_5_TEXT.add("亲密度上升至5级，祝有情人终成眷属");

        INTIMATE_UP_6_TEXT.add("亲密度上升至6级，恭喜他们生生世世在一起");
        INTIMATE_UP_6_TEXT.add("亲密度上升至6级，真是如胶似漆呀");
        INTIMATE_UP_6_TEXT.add("亲密度上升至6级，希望大家早日吃到他俩的喜糖");

        INTIMATE_UP_7_TEXT.add("亲密度上升至7级，祝福他们天长地久");
        INTIMATE_UP_7_TEXT.add("亲密度上升至7级，给这份爱加上一个期限：一万年！");
        INTIMATE_UP_7_TEXT.add("亲密度上升至7级，山无棱天地和才敢与君绝");


        CHAT_GIFT_TEXT.add("掌声响起来...");
        CHAT_GIFT_TEXT.add("真是魅力无限~");
        CHAT_GIFT_TEXT.add("不知是谁的心尖尖");

        HANG_UP_10_TEXT.add("他俩相见恨晚");
        HANG_UP_10_TEXT.add("也不知道在讲啥小秘密");
        HANG_UP_10_TEXT.add("真是羡煞旁人");

        HANG_UP_30_TEXT.add("他俩情投意合");
        HANG_UP_30_TEXT.add("真是耳鬓厮磨");
        HANG_UP_30_TEXT.add("也不知道手酸不酸");

        HANG_UP_60_TEXT.add("他俩无所不谈");
        HANG_UP_60_TEXT.add("恋恋不舍不想挂电话");
        HANG_UP_60_TEXT.add("大家猜猜是谁先挂电话");

        AUDIT_TAG_PASS_TEXT.add("快去看看吧");
        AUDIT_TAG_PASS_TEXT.add("速度围观");
        AUDIT_TAG_PASS_TEXT.add("掌声在哪里");

        PRAISE_TAG_TEXT.add("标签被很多人点赞，快去围观吧");
        PRAISE_TAG_TEXT.add("标签被赞爆了，怎么可以如此受欢迎");
        PRAISE_TAG_TEXT.add("标签收到广大鹊友的点赞，不去康康吗？");

        SPECIAL_DATE_STR.add("1940-06-03");
        SPECIAL_DATE_STR.add("1941-03-16");
        SPECIAL_DATE_STR.add("1986-05-04");
        SPECIAL_DATE_STR.add("1987-04-12");
        SPECIAL_DATE_STR.add("1988-04-10");
        SPECIAL_DATE_STR.add("1989-04-16");
        SPECIAL_DATE_STR.add("1990-04-15");
        SPECIAL_DATE_STR.add("1991-04-14");

        WE_CHAT_PREFIX_LIST.add("wxid_");
        WE_CHAT_PREFIX_LIST.add("wxi_");
        WE_CHAT_PREFIX_LIST.add("wxid");

        FAMILY_CUSTOM_TAG.add("红包多");
        FAMILY_CUSTOM_TAG.add("礼物多");
        FAMILY_CUSTOM_TAG.add("爆照多");

        FAMILY_GRID.add("config/app/familyGrid31.jpg");
        FAMILY_GRID.add("config/app/familyGrid32.jpg");
        FAMILY_GRID.add("config/app/familyGrid33.jpg");
        FAMILY_GRID.add("config/app/familyGrid34.jpg");
        FAMILY_GRID.add("config/app/familyGrid35.jpg");
        FAMILY_GRID.add("config/app/familyGrid36.jpg");
        FAMILY_GRID.add("config/app/familyGrid37.jpg");
        FAMILY_GRID.add("config/app/familyGrid38.jpg");
        FAMILY_GRID.add("config/app/familyGrid39.jpg");
        FAMILY_GRID.add("config/app/familyGrid40.jpg");


        NEW_FAMILY_GRID.add("config/app/familyGrid41.png");
        NEW_FAMILY_GRID.add("config/app/familyGrid42.png");
        NEW_FAMILY_GRID.add("config/app/familyGrid43.png");
        NEW_FAMILY_GRID.add("config/app/familyGrid44.png");
        NEW_FAMILY_GRID.add("config/app/familyGrid45.png");
        NEW_FAMILY_GRID.add("config/app/familyGrid46.png");
        NEW_FAMILY_GRID.add("config/app/familyGrid47.png");
        NEW_FAMILY_GRID.add("config/app/familyGrid48.png");
        NEW_FAMILY_GRID.add("config/app/familyGrid49.png");
        NEW_FAMILY_GRID.add("config/app/familyGrid50.png");


        FAMILY_RANDOM_TEXT.add("多人免费聊天，让快乐加倍");
        FAMILY_RANDOM_TEXT.add("同城交友相亲，聊天更轻松");
        FAMILY_RANDOM_TEXT.add("免费语音房，多人上麦嗨翻天");
        FAMILY_RANDOM_TEXT.add("语音连麦，轻松恋爱");
        FAMILY_RANDOM_TEXT.add("全民抢红包，天天过大年");
        FAMILY_RANDOM_TEXT.add("天天抢红包，随时有大奖");

        FAMILY_REDPACKET_RANDOM_TEXT.add("红包来了，快来抢红包！");
    }

    /**
     * 获取全局唯一的ID
     */
    public static String getGlobalUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 组装微信unionId
     */
    public static String buildAccountId(String unionId) {
        return unionId + "_unionid";
    }

    /**
     * 是否为非法的昵称
     *
     * @param name
     * @return
     */
    public static boolean isIllegalNickname(String name) {
        // 违规字符替换
        boolean replace = !name.equals(repalceNicknameSpecialChars(name));
        if (replace) {
            return true;
        }
        return false;
    }

    /**
     * 是否为非法的文本
     *
     * @param text
     * @return
     */
    public static boolean isIllegalText(String text) {
        return !text.equals(BusiUtils.filterSpecialChars(text));
    }

    /**
     * 替换转义字符和移除非法字符
     */
    private static String repalceNicknameSpecialChars(String source) {
        String filterSpecialChars = BusiUtils.filterSpecialChars(source);
        String result = BusiUtils.escapeChars(filterSpecialChars);
        return result;
    }

    /**
     * 过滤掉特殊国家的敏感词
     *
     * @param source 源字符串
     */
    public static String filterSpecialChars(String source) {
        if (source == null) {
            return source;
        }
        Matcher matcher = CHARS_SPECIAL_PATTERN.matcher(source);
        if (matcher.find()) {
            source = matcher.replaceAll("");
        }
        CharMatcher charsToPreserve = CharMatcher.anyOf("\r\n\t");
        CharMatcher allButPreserved = charsToPreserve.negate();
        CharMatcher controlCharactersToRemove = CharMatcher.javaIsoControl().and(allButPreserved);
        return controlCharactersToRemove.removeFrom(source);
    }

    /**
     * 将影响客户端显示的字符进行转义，比如含有网页标签内容转义
     */
    public static String escapeChars(String source) {
        Set<String> keys = CHARS_EXCHANGE_MAPPING.keySet();
        for (String text : keys) {
            source = source.replaceAll(text, CHARS_EXCHANGE_MAPPING.get(text));
        }
        return source;
    }

    /**
     * 敏感业务数据加密
     * 《 MphkwqQ2K 》
     * @param content
     * @return
     */
    public static String encrypt(String content) {
        return EncryptUtils.encryptByMysqlAES(content, DataKeyConfig.DATABASE);
    }

    /**
     * 敏感业务数据解密
     *
     * @param content
     * @return
     */
    public static String decrypt(String content) {
        return EncryptUtils.decryptByMysqlAES(content, DataKeyConfig.DATABASE);
    }


    /**
     * 根据身份证号码计算年龄 -1 非法年龄 异常结果
     *
     * @param idNumber
     */

    public static int getAgeByIdNumber(String idNumber) {
        if (StringUtils.isEmpty(idNumber)) {
            return 18;
        }
        String dateStr;
        if (idNumber.length() == 15) {
            dateStr = "19" + idNumber.substring(6, 12);
        } else if (idNumber.length() == 18) {
            dateStr = idNumber.substring(6, 14);
        } else {//默认是合法身份证号，但不排除有意外发生
            return -1;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        try {
            Date birthday = simpleDateFormat.parse(dateStr);
            Integer age = getAgeByDate(birthday);
            return null == age ? 18 : age;
        } catch (ParseException e) {
            return -1;
        }
    }

    /**
     * 计算和18岁的差值
     *
     * @param birthDay
     * @return
     */
    public static int getDiffAdultAge(Date birthDay) {
        Date ageDate = DateUtils.plusYear(new Date(), -18);
        Integer ageYear = DateUtils.getYear(ageDate);
        Integer birthYear = DateUtils.getYear(birthDay);
        if (ageYear.equals(birthYear)) {
            // 月份不足18岁  直接加一年
            return 1;
        }
        return ageYear - birthYear;
    }

    /**
     * 根据生日计算年龄
     *
     * @param birthday 这样格式的生日 yyyyMMdd
     */
    public static Integer getAgeByDate(Date birthday) {
        if (null == birthday) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        if (calendar.getTimeInMillis() - birthday.getTime() < 0L) {
            return null;
        }
        int yearNow = calendar.get(Calendar.YEAR);
        int monthNow = calendar.get(Calendar.MONTH);
        int dayOfMonthNow = calendar.get(Calendar.DAY_OF_MONTH);
        calendar.setTime(birthday);
        int yearBirthday = calendar.get(Calendar.YEAR);
        int monthBirthday = calendar.get(Calendar.MONTH);
        int dayOfMonthBirthday = calendar.get(Calendar.DAY_OF_MONTH);
        int age = yearNow - yearBirthday;
        // 生日月份相等并且生日天数大于当前天数,还未过生日,减一; 生日月份大于当前月份,还未过生日 -1
        if ((monthNow == monthBirthday && dayOfMonthNow < dayOfMonthBirthday) || monthNow < monthBirthday) {
            age--;
        }
        return age;
    }

    /**
     * 根据身份证号获取性别(调用方法之前要做身份证号的校验)
     *
     * @param idCard
     * @return
     */
    public static SexType getSexByIdCard(String idCard) {
        if (StringUtils.isEmpty(idCard)) {
            return null;
        }
        // 18位身份证号
        if (idCard.length() == EIGHTEEN_ID_CARD) {
            // 判断性别
            if (Integer.parseInt(idCard.substring(16).substring(0, 1)) % 2 == 0) {
                return SexType.Female;
            } else {
                return SexType.Male;
            }
            // 15位身份证号
        } else if (idCard.length() == FIFTEEN_ID_CARD) {
            if (Integer.parseInt(idCard.substring(14, 15)) % 2 == 0) {
                return SexType.Female;
            } else {
                return SexType.Male;
            }
        }
        return null;
    }

    /**
     * 获取随机生日
     *
     * @return
     */
    public static Date getBirthday(SexType sex) {
        // 25-35岁(男) 20-32岁(女)
        int age = SexType.Male == sex ? RandomUtils.getInt(0, 10) + 25 : RandomUtils.getInt(0, 12) + 20;
        return DateUtils.plusYear(new Date(), -age);
    }

    /**
     * 获取随机昵称
     */
    public static String getNickname(String nickname) {
        if (StringUtils.isNotEmpty(nickname)) {
            return nickname;
        }
        int random = RandomUtils.getInt(0, 1000000);
        String formatId = StringUtils.leftPad(String.valueOf(random), 6, "0");
        return "Mt@" + formatId;
    }

    /**
     * 根据用户编号判断是否是游客
     */
    public static Boolean isVisitUser(Long userId) {
        if (userId == null) {
            return null;
        }
        return userId < 0;
    }

    /**
     * 将SessionId转成成Tok
     * DataKeyConfig.SESSION(dev) = 1|4|7|13|17|28
     *
     * @param sessionId
     * @return
     */
    public static String sessionIdToTok(String sessionId) {
        return EncryptUtils.encryptBySIM(sessionId, DataKeyConfig.SESSION);
    }

    /**
     * 将Tok转换成SessionId
     *
     * @param tok
     * @return
     */
    public static String tokToSessionId(String tok) {
        return EncryptUtils.decryptBySIM(tok, DataKeyConfig.SESSION);
    }


    /**
     * 检查传入的客户端类型是否被包含在指定列表中
     *
     * @param clientTypes 多个，使用逗号分割
     * @param clientType  客户端类型
     * @return
     */
    public static Boolean isMatchClientType(String clientTypes, ClientType clientType) {
        if (StringUtils.isBlank(clientTypes) || clientTypes.equals(ClientType.All.getId())) {
            return true;
        }
        if (clientType == null) {
            return false;
        }
        String target = clientType.getId();
        return StringUtils.containsTarget(clientTypes, target);
    }

    /**
     * 组装新的的H5活动路径
     *
     * @param configUrl
     * @return
     */
    public static String buildH5ActiveUrl(ClientTouchType touchType, String configUrl) {
        if (StringUtils.isEmpty(configUrl)) {
            return configUrl;
        }
        if (ClientTouchType.Url != touchType) {
            return configUrl;
        }
        if (configUrl.startsWith("http")) {
            return configUrl;
        }
        if (UnifiedConfig.isProdEnv()) {
            return AppConfig.H5_URL + configUrl;
        }
        return AppConfig.H5_URL + GlobalConstant.TEST_H5_FRONT_FILE_URL + configUrl;
    }

    /**
     * 物品tips 封装
     *
     * @param prodConfigTips
     * @return
     */
    public static Map<String, String> buildTipsByProd(String prodConfigTips) {
        Map<String, String> tips = UrlParamsMap.build(prodConfigTips).getRawMap();
        String type = tips.get("touchType");
        if (ClientTouchType.Url.name().equals(type)) {
            ClientTouchType touchType = EnumUtils.byId(type, ClientTouchType.class);
            String touchValue = tips.get("touchValue");
            if (StringUtils.isNotEmpty(touchValue) && touchValue.endsWith("?nav")) {
                touchValue = touchValue + "=n";
            }
            tips.put("touchValue", URLDecoder.decode(BusiUtils.buildH5ActiveUrl(touchType, touchValue)));
        }
        return tips;
    }


    /**
     * 根据手机型号匹配对应首页广告图片
     */
    public static String buildAdResUrlByDeviceMode(String deviceMode, AdDefineDTO adDefine) {
        if (deviceMode.contains("iPhone") && deviceMode.contains("X")) {
            // iPhone X
            return StringUtils.getQueryString(adDefine.getResUrl(), "ipx");
        } else if (deviceMode.contains("iPhone") && deviceMode.contains("Plus")) {
            // iPhone Plus
            return StringUtils.getQueryString(adDefine.getResUrl(), "iplus");
        }
        return adDefine.getResUrl();
    }


    /**
     * 创建勋章列表
     *
     * @param officalManagerPic 官方
     * @param roomGuardPic      守护
     * @param roomManagerPic    房管
     * @param badgesPic         其他勋章
     * @return
     */
    public static LinkedList<String> createBadgesPic(String officalManagerPic, String roomGuardPic, String roomManagerPic, List<String> badgesPic) {
        LinkedList<String> picList = new LinkedList<>();
        // 为了确保守护、官方等图标不重复，并保证次序优先级
        List<String> tmp = new ArrayList<>();
        if (badgesPic != null) {
            tmp.addAll(badgesPic);
        }
        if (StringUtils.isNotEmpty(officalManagerPic) && !tmp.contains(officalManagerPic)) {
            picList.add(officalManagerPic);
        }
        if (StringUtils.isNotEmpty(roomGuardPic) && !tmp.contains(roomGuardPic)) {
            picList.add(roomGuardPic);
        }
        if (StringUtils.isNotEmpty(roomManagerPic) && !tmp.contains(roomGuardPic)) {
            picList.add(roomManagerPic);
        }
        if (badgesPic != null) {
            picList.addAll(badgesPic);
        }
        return picList;
    }

    /**
     * 常规收益计算
     * 不管真人实名、固定提成比例
     */
    public static Pair<Long, Long> presentCommonCash(long totalBeans, long platformBeans){
        long rechargeCash = new BigDecimal((totalBeans - platformBeans)  * GlobalConstant.RESOURCE_PRESENT_RATE * GlobalConstant.RECHARGE_BEAN_EXCHANGE_CASH).longValue();
        long platformCash = new BigDecimal(platformBeans / GlobalConstant.PLATFORM_BEAN_EXCHANGE_RATE  * GlobalConstant.RESOURCE_PRESENT_RATE * GlobalConstant.RECHARGE_BEAN_EXCHANGE_CASH).longValue();
        return Pair.with(rechargeCash + platformCash, platformCash);
    }

    /**
     * 将金币转成零钱
     *
     * @param beans
     * @return
     */
    public static Long beansToCash(double beans) {
        return new BigDecimal(beans * GlobalConstant.RECHARGE_BEAN_EXCHANGE_CASH).longValue();
    }

    /**
     * 将金币转成零钱
     *
     * @param beans
     * @return
     */
    public static Integer beansToCashInt(double beans) {
        return BusiUtils.beansToCash(beans).intValue();
    }

    /**
     * 将已放大倍数的整数零钱转成元(小数位为0需保留)
     *
     * @param cash
     * @return
     */
    public static String cashToYuanStr(Long cash) {
        return BusiUtils.cashToYuanStr(cash, 2);
    }

    /**
     * 将已放大倍数的整数零钱转成元(小数位为0需保留)
     *
     * @param cash
     * @param precision 保留小数位
     * @return
     */
    public static String cashToYuanStr(Long cash, Integer precision) {
        if (cash == null) {
            return "0";
        }
        return NumberUtils.formatStyle1WithRoundingDown(cash / CASH_SCALE, precision);
    }

    /**
     * 将已放大倍数的整数零钱转成元(小数位为0不保留)
     *
     * @param cash
     * @param precision 保留小数位
     * @return
     */
    public static String cashToYuanSimplifyStr(Long cash, Integer precision) {
        if (cash == null) {
            return "0";
        }
        return NumberUtils.formatWithRoundingDown(cash / CASH_SCALE, precision);
    }

    /**
     * 将已放大倍数的整数零钱转成元 向上取整
     *
     * @param cash
     * @return
     */
    public static Long cashToYuan(Long cash) {
        if (cash == null) {
            return 0L;
        }
        return cash / 10000;
    }

    /**
     * 将已放大倍数的整数零钱转成分 向上取整
     *
     * @param cash
     * @return
     */
    public static Long cashToFen(Long cash) {
        if (cash == null) {
            return 0L;
        }
        return cash / 100;
    }

    /**
     * 金币转换为元保留两位小数
     *
     * @param beans
     * @return
     */
    public static Double beansToYuan(Integer beans) {
        if (beans == null) {
            return 0.0;
        }
        return NumberUtils.rounding(beans / 10D);
    }

    /**
     * 将元转成零钱
     *
     * @param money
     * @return
     */
    public static Long yuanToCash(Double money) {
        return new BigDecimal(money * CASH_SCALE).longValue();
    }

    /**
     * 将元转成零钱
     *
     * @param money
     * @return
     */
    public static Long yuanToCash(String money) {
        return yuanToCash(Double.valueOf(money));
    }

    /**
     * 将元转成零钱
     *
     * @param money
     * @return
     */
    public static Long yuanToCash(Integer money) {
        return new BigDecimal(money * CASH_SCALE).longValue();
    }

    /**
     * 将元转成分
     *
     * @param money
     * @return
     */
    public static Long yuanToFen(Double money) {
        return new BigDecimal(money * 100).longValue();
    }


    /**
     * 将分转成元
     *
     * @param fen
     * @return
     */
    public static Long fenToYuan(Long fen) {
        return new BigDecimal(fen / 100).longValue();
    }

    /**
     * 将分转成元
     *
     * @param fen
     * @return
     */
    public static Double fenToYuan1(Long fen) {
        return new BigDecimal(fen / 100D).doubleValue();
    }

    /**
     * 根据好友双方ID生成一个好友关系ID
     * ID 小的放前面
     */
    public static String generateRelationId(Long userId, Long friendId) {
        if (userId < friendId) {
            return userId + "_" + friendId;
        }
        return friendId + "_" + userId;
    }

    /**
     * 根据生日获取星座
     *
     * @param birthday
     * @return
     */
    public static ConstellationType getConstellation(Date birthday) {
        if (birthday == null) {
            return null;
        }
        List<ConstellationType> constellationList = ConstellationType.getTypeList();
        List<String> idList = constellationList.stream().map(ConstellationType::getId).collect(Collectors.toList());
        idList.add(ConstellationType.Capricornus.getId());
        String[] constellationArr = idList.toArray(new String[0]);
        int month = DateUtils.getMonth(birthday);
        int day = DateUtils.getDay(birthday);
        String constellationId = day < DAY_ARR[month] ? constellationArr[month - 1] : constellationArr[month];
        return EnumUtils.byId(constellationId, ConstellationType.class);
    }

    /**
     * 游客状态下获取客服地址
     *
     * @return
     */
    public static String getVisitCustomerUrl() {
        // 接口路径 生成游客session 且跳转路径
        return AppConfig.API_URL + "/ccs/svc/visitor";
    }

    /**
     * 经纬度获取距离 单位m
     *
     * @param lat
     * @param lng
     * @param lat2
     * @param lng2
     * @return
     */
    public static Long getDistance(String lat, String lng, String lat2, String lng2) {
        Long distance = null;
        if (StringUtils.isEmpty(lat) || StringUtils.isEmpty(lng) || StringUtils.isEmpty(lat2) || StringUtils.isEmpty(lng2)) {
            return null;
        }
        try {
            // 两个城市的经纬度
            Point2D pointCity = new Point2D.Double(Double.parseDouble(lng), Double.parseDouble(lat));
            Point2D pointOtherCity = new Point2D.Double(Double.parseDouble(lng2), Double.parseDouble(lat2));
            Double distanceD = getDistanceDouble(pointCity, pointOtherCity);
            // 四舍五入取整
            BigDecimal tmp = new BigDecimal(distanceD).setScale(0, BigDecimal.ROUND_HALF_UP);
            distance = tmp.longValue();
        } catch (Exception e) {
            LOG.error(MsgUtils.format("经纬度计算距离出错,lat:{},lng:{},lat2:{},lng2:{}，原因：{}", lat, lng, lat2, lng2, e));
        }
        return distance;
    }

    public static void main(String[] args) throws Exception{
        //System.out.println(EncryptUtils.md5("c0d71564cd4e4bf0b87343a27277e5122b77a029794b276cb20b2b9d31e896ee"));
        //System.out.println(StringUtils.getUUID());
        //System.out.println("FC:B2:BD:3B:47:C9:3E:0F:2D:D4:E4:6E:6A:15:D7:1C".replaceAll(":", "").toLowerCase());

        // 文件修改
        /*File folder = new File("C:\\Users\\<USER>\\Desktop\\思会\\UI\\femalePic"); // 设置图片所在文件夹的路径
        File[] listOfFiles = folder.listFiles();

        if (listOfFiles == null) {
            return;
        }

        int counter = 1;
        for (File file : listOfFiles) {
            if (file.isFile() && file.getName().endsWith(".jpg")) { // 修改为你的图片格式，例如.png, .jpeg
                String newName = "female_head_" + counter + ".jpg"; // 新的文件名格式
                File newFile = new File(folder + File.separator + newName);
                boolean b = file.renameTo(newFile);
                counter++;
            }
        }
        System.out.println(1111111);*/
    }


    /**
     * 经纬度获取距离 单位Km
     *
     * @param lat
     * @param lng
     * @param lat2
     * @param lng2
     * @return
     */
    public static Double getDistanceKm(String lat, String lng, String lat2, String lng2) {
        Double distanceKm = null;
        if (StringUtils.isEmpty(lat) || StringUtils.isEmpty(lng) || StringUtils.isEmpty(lat2) || StringUtils.isEmpty(lng2)) {
            return null;
        }
        try {
            // 两个城市的经纬度
            Point2D pointCity = new Point2D.Double(Double.parseDouble(lng), Double.parseDouble(lat));
            Point2D pointOtherCity = new Point2D.Double(Double.parseDouble(lng2), Double.parseDouble(lat2));
            Double distanceD = getDistanceDouble(pointCity, pointOtherCity);
            // 保留两位小数
            BigDecimal tmp = new BigDecimal(distanceD / 1000).setScale(2, BigDecimal.ROUND_HALF_UP);
            distanceKm = tmp.doubleValue();
            // 小于0km,返回0.01Km
            distanceKm = distanceKm <= 0D ? 0.01D : distanceKm;
        } catch (Exception e) {
            LOG.error(MsgUtils.format("经纬度计算Km距离出错,lat:{},lng:{},lat2:{},lng2:{}，原因：{}", lat, lng, lat2, lng2, e));
        }
        return distanceKm;
    }


    /**
     * 计算城市距离
     * 单位：米
     *
     * @param pointA
     * @param pointB
     * @return
     */
    private static Double getDistanceDouble(Point2D pointA, Point2D pointB) {
        // 经纬度（角度）转弧度。弧度用作参数，以调用Math.cos和Math.sin
        double radiansAX = Math.toRadians(pointA.getX()); // A经弧度
        double radiansAY = Math.toRadians(pointA.getY()); // A纬弧度
        double radiansBX = Math.toRadians(pointB.getX()); // B经弧度
        double radiansBY = Math.toRadians(pointB.getY()); // B纬弧度

        // 公式中“cosβ1cosβ2cos（α1-α2）+sinβ1sinβ2”的部分，得到∠AOB的cos值
        double cos = Math.cos(radiansAY) * Math.cos(radiansBY) * Math.cos(radiansAX - radiansBX) + Math.sin(radiansAY) * Math.sin(radiansBY);
        // 反余弦值
        Double acos = Math.acos(cos);
        if (acos.isNaN()) {
            return 0D;
        }
        // 最终结果
        return EARTH_RADIUS * acos;
    }

    /**
     * 主页获取真人标识图标
     *
     * @param realPeople
     * @return
     */
    public static String getHomeRealIcon(BoolType realPeople) {
        if (realPeople.boolValue()) {
            return SMALL_REAL_PEOPLE_AUTH_MARK;
        }
        return NO_REAL_PEOPLE;
    }

    /**
     * 主页获取贵族等级图标
     *
     * @param royalLevel
     * @return
     */
    public static String getHomeRoyalIcon(Integer royalLevel) {
        if (royalLevel == null || royalLevel == 0) {
            return NO_ROYAL_LEVEL_PIC;
        }
        return MsgUtils.format(ROYAL_LEVEL_PIC, royalLevel);
    }

    /**
     * 获取家族等级图标
     *
     * @param familyLevel
     * @return
     */
    public static String getFamilyLevelPic(Integer familyLevel) {
        if (familyLevel == null) {
            return null;
        }
        return MsgUtils.format(FAMILY_LEVEL_PIC, familyLevel);
    }

    /**
     * 获取财富等级图标
     */
    public static String getUserLevelPic(Integer userLevel) {
        if (userLevel == null) {
            return null;
        }
        return MsgUtils.format(USER_LEVEL_PIC, userLevel);
    }

    /**
     * 获取魅力等级图标
     */
    public static String getCharmLevelPic(Integer charmLevel) {
        if (charmLevel == null) {
            return null;
        }
        return MsgUtils.format(CHARM_LEVEL_PIC, charmLevel);
    }

    /**
     * 过滤默认昵称敏感词
     *
     * @param nickname
     * @return
     */
    private static String filterNicknameSensitiveWord(String nickname) {
        for (String word : NICKNAME_SENSITIVE_WORDS) {
            nickname = nickname.replace(word, RandomUtils.randomAlphabetic(word.length()).toUpperCase());
        }
        return nickname;
    }

    /**
     * 时间格式化
     */
    public static String formatTime(Date minTime, Date maxTime, String suffix, DatePattern datePattern){
        Long secondDuration = DateUtils.getDiffSeconds(minTime, maxTime);
        // 时间差小于1小时显示多少分钟前
        if (secondDuration < 3600) {
            return MsgUtils.format("{}分钟前{}", Math.max(1, secondDuration / 60), suffix);
        }
        // 时间差小于1天显示多少小时前
        if (secondDuration < 24 * 3600) {
            return MsgUtils.format("{}小时前{}", secondDuration / 3600);
        }

        return DateUtils.toString(minTime, datePattern);
    }

    /**
     * 在线离线时间格式化
     *
     * @param duration
     * @return
     */
    public static String formatOfflineTime(Long duration) {
        // 离线时间小于1小时显示多少分钟前在线
        if (duration < 3600) {
            return MsgUtils.format("{}分钟前在线", Math.max(1, duration / 60));
        }
        // 离线时间小于1天小时多少小时前在线
        if (duration < 24 * 3600) {
            return MsgUtils.format("{}小时前在线", duration / 3600);
        }
        return "离线";
    }

    /**
     * 时间格式换
     */
    public static String formatTime01(Date date) {
        Long duration = DateUtils.getDiffSeconds(date);
        // 时间小于1小时显示多少分钟前在线
        if (duration < 3600) {
            return MsgUtils.format("{}分钟前", Math.max(1, duration / 60));
        }
        // 时间小于1天小时多少小时前在线
        if (duration < 24 * 3600) {
            return MsgUtils.format("{}小时前", duration / 3600);
        }
        return DateUtils.toString(date, DatePattern.YMD_HM);
    }

    /**
     * 获取图片昵称水印
     *
     * @param nickname
     * @return
     */
    public static String getPicWaterMarkByNickName(String nickname) {
        nickname = "玖LOVE@" + nickname;
        return MsgUtils.format(WATER_MARK_PARAM, Base64Utils.encodeToUrlSafeString(nickname.getBytes()));
    }


    /**
     * 换算体质
     *
     * @param originalHeight
     * @param originalWeight
     * @return
     */
    public static BmiLevelType getBmiLevelType(Integer originalHeight, Integer originalWeight) {
        if (null == originalHeight || null == originalWeight) {
            return null;
        }
        Double weight = originalWeight * 1D;
        Double height = originalHeight / 100D;
        BigDecimal tmp = new BigDecimal(height).setScale(2, BigDecimal.ROUND_HALF_UP);
        height = tmp.doubleValue();
        // 体质 = 体重(kg) / (身高m * 身高m)
        Double bmi = weight / (height * height);
        Integer bmiScore = bmi.intValue();
        BmiLevelType[] types = BmiLevelType.values();
        for (BmiLevelType type : types) {
            if (bmiScore > type.getMin() && bmiScore <= type.getMax()) {
                return type;
            }
        }
        return null;
    }


    /**
     * 判断是否直辖市
     *
     * @param province
     * @return
     */
    public static boolean isMunicipalityCity(String province) {
        if (province.startsWith("北京") || province.startsWith("天津") || province.startsWith("上海") || province.startsWith("重庆")) {
            return true;
        }
        return false;
    }

    /**
     * 获取两个用户之间的用户区域显示信息
     */
    public static UserDistanceDTO getAreaInfo(UUserLocation location, UUserLocation otherLocation) {
        if (otherLocation == null) {
            return null;
        }
        // 都没有省市信息
        if (StringUtils.isEmpty(otherLocation.getProvince()) || StringUtils.isEmpty(otherLocation.getCity())) {
            if (StringUtils.isNotEmpty(otherLocation.getCity())) {
                return new UserDistanceDTO(BusiUtils.getSimpleCity(otherLocation.getCity()), BoolType.False);
            }
            return null;
        }
        // 简易省份城市转化
        String otherProvince = BusiUtils.getSimpleProvince(otherLocation.getProvince());
        String otherCity = BusiUtils.getSimpleCity(otherLocation.getCity());
        // 当前用户没有定位信息
        if (location == null) {
            return new UserDistanceDTO(otherProvince + " " + otherCity, BoolType.False);
        }
        // 当前用户没有省市信息
        if (StringUtils.isEmpty(location.getProvince()) || StringUtils.isEmpty(location.getCity())) {
            return new UserDistanceDTO(otherProvince + " " + otherCity, BoolType.False);
        }
        String otherDistrict = StringUtils.overflowString(otherLocation.getDistrict(), 4);
        // 同一个城市，显示对应所在的县/区
        if (location.getCity().equals(otherLocation.getCity())) {
            if (StringUtils.isEmpty(otherDistrict)) {
                return new UserDistanceDTO(otherLocation.getCity(), BoolType.True);
            } else {
                return new UserDistanceDTO(otherDistrict, BoolType.True);
            }
        }
        if (isMunicipalityCity(otherLocation.getProvince())) {
            // 不同市，但是对方是如果是直辖市,显示到市 + 区
            return new UserDistanceDTO(otherCity + " " + (otherDistrict == null ? "" : otherDistrict), BoolType.False);
        }
        // 同省不同市，显示对方所在的市
        if (location.getProvince().equals(otherLocation.getProvince())) {
            return new UserDistanceDTO(otherCity, BoolType.False);
        }
        // 不同市，不同省，显示省市
        return new UserDistanceDTO(otherProvince + " " + otherCity, BoolType.False);
    }

    /**
     * 获取简易城市
     *
     * @param city
     * @return
     */
    public static String getSimpleCity(String city) {
        if (StringUtils.isEmpty(city)) {
            return null;
        }
        return StringUtils.overflowString(StringUtils.substringBefore(city, "市"), 7);
    }

    /**
     * 获取简易城市
     *
     * @param city
     * @param end  保留字数
     * @return
     */
    public static String getSimpleCityByEnd(String city, Integer end) {
        if (StringUtils.isEmpty(city)) {
            return null;
        }
        String result = getSimpleCity(city);
        return StringUtils.substring(result, 0, end);
    }

    /**
     * 获取简易省份
     *
     * @param province
     * @return
     */
    public static String getSimpleProvince(String province) {
        for (ProvinceSimpleType type : ProvinceSimpleType.values()) {
            if (type.getProvince().contains(province)) {
                return type.getSimple();
            }
        }
        return StringUtils.substringBefore(province, "省");
    }


    /**
     * 是否正常的定位数据
     *
     * @param locationData
     * @return
     */
    public static boolean isInValidLocationData(String locationData) {
        if (StringUtils.isEmpty(locationData)) {
            return true;
        }
        if (locationData.indexOf("E") > 0) {
            return true;
        }
        return false;
    }

    /**
     * 增加指定天数到某天开始 eg:3.2 13:20 +14  -> 3.16 00:00
     *
     * @param date
     * @param days
     * @return
     */
    public static Date toTargetStartOfDay(Date date, int days) {
        Date now = new Date();
        Date begin = DateUtils.getStartOfDay(now);
        Date after = DateUtils.plusDays(begin, 15);
        return after;
    }

    /**
     * 是否默认头像
     */
    public static boolean isDefaultHeadPic(String headPic, SexType sexType) {
        if (sexType == SexType.Female) {
            return GlobalConstant.DEFAULT_HEAD_PIC_FEMALE.equals(headPic);
        }
        return GlobalConstant.DEFAULT_HEAD_PIC_MALE.equals(headPic);
    }

    /**
     * 获取默认头像
     *
     * @param sexType
     * @return
     */
    public static String getDefaultHeadPic(SexType sexType) {
        if (sexType == SexType.Female) {
            return GlobalConstant.DEFAULT_HEAD_PIC_FEMALE;
        }
        return GlobalConstant.DEFAULT_HEAD_PIC_MALE;
    }

    /**
     * 拼接用户昵称和ID
     *
     * @param name
     * @param id
     * @return
     */
    public static String concatNameAndId(String name, Long id) {
        return MsgUtils.format("{}【{}】", name, id);
    }

    /**
     * 拼接字符串1,2
     *
     * @param name1
     * @param name2
     * @return
     */
    public static String concatNameAndName(String name1, String name2) {
        return MsgUtils.format("{}【{}】", name1, name2);
    }

    /**
     * 获取匿名昵称
     *
     * @param name
     * @return
     */
    public static String getAnonymousNickName(String name) {
        if (StringUtils.isEmpty(name)) {
            return "***";
        }
        String result = name.substring(0, 1);
        if (name.length() >= 5) {
            return result + "****";
        }
        if (name.length() <= 1) {
            return result + "*";
        }
        for (int i = 0; i < name.length() - 1; i++) {
            result += "*";
        }
        return result;
    }

    /**
     * 身份证是否成年
     *
     * @param idCard
     * @return
     */
    public static boolean isAdult(String idCard) {
        int age = getAgeByIdNumber(idCard);
        if (age < 18) {
            return false;
        }
        return true;
    }

    /**
     * 是否是纯数字
     */
    public static boolean isAllNumber(String str){
        return ALL_NUMBER.matcher(str).matches();
    }

    /**
     * 是否仅包含汉字+数字
     */
    public static boolean isAllChinaAndNumber(String str){
        return ALL_CHINA_NUMBER.matcher(str).matches();
    }

    /**
     * 是否微信号
     *
     * @param weChat
     * @return
     */
    public static boolean isWeChat(String weChat) {
        Matcher matcher = WE_CHAT_PATTERN.matcher(weChat);
        boolean weChatSuc = matcher.matches();
        if (!matcher.matches()) {
            return MOBILE_PATTERN.matcher(weChat).matches();
        }
        return weChatSuc;
    }

    /**
     * 是否微信号wxid_ 前缀
     *
     * @param weChat
     * @return
     */
    public static boolean isWeChatWeiXinId(String weChat) {
        for (String idStr : WE_CHAT_PREFIX_LIST) {
            if (weChat.startsWith(idStr)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否手机号
     *
     * @param mobile
     * @return
     */
    public static boolean isMobileNumber(String mobile) {
        return MOBILE_PATTERN.matcher(mobile).matches();
    }

    /**
     * 是否是合规的邀请码
     */
    public static boolean isInviteCode(String inviteCode){
        inviteCode = inviteCode.trim();
        if(StringUtils.isEmpty(inviteCode) || inviteCode.length() != 6){
            return false;
        }
        return INVITE_CODE_PATTERN.matcher(inviteCode).matches();
    }

    /**
     * 根据生日获取下一个生日的日期
     *
     * @param birthday
     * @return
     */
    public static Date getNextBirthday(Date birthday, Date nowDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(birthday);
        int nowYear = DateUtils.getYear(nowDate);
        calendar.set(Calendar.YEAR, nowYear);
        // 今年的生日
        Date nowYearBirthday = calendar.getTime();
        if (nowYearBirthday.before(nowDate)) {
            // 生日已经过了，取下一年的生日
            calendar.set(Calendar.YEAR, nowYear + 1);
            return calendar.getTime();
        } else {
            return nowYearBirthday;
        }
    }

    /**
     * 根据生日获取下一个生日的日期
     *
     * @param nowDate
     * @return
     */
    public static Date getNextValentineDay(Date nowDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 14);
        // 今年的生日
        Date nowMonthValentine = calendar.getTime();
        if (nowMonthValentine.before(nowDate)) {
            // 本月情人节已经过了，取下一个月的情人节
            calendar.set(Calendar.MONTH, DateUtils.getMonth(nowDate));
            return calendar.getTime();
        } else {
            return nowMonthValentine;
        }
    }


    /**
     * 获取下一个相爱纪念日
     *
     * @param loveDate
     * @param nowDate
     * @return
     */
    public static Date getNextLoveDay(Date loveDate, Date nowDate) {
        Long diffDays = DateUtils.getDiffDays(loveDate, nowDate);
        if (diffDays == null) {
            return null;
        }
        if (diffDays > 99) {
            // 超过99天，计算周年纪念日
            Integer diffYeas = DateUtils.getYear(nowDate) - DateUtils.getYear(loveDate);
            Date nextYearLoveDay = DateUtils.plusYear(loveDate, diffYeas);
            if (nextYearLoveDay.after(nowDate)) {
                return nextYearLoveDay;
            } else {
                // 今年的周年纪念日已经过去，往后再推一年
                return DateUtils.plusYear(loveDate, diffYeas + 1);
            }
        }
        if (diffDays > 52) {
            return DateUtils.plusDays(loveDate, 99);
        }
        if (diffDays > 30) {
            return DateUtils.plusDays(loveDate, 52);
        }
        if (diffDays > 10) {
            return DateUtils.plusDays(loveDate, 30);
        }
        return DateUtils.plusDays(loveDate, 10);
    }

    /**
     * 获取家族随机自定义标签
     *
     * @return
     */
    public static String getFamilyCustomTag() {
        Collections.shuffle(FAMILY_CUSTOM_TAG);
        return FAMILY_CUSTOM_TAG.get(0);
    }

    /**
     * 获取排序数据的顺位数据
     *
     * @param min     最小单位为 1
     * @param max
     * @param numList 升序排列的
     * @return
     */
    public static Integer getOrderNextNum(Integer min, Integer max, List<Integer> numList) {
        if (numList.size() == max) {
            // 已满员
            return null;
        }
        // 0位不存在则直接返回首位
        if (numList.get(0) != 0) {
            return 0;
        }
        Integer total = 0;
        for (int i = min; i <= max; i++) {
            total += i;
        }
        Integer numTotal = 0;
        for (Integer currNum : numList) {
            numTotal += currNum;
        }
        Integer diff = total - numTotal;
        List<Integer> result = new ArrayList<>();
        for (int i = min; i <= diff; i++) {
            if (numList.contains(i)) {
                continue;
            }
            if (i > max) {
                continue;
            }
            result.add(i);
        }
        if (ListUtils.isEmpty(result)) {
            return null;
        }
        result = result.stream().sorted().collect(Collectors.toList());
        return result.get(0);
    }

    /**
     * 是否windows操作系统
     *
     * @return
     */
    public static boolean isWindows() {
        String os = System.getProperty("os.name");
        if (os.toLowerCase().contains("window")) {
            return true;
        }
        return false;
    }


    /**
     * 是否为新用户(24小时内注册)
     *
     * @param createTime
     * @return
     */
    public static boolean isNewUser(Date createTime) {
        return DateUtils.getDiffHours(createTime) <= 24;
    }

    /**
     * 图片是否包含真人标识
     *
     * @param pic
     * @return
     */
    public static boolean checkPicRealPeople(String pic) {
        if (StringUtils.isEmpty(pic)) {
            return false;
        }
        return pic.contains(GlobalConstant.PIC_REAL_PEOPLE_MARK);
    }

    /**
     * 限制勋章返回数量
     *
     * @param badgesPic
     * @return
     */
    public static List<String> getLimitBagPics(List<String> badgesPic) {
        if (ListUtils.isEmpty(badgesPic)) {
            return badgesPic;
        }
        if (badgesPic.size() > 2) {
            return badgesPic.stream().limit(2).collect(Collectors.toList());
        }
        return badgesPic;
    }


    /**
     * 取出字符串的标点符号
     *
     * @param str
     * @return
     */
    public static String removeSymbol(String str) {
        return str.replaceAll("[\\pP‘’“”]", "");
    }


    /**
     * 时间获取干支年
     *
     * @param date
     * @return
     */
    public static String dataToGanZhiYear(Date date) {
        // 获取年份
        int intYear = DateUtils.getYear(date);
        String year = String.valueOf(intYear);
        // 天干 >> 年份末尾数字
        int gan = Integer.valueOf(year.substring(year.length() - 1));
        // 地支 >> 本年的 除以12 的余数
        int zhi = intYear % 12;
        // 干支年
        return year + " " + (GAN_ARRY[gan] + ZHI_ARRY[zhi]) + "年";
    }

    /**
     * 一定范围内获取随机年龄
     *
     * @param age
     * @param random
     * @param minAge
     * @param maxAge
     * @return
     */
    public static Integer randomAge(Integer age, Integer random, Integer minAge, Integer maxAge) {
        Integer randomAge = RandomUtils.getInt(age - random, age + random);
        if (randomAge <= minAge) {
            return minAge;
        }
        if (randomAge >= maxAge) {
            return maxAge;
        }
        return randomAge;
    }

    /**
     * 中文乱码解决
     *
     * @param s
     * @return
     */
    public static String toUtf8String(String s) {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            if (c <= 255) {
                builder.append(c);
            } else {
                byte[] b;
                try {
                    b = Character.toString(c).getBytes("utf-8");
                } catch (Exception ex) {
                    System.out.println(ex);
                    b = new byte[0];
                }
                for (int j = 0; j < b.length; j++) {
                    int k = b[j];
                    if (k < 0) {
                        k += 256;
                    }
                    builder.append("%").append(Integer.toHexString(k).toUpperCase());
                }
            }
        }
        return builder.toString();
    }

    /**
     * 格式化某个字符为特定字符 eg: 哈哈哈->***
     *
     * @param content
     * @param character
     * @return
     */
    public static String formatContentToCharacter(String content, String character) {
        if (StringUtils.isEmpty(content)) {
            return content;
        }
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < content.length(); i++) {
            builder.append(character);
        }
        return builder.toString();
    }

    /**
     * 包名获取邀友渠道
     *
     * @param packageType
     * @return
     */
    public static String getInviteChannelCode(PackageType packageType) {
        switch (packageType) {
            case YL:
                return GlobalConstant.XY_INVITE_CHANNEL_CODE;
        }
        return GlobalConstant.DEFAULT_CHANNEL_CODE;
    }

    /**
     * 格式化数值，小数位为0保留
     *
     * @param price
     * @return
     */
    public static String formatNumberStyle1(Double price) {
        if (price == null) {
            return null;
        }
        return new DecimalFormat("0.00").format(price);
    }

    /**
     * 格式化数值，小数位为0不保留
     *
     * @param price
     * @return
     */
    public static String formatNumberStyle2(Double price) {
        if (price == null) {
            return null;
        }
        return new DecimalFormat("0.##").format(price);
    }

    /**
     * 格式化数值，保留一位小数
     * eg:
     * 1.00 -> 1.0
     * 1D -> 1.0
     * 1.0 -> 1.0
     * 100.0 -> 100.0
     * 1.234 -> 1.2
     * 1.26 -> 1.3 向上取整
     * @param price
     * @return
     */
    public static String formatNumberStyle3(Double price) {
        if (price == null) {
            return null;
        }
        return new DecimalFormat("#.0").format(price);
    }

    /**
     * 根据微信用户性别标识获取性别枚举
     * 1：男性
     * 2：女性
     * 备注：匹配不到默认返回男性
     */
    public static SexType getSexTypeByWx(Integer sexSign) {
        if (sexSign != null && sexSign == 2) {
            return SexType.Female;
        }
        return SexType.Male;
    }

    /**
     * 判断是否距当前时间多少天内
     */
    public static boolean checkTimeWithIn(long day, Date registerDate) {
        if (registerDate == null) {
            return false;
        }
        return DateUtils.getDiffDays(registerDate, new Date()) < day;
    }

    /**
     * 根据TCity构建地址信息
     */
    public static String buildAddr(TCity city) {
        if (city == null) {
            return null;
        }
        return city.getCity();
    }

    /**
     * 根据TCity构建地址信息
     * 备注：返回 省、市 但是不带 省、市 这两个字
     */
    public static String buildAddrPlus(TCity city) {
        if (city == null) {
            return null;
        }
        try {
            String province = city.getProvince().contains("省") ? city.getProvince().substring(0, city.getProvince().length() - 1) : city.getProvince();
            String cityStr = city.getCity().contains("市") ? city.getCity().substring(0, city.getCity().length() - 1) : city.getCity();
            return MsgUtils.format("{} {}", province, cityStr);
        }catch (Exception e){
            // do nothing
        }
        return null;
    }

    /**
     * 构建身高信息
     * 178cm
     */
    public static String buildHeight(Integer height) {
        if (height == null) {
            return null;
        }
        return height + "cm";
    }

    /**
     * 构建体重信息
     * 80kg
     */
    public static String buildWeight(Integer weight) {
        if (weight == null) {
            return null;
        }
        return weight + "kg";
    }

    /**
     * 根据枚举构建常规返回信息
     */
    public static String buildCommonInfoByIDEnum(EnumUtils.IDEnum idEnum) {
        if (idEnum == null) {
            return null;
        }
        return idEnum.getDesc();
    }

    /**
     * 构建用户ES经纬度保存格式
     *
     * @param lng 经度
     * @param lat 纬度度
     */
    public static String buildUserLocForEs(String lng, String lat) {
        if (StringUtils.isEmpty(lng) || StringUtils.isEmpty(lat)) {
            return null;
        }
        return lat + "," + lng;
    }

    /**
     * 解析用户ES经纬度保存格式
     *
     * @return first=纬度
     * @return second=经度
     */
    public static Pair<String, String> parseUserLocForEs(String loc) {
        String[] locArray = loc.split(",");
        return Pair.with(locArray[0], locArray[1]);
    }

    /**
     * 构建图片完整路径
     */
    public static String buildPicFullPath(String picPath) {
        if (picPath.startsWith("http")) {
            return picPath;
        }
        return AppConfig.FILE_URL + picPath;
    }

    /**
     * 集合解析为字符串
     */
    public static String collToStr(Collection<?> coll, String split) {
        if (Objects.isNull(split) || Objects.isNull(coll) || coll.isEmpty()) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (Object item : coll) {
            sb.append(item.toString());
            sb.append(split);
        }
        String sbStr = sb.toString();
        return sbStr.substring(0, sbStr.length() - 1);
    }

    /**
     * 字符串解析为集合
     */
    public static List<String> strToList(String str, String split) {
        if (StringUtils.isEmpty(str) || Objects.isNull(split)) {
            return null;
        }
        String[] strArray = str.split(split);
        return Arrays.asList(strArray);
    }

    /**
     * 构建亲密度等级图地址
     */
    public static String buildIntimateLevelPic(Integer intimateLevel) {
        if (Objects.isNull(intimateLevel) || intimateLevel <= 0) {
            return null;
        }
        return MsgUtils.format(IconConstant.INTIMATE_LEVEL_PIC, intimateLevel);
    }

    /**
     * 构建动态d点赞数、打赏数、浏览数显示
     */
    public static String buildPostDisplayNum(Integer value) {
        if (Objects.isNull(value)) {
            return "0";
        }
        if (value < 10000) {
            return String.valueOf(value);
        }
        BigDecimal tmp = new BigDecimal(value / 10000.0).setScale(1, RoundingMode.HALF_UP);
        return new DecimalFormat("0.#").format(tmp.doubleValue()) + "W";
    }

    /**
     * 获取VIP类型
     *
     * @param user
     * @return
     */
    public static VipType getVipType(UUserBasic user) {
        if (VipType.None == user.getVipType()) {
            return null;
        }
        if (user.getVipExpTime() == null || user.getVipExpTime().getTime() < System.currentTimeMillis()) {
            return null;
        }
        return user.getVipType();
    }

    /**
     * 获取VIP过期时间字符串格式
     */
    public static String getVipExpireTimeText(UUserBasic user, DatePattern datePattern){
        VipType vipType = getVipType(user);
        if(Objects.isNull(vipType)){
            return null;
        }
        return DateUtils.toString(user.getVipExpTime(), datePattern);
    }

    /**
     * 是否为VIP
     *
     * @param user
     * @return
     */
    public static boolean isVip(UUserBasic user) {
        return BusiUtils.getVipType(user) != null;
    }

    /**
     * 积分转平台金币
     */
    public static int yuanToPlatBean(double yuan){
        // 元转赠送金币
        return (int)(yuan * 10 * GlobalConstant.PLATFORM_BEAN_EXCHANGE_RATE);
    }

    /**
     * 获取女用户（系统搭讪、主动搭讪、主动视频）次数
     * 备注：两者取最高
     */
    public static int getFemaleLimitTimes(Number numLevelCfgTimes){
        // 如果参数其中一个为null或者是-1，就代表了其中有一个配置的是无限次数
        if(Objects.isNull(numLevelCfgTimes) || numLevelCfgTimes.intValue() == -1){
            return Integer.MAX_VALUE;
        }
        return numLevelCfgTimes.intValue();
    }

    /**
     * 构建ES动态话题储存值
     */
    public static String getPostGroupEsValue(List<String> groupIds){
        if(ListUtils.isEmpty(groupIds)){
            return null;
        }
        List<String> collect = groupIds.stream().map(m -> MsgUtils.format("#{}#", m)).collect(Collectors.toList());
        return collToStr(collect, ",");
    }

    /**
     * 根据性别获取匿名头像
     */
    public static String getAnonymousHeadPic(SexType sexType){
        return SexType.Female == sexType ? GlobalConstant.ANONYMOUS_FEMALE_HEAD_PIC : GlobalConstant.ANONYMOUS_MALE_HEAD_PIC;
    }

}
