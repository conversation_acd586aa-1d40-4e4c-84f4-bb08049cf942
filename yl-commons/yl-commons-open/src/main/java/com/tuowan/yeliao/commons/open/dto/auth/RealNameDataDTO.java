package com.tuowan.yeliao.commons.open.dto.auth;

/**
 * 实名认证 数据保存
 */
public class RealNameDataDTO {
    /**
     * 获取到的certifyId
     */
    private String certifyId;
    /**
     * 用户真实姓名
     */
    private String realName;
    /**
     * 用户省份证号
     */
    private String certNum;

    public static RealNameDataDTO build1(String certifyId, String realName, String certNum){
        RealNameDataDTO dto = new RealNameDataDTO();
        dto.setCertifyId(certifyId);
        dto.setRealName(realName);
        dto.setCertNum(certNum);
        return dto;
    }

    public String getCertifyId() {
        return certifyId;
    }

    public void setCertifyId(String certifyId) {
        this.certifyId = certifyId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getCertNum() {
        return certNum;
    }

    public void setCertNum(String certNum) {
        this.certNum = certNum;
    }
}
