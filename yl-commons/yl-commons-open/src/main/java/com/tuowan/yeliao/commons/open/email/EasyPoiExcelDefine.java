package com.tuowan.yeliao.commons.open.email;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;

import java.util.List;

/**
 * 导出excel 定义
 *
 * <AUTHOR>
 * @date 2021/4/13 19:24
 */
public enum EasyPoiExcelDefine {
    /** 女用户收益 */
    FEMALE_INCOME("女用户收益", "【{}】女用户收益明细", "【{}】女用户收益明细已送达", EasyPoiExcelItem.INVITOR_INCOME_LIST_DAY),
    ;

    /** excel 描述 */
    private String desc;
    /** excel 标题 */
    private String title;
    /** excel 内容 */
    private String content;
    /** 导出表头设置 */
    private List<ExcelExportEntity> entityList;

    EasyPoiExcelDefine(String desc, String title, String content, List<ExcelExportEntity> entityList) {
        this.desc = desc;
        this.title = title;
        this.content = content;
        this.entityList = entityList;
    }

    public String getDesc() {
        return desc;
    }

    public String getTitle() {
        return title;
    }

    public String getContent() {
        return content;
    }

    public List<ExcelExportEntity> getEntityList() {
        return entityList;
    }
}
