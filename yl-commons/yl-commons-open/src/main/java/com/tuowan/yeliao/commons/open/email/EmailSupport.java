package com.tuowan.yeliao.commons.open.email;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.easyooo.framework.common.util.MsgUtils;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import javax.mail.util.ByteArrayDataSource;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;

/**
 * spring 邮件支持
 */
@Component
public class EmailSupport {
    protected final Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private JavaMailSenderImpl mailSender;

    /**
     * 发送邮件
     */
    public void sendAttachmentEmail(PackageType packageType, String toUser, EasyPoiExcelDefine define, List<Map<String, Object>> resultList){
        try {
            // 根据数据合集获取邮件附件字节
            byte[] attachment = createByte(define, resultList);
            // 创建邮件信息类
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            // 创建MimeMessageHelper对象，MimeMessage的辅助类
            MimeMessageHelper message = new MimeMessageHelper(mimeMessage, true);
            // 发送方
            message.setFrom(GlobalConstant.OFFICIAL_EMAIL);
            // 接收方
            message.setTo(toUser);
            // 邮件标题
            message.setSubject(MsgUtils.format(define.getTitle(), packageType.getDesc()));
            // 邮件内容内容
            message.setText(MsgUtils.format(define.getContent(), packageType.getDesc()));
            // 邮件附件
            ByteArrayDataSource file = new ByteArrayDataSource(attachment, "application/vnd.ms-excel;charset=UTF-8");
            message.addAttachment(MimeUtility.encodeWord(MsgUtils.format("{}.xls", MsgUtils.format(define.getTitle(), packageType.getDesc())),"utf-8","B"), file);

            mailSender.send(mimeMessage);
        } catch (Exception e) {
            LOG.error("EmailSupport-createByte-error e:", e);
            throw new BusiException("邮件发送失败！");
        }
    }

    /**
     * 将excel转换成一个数组
     */
    private byte[] createByte(EasyPoiExcelDefine define, List<Map<String, Object>> resultList){
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(define.getTitle(), define.getTitle()), define.getEntityList(), resultList);
            workbook.setSheetName(0, define.getTitle());
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            workbook.write(baos);
            return baos.toByteArray();
        }catch (Exception e){
            LOG.error("EmailSupport-createByte-error e:", e);
        }
        return null;
    }
}
