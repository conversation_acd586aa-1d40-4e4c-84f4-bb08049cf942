package com.tuowan.yeliao.commons.open.storage;


import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.DeleteObjectsRequest;
import com.easyooo.framework.common.util.FileUtils;
import com.easyooo.framework.common.util.ImageUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.config.configuration.impl.AliyunConfig;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Decoder;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Set;

/**
 * 文件上传支持类
 *
 * <AUTHOR>
 * @date 2018/6/9 21:52
 */
@Component
public class StorageSupport implements InitializingBean {

    private final Logger LOG = LoggerFactory.getLogger(StorageSupport.class);

    @Autowired(required = false)
    private SocialRedisTemplate socialRedisTemplate;

    private OSSClient client;

    /**
     * 上传文件
     *
     * @param path
     * @param bytes
     * @return 返回相对路径
     */
    public String upload(StorageType.OSSPath path, byte[] bytes) throws Exception {
        InputStream is = new ByteArrayInputStream(bytes);
        return upload(path, is);
    }

    /**
     * 上传文件
     *
     * @param path
     * @param stream
     * @return
     * @throws Exception
     */
    public String upload(StorageType.OSSPath path, InputStream stream) throws Exception {
        return this.upload(path, null, stream);
    }

    /**
     * 上传文件
     *
     * @param path
     * @param key    自定义文件路径
     * @param stream
     * @return 返回相对路径
     */
    public String upload(StorageType.OSSPath path, String key, InputStream stream) throws Exception {
        if (key == null) {
            key = buildKey(path);
        }
        String bucketName = AliyunConfig.OSS_BUCKET;
        if (StringUtils.isNotEmpty(path.getBucketName())) {
            // 使用自定义存储空间
            bucketName = path.getBucketName();
        }
        client.putObject(bucketName, key, compressIfNeed(path, stream));
        return key;
    }

    /**
     * 上传主播照片不需要压缩
     *
     * @param path
     * @param key   自定义文件路径
     * @param bytes
     * @return 返回相对路径
     */
    public String upload(StorageType.OSSPath path, String key, byte[] bytes) throws Exception {
        if (key == null) {
            key = buildKey(path);
        }
        InputStream is = new ByteArrayInputStream(bytes);
        String bucketName = AliyunConfig.OSS_BUCKET;
        if (StringUtils.isNotEmpty(path.getBucketName())) {
            // 使用自定义存储空间
            bucketName = path.getBucketName();
        }
        client.putObject(bucketName, key, is);
        return key;
    }

    /**
     * 上传网络文件
     *
     * @param path
     * @param url  网络图片地址
     * @return 返回相对路径
     */
    public String upload(StorageType.OSSPath path, String url) {
        return this.upload(path, null, url);
    }

    /**
     * 上传网络文件
     *
     * @param path
     * @param key  自定义文件名
     * @param url  网络图片地址
     * @return
     */
    public String upload(StorageType.OSSPath path, String key, String url) {
        try {
            return upload(path, key, FileUtils.download(url));
        } catch (Exception e) {
            LOG.error("上传文件失败，原因：", e);
            return null;
        }
    }

    /**
     * 加入待删除队列
     *
     * @param key
     */
    public void putToDeleteQueue(String key) {
        if (key.startsWith("/")) {
            key = key.substring(1);
        }
        RedisKey redisKey = buildOSSDeleteObjectsQueueKey();
        // 文件保留3天
        Double expTime = System.currentTimeMillis() + (3 * 24 * 3600 * 1000D);
        socialRedisTemplate.zadd(redisKey, expTime, key);
    }

    /**
     * 获取等待删除的队列列表
     *
     * @return
     */
    public List<String> getWaitDeleteObjects() {
        RedisKey redisKey = buildOSSDeleteObjectsQueueKey();
        return socialRedisTemplate.zrangeByScore(redisKey, 0, System.currentTimeMillis(), 0, 1000);
    }

    /**
     * 批量删除文件
     *
     * @param keys
     */
    public void deleteObjects(List<String> keys) {
        try {
            DeleteObjectsRequest request = new DeleteObjectsRequest(AliyunConfig.OSS_BUCKET);
            request.setKeys(keys);
            client.deleteObjects(request);
        } catch (Exception e) {
            LOG.error("删除文件失败，原因：", e);
        }
    }

    /**
     * 从等待删除队列中移除
     *
     * @param keys
     */
    public void removeWaitDeleteObjects(Set<String> keys) {
        RedisKey redisKey = buildOSSDeleteObjectsQueueKey();
        socialRedisTemplate.zrem(redisKey, keys.toArray(new String[0]));
    }


    /**
     * 生成OSS文件key
     *
     * @param path
     * @return
     */
    public String buildKey(StorageType.OSSPath path) {
        String key = path.getKey() + "/" + StringUtils.getUUID();
        if (path.getFileFormat().equalsIgnoreCase(StorageType.FileFormat.None.name())) {
            return key;
        }
        return key + "." + path.getFileFormat();
    }

    /**
     * 压缩文件(只有图片才需要压缩)
     *
     * @param path
     * @param stream
     * @return
     * @throws Exception
     */
    private InputStream compressIfNeed(StorageType.OSSPath path, InputStream stream) throws Exception {
        StorageType.ImgAttr attr = path.getImgAttr();
        if (attr == null) {
            return stream;
        }
        byte[] bytes = ImageUtils.compress(stream, attr.getWidth(), attr.getHeight(), attr.getQuality(), path.getFileFormat());
        return new ByteArrayInputStream(bytes);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(AliyunConfig.ACCESS_KEY_ID, AliyunConfig.ACCESS_KEY_SECRET);
        ClientConfiguration clientConfig = new ClientConfiguration();
        client = new OSSClient(AliyunConfig.OSS_ENDPOINT, credentialsProvider, clientConfig);
    }

    private RedisKey buildOSSDeleteObjectsQueueKey() {
        return RedisKey.create(SocialKeyDefine.OSSDeleteObjectsQueue);
    }

    /**
     * 上传 base64 编码文件到oss
     *
     * @param path
     * @param fileUrl 64位编码文件
     * @return 返回相对路径
     */
    public String uploadBase64Oss(StorageType.OSSPath path,String fileUrl) {
        BASE64Decoder base64Decoder = new BASE64Decoder();
        try {
            byte[] bytes = base64Decoder.decodeBuffer(fileUrl);
            InputStream inputStream = new ByteArrayInputStream(bytes);
            //上传图片返回 oss连接
            String url = upload(path, inputStream);
            return url;
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
