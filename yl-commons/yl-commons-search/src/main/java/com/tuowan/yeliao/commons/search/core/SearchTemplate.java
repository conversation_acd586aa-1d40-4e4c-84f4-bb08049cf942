package com.tuowan.yeliao.commons.search.core;

import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.support.elasticsearch.ESMapping;
import com.easyooo.framework.support.elasticsearch.IndexType;
import com.easyooo.framework.support.elasticsearch.MappingType;
import com.tuowan.yeliao.commons.config.configuration.impl.SearchConfig;
import com.tuowan.yeliao.commons.search.listener.DefaultActionListener;
import com.tuowan.yeliao.commons.search.utils.SearchUtils;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 全文搜索操作实例对象
 *
 * <AUTHOR>
 * @date 2021/11/20 16:47
 */
@Component
public class SearchTemplate implements InitializingBean {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    private RestHighLevelClient client;

    /**
     * 创建文档索引
     * <p>
     * 同一个文档索引只需创建一次
     *
     * @param entity
     */
    public void createIndex(Object entity) {
        CreateIndexRequest createIndexRequest = new CreateIndexRequest(SearchUtils.getIndex(entity));
        try {
            XContentBuilder contentBuilder = XContentFactory.jsonBuilder();
            contentBuilder.startObject();
            {
                contentBuilder.startObject("properties");
                {
                    Field[] fields = entity.getClass().getDeclaredFields();
                    for (Field field : fields) {
                        contentBuilder.startObject(field.getName());
                        if (field.isAnnotationPresent(ESMapping.class)) {
                            ESMapping mapping = field.getAnnotation(ESMapping.class);
                            MappingType type = mapping.type();
                            if (type != null) {
                                contentBuilder.field("type", type.getId());
                            }
                            IndexType indexType = mapping.index();
                            if (IndexType.Analyzed == indexType) {
                                // 需要分词搜索(使用IK插件)
                                contentBuilder.field("analyzer", "ik_max_word");
                                contentBuilder.field("search_analyzer", "ik_max_word");
                            } else if (IndexType.No == indexType) {
                                // 不需要分词搜索(按关键词搜索)
                                contentBuilder.field("index", "false");
                            }
                        }
                        contentBuilder.endObject();
                    }
                }
                contentBuilder.endObject();
            }
            contentBuilder.endObject();
            createIndexRequest.mapping(contentBuilder);
            client.indices().create(createIndexRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            LOG.error("Execute create index fail, reason:", e);
        }
    }

    /**
     * 删除索引
     *
     * @param entity
     */
    public void deleteIndex(Object entity) {
        DeleteIndexRequest request = new DeleteIndexRequest(SearchUtils.getIndex(entity));
        try {
            client.indices().delete(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            LOG.error("Execute delete index fail, reason:", e);
        }
    }

    /**
     * 保存文档
     *
     * @param entity
     */
    public void save(Object entity) {
        RequestTarget requestTarget = SearchUtils.buildRequestTarget(entity);
        IndexRequest indexRequest = new IndexRequest(requestTarget.getIndex());
        indexRequest.id(requestTarget.getId());
        indexRequest.source(JsonUtils.toJsonMap(entity));
        client.indexAsync(indexRequest, RequestOptions.DEFAULT, new DefaultActionListener<IndexResponse>() {
            @Override
            public void onFailure(Exception e) {
                LOG.error("Execute index request fail, reason:", e);
            }
        });
    }

    /**
     * 批量保存文档
     *
     * @param entities
     */
    public void saveAll(Collection entities) {
        BulkRequest bulkRequest = new BulkRequest();
        for (Object entity : entities) {
            RequestTarget requestTarget = SearchUtils.buildRequestTarget(entity);
            IndexRequest indexRequest = new IndexRequest(requestTarget.getIndex());
            indexRequest.id(requestTarget.getId());
            indexRequest.source(JsonUtils.toJsonMap(entity));
            bulkRequest.add(indexRequest);
        }
        client.bulkAsync(bulkRequest, RequestOptions.DEFAULT, new DefaultActionListener<BulkResponse>() {
            @Override
            public void onFailure(Exception e) {
                LOG.error("Execute bulk index request fail, reason:", e);
            }
        });
    }

    /**
     * 更新文档
     *
     * @param entity
     * @param isSelective 是否选择性更新，值为空不更新
     */
    public void update(Object entity, boolean isSelective) {
        RequestTarget requestTarget = SearchUtils.buildRequestTarget(entity);
        try {
            // 判断文档是否存在，存在再更新(不存在直接更新会报document_missing_exception)
            GetRequest getRequest = new GetRequest(requestTarget.getIndex(), requestTarget.getId());
            if (!client.exists(getRequest, RequestOptions.DEFAULT)) {
                return;
            }
        } catch (IOException e) {
            LOG.error("Execute get request fail, reason", e);
            return;
        }
        UpdateRequest updateRequest = new UpdateRequest(requestTarget.getIndex(), requestTarget.getId());
        if (isSelective) {
            Map<String, Object> data = JsonUtils.toJsonMap(entity);
            // 过滤值为空的Key
            Iterator<Map.Entry<String, Object>> it = data.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<String, Object> result = it.next();
                if (result.getValue() == null) {
                    it.remove();
                }
            }
            updateRequest.doc(data);
        } else {
            updateRequest.doc(JsonUtils.toJsonMap(entity));
        }
        client.updateAsync(updateRequest, RequestOptions.DEFAULT, new DefaultActionListener<UpdateResponse>() {
            @Override
            public void onFailure(Exception e) {
                LOG.error("Execute update request fail, reason", e);
            }
        });
    }

    /**
     * 判断文档是否存在
     *
     * @param entityClass
     * @param id
     * @param <T>
     * @return
     */
    public <T> boolean exists(Class<T> entityClass, Object id) {
        RequestTarget requestTarget = SearchUtils.buildRequestTarget(entityClass, id);
        GetRequest getRequest = new GetRequest(requestTarget.getIndex(), requestTarget.getId());
        try {
            return client.exists(getRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            LOG.error("Execute get request fail, reason", e);
            return false;
        }
    }

    /**
     * 根据文档ID获取文档信息
     *
     * @param entityClass
     * @param id
     * @param <T>
     * @return
     */
    public <T> T findById(Class<T> entityClass, Object id) {
        RequestTarget requestTarget = SearchUtils.buildRequestTarget(entityClass, id);
        GetRequest getRequest = new GetRequest(requestTarget.getIndex(), requestTarget.getId());
        try {
            GetResponse response = client.get(getRequest, RequestOptions.DEFAULT);
            return JsonUtils.deserializeAsObject(response.getSourceAsString(), entityClass);
        } catch (IOException e) {
            LOG.error("Execute get request fail, reason", e);
            return null;
        }
    }

    /**
     * 根据文档ID删除文档
     *
     * @param entityClass
     * @param id
     */
    public void delete(Class entityClass, Object id) {
        RequestTarget requestTarget = SearchUtils.buildRequestTarget(entityClass, id);
        DeleteRequest deleteRequest = new DeleteRequest(requestTarget.getIndex(), requestTarget.getId());
        client.deleteAsync(deleteRequest, RequestOptions.DEFAULT, new DefaultActionListener<DeleteResponse>() {
            @Override
            public void onFailure(Exception e) {
                LOG.error("Execute delete request fail, reason:", e);
            }
        });
    }

    /**
     * 根据文档ID频率删除
     *
     * @param entityClass
     * @param ids
     */
    public void deleteAll(Class entityClass, Collection ids) {
        BulkRequest bulkRequest = new BulkRequest();
        RequestTarget requestTarget = null;
        for (Object id : ids) {
            if (requestTarget == null) {
                requestTarget = SearchUtils.buildRequestTarget(entityClass, id);
            } else {
                requestTarget.setId(id.toString());
            }
            DeleteRequest deleteRequest = new DeleteRequest(requestTarget.getIndex(), requestTarget.getId());
            bulkRequest.add(deleteRequest);
        }
        client.bulkAsync(bulkRequest, RequestOptions.DEFAULT, new DefaultActionListener<BulkResponse>() {
            @Override
            public void onFailure(Exception e) {
                LOG.error("Execute bulk delete request fail, reason:", e);
            }
        });
    }

    /**
     * 搜索文档
     *
     * @param entity
     * @param searchSourceBuilder
     * @param <T>
     * @return
     */
    public <T> List<T> search(Class<T> entity, SearchSourceBuilder searchSourceBuilder) {
        try {
            RequestTarget requestTarget = SearchUtils.buildRequestTarget(entity, null);
            SearchRequest searchRequest = new SearchRequest(requestTarget.getIndex());
            searchRequest.source(searchSourceBuilder);
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            List<T> resultList = new ArrayList<>();
            for (SearchHit hit : hits) {
                resultList.add(JsonUtils.deserializeAsObject(hit.getSourceAsString(), entity));
            }
            return resultList;
        } catch (IOException e) {
            LOG.error("Execute search request fail, reason:", e);
        }
        return new ArrayList<>();
    }

    /**
     * 搜索文档
     *
     * @param entityClass
     * @param searchSourceBuilder
     * @return
     */
    public SearchHit[] searchHits(Class entityClass, SearchSourceBuilder searchSourceBuilder) {
        try {
            RequestTarget requestTarget = SearchUtils.buildRequestTarget(entityClass, null);
            SearchRequest searchRequest = new SearchRequest(requestTarget.getIndex());
            searchRequest.source(searchSourceBuilder);
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            return response.getHits().getHits();
        } catch (IOException e) {
            LOG.error("Execute search request fail, reason:", e);
        }
        return new SearchHit[0];
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        RestClientBuilder builder = RestClient.builder(SearchConfig.HTTP_HOSTS);
        // 连接延时配置
        builder.setRequestConfigCallback(requestConfigBuilder -> {
            requestConfigBuilder.setConnectTimeout(SearchConfig.CONNECT_TIMEOUT);
            requestConfigBuilder.setSocketTimeout(SearchConfig.SOCKET_TIMEOUT);
            requestConfigBuilder.setConnectionRequestTimeout(SearchConfig.CONNECTION_REQUEST_TIMEOUT);
            return requestConfigBuilder;
        });
        // 连接池数配置
        builder.setHttpClientConfigCallback(httpClientBuilder -> {
            httpClientBuilder.setMaxConnTotal(SearchConfig.MAX_CONN_TOTAL);
            httpClientBuilder.setMaxConnPerRoute(SearchConfig.MAX_CONN_PER_ROUTE);
            httpClientBuilder.setDefaultCredentialsProvider(SearchConfig.CREDENTIALS_PROVIDER);
            httpClientBuilder.setKeepAliveStrategy((response, context) -> TimeUnit.MINUTES.toMillis(3));
            return httpClientBuilder;
        });
        client = new RestHighLevelClient(builder);
    }
}
