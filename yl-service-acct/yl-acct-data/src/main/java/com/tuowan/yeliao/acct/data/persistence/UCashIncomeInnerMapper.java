/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.acct.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.acct.data.entity.UCashIncomeInner;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "u_cash_income_inner", schema = "yl_acct")
public interface UCashIncomeInnerMapper {
    int deleteByPrimaryKey(UCashIncomeInner record);

    int insert(UCashIncomeInner record);

    UCashIncomeInner selectByPrimaryKey(UCashIncomeInner record);

    int updateByPrimaryKeySelective(UCashIncomeInner record);

    int updateByPrimaryKey(UCashIncomeInner record);
}