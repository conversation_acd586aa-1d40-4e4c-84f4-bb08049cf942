package com.tuowan.yeliao.acct.service.cash;

import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.acct.comp.cash.WithdrawComponent;
import com.tuowan.yeliao.acct.data.dto.gongm.SendInterviewDTO;
import com.tuowan.yeliao.acct.data.dto.withdraw.ProtocolDTO;
import com.tuowan.yeliao.acct.data.dto.withdraw.WithdrawDTO;
import com.tuowan.yeliao.acct.data.dto.yun.OrderResponseDTO;
import com.tuowan.yeliao.acct.data.entity.*;
import com.tuowan.yeliao.acct.data.enums.WithdrawDisplayStatus;
import com.tuowan.yeliao.acct.data.enums.WithdrawStatus;
import com.tuowan.yeliao.acct.data.enums.WithdrawType;
import com.tuowan.yeliao.acct.data.persistence.*;
import com.tuowan.yeliao.acct.data.utils.YunApiUtils;
import com.tuowan.yeliao.acct.service.async.AsyncService;
import com.tuowan.yeliao.acct.web.form.cash.withdraw.*;
import com.tuowan.yeliao.acct.web.vo.cash.withdraw.*;
import com.tuowan.yeliao.commons.comp.consume.ConsumePresentComponent;
import com.tuowan.yeliao.commons.comp.consume.dto.ConsumePresentDTO;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.comp.notice.dto.WithdrawNoticeDTO;
import com.tuowan.yeliao.commons.config.enums.ObjectLockType;
import com.tuowan.yeliao.commons.config.lock.ObjectLockTemplate;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.dto.user.UserBusiDTO;
import com.tuowan.yeliao.commons.data.entity.user.UIdentityAuth;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserExt;
import com.tuowan.yeliao.commons.data.entity.user.UUserKeyMark;
import com.tuowan.yeliao.commons.data.enums.config.NoticeSysType;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.commons.data.enums.user.InvitePsType;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.enums.user.UserBusiKeyMark;
import com.tuowan.yeliao.commons.data.enums.user.UserCashSourceType;
import com.tuowan.yeliao.commons.data.manager.commons.BusiManager;
import com.tuowan.yeliao.commons.data.manager.commons.SmsManager;
import com.tuowan.yeliao.commons.data.manager.config.AdManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.manager.user.UserKeyMarkManager;
import com.tuowan.yeliao.commons.data.manager.user.WhitelistManager;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.data.utils.HtmlUrlUtils;
import com.tuowan.yeliao.commons.open.sms.JmSmsSupport;
import com.tuowan.yeliao.commons.web.common.form.SexForm;
import com.tuowan.yeliao.commons.web.common.vo.PageVO;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.commons.web.proxy.config.MQInvoke;
import com.tuowan.yeliao.commons.web.proxy.config.NotifyMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 提现业务封装
 *
 * <AUTHOR>
 * @date 2020/7/16 15:59
 */
@Service
public class WithdrawService {
    private Logger LOG = LoggerFactory.getLogger(this.getClass());
    /** 邀请人满额提现邀请奖励 幂等控制 */
    private static final String INVITE_FULL_AMOUNT_AWARD = "IFAA_{}_{}";

    @Autowired
    private AdManager adManager;
    @Autowired
    private UCashMapper uCashMapper;
    @Autowired
    private UCashIncomeDayStatMapper uCashIncomeDayStatMapper;
    @Autowired
    private WithdrawComponent withdrawComponent;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private NoticeComponent noticeComponent;
    @Autowired
    private UCashWithdrawOrderMapper uCashWithdrawOrderMapper;
    @Autowired
    private UCashAuthInfoMapper uCashAuthInfoMapper;
    @Autowired
    private UserQueryMapper userQueryMapper;
    @Autowired
    private UCashWithdrawTplMapper uCashWithdrawTplMapper;
    @Autowired
    private WhitelistManager whitelistManager;
    @Autowired
    private ObjectLockTemplate objectLockTemplate;
    @Autowired
    private ConsumePresentComponent consumePresentComponent;
    @Autowired
    private UserKeyMarkManager keyMarkManager;
    @Autowired
    private BusiManager busiManager;
    @Autowired
    private UCashWithdrawProtocolMapper cashWithdrawProtocolMapper;
    @Autowired
    private JmSmsSupport jmSmsSupport;
    @Autowired
    private SmsManager smsManager;
    @Autowired
    private AsyncService asyncService;

    /**
     * 获取 完成手机号码绑定、实名认证 并且还没有电签的女用户
     */
    public List<Long> getWaitInterviewUsers(){
        return userQueryMapper.queryWaitInterviewUsers();
    }

    /**
     * 程序电签
     */
    public void saveCgInterview(Long userId){
        UIdentityAuth auth = userInfoManager.getUserIdentityAuth(userId);
        // 1、是否手机号绑定
        if(StringUtils.isEmpty(auth.getMobile())){
            return;
        }
        // 2、是否完成实名认证
        if(StringUtils.isEmpty(auth.getIdentityNo())){
            return;
        }
        // 3、是否电签
        UCashWithdrawProtocol withdrawProtocol = withdrawComponent.getWithdrawProtocol(userId);
        if(Objects.nonNull(withdrawProtocol)){
            return;
        }
        // 开始电签
        SendInterviewDTO dto = withdrawComponent.sendInterview(auth);
        withdrawComponent.confirmInterview(userInfoManager.getUserIdentityAuth(userId), null, dto.getContractId());
    }

    /**
     * 尝试工猫自动电签
     */
    @MQInvoke
    public void saveGmAutoInterview(){
        // 判断用户是否实名认证完成
        Long userId = GlobalUtils.uid();
        UIdentityAuth auth = userInfoManager.getUserIdentityAuth(userId);
        if(StringUtils.isEmpty(auth.getIdentityNo())){
            return;
        }
        // 均已完成 判断是否完成工猫电签
        UCashWithdrawProtocol withdrawProtocol = withdrawComponent.getWithdrawProtocol(userId);
        if(Objects.nonNull(withdrawProtocol)){
            // 已经完成工猫电签
            return;
        }
        // 开始电签
        boolean signResult = YunApiUtils.userSign(auth.getRealname(), BusiUtils.decrypt(auth.getIdentityNo()));
        if(!signResult){ // 电签失败
            LOG.info("WithdrawService-saveGmAutoInterview-info userId:{} 电签失败。", userId);
            return;
        }
        UCashWithdrawProtocol protocol = new UCashWithdrawProtocol(auth.getUserId());
        protocol.setContractId("yzh");
        protocol.setStatus(BoolType.True);
        protocol.setUpdateTime(new Date());
        protocol.setCreateTime(new Date());
        withdrawComponent.saveUserYzSign(protocol);
    }

    /**
     * 获取提现首页信息
     *
     * @return
     */
    @BusiCode
    public WithdrawInfoVO getWithdrawInfo(WithdrawInfoForm form) {
        Long userId = GlobalUtils.uid();
        UserBusiDTO busi = GlobalUtils.busi();
        ClientType clientType = GlobalUtils.clientType();
        UUserExt userExt = userInfoManager.getUserExt(userId);
        // 累计总计
        UCash uCash = uCashMapper.selectByPrimaryKey(new UCash(userId));
        // 今日零钱统计
        UCashIncomeDayStat dayStat = uCashIncomeDayStatMapper.selectByPrimaryKey(new UCashIncomeDayStat(userId, DateUtils.trunc(new Date())));
        // 提现模板列表
        List<UCashWithdrawTpl> tplList = withdrawComponent.getTplList(userId, clientType, GlobalUtils.sexType(), uCash);
        if(Objects.nonNull(userExt.getInviteUserId()) && InvitePsType.Pack == userInfoManager.getUserExt(userExt.getInviteUserId()).getInvitePsType()){
            tplList = Collections.emptyList();
        }
        // 提现授权账号信息, 如果是分身账号，则取主账号的
        Long masterId = GlobalUtils.masterId() == null ? userId : GlobalUtils.masterId();
        UIdentityAuth identityAuth = userInfoManager.getUserIdentityAuth(masterId);

        WithdrawInfoVO vo = WithdrawInfoVO.create(uCash, dayStat, tplList, identityAuth, clientType, busi.getRealName(), form.getSourceClientType());
        vo.setCustomerUrl(HtmlUrlUtils.getCustomerUrl(GlobalUtils.sessionId()));
        vo.setWithdrawUrl(withdrawComponent.buildWithdrawUrl(GlobalUtils.clientType(), GlobalUtils.sessionId()));
        vo.setUserId(GlobalUtils.uid());
        vo.setSexType(GlobalUtils.sexType());
        vo.setIncomeReward(BoolType.False);
        vo.setWithdrawTips("1、显示的金额是实际到账金额，无需额外扣手续费。\n" +
                "2、提现需要绑定手机号和完成实名认证，否则无法提现。\n" +
                "3、提现时支付平台会收取税点及手续费，提现模板展示金额为实际到账金额。\n" +
                "4、秒到账模板提现实时到账，非秒到账模板隔天到账。");
        // 提现首页banner广告
        vo.setAdList(adManager.listwithdrawBannerAds(clientType, GlobalUtils.packageType(), GlobalUtils.clientVersion()));
        // 自由职业者服务协议
        vo.setWithdrawRule(withdrawComponent.getYzhSignRuleUrl());
        // 是否完成工猫电签
        vo.setFinishGmSg(BoolType.valueOf(Objects.nonNull(cashWithdrawProtocolMapper.selectByPrimaryKey(new UCashWithdrawProtocol(userId)))));
        // 是否具备转账权限
        vo.setTransferRight(BoolType.valueOf(SettingsConfig.getString(SettingsType.TransferRightUser).contains(userId.toString())));

        return vo;
    }

    /**
     * 发起电签
     */
    @BusiCode
    public SendInterviewVO saveSendInterview(){
        Long userId = GlobalUtils.uid();
        objectLockTemplate.acquireTransactionLock(ObjectLockType.WithdrawInterview, userId);
        UIdentityAuth auth = userInfoManager.getUserIdentityAuth(userId);
        // 1、判断是否绑定手机号
        if(StringUtils.isEmpty(auth.getMobile())){
            throw new BusiException(ErrCodeType.WdNoBindMobile);
        }
        // 2、判断是否实名认证
        if(StringUtils.isEmpty(auth.getIdentityNo())){
            throw new BusiException(ErrCodeType.WdNoRealName);
        }
        // 3、工猫发起验签、并记录合同ID
        // withdrawComponent.sendInterview(auth);
        // 4、给用户发送一个验证码（工猫已经配置五验证码电签了）
        String authCode = smsManager.getAuthCode(auth.getMobile());
        jmSmsSupport.sendInterviewCode(auth.getMobile(), GlobalUtils.packageType().getDesc(), authCode);

        return SendInterviewVO.build1(MsgUtils.format("{}****{}",
                auth.getMobile().substring(0, 3), auth.getMobile().substring(auth.getMobile().length() - 4)));
    }

    /**
     * 确认电签
     */
    @BusiCode
    public void saveConfirmInterview(ConfirmInterviewForm form){
        Long userId = GlobalUtils.uid();
        objectLockTemplate.acquireTransactionLock(ObjectLockType.WithdrawInterview, userId);
        // 验证码校验
        UIdentityAuth auth = userInfoManager.getUserIdentityAuth(userId);
        smsManager.verifyAuthCode(auth.getMobile(), form.getCode());
        // withdrawComponent.confirmInterview(userInfoManager.getUserIdentityAuth(userId), null, null);
    }

    /**
     * 同意协议
     */
    @BusiCode
    public void saveAgreeProtocol(AgreeProtocolForm form) {
        Long userId = GlobalUtils.uid();
        objectLockTemplate.acquireTransactionLock(ObjectLockType.WithdrawInterview, userId);
        UIdentityAuth auth = userInfoManager.getUserIdentityAuth(userId);
        // 1、判断该用户是否绑定手机号
        if(Objects.isNull(auth.getMobile())){
            throw new BusiException(ErrCodeType.InterviewNoBindMobile);
        }
        // 2、判断用户是否实名认证
        if(Objects.isNull(auth.getIdentityNo())){
            throw new BusiException(ErrCodeType.InterviewNoRealName);
        }
        UCashWithdrawProtocol withdrawProtocol = withdrawComponent.getWithdrawProtocol(userId);
        if(Objects.isNull(withdrawProtocol)){ // 说明还没有电签
            throw new BusiException(ErrCodeType.WithDrawNoInterview);
        }
        withdrawComponent.agreeProtocol(userId, form.getStatus());
    }

    /**
     * 查询单挑提现记录
     *
     * @param form
     * @return
     */
    @BusiCode
    public UCashWithdrawOrder getWithdrawLog(WithdrawQueryForm form) {
        return withdrawComponent.getWithdrawLog(form.getOrderNo());
    }

    /**
     * 申请提现
     *
     * @param form
     * @return
     */
    @BusiCode(BusiCodeDefine.ApplyWithdraw)
    public WithdrawApplyVO saveWithdrawApply(WithdrawApplyForm form) {
        Long userId = GlobalUtils.uid();
        // 判断是否存在等待处理的举报记录
        if (userQueryMapper.countReportRecord(userId) > 0) {
            throw new BusiException("您还有举报未处理，请联系客服处理！");
        }
        // 判断是否存在提现黑名单
        String limitUserIds = SettingsConfig.getString(SettingsType.WithDrawBlackList);
        if (limitUserIds.contains(String.valueOf(userId))) {
            throw new BusiException("异常账户，如有疑问请联系客服！");
        }
        // 判断是否在提现黑名单中（cms运营可操作）
        boolean isBan = whitelistManager.banWithDraw(userId);
        if (isBan) {
            throw new BusiException("您的账号异常，请联系客服处理！");
        }
        // 如果邀请人是【打包】模式 则不允许用户自提
        UUserExt userExt = userInfoManager.getUserExt(userId);
        if(Objects.nonNull(userExt.getInviteUserId()) && InvitePsType.Pack == userInfoManager.getUserExt(userExt.getInviteUserId()).getInvitePsType()){
            throw new BusiException("不允许提现，收益将于每天凌晨转账至邀请人");
        }
        // 判断是否完成电签
        UCashWithdrawProtocol withdrawProtocol = withdrawComponent.getWithdrawProtocol(userId);
        if(Objects.isNull(withdrawProtocol)){
            throw new BusiException("您还未完成电签，请联系客服处理！");
        }
        UIdentityAuth authInfo = userInfoManager.getUserIdentityAuth(userId);
        // 判断用户是否完成 《实名认证》
        if(StringUtils.isEmpty(authInfo.getIdentityNo())){
            throw new BusiException(ErrCodeType.WdNoRealName);
        }
        // 如果是提现到微信、判断是否绑定微信
        if(WithdrawType.Weixin == form.getType() && StringUtils.isEmpty(authInfo.getWeixinUserId())){
            throw new BusiException(ErrCodeType.WdNoBindWx);
        }
        // 如果是提现到支付宝、判断是否绑定支付宝
        if(WithdrawType.Alipay == form.getType() && StringUtils.isEmpty(authInfo.getAlipayUserId())){
            throw new BusiException(ErrCodeType.WdNoBindAlipay);
        }
        userLockTemplate.acquireTransactionLock(userId);
        // 提现，共用主账号的实名认证和微信支付宝绑定信息
        Long masterId = GlobalUtils.masterId() == null ? userId : GlobalUtils.masterId();
        // 保存提现记录
        WithdrawDTO result = withdrawComponent.saveWithdrawLog(userId, masterId, GlobalUtils.sexType(), form.getTplId(), form.getType());
        // 发送系统通知
        WithdrawNoticeDTO noticeDTO = new WithdrawNoticeDTO(userId, new Date(), result.getRealMoney().toString(), null);
        noticeComponent.sendSystemNotice(noticeDTO, result.isQuick() ? NoticeSysType.WithdrawApplyQuick : NoticeSysType.WithdrawApplySlow);
//        if (result.getRiskInfo() != null) {
//            WithdrawRiskDTO riskInfo = result.getRiskInfo();
//            if (riskInfo.getHourWithdrawMoney() != null && riskInfo.getHourWithdrawMoney() > 25000_00
//                    && riskInfo.getHourWithdrawMoney() - BusiUtils.yuanToFen(result.getMoney()) < 25000_00) {
//                GlobalUtils.extValue(BusinessDataKey.Content, MsgUtils.format("就聊提现金额时段预警，当前超过2.5万元"));
//                dingtalkMessageComponent.sendMatchMsgs();
//            }
//            if (riskInfo.getHourWithdrawUserCount() != null && riskInfo.getHourWithdrawUserCount().equals(100L)) {
//                GlobalUtils.extValue(BusinessDataKey.Content, MsgUtils.format("就聊提现人数时段预警，当前100人"));
//                dingtalkMessageComponent.sendMatchMsgs();
//            }
//
//        }
        return WithdrawApplyVO.create(result.getCash(), result.getOrderNo(), result.isQuick(), userId);
    }


    /**
     * 提交提现申请
     *
     * @param form
     */
    @BusiCode(BusiCodeDefine.SubmitWithdraw)
    public void saveWithdrawSubmit(WithdrawSubmitForm form) {
        saveWithdrawSubmitForGm(form.getUserId(), form.getOrderNo());
    }

    /**
     * 提交提现申请
     */
    public void saveWithdrawSubmitForGm(Long userId, String orderNo){
        // 开启用户锁
        userLockTemplate.acquireTransactionLock(userId);
        UCashWithdrawOrder log = withdrawComponent.getWithdrawLog(orderNo);
        if (WithdrawStatus.Wait != log.getStatus()) {
            return;
        }
        UIdentityAuth identityAuth = userInfoManager.getUserIdentityAuth(log.getUserId());
        // 开始下单
        String wxAppId = null;
        String openUserId;
        if (WithdrawType.Alipay == log.getWithdrawType()) {
            openUserId = identityAuth.getAlipayUserId();
        } else {
            openUserId = identityAuth.getWeixinUserId();
            wxAppId = identityAuth.getWeixinAppId();
        }
        OrderResponseDTO response = YunApiUtils.order(log.getWithdrawType(), log.getOrderNo(), identityAuth.getRealname(), BusiUtils.decrypt(identityAuth.getIdentityNo()), wxAppId, openUserId, log.getRealMoney());
//        WithdrawResponseDTO responseDTO = GmApiUtils.order(userId, log.getWithdrawType(), now, orderNo, identityAuth.getMobile(),
//                identityAuth.getRealname(), log.getRealMoney(), BusiUtils.decrypt(identityAuth.getIdentityNo()), openUserId, wxAppId);
        // 如果下单成功，则更新云账户订单号
        if (response.isSuccess()) {
            withdrawComponent.updateStatusToProcessing(log.getOrderNo(), null, log.getExpectSubmitTime());
            return;
        }
        LOG.warn("提交提现打款失败，原因：{}，userId: {}, orderNo: {}", response.getErrmsg(), log.getUserId(), log.getOrderNo());

        // 将提现修改为失败
        withdrawComponent.updateWithdrawResult(log, null, WithdrawStatus.Failure, "提现申请提交失败：" + response.getErrmsg());

        // 发送提现失败消息
        WithdrawNoticeDTO noticeDTO = new WithdrawNoticeDTO(log.getUserId(), log.getCreateTime(), String.valueOf(log.getRealMoney()),
                DateUtils.toString(log.getCreateTime(), DatePattern.MD2), HtmlUrlUtils.getWithDrawLogUrl(GlobalUtils.sessionId(), GlobalUtils.packageType()));
        noticeComponent.sendSystemNotice(noticeDTO, NoticeSysType.WithdrawOtherFail);
    }

    /**
     * 提交提现申请
     *
     * @param orderNo
     */
    public void saveWithdrawSubmit(Long userId, String orderNo) {
        // 开启用户锁
        userLockTemplate.acquireTransactionLock(userId);
        UCashWithdrawOrder log = withdrawComponent.getWithdrawLog(orderNo);
        if (WithdrawStatus.Wait != log.getStatus()) {
            return;
        }
        UIdentityAuth identityAuth = userInfoManager.getUserIdentityAuth(log.getUserId());
        // 开始下单
        String realName = identityAuth == null ? null : identityAuth.getRealname();
        String identifyNo = identityAuth == null ? null : identityAuth.getIdentityNo();
        String appId = null;
        String openUserId;
        if (WithdrawType.Alipay == log.getWithdrawType()) {
            openUserId = identityAuth.getAlipayUserId();
        } else {
            openUserId = identityAuth.getWeixinUserId();
            appId = identityAuth.getWeixinAppId();
        }
        OrderResponseDTO response = YunApiUtils.order(log.getWithdrawType(), log.getOrderNo(), realName, BusiUtils.decrypt(identifyNo), appId, openUserId, log.getRealMoney());
        if (response == null) {
            // 接口调用失败，交给定时任务，等待下次处理
            LOG.warn("提现申请下单无响应，已交给定时任务等待下次处理，订单号：{}", log.getOrderNo());
            return;
        }
        // 如果下单成功，则更新云账户订单号
        if (response.isSuccess()) {
            withdrawComponent.updateStatusToProcessing(log.getOrderNo(), response.getWithdrawPathOrderNo(), log.getExpectSubmitTime());
            return;
        }
        LOG.warn("提交提现打款失败，原因：{}，userId: {}, orderNo: {}", response.getErrmsg(), log.getUserId(), log.getOrderNo());

        // 将提现修改为失败
        withdrawComponent.updateWithdrawResult(log, null, WithdrawStatus.Failure, "提现申请提交失败：" + response.getErrmsg());

        // 发送提现失败消息
        WithdrawNoticeDTO noticeDTO = new WithdrawNoticeDTO(log.getUserId(), log.getCreateTime(), String.valueOf(log.getRealMoney()),
                DateUtils.toString(log.getCreateTime(), DatePattern.MD2), HtmlUrlUtils.getWithDrawLogUrl(GlobalUtils.sessionId(), GlobalUtils.packageType()));
        noticeComponent.sendSystemNotice(noticeDTO, NoticeSysType.WithdrawOtherFail);
    }

    /**
     * 更新提现状态
     *
     * @param form
     */
    @BusiCode(value = BusiCodeDefine.UserCashOut, forward = NotifyMode.DYNAMIC)
    public void updateWithdrawResult(WithdrawCallbackForm form) {
        // 开启用户锁
        userLockTemplate.acquireTransactionLock(form.getUserId());
        UCashWithdrawOrder withdrawLog = withdrawComponent.getWithdrawLogForUpdate(form.getOrderNo());
        // 不是进行中状态，直接返回
        if (withdrawLog.getStatus().getDisplayStatus() != WithdrawDisplayStatus.Processing) {
            return;
        }
        // 更新提现结果
        withdrawComponent.updateWithdrawResult(withdrawLog, form.getPayOrderNo(), form.getStatus(), form.getStatusReason());
//        if (WithdrawStatus.Success == form.getStatus()) {
//            try {
//                // 累计提现任务
//                UCash curCash = uCashMapper.selectByPrimaryKey(new UCash(withdrawLog.getUserId()));
//                danDanZComponent.saveProcessTask(withdrawLog.getUserId(), BackCodeDefine.WithDraw.name(), curCash.getWithdrawTotal());
//            } catch (Exception e) {
//                LOG.error("累计提现任务失败，用户ID:{}，订单号：{}，原因:{}", withdrawLog.getUserId(), withdrawLog.getOrderNo(), e);
//            }
//        }
        // 提现失败，发送系统通知
        if (WithdrawStatus.Failure == form.getStatus()) {
            WithdrawNoticeDTO noticeDTO = new WithdrawNoticeDTO(withdrawLog.getUserId(), withdrawLog.getCreateTime(), String.valueOf(withdrawLog.getRealMoney()),
                    DateUtils.toString(withdrawLog.getCreateTime(), DatePattern.MD2), HtmlUrlUtils.getWithDrawLogUrl(GlobalUtils.sessionId(), GlobalUtils.packageType()));
            NoticeSysType sysType = NoticeSysType.WithdrawOtherFail;
            // 251 身份证姓名不匹配
            // 262 支付宝账号与姓名不匹配
            // 2057 微信账号与姓名不匹配
            /*if ("251".equals(form.getStatusDetail()) || "262".equals(form.getStatusDetail()) || "2057".equals(form.getStatusDetail())) {
                sysType = NoticeSysType.WithdrawAuthFail;
            }*/
            noticeComponent.sendSystemNotice(noticeDTO, sysType);
            GlobalUtils.extValue(BusinessDataKey.BreakNotify, true);
        } else {
            GlobalUtils.extValue(BusinessDataKey.Money, withdrawLog.getMoney());
            GlobalUtils.extValue(BusinessDataKey.TargetUserId, withdrawLog.getUserId());
        }
    }

    /**
     * 发送余额提醒到钉钉群
     */
    @BusiCode(value = BusiCodeDefine.BalanceRemind, forward = NotifyMode.DYNAMIC)
    public void sendBalanceRemind() {
        if (!withdrawComponent.isNeedRemind()) {
            GlobalUtils.extValue(BusinessDataKey.BreakNotify, true);
        }
    }

    /**
     * 提现历史记录
     *
     * @return
     */
    @BusiCode
    public PageVO getWithdrawHistory() {
        // 至多显示100条记录
        List<UCashWithdrawOrder> logList = withdrawComponent.queryWithdrawHistory(GlobalUtils.uid(), 100);
        List<WithdrawHistoryVO> values = new ArrayList<>();
        for (UCashWithdrawOrder log : logList) {
            WithdrawHistoryVO vo = new WithdrawHistoryVO();
            if (StringUtils.isNotEmpty(log.getStatusReason()) && log.getStatusReason().contains("冻结恢复")) {
                continue;
            }
            buildWithDrawLog(log, vo);
            values.add(vo);
        }
        return new PageVO(values, 0, 100);
    }

    /**
     * 获取需要撤销的提现申请
     *
     * @param form
     */
    @BusiCode
    public List<String> getRevokeApplyWithdrawList(WithdrawRevokeForm form) {
        return uCashWithdrawOrderMapper.queryWaitRevokeList(form.getUserId());
    }

    /**
     * 审核提现申请（运营一审）
     *
     * @param form
     */
    @BusiCode
    public void saveAudit(WithdrawAuditForm form) {
        UCashWithdrawOrder withdrawLog = withdrawComponent.getWithdrawLogForUpdate(form.getOrderNo());
        if (withdrawLog == null) {
            throw new BusiException("提现申请不存在");
        }
        if (withdrawLog.getStatus() != WithdrawStatus.WaitAudit) {
            throw new BusiException("提现申请不是待审核状态");
        }
        withdrawLog.setAuditUserId(GlobalUtils.uid());
        withdrawLog.setAuditReason(form.getAuditReason());
        withdrawLog.setAuditTime(DateUtils.nowTime());
        withdrawLog.setStatus(WithdrawStatus.WaitReview);
        userLockTemplate.acquireTransactionLock(withdrawLog.getUserId());
        if (form.getPass() == BoolType.False) {
            // 将提现修改为拒绝
            withdrawComponent.updateWithdrawResult(withdrawLog, null, WithdrawStatus.Refuse, "提现申请一审拒绝：" + form.getAuditReason());
            examineFailNotice(withdrawLog, form.getAuditReason());
        } else {
            UUserBasic basic = userInfoManager.getUserBasic(withdrawLog.getUserId());
            if (Status.Disable == basic.getStatus()) {
                throw new BusiException("当前用户已被永久禁用,不可审核通过");
            }
            // 一审通过，直接修改状态等待二审
            uCashWithdrawOrderMapper.updateByPrimaryKeySelective(withdrawLog);
        }

    }

    /**
     * 审核提现申请（财务二审）
     *
     * @param form
     */
    @BusiCode
    public void saveReview(WithdrawAuditForm form) {
        UCashWithdrawOrder withdrawLog = withdrawComponent.getWithdrawLogForUpdate(form.getOrderNo());
        if (withdrawLog == null) {
            throw new BusiException("提现申请不存在");
        }
        if (withdrawLog.getStatus() != WithdrawStatus.WaitReview) {
            throw new BusiException("提现申请不是一审通过状态");
        }
        withdrawLog.setReviewUserId(GlobalUtils.uid());
        withdrawLog.setReviewReason(form.getAuditReason());
        withdrawLog.setReviewTime(DateUtils.nowTime());

        userLockTemplate.acquireTransactionLock(withdrawLog.getUserId());
        if (form.getPass() == BoolType.False) {
            // 将提现修改为拒绝
            withdrawComponent.updateWithdrawResult(withdrawLog, null, WithdrawStatus.ReviewRefused, "提现申请二审核拒绝：" + form.getAuditReason());
            examineFailNotice(withdrawLog, form.getAuditReason());
        } else {
            UUserBasic basic = userInfoManager.getUserBasic(withdrawLog.getUserId());
            if (Status.Disable == basic.getStatus()) {
                throw new BusiException("当前用户已被永久禁用,不可审核通过");
            }
            // 查询该用户最近待提交打款订单的提交时间（适配 云账户强校验）
            Date lastSubmitTime = withdrawComponent.getUserWaitWithdrawOrderLastSubmitTime(withdrawLog.getUserId());
            // 二审通过，设置计划提交时间，等待定时任务处理
            withdrawLog.setExpectSubmitTime(Objects.isNull(lastSubmitTime) ? DateUtils.nowTime() : DateUtils.plusMinutes(lastSubmitTime, 2));
            withdrawLog.setStatus(WithdrawStatus.Wait);
            uCashWithdrawOrderMapper.updateByPrimaryKeySelective(withdrawLog);
        }
    }

    /**
     * 挂起订单，冻结
     *
     * @param form
     */
    @BusiCode
    public void saveHangup(WithdrawHangUpForm form) {
        uCashWithdrawOrderMapper.updateHangUp(form.getUserId(), form.getReason());
    }

    /**
     * 冻结的订单恢复
     *
     * @param form
     */
    @BusiCode
    public void saveRevokeHangup(WithdrawHangUpForm form) {
        userLockTemplate.acquireTransactionLock(form.getUserId());
        if (userQueryMapper.countReportRecord(form.getUserId()) > 0) {
            // 还存在其他的举报记录，不能恢复订单
            return;
        }
        UCashWithdrawOrder withdraw = uCashWithdrawOrderMapper.selectByKeyForUpdate(new UCashWithdrawOrder(form.getOrderNo()));
        if (withdraw == null) {
            return;
        }
        // 生成新的订单
        UCashWithdrawTpl tpl = uCashWithdrawTplMapper.selectByPrimaryKey(new UCashWithdrawTpl(withdraw.getTplId()));
        UCashWithdrawOrder log = withdrawComponent.revokeHangup(withdraw.getUserId(), tpl, withdraw.getWithdrawType(), withdraw);
        // 作废原有的订单
        withdraw.setStatus(WithdrawStatus.Failure);
        withdraw.setStatusTime(DateUtils.nowTime());
        withdraw.setStatusReason("冻结恢复，作废当前订单，重新生成提现订单，新的订单ID:" + log.getOrderNo());
        uCashWithdrawOrderMapper.updateByPrimaryKeySelective(withdraw);
    }


    public List<UCashWithdrawOrder> getHangUpList(WithdrawHangUpForm form) {
        return uCashWithdrawOrderMapper.listHangup(form.getUserId());
    }

    /**
     * 港澳台往来居住证申请免验证
     *
     * @param form
     */
    @BusiCode
    public void saveCommitUserAuth(WithdrawAuthForm form) {
        UCashAuthInfo authInfo = uCashAuthInfoMapper.selectByPrimaryKey(new UCashAuthInfo(form.getUserId()));
        if (authInfo == null || authInfo.getStatus() == WithdrawStatus.Success) {
            throw new BusiException("认证信息不存在或已通过，请检查");
        }
        UIdentityAuth identityAuth = userInfoManager.getUserIdentityAuth(form.getUserId());
        if (identityAuth == null || StringUtils.isEmpty(identityAuth.getRealname()) || StringUtils.isEmpty(identityAuth.getIdentityNo())) {
            throw new BusiException("请先实名认证");
        }
        // 开始下单
        String realName = identityAuth.getRealname();
        String identifyNo = identityAuth.getIdentityNo();
        String[] images = new String[2];
        try {
            images[0] = Base64Utils.encode(IOUtils.toByteArray(FileUtils.download(GlobalConstant.FILE_OSS_URL + authInfo.getFrontPic())));
            images[1] = Base64Utils.encode(IOUtils.toByteArray(FileUtils.download(GlobalConstant.FILE_OSS_URL + authInfo.getBackPic())));
        } catch (Exception e) {
            throw new BusiException("图片转换失败");
        }
        boolean success = YunApiUtils.auth(authInfo.getUserId(), realName, identifyNo, images, authInfo.getBirthday(), identityAuth.getSex());
        if (success) {
            // 状态更改为进行中
            authInfo.setStatus(WithdrawStatus.Processing);
            uCashAuthInfoMapper.updateByPrimaryKeySelective(authInfo);
            return;
        }
        throw new BusiException("身份认证提交失败失败");
    }

    /**
     * 更新认证结果
     *
     * @param status
     * @param comment
     * @return
     */
    @BusiCode
    public boolean saveAuthResult(Long userId, String status, String comment) {
        try {
            UCashAuthInfo authInfo = uCashAuthInfoMapper.selectByPrimaryKey(new UCashAuthInfo(userId));
            if (authInfo == null) {
                LOG.error("云账户认证回调处理错误，没有找到用户信息：{}", userId);
                return true;
            }
            if (authInfo.getStatus() == WithdrawStatus.Success) {
                return true;
            }
            if ("pass".equals(status)) {
                authInfo.setStatus(WithdrawStatus.Success);
            } else {
                authInfo.setStatus(WithdrawStatus.Failure);
            }
            authInfo.setRemark(comment);
            uCashAuthInfoMapper.updateByPrimaryKeySelective(authInfo);
            return true;
        } catch (Exception e) {
            LOG.error("云账户认证回调处理错误", e);
        }
        return false;
    }

    /**
     * 提现成功邀请人分成处理
     */
    @MQInvoke
    public void saveWithdrawSucPresent(){
        // 提现用户
        Long userId = GlobalUtils.extLong(BusinessDataKey.TargetUserId);
        // 提现金额（元）
        Double money = GlobalUtils.extDouble(BusinessDataKey.Money);
        if(Objects.isNull(userId) || Objects.isNull(money)){
            return;
        }
        UUserExt userExt = userInfoManager.getUserExt(userId);
        if(Objects.nonNull(userExt.getInviteUserId())){
            /*
            // 该用户有邀请人，增加邀请人提成
            BigDecimal rate = new BigDecimal(GlobalConstant.INVITOR_WITHDRAW_PRESENT_RATE);
            BigDecimal presentCash = new BigDecimal(money).multiply(rate);
            ConsumePresentDTO presentDTO = ConsumePresentDTO.builder(UserCashSourceType.IvtWithdraw)
                    .setPaidUserId(GlobalConstant.SYS_USER_ID)
                    .setConsumeBeans(0L)
                    .setConsumePlatformBeans(0L)
                    .setPresentUserId(userExt.getInviteUserId())
                    .setPresentCash(BusiUtils.yuanToCash(presentCash.doubleValue()))
                    .setPresentPlatformCash(0L)
                    .setCreateTime(new Date());
            consumePresentComponent.addPresent(presentDTO, MapUtils.gmap("beInviteUserId", userId));

            UUserExt invitorExt = userInfoManager.getUserExt(userExt.getInviteUserId());
            if(Objects.nonNull(invitorExt.getInviteUserId())){
                // 有顶级邀请人，增加顶级邀请人提成
                BigDecimal topPresentCash = presentCash.multiply(rate);
                ConsumePresentDTO presentDTO1 = ConsumePresentDTO.builder(UserCashSourceType.WvtWithdraw)
                        .setPaidUserId(GlobalConstant.SYS_USER_ID)
                        .setConsumeBeans(0L)
                        .setConsumePlatformBeans(0L)
                        .setPresentUserId(invitorExt.getInviteUserId())
                        .setPresentCash(BusiUtils.yuanToCash(topPresentCash.doubleValue()))
                        .setPresentPlatformCash(0L)
                        .setCreateTime(new Date());
                consumePresentComponent.addPresent(presentDTO1, MapUtils.gmap("beInviteUserId", invitorExt.getUserId()));
            }*/

            // 处理被邀请人提现满额提成
            dealInviteFullAmountPresent(userId, userExt.getInviteUserId());
        }
    }

    /**
     * 被邀请人提现满额 邀请人提成
     * 规则：
     * 1、被邀请人提现满 50 给邀请人 0.2分成  ==》 50 * 0.2 = 10
     * 2、被邀请人提现满 200 给邀请人 0.15分成  ==》 200 * 0.15 = 30
     * 3、被邀请人提现满 1000 给邀请人 0.06分成  ==》 1000 * 0.06 = 60
     * 4、2023-12-15新增规则、邀请人每种奖励（10、30、60）最多只能领取两次
     */
    public void dealInviteFullAmountPresent(Long beInviteUser, Long invitorUser){
        // 1、
        dealInviteFullAmountPresentOne(beInviteUser, invitorUser);
        // 2、
        dealInviteFullAmountPresentTwo(beInviteUser, invitorUser);
        // 3、
        dealInviteFullAmountPresentThree(beInviteUser, invitorUser);
    }

    /**
     * 提现满50奖励10处理
     */
    private void dealInviteFullAmountPresentOne(Long beInviteUser, Long invitorUser){
        // 如果被邀请人已经被给过50元满额提成了
        if(!keyMarkManager.exists(beInviteUser, UserBusiKeyMark.FullWithdrawInviteAwardOne)){
            // 判断邀请人该种奖励是否已达到领取次数上限
            UUserKeyMark invitorReceiveTimesMark = keyMarkManager.getKeyMark(invitorUser, UserBusiKeyMark.FullWithdrawInviteAwardTimesOne);
            int receiveTimes = Objects.isNull(invitorReceiveTimesMark) ? 0 : Integer.parseInt(invitorReceiveTimesMark.getBusiValue());
            if(receiveTimes >= 2){ // 条件满足，说明已经达到该种类型的领取次数上限
                return;
            }
            UCash uCash = uCashMapper.selectByPrimaryKey(new UCash(beInviteUser));
            if(Objects.nonNull(uCash.getWithdrawTotal()) && uCash.getWithdrawTotal() >= BusiUtils.yuanToCash(50)){
                // 幂等控制
                if(!busiManager.saveIdempotentMapper(MsgUtils.format(INVITE_FULL_AMOUNT_AWARD, beInviteUser, "one"), new Date())){
                    return;
                }
                // 给邀请人发奖励
                ConsumePresentDTO presentDTO = ConsumePresentDTO.builder(UserCashSourceType.IvtWithdrawFullAmount)
                        .setPaidUserId(GlobalConstant.SYS_USER_ID)
                        .setConsumeBeans(0L)
                        .setConsumePlatformBeans(0L)
                        .setPresentUserId(invitorUser)
                        .setPresentCash(BusiUtils.yuanToCash(10))
                        .setPresentPlatformCash(0L)
                        .setCreateTime(new Date());
                consumePresentComponent.addPresent(presentDTO, MapUtils.gmap("beInviteUserId", beInviteUser));
                // 事物后插入标识
                keyMarkManager.saveOrUpdateUserMark(beInviteUser, UserBusiKeyMark.FullWithdrawInviteAwardOne);
                keyMarkManager.saveOrUpdateUserMark(invitorUser, UserBusiKeyMark.FullWithdrawInviteAwardTimesOne, String.valueOf(receiveTimes + 1));
            }
        }
    }

    /**
     * 提现满200奖励30处理
     */
    private void dealInviteFullAmountPresentTwo(Long beInviteUser, Long invitorUser){
        // 如果被邀请人已经被给过200元满额提成了
        if(!keyMarkManager.exists(beInviteUser, UserBusiKeyMark.FullWithdrawInviteAwardTwo)){
            // 判断邀请人该种奖励是否已达到领取次数上限
            UUserKeyMark invitorReceiveTimesMark = keyMarkManager.getKeyMark(invitorUser, UserBusiKeyMark.FullWithdrawInviteAwardTimesTwo);
            int receiveTimes = Objects.isNull(invitorReceiveTimesMark) ? 0 : Integer.parseInt(invitorReceiveTimesMark.getBusiValue());
            if(receiveTimes >= 2){ // 条件满足，说明已经达到该种类型的领取次数上限
                return;
            }
            UCash uCash = uCashMapper.selectByPrimaryKey(new UCash(beInviteUser));
            if(Objects.nonNull(uCash.getWithdrawTotal()) && uCash.getWithdrawTotal() >= BusiUtils.yuanToCash(200)){
                // 幂等控制
                if(!busiManager.saveIdempotentMapper(MsgUtils.format(INVITE_FULL_AMOUNT_AWARD, beInviteUser, "two"), new Date())){
                    return;
                }
                // 给邀请人发奖励
                ConsumePresentDTO presentDTO = ConsumePresentDTO.builder(UserCashSourceType.IvtWithdrawFullAmount)
                        .setPaidUserId(GlobalConstant.SYS_USER_ID)
                        .setConsumeBeans(0L)
                        .setConsumePlatformBeans(0L)
                        .setPresentUserId(invitorUser)
                        .setPresentCash(BusiUtils.yuanToCash(30))
                        .setPresentPlatformCash(0L)
                        .setCreateTime(new Date());
                consumePresentComponent.addPresent(presentDTO, MapUtils.gmap("beInviteUserId", beInviteUser));
                // 事物后插入标识
                keyMarkManager.saveOrUpdateUserMark(beInviteUser, UserBusiKeyMark.FullWithdrawInviteAwardTwo);
                keyMarkManager.saveOrUpdateUserMark(invitorUser, UserBusiKeyMark.FullWithdrawInviteAwardTimesTwo, String.valueOf(receiveTimes + 1));
            }
        }
    }

    /**
     * 提现满1000奖励60处理
     */
    private void dealInviteFullAmountPresentThree(Long beInviteUser, Long invitorUser){
        // 如果被邀请人已经被给过1000元满额提成了
        if(!keyMarkManager.exists(beInviteUser, UserBusiKeyMark.FullWithdrawInviteAwardThree)){
            // 判断邀请人该种奖励是否已达到领取次数上限
            UUserKeyMark invitorReceiveTimesMark = keyMarkManager.getKeyMark(invitorUser, UserBusiKeyMark.FullWithdrawInviteAwardTimesThree);
            int receiveTimes = Objects.isNull(invitorReceiveTimesMark) ? 0 : Integer.parseInt(invitorReceiveTimesMark.getBusiValue());
            if(receiveTimes >= 2){ // 条件满足，说明已经达到该种类型的领取次数上限
                return;
            }
            UCash uCash = uCashMapper.selectByPrimaryKey(new UCash(beInviteUser));
            if(Objects.nonNull(uCash.getWithdrawTotal()) && uCash.getWithdrawTotal() >= BusiUtils.yuanToCash(1000)){
                // 幂等控制
                if(!busiManager.saveIdempotentMapper(MsgUtils.format(INVITE_FULL_AMOUNT_AWARD, beInviteUser, "three"), new Date())){
                    return;
                }
                // 给邀请人发奖励
                ConsumePresentDTO presentDTO = ConsumePresentDTO.builder(UserCashSourceType.IvtWithdrawFullAmount)
                        .setPaidUserId(GlobalConstant.SYS_USER_ID)
                        .setConsumeBeans(0L)
                        .setConsumePlatformBeans(0L)
                        .setPresentUserId(invitorUser)
                        .setPresentCash(BusiUtils.yuanToCash(60))
                        .setPresentPlatformCash(0L)
                        .setCreateTime(new Date());
                consumePresentComponent.addPresent(presentDTO, MapUtils.gmap("beInviteUserId", beInviteUser));
                // 事物后插入标识
                keyMarkManager.saveOrUpdateUserMark(beInviteUser, UserBusiKeyMark.FullWithdrawInviteAwardThree);
                keyMarkManager.saveOrUpdateUserMark(invitorUser, UserBusiKeyMark.FullWithdrawInviteAwardTimesThree, String.valueOf(receiveTimes + 1));
            }
        }
    }

    /**
     * 提现审核 失败系统通知
     */
    private void examineFailNotice(UCashWithdrawOrder withdrawLog, String reason){
        if(StringUtils.isEmpty(reason.trim())){
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("date", DateUtils.toString(withdrawLog.getCreateTime(), DatePattern.MD2));
        params.put("money", withdrawLog.getRealMoney());
        params.put("reason", reason);
        noticeComponent.sendSystemNotice(withdrawLog.getUserId(), NoticeSysType.WithDrawApplyCheckRefuse, params);
    }

    /**
     * 提现记录封装
     *
     * @param log
     * @param vo
     */
    private void buildWithDrawLog(UCashWithdrawOrder log, WithdrawHistoryVO vo) {
        UCashWithdrawTpl tpl = uCashWithdrawTplMapper.selectByPrimaryKey(new UCashWithdrawTpl(log.getTplId()));
        vo.setOrderNo(log.getOrderNo());
        vo.setType(log.getWithdrawType());
        vo.setMoney(NumberUtils.format(log.getMoney()));
        vo.setRealMoney(NumberUtils.format(log.getRealMoney()));
        vo.setStatus(log.getStatus());
        vo.setStatusText(log.getStatus().getDisplayStatus().getDesc());
        vo.setTips(MsgUtils.format("-{}积分余额", BusiUtils.cashToYuanStr(tpl.getNeedCash(), 2)));
        // 状态原因
        switch (log.getStatus().getDisplayStatus()) {
            case Processing:
                vo.setStatusReason(MsgUtils.format("预计1~3天到账"));
                if (tpl.getIsQuick().boolValue()) {
                    vo.setStatusReason(MsgUtils.format("预计2小时内到账"));
                }
                break;
            case Success:
                vo.setStatusReason("已到账");
                break;
            case Failure:
                WithdrawStatus drawStatus = log.getStatus();
                if (WithdrawStatus.Refuse == drawStatus || WithdrawStatus.ReviewRefused == drawStatus) {
                    vo.setStatusReason("账户存在违规行为");
                    break;
                }
                if (StringUtils.isNotEmpty(log.getStatusReason()) && (log.getStatusReason().contains("举报") || log.getStatusReason().contains("封禁"))) {
                    vo.setStatusReason("账户存在违规行为");
                    break;
                }
                vo.setStatusReason(log.getStatusReason());
                break;
            default:
                break;
        }
        vo.setTime(DateUtils.toString(log.getCreateTime(), DatePattern.MD_HMS4));
    }
}
