package com.tuowan.yeliao.acct.web.form.recharge;


import com.tuowan.yeliao.commons.data.enums.user.VipTriggerType;
import com.tuowan.yeliao.commons.web.common.form.Form;

/**
 * VIP 查询表单
 *
 * <AUTHOR>
 * @date 2022/2/15 15:26
 */
public class VipQueryForm implements Form {

    /**
     * 到账用户ID(如果给自己充值，不需要填)
     */
    private Long targetUserId;
    /**
     * VIP开通界面的特权类型
     */
    private String type;
    /**
     * 跳转到 VIP 开通界面的触发类型
     */
    private VipTriggerType triggerType;

    public Long getTargetUserId() {
        return targetUserId;
    }

    public void setTargetUserId(Long targetUserId) {
        this.targetUserId = targetUserId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public VipTriggerType getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(VipTriggerType triggerType) {
        this.triggerType = triggerType;
    }
}
