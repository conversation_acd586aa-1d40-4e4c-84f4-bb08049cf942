package com.tuowan.yeliao.acct.web.vo.cash.exchange;

import com.alibaba.fastjson.annotation.JSONField;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.acct.data.entity.UExchangeOrder;
import com.tuowan.yeliao.acct.data.enums.BeansType;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;

import java.util.Date;
import java.util.Objects;

/**
 * 兑换历史信息封装
 *
 * <AUTHOR>
 * @date 2021/8/10 9:09
 */
public class CashToBeanExchangeHisVO {

    /** 兑换记录ID */
    @KeyProperty
    private Long logId;

    /** 兑换用户 */
    private Long userId;

    /** 消耗的零钱 */
    private String cash;

    /** 兑换的金币数量 */
    private Integer beans;

    /** 兑换的金币类型 */
    private BeansType beansType;

    /** 兑换的金币类型描述 */
    private String beansTypeDesc;

    /** 创建时间 */
    @JSONField(format = "M-d HH:mm:ss")
    private Date createTime;

    public static CashToBeanExchangeHisVO create1(UExchangeOrder log) {
        CashToBeanExchangeHisVO vo = new CashToBeanExchangeHisVO();
        vo.setLogId(log.getLogId());
        vo.setUserId(log.getUserId());
        vo.setCash(BusiUtils.cashToYuanSimplifyStr(log.getCash().longValue(), 2));
        vo.setBeans(log.getBeans());
        vo.setBeansType(log.getBeansType());
        vo.setBeansTypeDesc(Objects.isNull(log.getBeansType()) ? null : log.getBeansType().getDesc());
        vo.setCreateTime(log.getCreateTime());
        return vo;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getCash() {
        return cash;
    }

    public void setCash(String cash) {
        this.cash = cash;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public BeansType getBeansType() {
        return beansType;
    }

    public void setBeansType(BeansType beansType) {
        this.beansType = beansType;
    }

    public String getBeansTypeDesc() {
        return beansTypeDesc;
    }

    public void setBeansTypeDesc(String beansTypeDesc) {
        this.beansTypeDesc = beansTypeDesc;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
