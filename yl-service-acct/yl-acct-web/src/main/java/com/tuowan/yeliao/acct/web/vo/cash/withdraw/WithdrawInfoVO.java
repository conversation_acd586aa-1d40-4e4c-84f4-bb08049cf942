package com.tuowan.yeliao.acct.web.vo.cash.withdraw;

import com.easyooo.framework.common.util.NumberUtils;
import com.tuowan.yeliao.acct.data.entity.UCash;
import com.tuowan.yeliao.acct.data.entity.UCashIncomeDayStat;
import com.tuowan.yeliao.acct.data.entity.UCashWithdrawTpl;
import com.tuowan.yeliao.acct.web.vo.cash.cashinfo.WeekCashVO;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.data.entity.user.UIdentityAuth;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.data.utils.dto.AdDefineDTO;

import java.util.List;

/**
 * 提现首页信息
 *
 * <AUTHOR>
 * @date 2020/7/17 13:38
 */
public class WithdrawInfoVO {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户性别
     */
    private SexType sexType;
    /**
     * 当前零钱
     */
    private String cash;
    /**
     * 今日奖励
     */
    private String todayCash;
    /**
     * 累计提现
     */
    private String withdrawCash;
    /**
     * 提现金额列表
     */
    private List<WithdrawTplVO> tplList;
    /**
     * 提现方式列表
     */
    private List<WithdrawTypeVO> typeList;
    /**
     * 在线客服地址
     */
    private String customerUrl;
    /**
     * 提现页面地址
     */
    private String withdrawUrl;
    /**
     * 是否已经微信授权
     */
    private BoolType wechatAuthorized;
    /**
     * 是否参与周收益奖励
     */
    private BoolType incomeReward;

    /**
     * 周收益奖励信息
     */
    private WeekCashVO weekCashReward;

    /**
     * 提现的提示信息
     */
    private String withdrawTips;

    /** 充值页广告列表 */
    private List<AdDefineDTO> adList;

    /** 自由职业者服务协议 */
    private String withdrawRule;

    /** 是否完成工猫电签 */
    private BoolType finishGmSg;

    /** 是否具备转账功能 */
    private BoolType transferRight;

    public static WithdrawInfoVO create(UCash uCash, UCashIncomeDayStat dayStat, List<UCashWithdrawTpl> tplList,
                                        UIdentityAuth identityAuth, ClientType curClientType, BoolType realName, ClientType sourceClientType) {
        WithdrawInfoVO vo = new WithdrawInfoVO();
        if (uCash != null) {
            vo.setCash(BusiUtils.cashToYuanStr(uCash.getCash()));
            vo.setWithdrawCash(BusiUtils.cashToYuanStr(uCash.getWithdrawTotal()));
        } else {
            vo.setCash(NumberUtils.formatStyle1(0));
            vo.setWithdrawCash(NumberUtils.formatStyle1(0));
        }
        if (dayStat != null) {
            vo.setTodayCash(BusiUtils.cashToYuanStr(dayStat.getTotalCash()));
        } else {
            vo.setTodayCash(NumberUtils.formatStyle1(0));
        }
        vo.setTplList(WithdrawTplVO.create(tplList));
        vo.setTypeList(WithdrawTypeVO.create(identityAuth, curClientType, realName, sourceClientType));
        // IOS专用
        vo.setWechatAuthorized(BoolType.valueOf(identityAuth != null && identityAuth.getWeixinUserId() != null));
        return vo;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public SexType getSexType() {
        return sexType;
    }

    public void setSexType(SexType sexType) {
        this.sexType = sexType;
    }

    public String getCash() {
        return cash;
    }

    public void setCash(String cash) {
        this.cash = cash;
    }

    public String getTodayCash() {
        return todayCash;
    }

    public void setTodayCash(String todayCash) {
        this.todayCash = todayCash;
    }

    public String getWithdrawCash() {
        return withdrawCash;
    }

    public void setWithdrawCash(String withdrawCash) {
        this.withdrawCash = withdrawCash;
    }

    public List<WithdrawTplVO> getTplList() {
        return tplList;
    }

    public void setTplList(List<WithdrawTplVO> tplList) {
        this.tplList = tplList;
    }

    public List<WithdrawTypeVO> getTypeList() {
        return typeList;
    }

    public void setTypeList(List<WithdrawTypeVO> typeList) {
        this.typeList = typeList;
    }

    public String getCustomerUrl() {
        return customerUrl;
    }

    public void setCustomerUrl(String customerUrl) {
        this.customerUrl = customerUrl;
    }

    public String getWithdrawUrl() {
        return withdrawUrl;
    }

    public void setWithdrawUrl(String withdrawUrl) {
        this.withdrawUrl = withdrawUrl;
    }

    public BoolType getWechatAuthorized() {
        return wechatAuthorized;
    }

    public void setWechatAuthorized(BoolType wechatAuthorized) {
        this.wechatAuthorized = wechatAuthorized;
    }

    public BoolType getIncomeReward() {
        return incomeReward;
    }

    public void setIncomeReward(BoolType incomeReward) {
        this.incomeReward = incomeReward;
    }

    public WeekCashVO getWeekCashReward() {
        return weekCashReward;
    }

    public void setWeekCashReward(WeekCashVO weekCashReward) {
        this.weekCashReward = weekCashReward;
    }

    public String getWithdrawTips() {
        return withdrawTips;
    }

    public void setWithdrawTips(String withdrawTips) {
        this.withdrawTips = withdrawTips;
    }

    public List<AdDefineDTO> getAdList() {
        return adList;
    }

    public void setAdList(List<AdDefineDTO> adList) {
        this.adList = adList;
    }

    public String getWithdrawRule() {
        return withdrawRule;
    }

    public void setWithdrawRule(String withdrawRule) {
        this.withdrawRule = withdrawRule;
    }

    public BoolType getFinishGmSg() {
        return finishGmSg;
    }

    public void setFinishGmSg(BoolType finishGmSg) {
        this.finishGmSg = finishGmSg;
    }

    public BoolType getTransferRight() {
        return transferRight;
    }

    public void setTransferRight(BoolType transferRight) {
        this.transferRight = transferRight;
    }
}
