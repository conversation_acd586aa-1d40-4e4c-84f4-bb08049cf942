package com.tuowan.yeliao.log.comp.promotion;


import com.easyooo.framework.common.extend.SimpleMap;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.LogRedisTemplate;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.context.detail.ServiceHeader;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.core.enums.redis.LogKeyDefine;
import com.tuowan.yeliao.commons.data.entity.config.TPromotionConfig;
import com.tuowan.yeliao.commons.data.enums.config.PromotionEventType;
import com.tuowan.yeliao.commons.data.enums.config.PromotionProvider;
import com.tuowan.yeliao.commons.data.enums.config.PromotionReportType;
import com.tuowan.yeliao.commons.data.enums.config.PromotionType;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.support.config.impl.PromotionConfig;
import com.tuowan.yeliao.commons.eventbus.EventBusPublisher;
import com.tuowan.yeliao.commons.eventbus.enums.EventBusType;
import com.tuowan.yeliao.log.comp.promotion.dto.DeviceIdDTO;
import com.tuowan.yeliao.log.comp.promotion.dto.ReportEventResult;
import com.tuowan.yeliao.log.data.entity.CPromotionAndroid;
import com.tuowan.yeliao.log.data.entity.CPromotionIos;
import com.tuowan.yeliao.log.data.entity.CPromotionReport;
import com.tuowan.yeliao.log.data.entity.PromotionBase;
import com.tuowan.yeliao.log.data.enums.PromotionStatus;
import com.tuowan.yeliao.log.data.persistence.CPromotionAndroidMapper;
import com.tuowan.yeliao.log.data.persistence.CPromotionIosMapper;
import com.tuowan.yeliao.log.data.persistence.CPromotionReportMapper;
import com.tuowan.yeliao.user.api.remote.UserWebRemote;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 推广业务组件
 *
 * <AUTHOR>
 * @date 2022/4/12 21:44
 */
@Component
public class PromotionComponent implements ApplicationContextAware {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    private Map<PromotionProvider, IPromotionProvider> providerMap = new HashMap<>();

    @Autowired
    private CPromotionIosMapper cPromotionIosMapper;
    @Autowired
    private CPromotionAndroidMapper cPromotionAndroidMapper;
    @Autowired
    private CPromotionReportMapper cPromotionReportMapper;
    @Autowired
    private LogRedisTemplate logRedisTemplate;
    @Autowired
    private EventBusPublisher eventBusPublisher;
    @Autowired
    private UserWebRemote userWebRemote;
    @Autowired
    private UserInfoManager userInfoManager;

    /**
     * 处理授权认证
     *
     * @param provider
     * @param packageType
     * @param params
     */
    public void processAuthorize(PromotionProvider provider, PackageType packageType, SimpleMap params) {
        IPromotionProvider providerImpl = providerMap.get(provider);
        if (providerImpl == null) {
            LOG.error("当前推广厂商不支持，provider: {}", provider);
            return;
        }
        providerImpl.authorize(packageType, params);
    }

    /**
     * 获取访问令牌
     *
     * @param provider
     * @return
     */
    public String getAccessToken(PromotionProvider provider, PackageType packageType) {
        IPromotionProvider providerImpl = providerMap.get(provider);
        if (providerImpl == null) {
            LOG.error("当前推广厂商不支持，provider: {}", provider);
            return null;
        }
        return providerImpl.getAccessToken(packageType);
    }

    /**
     * 处理点击
     *
     * @param provider
     * @param typeCode
     * @param clientType
     * @param params
     */
    public void processClick(PromotionProvider provider, PackageType packageType, String typeCode, ClientType clientType, SimpleMap params) {
        IPromotionProvider providerImpl = providerMap.get(provider);
        if (providerImpl == null) {
            LOG.error("当前推广厂商不支持，provider: {}", provider);
            return;
        }
        PromotionBase promotion = providerImpl.parseData(clientType, packageType, params);
        DeviceIdDTO deviceInfo = buildDeviceId(clientType, promotion.getDeviceOaidMd5(), promotion.getDeviceImeiMd5(), promotion.getIp(), promotion.getUa(), true);
        if (deviceInfo.isEmpty()) {
            LOG.debug("设备标识为空，已忽略");
            return;
        }
        // 判断是否为无效的请求  备注：该方法主要是应对同个人短时间频繁点击监测链接的问题
        if (isInvalidRequest(deviceInfo, PromotionEventType.Click, packageType.getId())) {
            return;
        }
        // 如果该设备 在最近30天内存在【激活】数据 那么此次点击我们不记录数据库
        PromotionBase existsPromotion = getPromotionInfo(deviceInfo, clientType, packageType, true, false, false);
        if (existsPromotion != null) {
            return;
        }
        // 优先取自定义推广类型代码
        typeCode = StringUtils.defaultString(typeCode, provider.getTypeCode());
        // 新增推广记录
        addPromotionLog(clientType, promotion, typeCode, deviceInfo);
    }

    /**
     * 处理激活
     */
    public void processActive() {
        ClientType clientType = GlobalUtils.clientType();
        DeviceIdDTO deviceInfo = buildDeviceId();
        if (deviceInfo.isEmpty()) {
            LOG.info("推广设备号为空，无法处理激活，请求头信息：{}", JsonUtils.seriazileAsString(GlobalUtils.header()));
            return;
        }
        PromotionBase promotion = getPromotionInfo(deviceInfo, clientType, GlobalUtils.packageType(), false, false, false);
        if (promotion == null) { // 没有点击事件，有可能是市场回传（eg:vivo）, 或者是市场自然量
            processStoreActive(clientType);
            return;
        }
        // 如果该记录 已经上报过 【激活】 我们不再上报
        if (PromotionStatus.Success == promotion.getActiveStatus()) {
            return;
        }
        promotion.setUa(GlobalUtils.userAgent());
        TPromotionConfig config = PromotionConfig.getConfig(promotion.getTypeCode());
        if (config == null) {
            LOG.warn("未找到对应的推广配置，typeCode: {}", promotion.getTypeCode());
            return;
        }

        // 异步处理应用激活(商店激活应用渠道和归因渠道是一致的，无需处理)
        GlobalUtils.extValue(BusinessDataKey.ChannelCode, config.getChannelCode());
        eventBusPublisher.sendAsync(EventBusType.AppActive);

        PromotionReportType reportType = config.getReportType();
        if (!PromotionUtils.isMatchEvent(PromotionEventType.Activation, reportType.getEndEvent())) {
            LOG.debug("当前推广告记录无需执行激活操作");
            return;
        }
        IPromotionProvider providerImpl = providerMap.get(reportType.getProvider());
        if (providerImpl == null) {
            LOG.error("当前推广厂商不支持，provider: {}", reportType.getEndEvent());
            return;
        }
        try {
            // 执行上报激活事件
            ReportEventResult result = providerImpl.reportActiveEvent(clientType, promotion, config);
            if (result.isNeed()) {
                // 更新上报结果
                PromotionStatus status = result.isSuccess() ? PromotionStatus.Success : PromotionStatus.Failure;
                updateLogStatus(clientType, PromotionEventType.Activation, promotion, status, result);
            }
        } catch (Exception e) {
            LOG.error("上报激活事件失败，原因：", e);
        }
    }

    /**
     * 处理注册-思会
     */
    public void processRegister() {
        Long userId = GlobalUtils.uid();
        ClientType clientType = GlobalUtils.clientType();
        DeviceIdDTO deviceInfo = buildDeviceId();
        if (deviceInfo.isEmpty()) {
            LOG.info("推广设备号为空，无法处理注册，请求头信息：{}", JsonUtils.seriazileAsString(GlobalUtils.header()));
            return;
        }
        // 对于 头条、快手 这样的流平台来说，promotion 不应该为null，也就是说只有当 promotion 不为null时我们才处理
        PromotionBase promotion = getPromotionInfo(deviceInfo, clientType, GlobalUtils.packageType(), true, false, false);
        if (promotion == null) { // 没有点击事件，有可能是市场回传（eg:vivo）, 或者是市场自然量
            processStoreRegister(clientType, userId);
            return;
        }
        /*if (Objects.isNull(promotion.getActiveStatusTime()) || DateUtils.getDiffDays(promotion.getActiveStatusTime()) > 0) {
            // 如果跟激活时间相差24小时之后 也不再上报
            return;
        }*/
        // 如果已经上报过注册了 取消上报
        if (PromotionStatus.Success == promotion.getRegisterStatus()) {
            return;
        }
        promotion.setUa(GlobalUtils.userAgent());
        TPromotionConfig config = PromotionConfig.getConfig(promotion.getTypeCode());
        if (config == null) {
            LOG.warn("未找到对应的推广配置，typeCode: {}", promotion.getTypeCode());
            return;
        }
        // 异步处理应用注册(有归因渠道)
        GlobalUtils.extValue(BusinessDataKey.ChannelCode, config.getChannelCode());
        eventBusPublisher.sendAsync(EventBusType.AppRegister);

        PromotionReportType reportType = config.getReportType();
        if (!PromotionUtils.isMatchEvent(PromotionEventType.Register, reportType.getEndEvent())) {
            LOG.debug("当前推广告记录无需执行注册操作");
            return;
        }
        IPromotionProvider providerImpl = providerMap.get(reportType.getProvider());
        if (providerImpl == null) {
            LOG.error("当前推广厂商不支持，provider: {}", reportType.getProvider());
            return;
        }
        try {
            // 执行上报注册事件
            ReportEventResult result = providerImpl.reportRegisterEvent(clientType, promotion, config);
            if (result.isNeed()) {
                // 更新上报结果
                PromotionStatus status = result.isSuccess() ? PromotionStatus.Success : PromotionStatus.Failure;
                promotion.setRegisterUserId(userId);
                updateLogStatus(clientType, PromotionEventType.Register, promotion, status, result);
            }
        } catch (Exception e) {
            LOG.error("上报注册事件失败，原因：", e);
        }
    }

    /**
     * 处理付费
     *
     * @param userId    付费用户ID
     * @param payAmount 付费金额(单位分)
     */
    public void processPay(Long userId, Integer payAmount, boolean isFirstPaid) {
        // 无论是信息流 还是 市场；目前我们都仅上报首次付费 且都不回传付费金额
        if (userId == null || !isFirstPaid) {
            return;
        }
        ClientType clientType = GlobalUtils.clientType();
        DeviceIdDTO deviceInfo = buildDeviceId();
        if (deviceInfo.isEmpty()) {
            LOG.info("推广设备号为空，无法处理付费，请求头信息：{}", JsonUtils.seriazileAsString(GlobalUtils.header()));
            return;
        }
        // 对于流平台来说 promotion 不应该为null，也就是说要promotion不为null我们才处理
        PromotionBase promotion = getPromotionInfo(deviceInfo, clientType, GlobalUtils.packageType(), true, true, false);
        if (promotion == null) { // 没有点击事件，有可能是市场回传（eg:vivo）, 或者是市场自然量
            processStorePay(clientType, userId, payAmount, isFirstPaid);
            return;
        }
        /*if (Objects.isNull(promotion.getActiveStatusTime()) || DateUtils.getDiffDays(promotion.getActiveStatusTime()) > 0) {
            // 如果跟激活时间相差24小时之后 也不再上报
            return;
        }*/
        // 如果已经上报成功过付费了 取消上报
        if (PromotionStatus.Success == promotion.getPayStatus()) {
            return;
        }
        promotion.setUa(GlobalUtils.userAgent());
        TPromotionConfig config = PromotionConfig.getConfig(promotion.getTypeCode());
        if (config == null) {
            LOG.warn("未找到对应的推广配置，typeCode: {}", promotion.getTypeCode());
            return;
        }
        PromotionReportType reportType = config.getReportType();
        if (!PromotionUtils.isMatchEvent(PromotionEventType.Pay, reportType.getEndEvent())) {
            LOG.debug("当前推广告记录无需执行付费操作");
            return;
        }
        IPromotionProvider providerImpl = providerMap.get(reportType.getProvider());
        if (providerImpl == null) {
            LOG.error("当前推广厂商不支持，provider: {}", reportType.getProvider());
            return;
        }
        try {
            // 执行上报付费事件
            ReportEventResult result = providerImpl.reportPayEvent(clientType, promotion, config, userId, payAmount, isFirstPaid);
            // 如果需要上报且未更新付费上报状态，则更新
            if (result.isNeed()) {
                // 更新上报结果
                PromotionStatus status = result.isSuccess() ? PromotionStatus.Success : PromotionStatus.Failure;
                updateLogStatus(clientType, PromotionEventType.Pay, promotion, status, result);
            }
        } catch (Exception e) {
            LOG.error("上报付费事件失败，原因：", e);
        }
    }

    /**
     * 处理应用商店激活
     * <p>
     * 目前只有安卓
     *
     * @param clientType
     */
    private void processStoreActive(ClientType clientType) {
        if (ClientType.Android != clientType) {
            return;
        }
        // 判断是否为商店推广的渠道
        PromotionType type = PromotionType.getPromotionType(GlobalUtils.channelId());
        if (type == null) {
            return;
        }
        DeviceIdDTO deviceInfo = buildDeviceId();
        String typeCode = type.getId();
        TPromotionConfig config = PromotionConfig.getConfig(typeCode);
        if (config == null) {
            LOG.error("未找到对应的推广配置，typeCode: {}", typeCode);
            return;
        }
        PromotionProvider provider = config.getReportType().getProvider();
        IPromotionProvider providerImpl = providerMap.get(provider);
        if (providerImpl == null) {
            LOG.error("当前推广厂商不支持，provider: {}", provider);
            return;
        }
        CPromotionReport report = getPromotionReportInfo(typeCode, deviceInfo);
        if (report != null) {
            LOG.debug("商店推广上报记录已存在，deviceOaid:{}, deviceImei:{}", GlobalUtils.deviceOaid(), GlobalUtils.deviceImei());
            return;
        }
        try {
            // 上报激活事件
            PromotionBase promotion = PromotionBase.build(deviceInfo.getDeviceId(), deviceInfo.getDeviceId2(), GlobalUtils.deviceOaid(), GlobalUtils.deviceImei(), GlobalUtils.clientIp());
            ReportEventResult result = providerImpl.reportActiveEvent(clientType, promotion, config);
            if (result.isNeed() && result.isSuccess()) {
                CPromotionReport entity = new CPromotionReport();
                entity.setTypeCode(typeCode);
                entity.setClientType(clientType);
                entity.setDeviceId(deviceInfo.getDeviceId());
                entity.setDeviceId2(deviceInfo.getDeviceId2());
                entity.setDeviceOaid(GlobalUtils.deviceOaid());
                entity.setDeviceImei(GlobalUtils.deviceImei());
                entity.setActiveStatus(PromotionStatus.Success);
                entity.setActiveStatusTime(new Date());
                entity.setPackageType(GlobalUtils.packageType());
                entity.setCreateTime(entity.getActiveStatusTime());
                cPromotionReportMapper.insert(entity);
            }
        } catch (Exception e) {
            LOG.error("上报商店激活事件失败，原因：", e);
        }
    }

    /**
     * 处理应用商店注册
     * <p>
     * 目前只有安卓
     *
     * @param clientType
     */
    private void processStoreRegister(ClientType clientType, Long userId) {
        if (ClientType.Android != clientType) {
            return;
        }
        // 判断是否为商店推广的渠道
        PromotionType type = PromotionType.getPromotionType(GlobalUtils.channelId());
        if (type == null) {
            return;
        }
        DeviceIdDTO deviceInfo = buildDeviceId();
        String typeCode = type.getId();
        TPromotionConfig config = PromotionConfig.getConfig(typeCode);
        if (config == null) {
            LOG.error("未找到对应的推广配置，typeCode: {}", typeCode);
            return;
        }
        PromotionProvider provider = config.getReportType().getProvider();
        IPromotionProvider providerImpl = providerMap.get(provider);
        if (providerImpl == null) {
            LOG.error("当前推广厂商不支持，provider: {}", provider);
            return;
        }
        CPromotionReport report = getPromotionReportInfo(typeCode, deviceInfo);
        if (report != null && PromotionStatus.Success == report.getRegisterStatus()) {
            LOG.debug("商店推广上报记录已注册，deviceOaid:{}, deviceImei:{}", GlobalUtils.deviceOaid(), GlobalUtils.deviceImei());
            return;
        }
        try {
            // 上报注册事件
            PromotionBase promotion = PromotionBase.build(deviceInfo.getDeviceId(), deviceInfo.getDeviceId2(), GlobalUtils.deviceOaid(), GlobalUtils.deviceImei(), GlobalUtils.clientIp());
            ReportEventResult result = providerImpl.reportRegisterEvent(clientType, promotion, config);
            if (!result.isNeed()) {
                return;
            }
            Date nowTime = new Date();
            CPromotionReport update = new CPromotionReport();
            update.setRegisterStatus(result.isSuccess() ? PromotionStatus.Success : PromotionStatus.Failure);
            update.setRegisterStatusTime(nowTime);
            update.setRegisterUserId(userId);
            // 可能注册时没有激活数据，默认算作激活，新增一条记录
            if (report == null) {
                update.setTypeCode(typeCode);
                update.setClientType(clientType);
                update.setDeviceId(deviceInfo.getDeviceId());
                update.setDeviceId2(deviceInfo.getDeviceId2());
                update.setDeviceImei(GlobalUtils.deviceImei());
                update.setDeviceOaid(GlobalUtils.deviceOaid());
                update.setActiveStatus(PromotionStatus.Success);
                update.setActiveStatusTime(nowTime);
                update.setPackageType(GlobalUtils.packageType());
                update.setCreateTime(nowTime);
                cPromotionReportMapper.insert(update);
            } else {
                update.setLogId(report.getLogId());
                update.setCreateTime(report.getCreateTime());
                cPromotionReportMapper.updateByPrimaryKeySelective(update);
            }
        } catch (Exception e) {
            LOG.error("上报商店激活事件失败，原因：", e);
        }
    }
    /**
     * 处理应用商店付费
     * <p>
     * 目前只有安卓
     *
     * @param clientType
     */
    private void processStorePay(ClientType clientType, Long userId, Integer payAmount, boolean isFirstPaid) {
        if (ClientType.Android != clientType) {
            return;
        }
        // 判断是否为商店推广的渠道
        PromotionType type = PromotionType.getPromotionType(GlobalUtils.channelId());
        if (type == null) {
            return;
        }
        DeviceIdDTO deviceInfo = buildDeviceId();
        String typeCode = type.getId();
        TPromotionConfig config = PromotionConfig.getConfig(typeCode);
        if (config == null) {
            LOG.error("PromotionComponent-processStorePay-noConfigCanBeFound-typeCode: {}", typeCode);
            return;
        }
        PromotionProvider provider = config.getReportType().getProvider();
        IPromotionProvider providerImpl = providerMap.get(provider);
        if (providerImpl == null) {
            LOG.error("PromotionComponent-processStorePay-thisProviderIsNotSupport,provider: {}", provider);
            return;
        }
        CPromotionReport report = getPromotionReportInfo(typeCode, deviceInfo);
        if (report != null && PromotionStatus.Success == report.getPayStatus()) {
            LOG.debug("PromotionComponent-processStorePay-reportPayIsAlreadySuccess,deviceOaid:{},deviceImei:{}", GlobalUtils.deviceOaid(), GlobalUtils.deviceImei());
            return;
        }
        try {
            // 上报付费事件
            PromotionBase promotion = PromotionBase.build(deviceInfo.getDeviceId(), deviceInfo.getDeviceId2(), GlobalUtils.deviceOaid(), GlobalUtils.deviceImei(), GlobalUtils.clientIp());
            ReportEventResult result = providerImpl.reportPayEvent(clientType, promotion, config, userId, payAmount, isFirstPaid);
            if (!result.isNeed()) {
                return;
            }
            Date nowTime = new Date();
            CPromotionReport update = new CPromotionReport();
            update.setPayStatus(result.isSuccess() ? PromotionStatus.Success : PromotionStatus.Failure);
            update.setPayStatusTime(nowTime);
            update.setPayReport(BoolType.valueOf(result.isPayReport()));
            if (report == null) {
                update.setTypeCode(typeCode);
                update.setClientType(clientType);
                update.setDeviceId(deviceInfo.getDeviceId());
                update.setDeviceId2(deviceInfo.getDeviceId2());
                update.setDeviceImei(GlobalUtils.deviceImei());
                update.setDeviceOaid(GlobalUtils.deviceOaid());
                update.setPayStatus(PromotionStatus.Success);
                update.setPayStatusTime(nowTime);
                update.setPackageType(GlobalUtils.packageType());
                update.setCreateTime(nowTime);
                update.setPayReport(BoolType.valueOf(result.isPayReport()));
                cPromotionReportMapper.insert(update);
            } else {
                update.setLogId(report.getLogId());
                update.setCreateTime(report.getCreateTime());
                cPromotionReportMapper.updateByPrimaryKeySelective(update);
            }
        } catch (Exception e) {
            LOG.error("PromotionComponent-processStorePay-reportPayFail,e:{}", e);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IPromotionProvider> impls = applicationContext.getBeansOfType(IPromotionProvider.class);
        for (IPromotionProvider impl : impls.values()) {
            providerMap.put(impl.getSupportType(), impl);
        }
    }

    /**
     * 构建设备唯一标识
     *
     * @return
     */
    private DeviceIdDTO buildDeviceId() {
        ServiceHeader header = GlobalUtils.header();
        return buildDeviceId(header.getClientType(), header.getDeviceOaid(), header.getDeviceImei(), header.getClientIp(), header.getUserAgent(), false);
    }

    /**
     * 构建设备标识
     *
     * @param clientType
     * @param deviceOaid
     * @param deviceImei
     * @param ip
     * @param ua
     * @param isMd5      是否已经是MD5值
     * @return
     */
    private DeviceIdDTO buildDeviceId(ClientType clientType, String deviceOaid, String deviceImei, String ip, String ua, boolean isMd5) {
        String deviceId = GlobalConstant.NONE;
        String deviceId2 = GlobalConstant.NONE;
        if (ClientType.iOS == clientType) {
            // 第一优先级：IDFA
            if (StringUtils.isNotEmpty(deviceImei) && !PromotionUtils.isInvalidId(deviceImei)) {
                deviceId = isMd5 ? deviceImei : EncryptUtils.md5(deviceImei).toLowerCase();
            }
            // 第二优先级：IP+UA(模糊归因，1.0.9版本才开始支持，1.0.9以下版本UA为空)
            if (StringUtils.isNotEmpty(ip) && StringUtils.isNotEmpty(ua)) {
                deviceId2 = EncryptUtils.md5(MsgUtils.format("{}{}", ip, ua)).toLowerCase();
            }
        } else if (ClientType.Android == clientType) {
            // 第一优先级：OAID(Android10及以上版本)
            if (StringUtils.isNotEmpty(deviceOaid) && !PromotionUtils.isInvalidId(deviceOaid)) {
                deviceId = isMd5 ? deviceOaid : EncryptUtils.md5(deviceOaid).toLowerCase();
            }
            // 第二优先级：IMEI(Android10以下版本)
            if (StringUtils.isNotEmpty(deviceImei)) {
                deviceId2 = isMd5 ? deviceImei : EncryptUtils.md5(deviceImei).toLowerCase();
            }
        }
        return DeviceIdDTO.build(deviceId, deviceId2);
    }

    /**
     * 增加推广记录
     *
     * @param clientType
     * @param promotion
     * @param typeCode
     * @param deviceIdInfo
     */
    private void addPromotionLog(ClientType clientType, PromotionBase promotion, String typeCode, DeviceIdDTO deviceIdInfo) {
        promotion.setTypeCode(typeCode);
        promotion.setDeviceId(deviceIdInfo.getDeviceId());
        promotion.setDeviceId2(deviceIdInfo.getDeviceId2());
        promotion.setActiveStatus(PromotionStatus.Wait);
        promotion.setRegisterStatus(PromotionStatus.Wait);
        promotion.setPayStatus(PromotionStatus.Wait);
        promotion.setCreateTime(new Date());
        if (ClientType.iOS == clientType) {
            cPromotionIosMapper.insert((CPromotionIos) promotion);
        } else if (ClientType.Android == clientType) {
            cPromotionAndroidMapper.insert((CPromotionAndroid) promotion);
        }
    }

    /**
     * 获取推广信息
     *
     * @param clientType
     * @param isActive   是否已激活
     * @return
     */
    private PromotionBase getPromotionInfo(DeviceIdDTO deviceInfo, ClientType clientType, PackageType packageType, boolean isActive, boolean isReg, boolean isPay) {
        PromotionBase promotion;
        if (ClientType.iOS == clientType) {
            // 第一优先级：使用IDFA查找
            if (!Objects.equals(deviceInfo.getDeviceId(), GlobalConstant.NONE)) {
                promotion = cPromotionIosMapper.selectByDeviceId(deviceInfo.getDeviceId(), packageType.getId(), isActive, isReg, isPay);
                if (promotion != null) {
                    return promotion;
                }
            }
            // 第二优先级：使用IP + UA查找
            if (!Objects.equals(deviceInfo.getDeviceId2(), GlobalConstant.NONE)) {
                promotion = cPromotionIosMapper.selectByDeviceId2(deviceInfo.getDeviceId2(), packageType.getId(), isActive, isReg, isPay);
                if (promotion != null) {
                    return promotion;
                }
            }
            return null;
        }
        if (ClientType.Android == clientType) {
            // 第一优先级：使用OAID查找
            if (!Objects.equals(deviceInfo.getDeviceId(), GlobalConstant.NONE)) {
                promotion = cPromotionAndroidMapper.selectByDeviceId(deviceInfo.getDeviceId(), packageType.getId(), isActive, isReg, isPay);
                if (promotion != null) {
                    return promotion;
                }
            }
            // 第二优先级：使用IMEI查找(ANDROID ID)
            if (!Objects.equals(deviceInfo.getDeviceId2(), GlobalConstant.NONE)) {
                promotion = cPromotionAndroidMapper.selectByDeviceId2(deviceInfo.getDeviceId2(), packageType.getId(), isActive, isReg, isPay);
                if (promotion != null) {
                    return promotion;
                }
            }
            return null;
        }
        return null;
    }

    /**
     * 获取推广上报信息
     *
     * @param typeCode
     * @param deviceInfo
     * @return
     */
    private CPromotionReport getPromotionReportInfo(String typeCode, DeviceIdDTO deviceInfo) {
        CPromotionReport promotion;
        // 第一优先级：使用OAID查找
        if (!Objects.equals(deviceInfo.getDeviceId(), GlobalConstant.NONE)) {
            promotion = cPromotionReportMapper.selectByDeviceId(typeCode, deviceInfo.getDeviceId());
            if (promotion != null) {
                return promotion;
            }
        }
        // 第二优先级：使用IMEI查找
        if (!Objects.equals(deviceInfo.getDeviceId2(), GlobalConstant.NONE)) {
            promotion = cPromotionReportMapper.selectByDeviceId2(typeCode, deviceInfo.getDeviceId2());
            if (promotion != null) {
                return promotion;
            }
        }
        return null;
    }

    /**
     * 修改信息流状态
     *
     * @param clientType
     * @param eventType
     * @param promotion
     * @param status
     * @param result
     */
    private void updateLogStatus(ClientType clientType, PromotionEventType eventType, PromotionBase promotion, PromotionStatus status, ReportEventResult result) {
        PromotionBase update;

        if (ClientType.iOS == clientType) {
            update = new CPromotionIos();
        } else if (ClientType.Android == clientType) {
            update = new CPromotionAndroid();
        } else {
            return;
        }
        update.setLogId(promotion.getLogId());
        update.setCreateTime(promotion.getCreateTime());

        CPromotionReport report = new CPromotionReport();
        report.setClientType(clientType);
        report.setTypeCode(promotion.getTypeCode());
        report.setDeviceId(promotion.getDeviceId());
        report.setDeviceId2(promotion.getDeviceId2());
        report.setDeviceOaid(promotion.getDeviceOaid());
        report.setDeviceImei(promotion.getDeviceImei());
        report.setAdId(promotion.getAdId());
        report.setPromotionId(promotion.getPromotionId());
        report.setProjectId(promotion.getProjectId());
        report.setPackageType(GlobalUtils.packageType());

        if (PromotionEventType.Activation == eventType) {
            update.setActiveStatus(status);
            update.setActiveStatusTime(new Date());

            report.setActiveStatus(update.getActiveStatus());
            report.setActiveStatusTime(update.getActiveStatusTime());
        } else if (PromotionEventType.Register == eventType) {
            update.setRegisterStatus(status);
            update.setRegisterStatusTime(new Date());
            update.setRegisterUserId(promotion.getRegisterUserId());

            report.setRegisterStatus(update.getRegisterStatus());
            report.setRegisterStatusTime(update.getRegisterStatusTime());
            report.setRegisterUserId(update.getRegisterUserId());
        } else if (PromotionEventType.Pay == eventType) {
            update.setPayStatus(status);
            update.setPayStatusTime(new Date());

            report.setPayStatus(update.getPayStatus());
            report.setPayStatusTime(update.getPayStatusTime());
            report.setPayReport(BoolType.valueOf(result.isPayReport()));
        } else {
            return;
        }
        boolean success = false;
        if (ClientType.iOS == clientType) {
            success = cPromotionIosMapper.updateByPrimaryKeySelective((CPromotionIos) update) > 0;
        } else if (ClientType.Android == clientType) {
            success = cPromotionAndroidMapper.updateByPrimaryKeySelective((CPromotionAndroid) update) > 0;
        }
        try {
            // 只记录成功状态的数据
            if (success && PromotionStatus.Success == status) {
                // 根据推广类型和设备标识查询已上报记录，如果已存在则修改
                CPromotionReport exists = getPromotionReportInfo(promotion.getTypeCode(), DeviceIdDTO.build(promotion.getDeviceId(), promotion.getDeviceId2()));
                if (exists != null) {
                    report.setLogId(exists.getLogId());
                    report.setCreateTime(exists.getCreateTime());
                }
                if (cPromotionReportMapper.updateByPrimaryKeySelective(report) <= 0) {
                    report.setCreateTime(new Date());
                    cPromotionReportMapper.insert(report);
                }
            }
        } catch (Exception e) {
            LOG.error("更新或新增推广上报记录失败，原因：", e);
        }
    }

    /**
     * 5分钟内容如果是重复数据请求，则忽略
     *
     * @param deviceInfo
     * @param eventType
     * @return
     */
    private boolean isInvalidRequest(DeviceIdDTO deviceInfo, PromotionEventType eventType, String pk) {
        String requestId = deviceInfo.getRequestId();
        if (StringUtils.isEmpty(requestId)) {
            return false;
        }
        requestId = MsgUtils.format("{}_{}_{}", requestId, eventType.name(), pk);
        RedisKey redisKey = buildPromotionRequestMarkKey(requestId);
        if (logRedisTemplate.incr(redisKey) > 1) {
            LOG.info("接收到重复请求，deviceInfo: {}, eventType: {}", deviceInfo, eventType);
            return true;
        }
        return false;
    }

    private RedisKey buildPromotionRequestMarkKey(String requestId) {
        return RedisKey.create(LogKeyDefine.PromotionRequestMark, requestId);
    }
}
