/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.log.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.log.data.entity.CPromotionReport;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "C_PROMOTION_REPORT", schema = "YL_LOG")
public interface CPromotionReportMapper {
    int deleteByPrimaryKey(CPromotionReport record);

    int insert(CPromotionReport record);

    CPromotionReport selectByPrimaryKey(CPromotionReport record);

    int updateByPrimaryKeySelective(CPromotionReport record);

    int updateByPrimaryKey(CPromotionReport record);

    CPromotionReport selectByDeviceId(@Param("typeCode") String typeCode, @Param("deviceId") String deviceId);

    CPromotionReport selectByDeviceId2(@Param("typeCode") String typeCode, @Param("deviceId2") String deviceId2);
}