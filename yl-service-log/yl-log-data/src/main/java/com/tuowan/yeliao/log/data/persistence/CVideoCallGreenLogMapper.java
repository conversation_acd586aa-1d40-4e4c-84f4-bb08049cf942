/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.log.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.log.data.entity.CVideoCallGreenLog;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "C_VIDEO_CALL_GREEN_LOG", schema = "YL_LOG")
public interface CVideoCallGreenLogMapper {
    int deleteByPrimaryKey(CVideoCallGreenLog record);

    int insert(CVideoCallGreenLog record);

    CVideoCallGreenLog selectByPrimaryKey(CVideoCallGreenLog record);

    int updateByPrimaryKeySelective(CVideoCallGreenLog record);

    int updateByPrimaryKey(CVideoCallGreenLog record);
}