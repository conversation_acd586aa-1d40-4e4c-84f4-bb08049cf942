package com.tuowan.yeliao.social.api.request;

public class IntimateRequest {
    private Long userId;
    private Long friendId;

    public static IntimateRequest build(Long userId, Long friendId){
        IntimateRequest request = new IntimateRequest();
        request.setUserId(userId);
        request.setFriendId(friendId);
        return request;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }
}
