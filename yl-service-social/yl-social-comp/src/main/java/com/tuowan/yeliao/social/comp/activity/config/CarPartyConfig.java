package com.tuowan.yeliao.social.comp.activity.config;


import com.tuowan.yeliao.social.comp.activity.enums.CarPartyProdType;

/**
 * 座驾派对配置
 *
 * <AUTHOR>
 * @date 2021/11/8 17:15
 */
public class CarPartyConfig {

    /** 商品ID */
    private Integer prodId;

    /** 商品名称 */
    private String prodName;

    /** 商品图片 */
    private String prodPic;

    /** 动效链接 */
    private String dynamicUrl;

    /** 等级 */
    private Integer level;

    public CarPartyConfig(CarPartyProdType partyProdType) {
        this.prodId = Integer.valueOf(partyProdType.getId());
        this.prodName = partyProdType.getDesc();
        this.prodPic = partyProdType.getProdPic();
        this.dynamicUrl = partyProdType.getDynamicUrl();
        this.level = partyProdType.getLevel();
    }

    public Integer getProdId() {
        return prodId;
    }

    public void setProdId(Integer prodId) {
        this.prodId = prodId;
    }

    public String getProdName() {
        return prodName;
    }

    public void setProdName(String prodName) {
        this.prodName = prodName;
    }

    public String getProdPic() {
        return prodPic;
    }

    public void setProdPic(String prodPic) {
        this.prodPic = prodPic;
    }

    public String getDynamicUrl() {
        return dynamicUrl;
    }

    public void setDynamicUrl(String dynamicUrl) {
        this.dynamicUrl = dynamicUrl;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
}
