package com.tuowan.yeliao.social.comp.activity.config;

import java.util.Arrays;
import java.util.List;

public class PigBankConfig {

    /**
     * 存储罐活动任务配置
     */
    public static final List<PigBankTask> TASK_LIST;

    static {
        TASK_LIST = Arrays.asList(
                new PigBankTask("PB1", 1, 3, 10),
                new PigBankTask("PB2", 2, 10, 50),
                new PigBankTask("PB3", 3, 20, 100),
                new PigBankTask("PB4", 4, 50, 500),
                new PigBankTask("PB5", 5, 100, 1000)
        );
    }

    public static class PigBankTask{
        /**
         * 任务code标识
         */
        private String taskCode;
        /**
         * 排序
         */
        private Integer order;
        /**
         * 该任务奖励信息
         */
        private Integer awardCash;
        /**
         * 完成该任务需达到收益信息
         */
        private Integer finishCash;

        public PigBankTask(String taskCode, Integer order, Integer awardCash, Integer finishCash) {
            this.taskCode = taskCode;
            this.order = order;
            this.awardCash = awardCash;
            this.finishCash = finishCash;
        }

        public String getTaskCode() {
            return taskCode;
        }

        public void setTaskCode(String taskCode) {
            this.taskCode = taskCode;
        }

        public Integer getOrder() {
            return order;
        }

        public void setOrder(Integer order) {
            this.order = order;
        }

        public Integer getAwardCash() {
            return awardCash;
        }

        public void setAwardCash(Integer awardCash) {
            this.awardCash = awardCash;
        }

        public Integer getFinishCash() {
            return finishCash;
        }

        public void setFinishCash(Integer finishCash) {
            this.finishCash = finishCash;
        }
    }
}
