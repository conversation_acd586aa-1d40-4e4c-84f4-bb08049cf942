package com.tuowan.yeliao.social.comp.activity.dto;


import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;

/**
 * 春节活动信息封装
 *
 * <AUTHOR>
 * @date 2022/1/18 17:20
 */
public class SpringTaskDTO {

    /**
     * 任务代码
     */
    private String taskCode;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 任务描述
     */
    private String taskDesc;
    /**
     * 完成任务需要的数量
     */
    private Long needCount;
    /**
     * 当前达到的数量
     */
    private Long nowCount;
    /**
     * 待领取的数量
     */
    private Long waitReceiveCount;
    /**
     * 任务状态
     */
    private String taskStatus;

    private transient Integer orderNum;

    private ClientTouchType touchType;

    private String touchValue;

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskDesc() {
        return taskDesc;
    }

    public void setTaskDesc(String taskDesc) {
        this.taskDesc = taskDesc;
    }

    public Long getNeedCount() {
        return needCount;
    }

    public void setNeedCount(Long needCount) {
        this.needCount = needCount;
    }

    public Long getNowCount() {
        return nowCount;
    }

    public void setNowCount(Long nowCount) {
        this.nowCount = nowCount;
    }

    public Long getWaitReceiveCount() {
        return waitReceiveCount;
    }

    public void setWaitReceiveCount(Long waitReceiveCount) {
        this.waitReceiveCount = waitReceiveCount;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public ClientTouchType getTouchType() {
        return touchType;
    }

    public void setTouchType(ClientTouchType touchType) {
        this.touchType = touchType;
    }

    public String getTouchValue() {
        return touchValue;
    }

    public void setTouchValue(String touchValue) {
        this.touchValue = touchValue;
    }
}
