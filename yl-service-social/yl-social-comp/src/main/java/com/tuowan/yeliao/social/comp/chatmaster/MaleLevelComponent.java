package com.tuowan.yeliao.social.comp.chatmaster;

import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.EnumUtils;
import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.core.exception.InternalException;
import com.tuowan.yeliao.commons.data.entity.config.TMaleLevelNum;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserExt;
import com.tuowan.yeliao.commons.data.enums.user.MaleLevel;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.persistence.config.TMaleLevelNumMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 男用户等级组件
 */
@Component
public class MaleLevelComponent {
    private static final Logger LOG = LoggerFactory.getLogger(MaleLevelComponent.class);

    // 男用户等级缓存保留时间；也就是说缓存更新间隔（10分钟）
    private static final int MALE_NUM_LEVEL_REFRESH_SECOND = 600;

    @Autowired
    private TMaleLevelNumMapper maleLevelNumMapper;
    @Autowired
    private SocialRedisTemplate socialRedisTemplate;
    @Autowired
    private UserInfoManager userInfoManager;

    /**
     * 获取男用户等级
     */
    public MaleLevel maleNumLevel(Long userId){
        RedisKey redisKey = buildMaleNumLevel(userId);
        // 先从缓存中获取
        String cache = socialRedisTemplate.get(redisKey);
        if(StringUtils.isNotEmpty(cache)){
            return EnumUtils.byName(cache, MaleLevel.class);
        }
        MaleLevel level = null;
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        if(DateUtils.getDiffDays(userBasic.getCreateTime()) < 5){
            level = MaleLevel.New;
        }else{
            level = MaleLevel.Old;
        }
        // 缓存男用户的等级信息
        socialRedisTemplate.set(redisKey.ttl(MALE_NUM_LEVEL_REFRESH_SECOND), level.name());
        return level;
    }

    /**
     * 构建男用户等级缓存
     */
    private RedisKey buildMaleNumLevel(Long userId){
        return RedisKey.create(SocialKeyDefine.MaleNumLevel, userId);
    }

}
