package com.tuowan.yeliao.social.comp.chatmaster.dto;

import com.tuowan.yeliao.commons.data.enums.user.ChatMasterLevel;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;

/**
 * 新人扶持信息
 *
 */
public class NewSupportDTO {

    /**
     * 嘉宾等级
     */
    private ChatMasterLevel masterLevel;
    /**
     * 所需金额
     */
    private Long needMoney;

    public static NewSupportDTO build(ChatMasterLevel masterLevel, Integer needMoney) {
        NewSupportDTO dto = new NewSupportDTO();
        dto.setMasterLevel(masterLevel);
        dto.setNeedMoney(BusiUtils.yuanToCash(needMoney * 1D));
        return dto;
    }

    public ChatMasterLevel getMasterLevel() {
        return masterLevel;
    }

    public void setMasterLevel(ChatMasterLevel masterLevel) {
        this.masterLevel = masterLevel;
    }

    public Long getNeedMoney() {
        return needMoney;
    }

    public void setNeedMoney(<PERSON> needMoney) {
        this.needMoney = needMoney;
    }
}
