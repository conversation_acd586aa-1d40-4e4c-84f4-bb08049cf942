package com.tuowan.yeliao.social.comp.consume.dto;

import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;

public class FriendsTopDTO {
    private String title;
    private String pic;
    private ClientTouchType touchType;
    private String touchValue;

    public static FriendsTopDTO build1(String title, String pic, ClientTouchType touchType, String touchValue){
        FriendsTopDTO dto = new FriendsTopDTO();
        dto.setTitle(title);
        dto.setPic(pic);
        dto.setTouchType(touchType);
        dto.setTouchValue(touchValue);
        return dto;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public ClientTouchType getTouchType() {
        return touchType;
    }

    public void setTouchType(ClientTouchType touchType) {
        this.touchType = touchType;
    }

    public String getTouchValue() {
        return touchValue;
    }

    public void setTouchValue(String touchValue) {
        this.touchValue = touchValue;
    }
}
