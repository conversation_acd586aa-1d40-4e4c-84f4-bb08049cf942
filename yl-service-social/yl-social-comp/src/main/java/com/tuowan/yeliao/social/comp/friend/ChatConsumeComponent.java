package com.tuowan.yeliao.social.comp.friend;

import com.alibaba.nacos.common.utils.Pair;
import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.comp.consume.ConsumePresentComponent;
import com.tuowan.yeliao.commons.comp.consume.dto.ConsumePresentDTO;
import com.tuowan.yeliao.commons.comp.grant.GrantComponent;
import com.tuowan.yeliao.commons.comp.grant.dto.AwardDTO;
import com.tuowan.yeliao.commons.comp.grant.dto.GrantDTO;
import com.tuowan.yeliao.commons.comp.grant.dto.GrantDetailDTO;
import com.tuowan.yeliao.commons.comp.grant.dto.GrantProdDTO;
import com.tuowan.yeliao.commons.comp.luckybox.LuckyAwardDTO;
import com.tuowan.yeliao.commons.comp.luckybox.LuckyboxComponent;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.comp.message.dto.PrivateChatUserInfoDTO;
import com.tuowan.yeliao.commons.comp.user.UserBusiComponent;
import com.tuowan.yeliao.commons.comp.user.UserSocialBagComponent;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.RongImConfig;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.business.SocialGoodsDefine;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.ComponentException;
import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.core.http.Root;
import com.tuowan.yeliao.commons.data.dto.user.BeansConsumeDTO;
import com.tuowan.yeliao.commons.data.entity.config.TAwardDetail;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGift;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGoods;
import com.tuowan.yeliao.commons.data.entity.config.TSendGiftLimit;
import com.tuowan.yeliao.commons.data.entity.user.UMessageSetting;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserExt;
import com.tuowan.yeliao.commons.data.entity.user.UUserLocation;
import com.tuowan.yeliao.commons.data.enums.acct.WealthType;
import com.tuowan.yeliao.commons.data.enums.common.TargetPresentType;
import com.tuowan.yeliao.commons.data.enums.config.ProdGoodsType;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.enums.config.SocialGiftGroup;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.enums.general.RedisShardType;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.commons.data.enums.social.ChatContentType;
import com.tuowan.yeliao.commons.data.enums.social.GiftWallDpType;
import com.tuowan.yeliao.commons.data.enums.user.ChatMasterLevel;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.enums.user.UserCashSourceType;
import com.tuowan.yeliao.commons.data.manager.config.NewsManager;
import com.tuowan.yeliao.commons.data.manager.config.SocialProdManager;
import com.tuowan.yeliao.commons.data.manager.user.MessageSettingManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.persistence.config.TAwardDetailMapper;
import com.tuowan.yeliao.commons.data.persistence.config.TSendGiftLimitMapper;
import com.tuowan.yeliao.commons.data.persistence.user.UUserBasicMapper;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.data.utils.RedisShardUtils;
import com.tuowan.yeliao.commons.mq.enums.MessageTag;
import com.tuowan.yeliao.commons.mq.producer.MessageQueueProducer;
import com.tuowan.yeliao.social.comp.chatmaster.ChatMasterComponent;
import com.tuowan.yeliao.social.comp.consume.dto.BlindBoxInfoDTO;
import com.tuowan.yeliao.social.comp.consume.dto.GiftTvWallDTO;
import com.tuowan.yeliao.social.comp.friend.dto.MagicDrawResult;
import com.tuowan.yeliao.social.comp.friend.dto.MagicPrizeDTO;
import com.tuowan.yeliao.social.comp.friend.dto.PrivateChatFeeDTO;
import com.tuowan.yeliao.social.comp.log.LogComponent;
import com.tuowan.yeliao.social.data.config.InvitorPresentConfig;
import com.tuowan.yeliao.social.data.dto.family.FamilyMemberDTO;
import com.tuowan.yeliao.social.data.dto.friend.ChatConsumeDTO;
import com.tuowan.yeliao.social.data.dto.friend.PresentCashDTO;
import com.tuowan.yeliao.social.data.dto.friend.RelationDTO;
import com.tuowan.yeliao.social.data.dto.friend.SendGiftStatDTO;
import com.tuowan.yeliao.social.data.entity.*;
import com.tuowan.yeliao.social.data.entity.family.FFamily;
import com.tuowan.yeliao.social.data.enums.friend.ChatFeeWaiverType;
import com.tuowan.yeliao.social.data.enums.friend.ChatSourceType;
import com.tuowan.yeliao.social.data.manager.friend.ChatManager;
import com.tuowan.yeliao.social.data.manager.friend.MagicGiftManager;
import com.tuowan.yeliao.social.data.persistence.*;
import com.tuowan.yeliao.user.api.remote.UserWebRemote;
import com.tuowan.yeliao.user.api.request.QueryIpCityRequest;
import com.tuowan.yeliao.user.api.response.QueryIpCityResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import redis.clients.jedis.resps.Tuple;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台聊天相关消费组件
 * 包括聊天消息消息，聊天送礼消费
 *
 * <AUTHOR>
 * @date 2020/7/3 21:53
 */
@Component
public class ChatConsumeComponent {

    private static final Logger LOG = LoggerFactory.getLogger(ChatConsumeComponent.class);
    private static final String SEND_GIFT_LIMIT_FORMAT = "{}_{}";

    @Autowired
    private UserWebRemote userWebRemote;
    @Autowired
    private UserBusiComponent userBusiComponent;
    @Autowired
    private FChatConsumeMapper fChatConsumeMapper;
    @Autowired
    private FChatConsumeWaitMapper fChatConsumeWaitMapper;
    @Autowired
    private FriendRelationComponent friendRelationComponent;
    @Autowired
    private SocialRedisTemplate socialRedisTemplate;
    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private UUserBasicMapper uUserBasicMapper;
    @Autowired
    private SocialProdManager socialProdManager;
    @Autowired
    private ChatManager chatManager;
    @Autowired
    private NewsManager newsManager;
    @Autowired
    private UserSocialBagComponent userSocialBagComponent;
    @Autowired
    private FRelationBasicMapper fRelationBasicMapper;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private LuckyboxComponent luckyboxComponent;
    @Autowired
    private TAwardDetailMapper tAwardDetailMapper;
    @Autowired
    private USocialBlindBoxMapper uSocialBlindBoxMapper;
    @Autowired
    private MagicGiftManager magicGiftManager;
    @Autowired
    private GrantComponent grantComponent;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private MessageSettingManager messageSettingManager;
    @Autowired
    private ConsumePresentComponent consumePresentComponent;
    @Autowired
    private ChatMasterComponent chatMasterComponent;
    @Autowired
    private TSendGiftLimitMapper sendGiftLimitMapper;
    @Autowired
    private FSendGiftStatMapper sendGiftLimitStatMapper;
    @Autowired
    private MessageQueueProducer messageQueueProducer;
    @Autowired
    private LogComponent logComponent;

    /**
     * 索要礼物频率检查
     * @return true 能送
     * @return false 不能送
     */
    public Pair<Boolean, Long> checkRqGiftFrequency(Long userId, Long friendId){
        Long ttl = socialRedisTemplate.ttl(buildRqGiftFrequency(userId, friendId));
        return Pair.with(ttl == -2, ttl);
    }

    /**
     * 记录礼物索要
     */
    public void recordRqGiftFrequency(Long userId, Long friendId){
        CallbackAfterTransactionUtil.send(() -> {
            socialRedisTemplate.incr(buildRqGiftFrequency(userId, friendId));
        });
    }

    /**
     * 最近交互记录处理
     * 备注：
     * 1、最近交互包含（聊天、送礼【礼物、红包】、音视频通话）
     * 2、用接收者构建缓存
     */
    public void saveSocialLastlyInteractiveTime(String senderId, String receiverId){
        RedisKey redisKey = buildSocialLastlyInteractive(receiverId);
        Date now = new Date();
        // 删减超过6小时的记录
        socialRedisTemplate.zremrangeByScore(redisKey, 0D, DateUtils.plusHours(now, -6).getTime());
        // 新增交互记录
        socialRedisTemplate.zadd(redisKey, (double)now.getTime(), senderId);
        socialRedisTemplate.expire(redisKey);
    }

    /**
     * 获取时间范围内的交互记录
     */
    public List<String> getTimeRangeSocialInteractiveRecord(Long userId, Double minScore, Double maxScore){
        return socialRedisTemplate.zrangeByScore(buildSocialLastlyInteractive(String.valueOf(userId)), minScore, maxScore);
    }

    /**
     * 获取用户未回复人数
     */
    public Long getNoReplyPeople(String dateStr, Long userId, double minTime, double maxTime){
        RedisKey redisKey = buildUserMsgReplyData(dateStr, userId);
        return socialRedisTemplate.zcount(redisKey, minTime, maxTime);
    }

    /**
     * 处理回复情况
     */
    public void recordReplyData(Long senderId, Long receiverId, boolean needRecordNoReply, boolean needRecordReplied){
        CallbackAfterTransactionUtil.send(() -> {
            Date now = new Date();
            String dateStr = DateUtils.toString(new Date(), DatePattern.YMD2);
            if(needRecordNoReply){
                // 记录消息接收方 有一条待回复
                RedisKey toRedisKey = buildUserMsgReplyData(dateStr, receiverId);
                socialRedisTemplate.zadd(toRedisKey, (double)now.getTime(), String.valueOf(senderId));
                socialRedisTemplate.expire(toRedisKey);
            }
            if(needRecordReplied){
                // 记录消息发送方 已回复
                RedisKey fromRedisKey = buildUserMsgReplyData(dateStr, senderId);
                socialRedisTemplate.zadd(fromRedisKey, 0D, String.valueOf(receiverId));
                socialRedisTemplate.expire(fromRedisKey);
            }
        });
    }

    /**
     * 获取礼物墙信息
     * 备注：获取最近10条数据
     */
    public List<String> getCommonGiftWall(){
        return socialRedisTemplate.zrevrangeByIndex(buildGiftTvWall(), 0, 9);
    }

    /**
     * 应用启动获取大额礼物墙信息
     * 备注：获取规则 6小时内的礼物 并且没有播放过
     */
    public List<Tuple> getBaGiftWall(Long userId, Date now, int count){
        Long lastTime = socialRedisTemplate.getLong(buildUserLookBaGiftTime(userId));
        return socialRedisTemplate.zrevrangeByScoreWithScores(buildBaGiftTvWall(), now.getTime(), lastTime + 1, 0, count);
    }

    /**
     * 事物后记录用户查看大额礼物墙最近时间
     */
    public void saveUserLookBaGiftLastTime(Long userId, double score){
        CallbackAfterTransactionUtil.send(() -> {
            socialRedisTemplate.set(buildUserLookBaGiftTime(userId), String.valueOf((long)score));
        });
    }

    /**
     * 礼物上普通电视墙
     * @param tid
     * @param userId
     * @param friendId
     * @param giftId
     * @param totalBeans
     */
    public void saveGiftInCommonTvWall(Long tid, Long userId, Long friendId, Integer giftId, Integer count, Long totalBeans, Date now, GiftWallDpType type){
        RedisKey redisKey = buildGiftTvWall();
        socialRedisTemplate.zadd(redisKey, (double)now.getTime(), JsonUtils.seriazileAsString(GiftTvWallDTO.build(tid, userId, friendId, giftId, count, totalBeans, type)));
        // 做数据裁剪 仅保存20条数据
        Long size = socialRedisTemplate.zcard(redisKey);
        if(size > 20){
            socialRedisTemplate.zremrangeByRank(redisKey, 0L, (size - 20) - 1);
        }
    }

    /**
     * 礼物上大额电视墙
     * @param tid
     * @param userId
     * @param friendId
     * @param giftId
     * @param totalBeans
     */
    public void saveGiftInBaTvWall(Long tid, Long userId, Long friendId, Integer giftId, Integer count, Long totalBeans, Date now){
        RedisKey redisKey = buildBaGiftTvWall();
        socialRedisTemplate.zadd(redisKey, (double)now.getTime(), JsonUtils.seriazileAsString(GiftTvWallDTO.build(tid, userId, friendId, giftId, count, totalBeans, GiftWallDpType.BigAmount)));
        // 做数据裁剪 仅保存6小时内的数据
        socialRedisTemplate.zremrangeByScore(redisKey, 0, DateUtils.plusHours(now, -6).getTime());
    }

    /**
     * 获取当天未充值男用户私聊某个女用户次数
     */
    public int getNrMaleChatFemaleTimes(String dateStr, Long maleUser, Long femaleUser){
        return socialRedisTemplate.hgetInt(buildNrMaleChatFemaleTimes(maleUser, dateStr), String.valueOf(femaleUser), true);
    }

    /**
     * 记录未充值男用户私聊某个女用户次数
     */
    public void recordNrMaleChatFemaleTimes(String dateStr, Long maleUser, Long femaleUser){
        CallbackAfterTransactionUtil.send(() -> {
            RedisKey redisKey = buildNrMaleChatFemaleTimes(maleUser, dateStr);
            socialRedisTemplate.hincrBy(redisKey, String.valueOf(femaleUser), 1L);
            socialRedisTemplate.expire(redisKey);
        });
    }

    /**
     * 判断用户今日是否能一键搭讪
     * @return true 还能搭讪
     * @return false 不能搭讪
     */
    public boolean checkUserBatchChatUp(Long userId){
        return !socialRedisTemplate.exists(buildBatchChatUp(userId));
    }

    /**
     * 标记用户今日已使用一键搭讪
     */
    public void markUserBatchChatUp(Long userId){
        CallbackAfterTransactionUtil.send(() -> {
            socialRedisTemplate.set(buildBatchChatUp(userId), DateUtils.toString(new Date(), DatePattern.YMD_HMS));
        });
    }

    /**
     * 男女用户每天首次消息交互逻辑处理
     * @Param sexType 发送者性别
     * @Param userId 发送者
     * @Param friendId 接收者
     *
     * 备注：缓存中存储的值格式 femaleUserId_maleUserId
     *
     * type = 1 私聊； type = 2 主动搭讪； type = 3 系统搭讪
     */
    public void userFirstMsgDayDeal(SexType sexType, Long userId, Long friendId, Integer type){
        Date now = new Date();
        String dateStr = DateUtils.toString(now, DatePattern.YMD2);
        if(SexType.Female == sexType){
            // 发送方是女性
            String member = MsgUtils.format("{}_{}", userId, friendId);
            RedisKey key = buildUserFirstMsgForDayKey(dateStr, RedisShardUtils.getShardIndex(userId, RedisShardType.UserFirstMsgForDay));
            // 如果缓存中存在记录 则不做任何处理
            if(socialRedisTemplate.zexists(key, member)){
                return;
            }
            // 异步调用 log 去记录
            logComponent.saveUserFirstMsgForDayRecord(DateUtils.getStartOfDay(now), userId, friendId, type, now, null);
            // 往缓存中记录数据
            socialRedisTemplate.zadd(key, 0D, member);
            socialRedisTemplate.expire(key);
        }else{
            // 发送方是男性
            String member = MsgUtils.format("{}_{}", friendId, userId);
            RedisKey key = buildUserFirstMsgForDayKey(dateStr, RedisShardUtils.getShardIndex(friendId, RedisShardType.UserFirstMsgForDay));
            // 一定要缓存中存在值 并且分数等于0才去处理（等于1说明整套数据已经记录完整）
            Double score = socialRedisTemplate.zscore(key, member);
            if(score == null || score != 0D){
                return;
            }
            // 异步调用 log 去记录
            logComponent.saveUserFirstMsgForDayRecord(DateUtils.getStartOfDay(now), friendId, userId, null, null, now);
            // 修改缓存中的数据
            socialRedisTemplate.zadd(key, 1D, member);
        }
    }

    /**
     * 更新送礼限制信息
     */
    public void updateSendGiftIsLimit(String statSign){
        FSendGiftStat update = new FSendGiftStat(statSign);
        update.setIsLimit(BoolType.True);
        sendGiftLimitStatMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 统计用户送礼金额
     */
    public void statUserSendGiftMoney(Long userId, Long friendId, Double money){
        String sign = MsgUtils.format(SEND_GIFT_LIMIT_FORMAT, userId, friendId);
        FSendGiftStat updateObj = new FSendGiftStat(sign);
        updateObj.setStatMoney(money);
        try {
            FSendGiftStat giftLimitStat = sendGiftLimitStatMapper.selectByPrimaryKey(updateObj);
            if(Objects.isNull(giftLimitStat)){
                // 插入
                sendGiftLimitStatMapper.insert(new FSendGiftStat(sign, userId, friendId, money));
            }else {
                // 更新
                sendGiftLimitStatMapper.updateByPrimaryKeySelective(updateObj);
            }
        }catch (DuplicateKeyException e){
            // 更新
            sendGiftLimitStatMapper.updateByPrimaryKeySelective(updateObj);
        }
    }

    /**
     * 判断地区送礼是否达到限制金额
     * 判断顺序：
     * 1. 送礼人必须是男用户
     * 2. 收礼人必须是女用户
     * 3. 先判断限制开关是否打开
     * 4. 判断送礼用户是否在送礼白名单中(送礼白名单中的用户 不送地区送礼金额限制)
     * 5. 查询配置 判断是否达到限制
     * 备注：
     * 1. 先根据用户的定位信息判断
     * 2. 其次根据用户IP定位判断（在1没有信息的情况下）
     * @param userId 送礼用户的标识
     * @param friendId 收礼用户的标识
     * @param beans 当前礼物所消耗金币
     *
     * @return true 已经达到限制 不允许送礼
     * @return false 还未达到限制 允许送礼
     */
    public boolean checkSendGiftArriveLimit(Long userId, Long friendId, Integer beans){
        try {
            // 向全局上下文当中 插入统计数据<消耗金币>;
            GlobalUtils.extValue(BusinessDataKey.SendGiftStatInfo, SendGiftStatDTO.build1(beans));
            BoolType boolType = SettingsConfig.getBoolType(SettingsType.SendGiftAreaLimitSwitch);
            if(!boolType.boolValue()){
                return false;
            }
            String sendWhiteList = SettingsConfig.getString(SettingsType.SendGiftWhiteList);
            if(sendWhiteList.contains(userId.toString())){
                return false;
            }
            UUserLocation userLocation = userInfoManager.getUserLocation(userId);
            // 定位位置信息不存在 或者 最后更新时间大于10天
            boolean locationIsInValid = Objects.isNull(userLocation);// || DateUtils.getDiffDays(userLocation.getUpdateTime()) > 10;
            String province = locationIsInValid ? null : userLocation.getProvince();
            String city = locationIsInValid ? null : userLocation.getCity();
            if(StringUtils.isEmpty(province) || StringUtils.isEmpty(city)){
                // 远程调用 从user模块获取
                Root<QueryIpCityResponse> res = userWebRemote.queryIpCity(QueryIpCityRequest.build1(GlobalUtils.clientIp()));
                if(!res.isFailure()){
                    QueryIpCityResponse cityInfo = res.getData();
                    province = cityInfo.getProvince();
                    city = cityInfo.getCity();
                }
            }
            // 如果还是获取不到 送礼用户的省市信息直接返回false
            if(StringUtils.isEmpty(province) || StringUtils.isEmpty(city)){
                return false;
            }
            List<TSendGiftLimit> cfgs = sendGiftLimitMapper.selectByProvince(new TSendGiftLimit(province));
            if(ListUtils.isEmpty(cfgs)){
                return false;
            }
            List<TSendGiftLimit> waitCheck = new ArrayList<>();
            for(TSendGiftLimit item : cfgs){
                if(item.getStatus() == Status.Enable && (StringUtils.isEmpty(item.getCity()) || city.equals(item.getCity()))){
                    waitCheck.add(item);
                }
            }
            if(ListUtils.isEmpty(waitCheck)){
                return false;
            }
            FSendGiftStat giftLimitStat = sendGiftLimitStatMapper.selectByPrimaryKey(new FSendGiftStat(MsgUtils.format(SEND_GIFT_LIMIT_FORMAT, userId, friendId)));
            double alreadySendMoney = Objects.isNull(giftLimitStat) ? 0.0 : giftLimitStat.getStatMoney();
            // 校验
            for (TSendGiftLimit item : waitCheck) {
                if(alreadySendMoney + BusiUtils.beansToYuan(beans) >= item.getLimitMoney()){
                    if(Objects.nonNull(giftLimitStat) && BoolType.True != giftLimitStat.getIsLimit()){
                        messageQueueProducer.sendAsync(BusiCodeDefine.SendChatGiftArriveLimit, MessageTag.FriendChat);
                    }
                    return true;
                }
            }
            return false;
        }catch (Exception e){
            LOG.error("ChatConsumeComponent-checkSendGiftArriveLimit-error reason:", e);
        }
        return false;
    }

    /**
     * 查看付费照片
     *
     * @param userId 查看照片方
     * @param friendId 照片归属方
     */
    public void saveLookChargePic(Long userId, Long friendId, Long totalBeans){
        // 扣除金币
        BeansConsumeDTO consumeDTO = userBusiComponent.addBeans(userId, totalBeans);
        // 计算提成
        Pair<Long, Long> cashPair = BusiUtils.presentCommonCash(consumeDTO.getConsumeTotalBeans(), consumeDTO.getConsumePlatformBeans());
        // 消费记录
        ChatConsumeDTO dto = new ChatConsumeDTO();
        dto.setTid(GlobalUtils.tid());
        dto.setTotalBeans(consumeDTO.getConsumeTotalBeans());
        dto.setPlatformBeans(consumeDTO.getConsumePlatformBeans());
        dto.setFromUserId(userId);
        dto.setToUserId(friendId);
        dto.setSourceType(ChatSourceType.ChargeMedia);
        dto.setContentType(ChatContentType.ChargePic);
        dto.setFeeUserId(userId);
        dto.setConsumeTime(new Date());

        dto.setTargetPresentUserId(friendId);
        dto.setTargetPresentCash(cashPair.getFirst());
        dto.setTargetPlatformCash(cashPair.getSecond());
        chatManager.addChatConsumeLog(dto, BusiUtils.generateRelationId(userId, friendId));
        // 给对方加积分
        consumePresentComponent.addPresent(UserCashSourceType.ChargePic, userId, friendId, cashPair.getFirst(), cashPair.getSecond());
    }

    /**
     * 查看付费视频
     */
    public void saveLookChargeVideo(Long userId, Long friendId, Long totalBeans){
        // 扣除金币
        BeansConsumeDTO consumeDTO = userBusiComponent.addBeans(userId, totalBeans);
        // 计算提成
        Pair<Long, Long> cashPair = BusiUtils.presentCommonCash(consumeDTO.getConsumeTotalBeans(), consumeDTO.getConsumePlatformBeans());
        // 消费记录
        ChatConsumeDTO dto = new ChatConsumeDTO();
        dto.setTid(GlobalUtils.tid());
        dto.setTotalBeans(consumeDTO.getConsumeTotalBeans());
        dto.setPlatformBeans(consumeDTO.getConsumePlatformBeans());
        dto.setFromUserId(userId);
        dto.setToUserId(friendId);
        dto.setSourceType(ChatSourceType.ChargeMedia);
        dto.setContentType(ChatContentType.ChargeVideo);
        dto.setFeeUserId(userId);
        dto.setConsumeTime(new Date());

        dto.setTargetPresentUserId(friendId);
        dto.setTargetPresentCash(cashPair.getFirst());
        dto.setTargetPlatformCash(cashPair.getSecond());
        chatManager.addChatConsumeLog(dto, BusiUtils.generateRelationId(userId, friendId));
        // 给对方加积分
        consumePresentComponent.addPresent(UserCashSourceType.ChargeVideo, userId, friendId, cashPair.getFirst(), cashPair.getSecond());
    }

    /**
     * 聊天送礼消费，消费数量默认1，没有背包
     *
     * @param myUserId
     * @param targetUser
     * @param gift
     * @param sendCount
     * @param totalBeans
     * @param totalSilver
     * @param fromBag
     */
    public Long saveSendGift(ChatSourceType sourceType, Long myUserId, UUserBasic targetUser, TProdSocialGift gift, Integer sendCount, Long totalBeans, Long totalSilver, boolean fromBag) {
        Long rechargeBeans = 0L;
        Long platformBeans = 0L;
        if (fromBag) {
            reduceGiftFromBag(GlobalUtils.uid(), gift, sendCount);
        } else {
            if (GlobalUtils.beans() < totalBeans) {
                throw new BusiException(ErrCodeType.BeanNotEnough);
            }
            BeansConsumeDTO consumeDTO = userBusiComponent.addBeans(myUserId, -totalBeans);
            rechargeBeans = consumeDTO.getConsumeRechargeBeans();
            platformBeans = consumeDTO.getConsumePlatformBeans();
        }
        // 记录送礼消费记录
        ChatConsumeDTO dto = buildChatGiftConsumeDTO(sourceType, myUserId, targetUser, gift, sendCount, totalBeans, platformBeans, totalSilver);
        GlobalUtils.extValue(BusinessDataKey.ConsumerTotalBeans, dto.getTotalBeans());
        GlobalUtils.extValue(BusinessDataKey.ConsumePlatformBeans, dto.getPlatformBeans());
        GlobalUtils.extValue(BusinessDataKey.ConsumerTotalSilver, dto.getTotalSilver());
        GlobalUtils.extValue(BusinessDataKey.PresentCash, dto.getTargetPresentCash());
        GlobalUtils.extValue(BusinessDataKey.FriendSex, targetUser.getSex());
        GlobalUtils.extValue(BusinessDataKey.ConsumeGiftId, gift.getGiftId());
        GlobalUtils.extValue(BusinessDataKey.ConsumerGiftName, gift.getGiftName());
        GlobalUtils.extValue(BusinessDataKey.ConsumerGiftPic, gift.getPic());
        GlobalUtils.extValue(BusinessDataKey.TargetUserInfo, FamilyMemberDTO.createSimple(targetUser));
        chatManager.addChatConsumeLogForGift(dto, fromBag);

        // 增加聊主提成记录
        if(Objects.nonNull(dto.getTargetPresentCash()) && dto.getTargetPresentCash() > 0){
            ConsumePresentDTO presentDTO = ConsumePresentDTO.builder(UserCashSourceType.SendGift)
                    .setPaidUserId(GlobalUtils.uid())
                    .setConsumeCount(sendCount)
                    .setConsumeBeans(fromBag ? 0L : totalBeans) // 从背包送出的这个参数记为0
                    .setConsumePlatformBeans(platformBeans)
                    .setPresentUserId(dto.getTargetPresentUserId())
                    .setPresentCash(dto.getTargetPresentCash())
                    .setPresentPlatformCash(dto.getTargetPlatformCash())
                    .setCreateTime(dto.getConsumeTime());
            consumePresentComponent.addPresent(presentDTO, MapUtils.gmap("giftId", gift.getGiftId()));
        }

        // 直接邀请人提成记录
        if(Objects.nonNull(dto.getInvitorPresentCash()) && dto.getInvitorPresentCash() > 0){
            ConsumePresentDTO presentDTO1 = ConsumePresentDTO.builder(UserCashSourceType.IvtPayGift)
                    .setPaidUserId(GlobalUtils.uid())
                    .setConsumeCount(sendCount)
                    .setConsumeBeans(fromBag ? 0L : totalBeans) // 从背包送出的这个参数记为0
                    .setConsumePlatformBeans(platformBeans)
                    .setPresentUserId(dto.getInvitorUserId())
                    .setPresentCash(dto.getInvitorPresentCash())
                    .setPresentPlatformCash(dto.getInvitorPlatformCash())
                    .setCreateTime(dto.getConsumeTime());
            consumePresentComponent.addPresent(presentDTO1, MapUtils.gmap("beInviteUserId", dto.getTargetPresentUserId()));
        }

        return dto.getTargetPresentCash();
    }

    /**
     * 开盲盒
     *
     * @param relationDTO
     * @param targetUser
     * @param gift
     * @param count
     * @param totalBeans
     * @return
     */
    public BlindBoxInfoDTO saveSendBlindBox(RelationDTO relationDTO, UUserBasic targetUser, TProdSocialGift gift, Integer count, Long totalBeans) {
        Long userId = GlobalUtils.uid();
        if (GlobalUtils.beans() < totalBeans) {
            // TODO　提示的版本兼容
            throw new ComponentException(ErrCodeType.BeanNotEnough, String.valueOf(totalBeans));
        }
        if (relationDTO.getRelation() == null) {
            relationDTO = friendRelationComponent.initRelation(relationDTO.getMyUserId(), relationDTO.getTargetUserId());
        }
        // 赠送盲盒，只能使用充值金币
        userBusiComponent.addRechargeBeans(userId, -totalBeans);

        // 开盲盒操作
        BlindBoxInfoDTO blindBoxInfo = doOpenBlindBox(gift, count);

        // 送礼记录 prizeBeans 总价值为所有中奖礼物的总价，realBeans = 盲盒的总价值 = totalBeans
        Long prizeBeans = blindBoxInfo.getTotalBeans().longValue();

        // 新增送礼记录
        //ChatConsumeDTO dto = buildChatGiftConsumeDTO(relationDTO, targetUser, gift, count, prizeBeans, totalBeans, 0L, 0, false);
        //chatManager.addChatConsumeLog(dto, relationDTO.getRelationId(), ChatStatus.Success, ChatConsumeStatus.Success, null);

        // 新增开盲盒对应的礼物清单
        addBlindBoxGiftListLog(relationDTO.getMyUserId(), relationDTO.getTargetUserId(), gift.getGiftId(), blindBoxInfo);
        return blindBoxInfo;
    }

    /**
     * 执行魔法礼物抽奖操作
     *
     * @param userId 用户ID
     * @param gift   魔法礼物
     * @param count  送礼数量
     */
    public MagicDrawResult doMagicDraw(Long userId, TProdSocialGift gift, Integer count) {
        Date consumeTime = new Date(GlobalUtils.reqTime());
        Map<String, Integer> codeMap = magicGiftManager.buildMagicPoolCodeMap(userId, gift.getGiftId(), count, consumeTime);
        List<LuckyAwardDTO> awardList = new ArrayList<>();
        codeMap.forEach((poolCode, times) -> {
            // 合并抽取的不同坑位
            if (times != null && times > 0) {
                awardList.addAll(luckyboxComponent.startLucking(poolCode, times));
            }
        });
        // 抽奖完成，累计用户最近的魔法礼物送出次数
        magicGiftManager.incrSendCount(userId, gift.getGiftId(), count, consumeTime);
        List<GrantDTO> grantList = grantComponent.saveCombineAutoGrant(userId, buildAwardList(awardList));
        Map.Entry<Long, List<GrantDetailDTO>> result = getAwardAboutBeans(grantList);

        // 将奖励分组整合
        List<MagicPrizeDTO> prizeList = new ArrayList<>();
        Map<String, GrantProdDTO> combineMap = new HashMap<>();
        for (GrantDetailDTO g : result.getValue()) {
            if (ListUtils.isEmpty(g.getProds())) {
                continue;
            }
            for (GrantProdDTO p : g.getProds()) {
                String prodKey = buildProdKey(p.getProdType(), p.getProdId());
                if (combineMap.get(prodKey) == null) {
                    combineMap.put(prodKey, p);
                } else {
                    GrantProdDTO combineDto = combineMap.get(prodKey);
                    combineDto.setAwardCount(combineDto.getAwardCount() + p.getAwardCount());
                }
            }
        }
        for (GrantProdDTO combineDTO : combineMap.values()) {
            prizeList.add(MagicPrizeDTO.create(combineDTO));
        }
        // 按价值倒序
        prizeList.sort(Comparator.comparing(MagicPrizeDTO::getTotalBeans).reversed());
        return MagicDrawResult.create(result.getKey(), prizeList);
    }

    /**
     * 获得魔法礼物奖励估值
     */
    private Map.Entry<Long, List<GrantDetailDTO>> getAwardAboutBeans(List<GrantDTO> grants) {
        Long awardBeans = 0L;
        List<GrantDetailDTO> detailList = new ArrayList<>();
        for (GrantDTO grant : grants) {
            awardBeans += grant.getAboutBeans();
            detailList.addAll(grant.getDetailList());
        }
        return new AbstractMap.SimpleEntry<>(awardBeans, detailList);
    }

    private List<AwardDTO> buildAwardList(List<LuckyAwardDTO> awards) {
        List<AwardDTO> target = new ArrayList<>(awards.size());
        if (ListUtils.isNotEmpty(awards)) {
            for (LuckyAwardDTO award : awards) {
                target.add(AwardDTO.create(award.getAwardConfigCode(), award.getAwardTimes()));
            }
        }
        return target;
    }

    private String buildProdKey(ProdGoodsType prodType, Integer prodId) {
        return prodType.name() + prodId;
    }

    /**
     * 聊天消费
     */
    public ChatConsumeDTO saveSendMsg(RelationDTO relationDTO, ChatSourceType sourceType, ChatContentType contentType) {
        // 当前用户
        Long userId = relationDTO.getMyUserId();
        // 判断是否需要付费
        Long totalBean = 0L;
        Long realPlatformBeans = 0L;
        Long presentCash = 0L;
        SocialGoodsDefine socialGoodsDefine = null;
        Integer prodId = null;
        Integer useBagCnt = 0;

        UUserBasic targetUser = userInfoManager.getUserBasic(relationDTO.getTargetUserId());
        // 目标用户消息单价设置（用户注册的时候就会初始化UMessageSetting）
        UMessageSetting targetMesSet = messageSettingManager.getSetting(targetUser.getUserId());
        if (ChatSourceType.ChatUp == sourceType) {
            targetMesSet.setMsgFee(GlobalConstant.CHAT_UP_PRICE);
        }
        // 获取聊天费用类型
        ChatFeeWaiverType feeWaiverType = this.getChatFeeType(targetMesSet, relationDTO.getIntimateLevel());
        // 不免费逻辑
        if (ChatFeeWaiverType.NotFree == feeWaiverType) {
            // 私信的费用,背包使用，金币扣除信息
            PrivateChatFeeDTO chatFeeDTO = saveChatConsumeFeeInfo(userId, sourceType, targetMesSet);
            // 使用的物品
            TProdSocialGoods prod = chatFeeDTO.getProd();
            // 用户需要扣除的金币
            totalBean = chatFeeDTO.getTotalBean();
            // 用户实际扣除的平台金币
            realPlatformBeans = chatFeeDTO.getPlatformBeans();
            // 使用背包的数量
            useBagCnt = chatFeeDTO.getUseBagCnt();
            // 背包使用的物品道具
            socialGoodsDefine = Objects.nonNull(prod) ? SocialGoodsDefine.getByGoodId(prod.getGoodsId()) : null;
            prodId = Objects.nonNull(prod) ? prod.getGoodsId() : null;
            // 要付费的聊天记录，添加待提成记录
            FChatConsumeWait wait = this.addWaitConsumeChat(GlobalUtils.tid(), DateUtils.truncMillis(GlobalUtils.reqTime()), relationDTO, targetUser, totalBean.intValue(), realPlatformBeans.intValue(), prodId, useBagCnt, sourceType);
            // 缓存消息接收者的待回复消息记录
            Long tid = GlobalUtils.tid(), sendTime = GlobalUtils.reqTime();
            // 记录聊天消费记录, 免费的不记录消费记录，只记录聊天内容日志
            ChatConsumeDTO dto = buildChatMsgConsumeDTO(relationDTO, socialGoodsDefine, useBagCnt, wait, sourceType, contentType);
            chatManager.addChatConsumeLog(dto, relationDTO.getRelationId());
            presentCash = dto.getTargetPresentCash();
            CallbackAfterTransactionUtil.send(() -> {
                // 生成消息接收者的待回复聊天记录缓存，有效期：15分钟
                RedisKey key = buildWaitReplayKey(relationDTO.getTargetUserId(), relationDTO.getMyUserId());
                socialRedisTemplate.zadd(key, sendTime.doubleValue(), tid.toString());
                socialRedisTemplate.expire(key);
            });
        }
        GlobalUtils.extValue(BusinessDataKey.ChatFeeWaiverType, feeWaiverType);
        GlobalUtils.extValue(BusinessDataKey.IsCp, relationDTO.getIsCp());
        return new ChatConsumeDTO(totalBean, prodId, useBagCnt, presentCash);
    }

    /**
     * 消耗私信的费用,背包使用，金币扣除
     *
     * @param userId
     * @param sourceType
     * @param targetMesSet 就算用户不设置 系统会默认给最小单价值
     * @return
     */
    private PrivateChatFeeDTO saveChatConsumeFeeInfo(Long userId, ChatSourceType sourceType, UMessageSetting targetMesSet) {
        // 如果是一键搭讪 这里直接返回
        if(ChatSourceType.TodayFate == sourceType){
            TProdSocialGoods socialGood = socialProdManager.getSocialGood(SocialGoodsDefine.FastCu.getGoodsId());
            return new PrivateChatFeeDTO(socialGood, 0L, 1, 0L);
        }
        // 聊天原价
        Long totalBean = targetMesSet.getMsgFee().longValue();
        // 私聊、搭讪 对应的物品类型
        SocialGoodsDefine goodsDefine = ChatSourceType.ChatUp == sourceType ? SocialGoodsDefine.ChatUp : SocialGoodsDefine.ChatTicket;
        TProdSocialGoods goods = socialProdManager.getSocialGood(goodsDefine.getGoodsId());
        // 直接去扣减用户的物品券（返回true 则说明用户有物品券、并且扣减成功；如果扣减不成功会直接抛出异常）
        if(reduceGoodsFromBag(userId, goods)){
            return new PrivateChatFeeDTO(goods, 0L, 1);
        }
        // 余额校验
        if (GlobalUtils.beans() < totalBean) {
            throw new BusiException(ErrCodeType.BeanNotEnough);
        }
        // 扣除金币
        BeansConsumeDTO beansConsumeInfo = userBusiComponent.addBeans(userId, -totalBean);
        // 消费使用的平台赠送金币数量
        Long platformBeans = beansConsumeInfo.getConsumePlatformBeans();
        GlobalUtils.extValue(BusinessDataKey.ConsumerTotalBeans, totalBean);
        return new PrivateChatFeeDTO(null, totalBean, 0, platformBeans);
    }

    /**
     * 从背包扣除礼物
     *
     * @param userId
     * @param gift
     * @return
     */
    private void reduceGiftFromBag(Long userId, TProdSocialGift gift, Integer count) {
        int bagCount = userSocialBagComponent.getGiftCnt(userId, gift.getGiftId());
        if (bagCount < count) {
            throw new ComponentException("背包数量不足");
        }
        userSocialBagComponent.reduceGift(userId, gift.getGiftId(), count);
    }

    /**
     * 优先使用聊天券
     *
     * @param userId
     * @param goods
     * @return 返回物品扣减数量
     */
    private boolean reduceGoodsFromBag(Long userId, TProdSocialGoods goods) {
        if (userSocialBagComponent.getGoodsCnt(userId, null, goods.getGoodsType(), goods.getGoodsId()) == 0) {
            return false;
        }
        userSocialBagComponent.reduceGoods(userId, goods.getGoodsId(), 1);
        return true;
    }

    /**
     * 是否需要提醒真人认证
     */
    public boolean needRealHeadRemind(Long userId, Long friend) {
        if (!socialRedisTemplate.exists(buildWaitReplayKey(userId, friend))) {
            return false;
        }
        RedisKey realHeadRemindKey = buildReadHeadRemindKey();
        if (socialRedisTemplate.sadd(realHeadRemindKey, userId.toString()) > 0) {
            socialRedisTemplate.expire(realHeadRemindKey);
            return true;
        }
        return false;
    }

    /**
     * 生成用户待回复消息缓存Key
     *
     * @param targetUserId
     * @return
     */
    public RedisKey buildWaitReplayKey(Long targetUserId, Long sendUserId) {
        return RedisKey.create(SocialKeyDefine.UserWaitReply, targetUserId, sendUserId);
    }

    public RedisKey buildReadHeadRemindKey() {
        return RedisKey.create(SocialKeyDefine.RealHeadRemind);
    }

    /**
     * 创建一条待消费聊天记录
     *
     * @param dto
     */
    public FChatConsumeWait addWaitConsumeChat(Long tid, Date createTime, RelationDTO dto, UUserBasic targetUser, Integer totalBeans, Integer platformBeans, Integer goodsId, Integer useBagCnt, ChatSourceType chatSourceType) {
        FChatConsumeWait wait = new FChatConsumeWait();
        wait.setTid(tid);
        wait.setCreateTime(createTime);
        wait.setRelationId(dto.getRelationId());
        wait.setFromUserId(dto.getMyUserId());
        wait.setToUserId(dto.getTargetUserId());
        wait.setTotalBeans(totalBeans);
        wait.setPlatformBeans(platformBeans);
        wait.setUseBagCnt(useBagCnt);
        wait.setGoodsId(goodsId);
        //收益人ID
        PresentCashDTO presentDTO = null;
        if(ChatSourceType.ChatUp == chatSourceType || ChatSourceType.TodayFate == chatSourceType || ChatSourceType.SystemChatUp == chatSourceType){
            presentDTO = PresentCashDTO.build01(targetUser.getUserId(), 100L, 100L);
        }else{
            presentDTO = this.calcPresentCash(totalBeans.longValue(), platformBeans.longValue(), GlobalConstant.RESOURCE_PRESENT_RATE, targetUser, goodsId);
        }

        wait.setPresentCash(presentDTO.getTargetPresentCash());
        wait.setPlatformCash(presentDTO.getTargetPlatformCash());

        wait.setInvitorUserId(presentDTO.getInvitorUserId());
        wait.setInvitorPresentCash(presentDTO.getInvitorPresentCash());
        wait.setInvitorPlatformCash(presentDTO.getInvitorPlatformCash());

        fChatConsumeWaitMapper.insert(wait);
        return wait;
    }

    /**
     * 计算各方提成金额
     * 备注：物品券携带的提成收益 是不给邀请人分成的
     * @param present    原始分成比例
     * @param totalBeans    实际消耗总金币（里面包含了平台金币）
     * @param platformBeans    实际消耗总平台金币
     * @param targetUser 直接提成用户
     * @return
     */
    public PresentCashDTO calcPresentCash(Long totalBeans, Long platformBeans, double present, UUserBasic targetUser, Integer prodId) {
        UUserExt userExt = userInfoManager.getUserExt(targetUser.getUserId());
        PresentCashDTO dto = new PresentCashDTO();
        // 避免上游乱传值
        totalBeans = totalBeans == null ? 0L : totalBeans;
        platformBeans = platformBeans == null ? 0L : platformBeans;
        // 如果该用户存在分成模式
        if(Objects.nonNull(userExt.getPresentType()) && TargetPresentType.None != userExt.getPresentType()){
            present = userExt.getPresentType().getZjPresent();
        }
        // 提成方是女用户 要求女用户真人认证后才有收益提成
        /*if(SexType.Female == targetUser.getSex() && BoolType.True != targetUser.getRealPerson()){
            present = 0D;
        }*/
        // 提成方是男用户 要求男用户真人+实名认证后才有收益
        /*if(SexType.Male == targetUser.getSex() && (BoolType.True != targetUser.getRealPerson() || BoolType.True != targetUser.getRealName())){
            present = 0D;
        }*/
        // 赠送金币的提成
        Long platformCash = BusiUtils.beansToCash(platformBeans / GlobalConstant.PLATFORM_BEAN_EXCHANGE_RATE * present);
        // 充值金币的提成
        Long rechargeCash = BusiUtils.beansToCash((totalBeans - platformBeans) * present);

        dto.setPresentUserId(targetUser.getUserId());
        dto.setTargetPresentCash(platformCash + rechargeCash + calcProdCash(prodId));
        dto.setTargetPlatformCash(platformCash + calcProdCash(prodId));

        // 直接邀请人所拿提成 （备注：只有提成用户是女性 她的邀请人才拿提成）
        if(SexType.Female == targetUser.getSex() && Objects.nonNull(userExt.getInviteUserId())){
            // 赠送金币收益不给邀请人分成
            Pair<Long, Long> inviteCash = getInviteCash(rechargeCash, 0L, userExt.getInviteUserId());
            dto.setInvitorUserId(userExt.getInviteUserId());
            dto.setInvitorPresentCash(inviteCash.getFirst() + inviteCash.getSecond());
            dto.setInvitorPlatformCash(inviteCash.getSecond());
        }
        return dto;
    }

    private Long calcProdCash(Integer prodId){
        SocialGoodsDefine goods = SocialGoodsDefine.getByGoodId(prodId);
        if(Objects.isNull(goods)){
            return 0L;
        }
        return Objects.isNull(goods.getPresentCash()) ? 0L : goods.getPresentCash();
    }

    /**
     * 获取邀请人提成收益
     * @param rechargeCash 被邀请人 充值金币收益
     * @param platformCash 被邀请人 平台金币收益
     * @param invitorUserId 邀请人
     * @return first 邀请人充值金币收益、 second 邀请人赠送金币收益
     */
    private Pair<Long, Long> getInviteCash(long rechargeCash, long platformCash, Long invitorUserId){
        if(Objects.isNull(invitorUserId)){ // 说明没有邀请人
            return Pair.with(0L, 0L);
        }
        // 获取邀请人阶段提成比例
        UUserExt userExt = userInfoManager.getUserExt(invitorUserId);
        double invitorPresentScale = InvitorPresentConfig.getInvitorPresentScale(userExt.getPresentType(), userExt.getInviteFixScale(), userExt.getTotalRpInvite());
        return Pair.with((long)(rechargeCash * invitorPresentScale), (long)(platformCash * invitorPresentScale));
    }

    /**
     * i
     * 处理聊天回复提成
     *
     * @param userId   用户ID
     * @param friendId 好友ID
     * @param wait     待提成消费记录
     */
    public boolean doChatWithDraw(Long userId, Long friendId, FChatConsumeWait wait) {
        Date nowTime = DateUtils.nowTime();
        // 删除待提现记录
        if (fChatConsumeWaitMapper.deleteByPrimaryKey(wait) > 0) {
            if(Objects.nonNull(wait.getPresentCash())){
                // 私信聊天券
                SocialGoodsDefine msgGoods = SocialGoodsDefine.getByGoodId(wait.getGoodsId());
                String discountInfo = Objects.isNull(msgGoods) ? null : msgGoods.getGoodsName();
                // 不管男女，均增加用户零钱
                ConsumePresentDTO presentDTO = ConsumePresentDTO.builder(UserCashSourceType.PayMsg)
                        .setPaidUserId(friendId)
                        .setConsumeBeans(wait.getTotalBeans().longValue())
                        .setConsumePlatformBeans(wait.getPlatformBeans().longValue())
                        .setPresentUserId(userId)
                        .setPresentCash(wait.getPresentCash())
                        .setPresentPlatformCash(wait.getPlatformCash())
                        .setCreateTime(nowTime);
                consumePresentComponent.addPresent(presentDTO, discountInfo);
            }

            // 判断提成方是否有邀请人
            if(Objects.nonNull(wait.getInvitorUserId())){
                ConsumePresentDTO presentDTO1 = ConsumePresentDTO.builder(UserCashSourceType.IvtPayMsg)
                        .setPaidUserId(friendId)
                        .setConsumeBeans(wait.getTotalBeans().longValue())
                        .setConsumePlatformBeans(wait.getPlatformBeans().longValue())
                        .setPresentUserId(wait.getInvitorUserId())
                        .setPresentCash(wait.getInvitorPresentCash())
                        .setPresentPlatformCash(wait.getInvitorPlatformCash())
                        .setCreateTime(nowTime);
                consumePresentComponent.addPresent(presentDTO1, MapUtils.gmap("beInviteUserId", userId));
            }
            return true;
        }
        return false;
    }

    /**
     * 处理聊天退款
     *
     * @param wait
     */
    public void doChatRefund(FChatConsumeWait wait) {
        if (fChatConsumeWaitMapper.deleteByPrimaryKey(wait) > 0) {
            // 回退给发送方，发送方枷锁
            userLockTemplate.acquireTransactionLock(wait.getFromUserId());
            FChatConsume update = new FChatConsume(wait.getCreateTime(), wait.getTid());
            //update.setConsumeStatus(ChatConsumeStatus.Refund);
            //update.setConsumeStatusDate(DateUtils.nowTime());
            fChatConsumeMapper.updateByPrimaryKeySelective(update);
            // 退费时，充值金币和赠送金币都原路退回
            Integer platformBeans = wait.getPlatformBeans() == null ? 0 : wait.getPlatformBeans();
            Integer rechargeBeans = wait.getTotalBeans() - platformBeans;
            if (platformBeans > 0) {
                userBusiComponent.addBeans(wait.getFromUserId(), platformBeans.longValue());
            }
            if (rechargeBeans > 0) {
                userBusiComponent.addRechargeBeans(wait.getFromUserId(), rechargeBeans.longValue());
            }
            LOG.info("过期待提成聊天记录退回：{}", JsonUtils.seriazileAsString(wait));
        }
    }

    /**
     * 计算聊天礼物消费信息
     */
    private ChatConsumeDTO buildChatGiftConsumeDTO(ChatSourceType sourceType, Long myUserId, UUserBasic targetUser, TProdSocialGift gift, Integer sendCount,
                                                   Long totalBeans, Long platformBeans, Long totalSilver) {
        ChatConsumeDTO dto = new ChatConsumeDTO();
        dto.setTid(GlobalUtils.tid());
        dto.setConsumeTime(consumeTime());
        dto.setProdId(gift.getGiftId());
        dto.setProdCnt(sendCount);
        dto.setTotalBeans(totalBeans);
        dto.setPlatformBeans(platformBeans);
        dto.setTotalSilver(totalSilver);
        dto.setFromUserId(myUserId);
        dto.setToUserId(targetUser.getUserId());
        dto.setSourceType(sourceType);
        dto.setContentType(ChatContentType.Gift);
        dto.setFeeUserId(myUserId);
        double presentRate = SocialGiftGroup.Lucky == gift.getGroupCode() ? 0.05 : GlobalConstant.RESOURCE_PRESENT_RATE;
        PresentCashDTO presentDTO = this.calcPresentCash(totalBeans, platformBeans, presentRate, targetUser, null);

        // 女用户提成
        dto.setTargetPresentUserId(presentDTO.getPresentUserId());
        dto.setTargetPresentCash(presentDTO.getTargetPresentCash());
        dto.setTargetPlatformCash(presentDTO.getTargetPlatformCash());

        // 女用户直接邀请人提成
        dto.setInvitorUserId(presentDTO.getInvitorUserId());
        dto.setInvitorPresentCash(presentDTO.getInvitorPresentCash());
        dto.setInvitorPlatformCash(presentDTO.getInvitorPlatformCash());

        return dto;
    }

    /**
     * 获取消费时间，为了防止程序和数据库的精度不一致的问题
     * 去掉毫秒数。
     *
     * @return
     */
    private Date consumeTime() {
        return DateUtils.truncMillis(GlobalUtils.reqTime());
    }

    /**
     * 根据双方好友关系判断聊天收费类型
     *
     * @return
     */
    public ChatFeeWaiverType getChatFeeType(UMessageSetting friendSetting, Integer intimateLevel) {
        // 判断对方是否设置的免费
        if(friendSetting.getMsgFee() == 0){
            return ChatFeeWaiverType.SelfFree;
        }
        // 亲密度14等级聊天免费特权
        /*if (IntimatePrivilege.LTMF.isOpen() && IntimatePrivilege.LTMF.getLevel() <= intimateLevel) {
            return ChatFeeWaiverType.IntimateFree;
        }*/
        // 其他情况都收费
        return ChatFeeWaiverType.NotFree;
    }

    /**
     * 构造一个聊天消息的DTO
     *
     * @param relationDTO
     * @return
     */
    private ChatConsumeDTO buildChatMsgConsumeDTO(RelationDTO relationDTO, SocialGoodsDefine goodsDefine, Integer useBagCnt, FChatConsumeWait wait, ChatSourceType sourceType, ChatContentType contentType) {
        ChatConsumeDTO dto = new ChatConsumeDTO();
        dto.setTid(GlobalUtils.tid());
        dto.setConsumeTime(wait.getCreateTime());
        dto.setFromUserId(relationDTO.getMyUserId());
        dto.setToUserId(relationDTO.getTargetUserId());
        dto.setSourceType(sourceType);
        dto.setContentType(contentType);
        dto.setFeeUserId(relationDTO.getMyUserId());
        dto.setProdId(Objects.isNull(goodsDefine) ? null : goodsDefine.getGoodsId());
        dto.setProdCnt(useBagCnt);
        dto.setTotalBeans(wait.getTotalBeans().longValue());
        dto.setPlatformBeans(wait.getPlatformBeans().longValue());

        // 直接提成用户
        dto.setTargetPresentUserId(relationDTO.getTargetUserId());
        dto.setTargetPresentCash(wait.getPresentCash());
        dto.setTargetPlatformCash(wait.getPlatformCash());

        // 直接邀请人提成
        dto.setInvitorUserId(wait.getInvitorUserId());
        dto.setInvitorPresentCash(wait.getInvitorPresentCash());
        dto.setInvitorPlatformCash(wait.getInvitorPlatformCash());

        return dto;
    }


    /**
     * 后台发送一条私信消息
     *
     * @param userId      发送方ID
     * @param friendId    接收方ID
     * @param content     消息内容：当消息类型为图片消息时，content是缩略图的 base64 编码
     * @param imgUrl      图片地址
     * @param contentType 消息内容类型
     */
    public void sendChatMsg(Long userId, Long friendId, Object content, String imgUrl, ChatContentType contentType) {
        sendChatMsg(userId, friendId, content, imgUrl, contentType, 0L, 0);
    }

    public void sendChatMsg(Long userId, Long friendId, Object content, String tips, String imgUrl, ChatContentType contentType) {
        sendChatMsg(userId, friendId, content, tips, imgUrl, contentType, 0L, 0, false);
    }

    /**
     * 后台发送一条私信消息(携带费用信息)
     *
     * @param userId      发送方ID
     * @param friendId    接收方ID
     * @param content     消息内容：当消息类型为图片消息时，content是缩略图的 base64 编码
     * @param imgUrl      图片地址
     * @param contentType 消息内容类型
     * @Param fee         消息费用
     */
    public void sendChatMsg(Long userId, Long friendId, Object content, String imgUrl, ChatContentType contentType, Long fee, Integer ticketCnt) {
        sendChatMsg(userId, friendId, content, null, imgUrl, contentType, fee, ticketCnt, false);
    }

    /**
     * 后台发送一条私信消息(携带费用信息)
     *
     * @param userId      发送方ID
     * @param friendId    接收方ID
     * @param content     消息内容：当消息类型为图片消息时，content是缩略图的 base64 编码
     * @param imgUrl      图片地址
     * @param contentType 消息内容类型
     * @Param fee         消息费用
     */
    public void sendChatMsg(Long userId, Long friendId, Object content, String tips, String imgUrl, ChatContentType contentType, Long fee, Integer ticketCnt, boolean heartTouch) {
        PrivateChatUserInfoDTO dto = buildPrivateChatExtraInfo(userId, friendId, fee, ticketCnt, heartTouch, tips);
        GlobalUtils.extValue(BusinessDataKey.SenderId, userId.toString());
        GlobalUtils.extValue(BusinessDataKey.UserIds, new Long[]{friendId});
        GlobalUtils.extValue(BusinessDataKey.ChatContent, content);
        GlobalUtils.extValue(BusinessDataKey.ChatContentType, contentType);
        GlobalUtils.extValue(BusinessDataKey.ImgUrl, imgUrl);
        GlobalUtils.extValue(BusinessDataKey.PushContent, "你有一条新的消息");
        GlobalUtils.extValue(BusinessDataKey.Extra, dto);
        GlobalUtils.extValue(BusinessDataKey.OverwriteBusiCode, BusiCodeDefine.BackupChatMsg);
        messageComponent.sendMatchMsgs();
    }

    /**
     * 后台发送一条私信邀请消息消息
     *
     * @param userId   发送方ID
     * @param friendId 接收方ID
     * @Param fee         消息费用
     */
    public void sendInviteMsg(Long userId, Long friendId, FFamily family) {
        GlobalUtils.extValue(BusinessDataKey.SenderId, userId.toString());
        GlobalUtils.extValue(BusinessDataKey.UserIds, new Long[]{friendId});
        GlobalUtils.extValue(BusinessDataKey.CustomMap, buildBackUpInviteContent(family));
        GlobalUtils.extValue(BusinessDataKey.ChatContentType, ChatContentType.MessageInvite);
        GlobalUtils.extValue(BusinessDataKey.PushContent, "你有一条新的消息");
        GlobalUtils.extValue(BusinessDataKey.ObjectName, RongImConfig.CHAT_CUSTOM_OBJECT_NAME);
        GlobalUtils.extValue(BusinessDataKey.OverwriteBusiCode, BusiCodeDefine.BackupChatMsg);
        GlobalUtils.extValue(BusinessDataKey.Extra, buildPrivateChatExtraInfo(userId, friendId, 0L, 0, false, null));
        messageComponent.sendMatchMsgs();
    }

    private Object buildBackUpInviteContent(FFamily family) {
        Map<String, Object> context = new HashMap<>();
        context.put("content", "快来和大家一起玩呀");
        context.put("title", family.getFamilyName());
        context.put("pic", family.getCoverPic());
        context.put("inviteType", "JoinFamily");
        context.put("touchType", ClientTouchType.EnterFamily);
        context.put("touchValue", family.getFamilyId().toString());
        context.put("touchText", "回到家族");
        return context;
    }

    /**
     * 判断女用户俩聊天次数是否到达上限
     * true : 可以进行下一步
     * false: 到达上限
     * @param userId
     * @param friendId
     * @param chatMasterLevel
     * @return
     */
    public boolean checkFemaleChatTimes(Long userId, Long friendId, ChatMasterLevel chatMasterLevel) {
        try{
            // 只对女性用户做限制
            if (SexType.Female != GlobalUtils.sexType()) {
                return true;
            }
            // 设置开关
            boolean checkOpen = SettingsConfig.getBoolean(SettingsType.OpenFemaleChatLimit);
            if (!checkOpen) {
                return true;
            }
            // 根据用户等级判断用户的聊天限制次数
            UrlParamsMap paramsMap = UrlParamsMap.build(SettingsConfig.getString(SettingsType.FemaleMaxChatUserNum));
            Integer chatLimitTimes = paramsMap.getInt(chatMasterLevel.getId());
            // 如果是非红包匹配、女用户、设置开关开启，将当前聊天添加到用戶聊天限制緩存
            String dayStr = DateUtils.toString(new Date(), DatePattern.YMD2);
            RedisKey redisKey = buildChatLimitRedisKey(dayStr, userId);
            boolean result = socialRedisTemplate.femaleChatLimit(redisKey, friendId.toString(), chatLimitTimes);
            socialRedisTemplate.expire(redisKey);
            return result;
        } catch (Exception e) {
            LOG.error("ChatService-checkFemaleChatTimes-error,userId:{},friendId:{}",userId,friendId,e);
        }
        return true;
    }
    private RedisKey buildChatLimitRedisKey(String dayStr, Long userId) {
        return RedisKey.create(SocialKeyDefine.FemaleChatUserSet, dayStr, userId);
    }

    /**
     * 构建私聊扩展信息
     *
     * @param userId
     * @param friendId
     * @return
     */
    private PrivateChatUserInfoDTO buildPrivateChatExtraInfo(Long userId, Long friendId, Long fee, Integer ticketCnt, boolean heartTouch, String tips) {
        String relationId = BusiUtils.generateRelationId(userId, friendId);
        FRelationBasic relation = fRelationBasicMapper.selectByPrimaryKey(new FRelationBasic(relationId));
        RelationDTO relationDTO = RelationDTO.create(userId, friendId, relation);
        if (relation == null) {
            relationDTO = friendRelationComponent.createDefaultRelation(userId, friendId);
        }
        PrivateChatUserInfoDTO myInfo = buildChatBasicInfo(userId, friendId, relationDTO);
        PrivateChatUserInfoDTO targetUserInfo = buildChatBasicInfo(friendId, userId, relationDTO);
        targetUserInfo.setFee(fee);
        if (fee > 0) {
            targetUserInfo.setUseChatTicket(BoolType.False);
            targetUserInfo.setExpiredText("退回");
            if (ticketCnt > 0) {
                targetUserInfo.setUseChatTicket(BoolType.True);
                targetUserInfo.setExpiredText("失效");
            }
        }
        myInfo.setTargetUserObj(targetUserInfo);
        myInfo.setHeartTouch(BoolType.valueOf(heartTouch));
        myInfo.setTips(tips);
        return myInfo;
    }


    public PrivateChatUserInfoDTO buildChatBasicInfo(Long userId, Long friendId, RelationDTO relation) {
        PrivateChatUserInfoDTO dto = new PrivateChatUserInfoDTO();
        UUserBasic basic = uUserBasicMapper.selectByPrimaryKey(new UUserBasic(userId));
        dto.setUserId(userId);
        dto.setNickname(basic.getNickname());
        dto.setHeadPic(basic.getHeadPic());
        dto.setSexType(basic.getSex());
        dto.setUserType(basic.getUserType());
        dto.setFee(0L);
        dto.setIntimateLevel(relation.getIntimateLevel());
        return dto;
    }

    /**
     * 后台发送送礼消息
     *
     * @param userId
     * @param friendId
     * @param gift
     * @param count
     * @param specialParams
     */
    public void sendBackupGiftMsg(Long userId, Long friendId, TProdSocialGift gift, Integer count, Map<String, Object> specialParams) {
        GlobalUtils.extValue(BusinessDataKey.SenderId, userId.toString());
        GlobalUtils.extValue(BusinessDataKey.UserIds, new Long[]{friendId});
        GlobalUtils.extValue(BusinessDataKey.CustomMap, buildBackupGiftContent(gift, count, specialParams));
        GlobalUtils.extValue(BusinessDataKey.ChatContentType, ChatContentType.Gift);
        GlobalUtils.extValue(BusinessDataKey.PushContent, "你有一条新的消息");
        GlobalUtils.extValue(BusinessDataKey.ObjectName, RongImConfig.CHAT_CUSTOM_OBJECT_NAME);
        GlobalUtils.extValue(BusinessDataKey.OverwriteBusiCode, BusiCodeDefine.BackupChatMsg);
        GlobalUtils.extValue(BusinessDataKey.Extra, buildPrivateChatExtraInfo(userId, friendId, 0L, 0, false, null));
        messageComponent.sendMatchMsgs();
    }

    /**
     * 构建送礼消息体
     *
     * @param gift
     * @return
     */
    private Map<String, Object> buildBackupGiftContent(TProdSocialGift gift, Integer count, Map<String, Object> specialParams) {
        Map<String, Object> context = new HashMap<>();
        context.put("beans", gift.getBeans());
        context.put("chatGiftId", gift.getGiftId());
        context.put("giftCount", count);
        context.put("giftName", gift.getGiftName());
        context.put("giftUnit", gift.getGiftUnit());
        context.put("pic", gift.getPic());
        context.put("selPic", gift.getSelPic());
        context.put("specialType", gift.getSpecialType());
        context.put("specialParams", specialParams);
        context.put("userExp", gift.getBeans());
        return context;
    }

    /**
     * 执行开盲盒操作
     *
     * @param blindBox
     * @param count
     * @return
     */
    private BlindBoxInfoDTO doOpenBlindBox(TProdSocialGift blindBox, Integer count) {
        String poolCode = buildBlindPoolCode(blindBox.getGiftId());
        List<LuckyAwardDTO> list = luckyboxComponent.startLucking(poolCode, count);
        // 奖励code 分组
        Map<String, List<LuckyAwardDTO>> awardMap = list.stream().collect(Collectors.groupingBy(dto -> dto.getAwardConfigCode()));
        Set<String> awardCode = awardMap.keySet();
        Map<String, List<TAwardDetail>> awardDetailMap = new HashMap<>();
        List<TAwardDetail> detailList = new ArrayList<>();
        // 奖励集合
        awardCode.forEach(code -> {
            List<TAwardDetail> details = tAwardDetailMapper.selectByCode(new TAwardDetail(code));
            awardDetailMap.put(code, details);
            detailList.addAll(details);
        });
        // TODO 测试环境临时放开，需重新配置奖池
        if (UnifiedConfig.isProdEnv() && detailList.size() != 1) {
            throw new ComponentException("盲盒配置错误，只支持拆出一种礼物");
        }
        // 商品查询
        Set<String> prodIds = detailList.stream().map(TAwardDetail::getAwardTypeValue).collect(Collectors.toSet());
        Map<Integer, TProdSocialGift> awardGifts = new HashMap<>();
        prodIds.forEach(prodId -> {
            Integer giftId = Integer.parseInt(prodId);
            awardGifts.put(giftId, socialProdManager.getSocialGift(giftId));
        });
        // 奖励数量
        Map<Integer, Integer> awardCounts = new HashMap<>();
        list.forEach(dto -> {
            List<TAwardDetail> details = awardDetailMap.get(dto.getAwardConfigCode());
            details.forEach(detail -> {
                Integer giftId = Integer.valueOf(detail.getAwardTypeValue());
                // 原值
                Integer oldValue = awardCounts.get(giftId);
                oldValue = null == oldValue ? 0 : oldValue;
                // 增长值
                Integer addValue = detail.getCount() * dto.getAwardTimes();
                awardCounts.put(giftId, oldValue + addValue);
            });
        });
        // 封装结果集
        List<BlindBoxInfoDTO> feedbackList = new ArrayList<>();
        awardGifts.forEach((giftId, gift) -> {
            feedbackList.add(new BlindBoxInfoDTO(gift, awardCounts.get(giftId)));
        });
        feedbackList.sort(Comparator.comparing(BlindBoxInfoDTO::getTotalBeans).reversed());
        return feedbackList.get(0);
    }

    /**
     * 新增开盲盒对应的礼物清单
     *
     * @param userId
     * @param friendId
     * @param blindGiftId
     * @param blindBoxInfo
     */
    private void addBlindBoxGiftListLog(Long userId, Long friendId, Integer blindGiftId, BlindBoxInfoDTO blindBoxInfo) {
        Long tid = GlobalUtils.tid();
        Date nowTime = DateUtils.nowTime();
        USocialBlindBox blindBox = new USocialBlindBox();
        blindBox.setTid(tid);
        blindBox.setCreateTime(nowTime);
        blindBox.setFromUserId(userId);
        blindBox.setToUserId(friendId);
        blindBox.setBlindGiftId(blindGiftId);
        blindBox.setGiftId(blindBoxInfo.getProdId());
        blindBox.setCount(blindBoxInfo.getProdCnt());
        uSocialBlindBoxMapper.insert(blindBox);
    }

    /**
     * 生成盲盒礼物抽奖代码
     *
     * @param giftId
     * @return
     */
    private String buildBlindPoolCode(Integer giftId) {
        return MsgUtils.format("BlindGift_{}_{}", giftId, 1);
    }

    /**
     * 标记消息等待回调
     */
    public void markWaitCallbackNew(String msgId) {
        CallbackAfterTransactionUtil.send(() -> {
            socialRedisTemplate.set(buildMsgCallbackKeyNew(msgId), "0");
        });
    }

    public void markWaitCallbackOld(Long fromUserId, Long toUserId, ChatContentType contentType, String content) {
        String type = contentType.name();
        if (contentType == ChatContentType.Audio || contentType == ChatContentType.DiceGame || contentType == ChatContentType.FingerGame) {
            content = contentType.name();
        }
        final String markContent = content;
        CallbackAfterTransactionUtil.send(() -> {
            String msgId = MsgUtils.format("{}#{}#{}#{}", fromUserId, toUserId, type, markContent);
            RedisKey redisKey = buildMsgCallbackKeyOld(msgId);
            socialRedisTemplate.rpush(redisKey, "0");
            socialRedisTemplate.expire(redisKey);
        });
    }

    /**
     * 处理私信消息回调
     */
    public boolean processCallbackNew(String  msgId) {
        return socialRedisTemplate.del(buildMsgCallbackKeyNew(msgId)) > 0;
    }

    public boolean processCallbackOld(String fromUserId, String toUserId, ChatContentType contentType, String content) {
        String msgId = MsgUtils.format("{}#{}#{}#{}", fromUserId, toUserId, contentType, content);
        return socialRedisTemplate.rpop(buildMsgCallbackKeyOld(msgId)) != null;
    }

    private RedisKey buildMsgCallbackKeyNew(String msgId) {
        return RedisKey.create(SocialKeyDefine.MsgCallbackNew, msgId);
    }

    private RedisKey buildMsgCallbackKeyOld(String msgId) {
        return RedisKey.create(SocialKeyDefine.MsgCallbackOld, EncryptUtils.md5(msgId));
    }

    /**
     * 构建 一键批量搭讪 缓存
     */
    private RedisKey buildBatchChatUp(Long userId){
        return RedisKey.create(SocialKeyDefine.BatchChatUpMark, userId);
    }

    /**
     * 构建未充值男用户私信当日私聊女用户次数
     */
    private RedisKey buildNrMaleChatFemaleTimes(Long maleUserId, String dateStr){
        return RedisKey.create(SocialKeyDefine.NrMaleChatFemaleTimes, dateStr, maleUserId);
    }

    /**
     * 构建礼物普通电视墙缓存
     */
    private RedisKey buildGiftTvWall(){
        return RedisKey.create(SocialKeyDefine.GiftCommonTvWall);
    }

    /**
     * 构建礼物大额电视墙缓存
     */
    private RedisKey buildBaGiftTvWall(){
        return RedisKey.create(SocialKeyDefine.GiftBaTvWall);
    }

    /**
     * 用户查看大额礼物的最大送礼时间戳记录
     */
    private RedisKey buildUserLookBaGiftTime(Long userId){
        return RedisKey.create(SocialKeyDefine.UserLookBaGiftTime, userId);
    }

    public RedisKey buildUserMsgReplyData(String dateStr, Long userId){
        return RedisKey.create(SocialKeyDefine.UserMsgReplyData, dateStr, userId);
    }

    /**
     * 社交行为最近交互时间记录
     */
    public RedisKey buildSocialLastlyInteractive(String userId){
        return RedisKey.create(SocialKeyDefine.SocialLastlyInteractive, userId);
    }

    /**
     * 礼物索要频率限制
     */
    public RedisKey buildRqGiftFrequency(Long userId, Long friendId){
        return RedisKey.create(SocialKeyDefine.RqGiftFrequency, userId, friendId);
    }

    /**
     * 女用户每天首次跟男用户建立私信交互记录缓存
     */
    public RedisKey buildUserFirstMsgForDayKey(String dateStr, Integer shardIndex){
        return RedisKey.create(SocialKeyDefine.UserFirstMsgForDayRecord, dateStr, shardIndex);
    }
}
