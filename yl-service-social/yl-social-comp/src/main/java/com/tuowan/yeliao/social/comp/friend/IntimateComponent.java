/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.social.comp.friend;

import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.comp.rank.vo.RankUserInfoVO;
import com.tuowan.yeliao.commons.config.enums.MiniLockType;
import com.tuowan.yeliao.commons.config.enums.ObjectLockType;
import com.tuowan.yeliao.commons.config.lock.MiniLockTemplate;
import com.tuowan.yeliao.commons.config.lock.ObjectLockTemplate;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.context.GlobalDataUtils;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.enums.busicode.BackCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.data.dto.user.OnlineDTO;
import com.tuowan.yeliao.commons.data.dto.user.UserBusiDTO;
import com.tuowan.yeliao.commons.data.entity.config.TLevel;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserMore;
import com.tuowan.yeliao.commons.data.enums.config.LevelType;
import com.tuowan.yeliao.commons.data.enums.user.OnlineStatus;
import com.tuowan.yeliao.commons.data.manager.user.ActionStatManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.data.utils.HtmlUrlUtils;
import com.tuowan.yeliao.social.comp.family.FamilyComponent;
import com.tuowan.yeliao.social.data.dto.friend.*;
import com.tuowan.yeliao.social.data.dto.relation.RelationBasicListDTO;
import com.tuowan.yeliao.social.data.entity.FIntimateDownLog;
import com.tuowan.yeliao.social.data.entity.FRelationBasic;
import com.tuowan.yeliao.social.data.enums.friend.IntimateLevel;
import com.tuowan.yeliao.social.data.enums.friend.IntimatePrivilege;
import com.tuowan.yeliao.social.data.enums.friend.IntimateStatus;
import com.tuowan.yeliao.social.data.manager.friend.FriendRelationManager;
import com.tuowan.yeliao.social.data.persistence.FIntimateDownLogMapper;
import com.tuowan.yeliao.social.data.persistence.FRelationBasicMapper;
import com.tuowan.yeliao.social.data.persistence.query.CommQueryMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.resps.Tuple;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 亲密度组件封装
 *
 * <AUTHOR>
 * @date 2020/7/22 14:14
 */
@Component
public class IntimateComponent {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 密友关系需要的最小亲密度等级（需要花费50元）
     */
    private static final Integer INTIMATE_FRIEND_MIN_LEVEL = 2;
    /**
     * 密友在线排序分
     */
    private static final Integer INTIMATE_ONLINE_SCORE = 100000000;

    @Autowired
    private FriendRelationManager relationManager;
    @Autowired
    private ObjectLockTemplate objectLockTemplate;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private FRelationBasicMapper fRelationBasicMapper;
    @Autowired
    private FIntimateDownLogMapper fIntimateDownLogMapper;
    @Autowired
    private FriendRelationComponent friendRelationComponent;
    @Autowired
    private CpComponent cpComponent;
    @Autowired
    private SocialRedisTemplate socialRedisTemplate;
    @Autowired
    private CommQueryMapper commQueryMapper;
    @Autowired
    private MiniLockTemplate miniLockTemplate;
    @Autowired
    private FamilyComponent familyComponent;
    @Autowired
    private ActionStatManager actionStatManager;

    /**
     * 增加好友亲密度
     * 同性之间互相没有亲密度
     *
     * @param userId     用户ID
     * @param friendId   好友ID
     * @param totalBeans 消费金币
     */
    public FRelationBasic addIntimateWithBeans(Long userId, Long friendId, Long totalBeans) {
        return addIntimate(userId, friendId, totalBeans);
    }

    /**
     * 获取我与对方的亲密度信息
     */
    public IntimateDTO getChatIntimateInfo(Long userId, Long friendId) {
        RelationDTO relationDTO = relationManager.getRelation(userId, friendId);
        return getChatIntimateInfo(relationDTO);
    }

    /**
     * 获取我与对方的亲密度信息
     */
    public IntimateDTO getChatIntimateInfo(RelationDTO relationDTO) {
        // 没有亲密度的下一等级 默认为 Lv0
        Integer intimateLevel = 0;
        if (relationDTO.getIntimateLevel() != null) {
            intimateLevel = relationDTO.getIntimateLevel();
        }
        TLevel next = relationManager.getNextIntimateLevel(intimateLevel);
        return new IntimateDTO(relationDTO.getRelation(), next, HtmlUrlUtils.getIntimateUrl());
    }

    /**
     * 增加好友礼物亲密度
     *
     * @param userId   用户ID
     * @param friendId 好友ID
     * @param addValue 亲密度变化值
     */
    public void addGiftIntimate(Long userId, Long friendId, Long addValue) {
        String relationId = BusiUtils.generateRelationId(userId, friendId);
        objectLockTemplate.acquireTransactionLock(ObjectLockType.FriendRelation, relationId);
        FRelationBasic relation = fRelationBasicMapper.selectByPrimaryKey(new FRelationBasic(relationId));
        if (relation == null) {
            relation = friendRelationComponent.initRelation(userId, friendId).getRelation();
        }
        // 计算礼物亲密度
        FRelationBasic update = new FRelationBasic(relationId);
        update.setGiftIntimateNum(Math.max(0, NumberUtils.toLong(relation.getGiftIntimateNum()) + addValue));
        fRelationBasicMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 增加好友亲密度
     * 同性之间互相没有亲密度
     *
     * @param userId   用户ID
     * @param friendId 好友ID
     * @param addValue 亲密度变化值
     */
    public FRelationBasic addIntimate(Long userId, Long friendId, Long addValue) {
        Date nowTime = DateUtils.nowTime();
        String relationId = BusiUtils.generateRelationId(userId, friendId);
        objectLockTemplate.acquireTransactionLock(ObjectLockType.FriendRelation, BusiUtils.generateRelationId(userId, friendId));
        FRelationBasic relation = fRelationBasicMapper.selectByPrimaryKey(new FRelationBasic(relationId));
        if (relation == null) {
            relation = friendRelationComponent.initRelation(userId, friendId).getRelation();
        }
        // 如果是清除亲密度
        boolean dismiss = addValue.equals(-Long.MAX_VALUE);
        if (dismiss) {
            addValue = -Math.abs(relation.getIntimateNum());
        }
        Integer levelBefore = relation.getIntimateLevel();
        // 计算亲密度
        relation.setIntimateNum(Math.max(0, relation.getIntimateNum() + addValue));
        // 确认最新的亲密度等级
        relation.setIntimateLevel(relationManager.confirmIntimateLevel(relation.getIntimateNum()).getLevelValue());
        // 设置亲密度变更时间
        if (addValue > 0) {
            relation.setLastAddtime(nowTime);
        } else {
            relation.setLastDowntime(nowTime);
            // 增加亲密度下降异动日志，当前只有系统自动维护亲密度等级时存在下降
            this.addIntimateDownLog(relation.getRelationId(), relation.getIntimateNum() - addValue, relation.getIntimateNum());
            if (relation.getIntimateNum() > 0 && relation.getCpStatus() == BoolType.True) {
                // 增加情侣空间动态
                Long diffDays = DateUtils.getDiffDays(relation.getLastAddtime(), DateUtils.nowTime());
                cpComponent.addIntimateDownMoment(userId, friendId, diffDays, addValue);
            }
        }
        // 计算今日累加值，判断今日亲密度累加值计算日期，不是当天则重置，是当天，则累加
        if (relation.getTodayNumDate() == null || relation.getTodayNumDate().before(DateUtils.nowDate())) {
            relation.setTodayNumTotal(addValue);
            // 今日亲密度变化值
            saveIntimateTodayChange(relationId, addValue);
            relation.setTodayNumDate(nowTime);
        } else {
            Long todayTotal = relation.getTodayNumTotal() + addValue;
            relation.setTodayNumTotal(todayTotal);
            // 今日亲密度变化值
            saveIntimateTodayChange(relationId, todayTotal);
        }
        // 清零情况下变化值为0
        if (dismiss) {
            relation.setTodayNumTotal(0L);
            // 今日亲密度变化值
            saveIntimateTodayChange(relationId, 0L);
        }
        // 处理双方密友关系，如果亲密度等级降到0，密友关系解除
        if (relation.getIntimateLevel() < INTIMATE_FRIEND_MIN_LEVEL && relation.getIntimateTime() != null) {
            relation.setIntimateTime(null);
            // 减少双方密友数量
            relationManager.decrIntimateCnt(userId);
            relationManager.decrIntimateCnt(friendId);
            // 解除存在的情侣空间
//            cpComponent.removeCpRelation(userId, friendId);
            // 修改情侣状态
            relation.setCpStatus(BoolType.False);
            // 刷新密友关系缓存
            this.refreshIntimateFriendCache(userId, friendId, 0L, false);
        }
        // 如果紧密度达到1，判断是否升级为密友关系
        if (relation.getIntimateLevel() >= INTIMATE_FRIEND_MIN_LEVEL) {
            if (relation.getIntimateTime() == null) {
                relation.setIntimateTime(DateUtils.nowTime());
                // 增加双方密友数量
                relationManager.incrIntimateCnt(userId);
                relationManager.incrIntimateCnt(friendId);
            }
            // 刷新密友关系缓存
            this.refreshIntimateFriendCache(userId, friendId, relation.getIntimateNum(), true);
        }
        // 更新亲密度关系(如果亲密度下降为0，并且没有相互关注和拉黑关系，则清理掉关系)
        if (!clearRelationIfNeed(relation)) {
            fRelationBasicMapper.updateByPrimaryKey(relation);
        }
        // 自动开通情侣空间
        /*if (BoolType.True != relation.getCpStatus()) {
            cpComponent.saveOpenCpSpace(userId, friendId, relation.getIntimateLevel());
        }*/
        // 发送亲密度变化通知
        this.sendIntimateChangeMsg(userId, friendId, relation, levelBefore);
        // 如果是增加亲密度，标记3天有增加标记
        if (addValue > 0) {
            socialRedisTemplate.set(buildIntimateIncrMarkKey(userId), "T");
            socialRedisTemplate.set(buildIntimateIncrMarkKey(friendId), "T");
        }
        return relation;
    }

    /**
     * 将关系移入历史表
     */
    private boolean clearRelationIfNeed(FRelationBasic relation) {
        if (relation == null) {
            return false;
        }
        if (relation.getIntimateNum() > 0 || relation.getBigFollowTime() != null || relation.getBigBlacklistTime() != null
                || relation.getSmallFollowTime() != null || relation.getSmallBlacklistTime() != null) {
            // 有亲密度，或者有其他关注或者拉黑关系，则不能清除关系
            return false;
        }
        fRelationBasicMapper.deleteByPrimaryKey(relation);
        return true;
    }

    /**
     * 删除用户关系
     */
    public void deleteUserRelation(FRelationBasic basic){
        fRelationBasicMapper.deleteByPrimaryKey(basic);
    }

    /**
     * 解除密友
     *
     * @param userId
     * @param friendId
     */
    public void saveDismissIntimate(Long userId, Long friendId) {
        // 使用很大的一个负亲密度来解除关系
        addIntimate(userId, friendId, -Long.MAX_VALUE);
    }

    /**
     * 发送亲密度变化通知
     */
    private void sendIntimateChangeMsg(Long userId, Long friendId, FRelationBasic relation, Integer levelBefore) {
        // 发送亲密度变化通知
        RelationDTO relationDTO = RelationDTO.create(userId, friendId, relation);
        GlobalUtils.extValue(BusinessDataKey.UserIds, new Long[]{userId, friendId});
        Map<String, Object> params = new HashMap<>();
        params.put("relationId", relationDTO.getRelationId());
        params.put("intimateNum", relationDTO.getIntimateNum());
        params.put("intimateLevel", relationDTO.getIntimateLevel());
        params.put("intimateLevelPic", BusiUtils.buildIntimateLevelPic(relationDTO.getIntimateLevel()));
        TLevel nextLevel = relationManager.getNextIntimateLevel(relationDTO.getIntimateLevel());
        params.put("nextIntimateNum", nextLevel.getExpLowerLimit());
        params.put("nextIntimateLevel", nextLevel.getLevelValue());
        params.put("changeTime", System.currentTimeMillis());
        GlobalUtils.extValue(BusinessDataKey.CustomMap, params);
        GlobalUtils.extValue(BusinessDataKey.IntimateLevel, relationDTO.getIntimateLevel());
        GlobalUtils.extValue(BusinessDataKey.IntimateNum, relationDTO.getIntimateNum());
        GlobalUtils.extValue(BusinessDataKey.OverwriteBusiCode, BackCodeDefine.IntimateChange);
        messageComponent.sendMatchMsgs();
        // 亲密度升级
        if (levelBefore < relationDTO.getIntimateLevel()) {
            // 用户基本信息
            UUserBasic user = userInfoManager.getUserBasic(userId);
            // 好友基本信息
            UUserBasic friend = userInfoManager.getUserBasic(friendId);
            // 情侣空间亲密度升级动态
            /*if (relation.getCpStatus() == BoolType.True && relationDTO.getIntimateLevel() > 1) {
                cpComponent.addIntimateLevelUpMoment(userId, friendId, relationDTO.getIntimateLevel());
            }*/
            // 亲密度升级tips
            /*String upText = getUpText(relationDTO.getIntimateLevel(), user.getSex() == friend.getSex());
            if (StringUtils.isNotEmpty(upText)) {
                messageComponent.sendChatTipsMsg(friend, userId, ChatTipsInfoDTO.create(upText));
                messageComponent.sendChatTipsMsg(user, friendId, ChatTipsInfoDTO.create(upText));
            }*/
            Map<BusinessDataKey, Object> extMap = new HashMap<>();
            extMap.put(BusinessDataKey.NewLevelValue, relationDTO.getIntimateLevel());
            extMap.put(BusinessDataKey.UserLevelType, LevelType.Intimate);

            extMap.put(BusinessDataKey.UserId, user.getUserId());
            extMap.put(BusinessDataKey.HeadPic, user.getHeadPic());

            extMap.put(BusinessDataKey.FriendId, friend.getUserId());
            extMap.put(BusinessDataKey.FriendHeadPic, friend.getHeadPic());
            extMap.put(BusinessDataKey.PrivilegeIcon, IntimatePrivilege.getPrivilegeIconInChat(relationDTO.getIntimateLevel()));
            messageComponent.sendMsgToUserInContext(BackCodeDefine.UserUpLevel, extMap, userId, friendId);
        }
    }

    /**
     * 亲密度升级文案
     *
     * @param intimateLevel
     * @param sameSex
     * @return
     */
    private String getUpText(Integer intimateLevel, boolean sameSex) {
        IntimateLevel configLevel = EnumUtils.byId(intimateLevel.toString(), IntimateLevel.class);
        // 一般是超越了配置等级的情况
        if (null == configLevel) {
            return null;
        }
        String text = configLevel.getUpText();
        if (!sameSex && StringUtils.isNotEmpty(configLevel.getDiffSexUpText())) {
            text = configLevel.getDiffSexUpText();
        }
        return text;
    }

    /**
     * 添加亲密度下降异动日志
     *
     * @param oldValue 旧值
     * @param newValue 新值
     */
    private void addIntimateDownLog(String relationId, Long oldValue, Long newValue) {
        FIntimateDownLog log = new FIntimateDownLog();
        log.setRelationId(relationId);
        log.setOldValue(oldValue);
        log.setNewValue(newValue);
        log.setCreateTime(DateUtils.nowTime());
        fIntimateDownLogMapper.insert(log);
    }

    /**
     * 存储今日发过降级消息缓存
     *
     * @param userId
     */
    public boolean saveIntimateDownMsgUserCache(Long userId) {
        RedisKey redisKey = buildIntimateDownMsgUserKey();
        Long line = socialRedisTemplate.sadd(redisKey, userId.toString());
        boolean success = line > 0;
        if (success) {
            socialRedisTemplate.expire(redisKey);
        }
        return success;
    }

    /**
     * 初始化密友列表缓存
     */
    public void initIntimateFriendCache(Long userId) {
        initIntimateFriendCache(userId, false);
    }

    /**
     * 初始化密友列表缓存
     */
    public void initIntimateFriendCache(Long userId, boolean reg) {
        // 如果是新注册的用户，没有密友，直接初始化空缓存
        RedisKey redisKey = buildIntimateFriendKey(userId);
        if (reg) {
            socialRedisTemplate.zadd(redisKey, 0D, "0");
            socialRedisTemplate.expire(redisKey);
            return;
        }
        if (socialRedisTemplate.exists(redisKey)) {
            // 已经加载过密友缓存
            return;
        }
        // 加锁，初始化密友缓存
        miniLockTemplate.execute(MiniLockType.IntimateFriend, userId.toString(), () -> {
            if (socialRedisTemplate.exists(redisKey)) {
                // 已经加载过密友缓存
                return null;
            }
            List<IntimateFriendDTO> intimateFriends = commQueryMapper.selectIntimateFriendIds(userId);
            if (ListUtils.isEmpty(intimateFriends)) {
                socialRedisTemplate.zadd(redisKey, 0D, "0");
                return null;
            }
            Map<String, Double> scoreMap = new HashMap<>();
            for (IntimateFriendDTO dto : intimateFriends) {
                Double score = dto.getIntimateNum().doubleValue();
                // 密友在线分
                if (userInfoManager.isOnline(dto.getFriendId())) {
                    score += INTIMATE_ONLINE_SCORE;
                }
                scoreMap.put(dto.getFriendId().toString(), score);
            }
            // 批量加载到缓存
            socialRedisTemplate.zadd(redisKey, scoreMap);
            socialRedisTemplate.expire(redisKey);
            return null;
        });
    }

    /**
     * 密友关系发生变化，刷新密友列表缓存
     *
     * @param userId         用户ID
     * @param friendId       好友ID
     * @param becomeIntimate 是否成为密友
     */
    private void refreshIntimateFriendCache(Long userId, Long friendId, Long intimateNum, boolean becomeIntimate) {
        CallbackAfterTransactionUtil.send(() -> {
            if (becomeIntimate) {
                // 添加到双方密友缓存
                RedisKey userKey = buildIntimateFriendKey(userId);
                if (socialRedisTemplate.exists(userKey)) {
                    Long score = intimateNum;
                    Double friendScore = socialRedisTemplate.zscore(userKey, friendId.toString());
                    friendScore = null == friendScore ? 0D : friendScore;
                    if (userInfoManager.isOnline(friendId) && friendScore < INTIMATE_ONLINE_SCORE) {
                        score += INTIMATE_ONLINE_SCORE;
                    }
                    socialRedisTemplate.zadd(userKey, score.doubleValue(), friendId.toString());
                    socialRedisTemplate.expire(userKey);
                }
                // 添加到双方密友缓存
                RedisKey friendKey = buildIntimateFriendKey(friendId);
                if (socialRedisTemplate.exists(friendKey)) {
                    Long score = intimateNum;
                    Double userScore = socialRedisTemplate.zscore(friendKey, userId.toString());
                    userScore = null == userScore ? 0D : userScore;
                    if (userInfoManager.isOnline(userId) && userScore < INTIMATE_ONLINE_SCORE) {
                        score += INTIMATE_ONLINE_SCORE;
                    }
                    socialRedisTemplate.zadd(friendKey, score.doubleValue(), userId.toString());
                    socialRedisTemplate.expire(userKey);
                }
            } else {
                // 从双方的密友关系中移除
                socialRedisTemplate.zrem(buildIntimateFriendKey(userId), friendId.toString());
                socialRedisTemplate.zrem(buildIntimateFriendKey(friendId), userId.toString());
            }
        });
    }


    /**
     * 亲密度下降发送系统用户缓存
     *
     * @return
     */
    private RedisKey buildIntimateDownMsgUserKey() {
        return RedisKey.create(SocialKeyDefine.IntimateDownMsgUser);
    }

    /**
     * 密友列表缓存Key
     *
     * @return
     */
    private RedisKey buildIntimateFriendKey(Long userId) {
        return RedisKey.create(SocialKeyDefine.IntimateFriend, userId);
    }

    public List<String> getIntimateFriendIds(Long userId) {
        // 根据需要初始化用户密友列表缓存
        this.initIntimateFriendCache(userId);
        // 获取在线的密友列表
        List<String> friendIds = socialRedisTemplate.zrevrangeByIndex(buildIntimateFriendKey(userId), 0, -1);
        if (ListUtils.isEmpty(friendIds)) {
            return friendIds;
        }
        friendIds.remove("0");
        return friendIds;
    }

    /**
     * 获取亲密度前十的密友
     *
     * @return
     */
    public Set<Long> getTopIntimateFriendIds(Long userId) {
        this.initIntimateFriendCache(userId);
        // 前十
        List<Tuple> onlineFriend = socialRedisTemplate.zrevrangeByScoreWithScores(buildIntimateFriendKey(userId), Integer.MAX_VALUE, INTIMATE_ONLINE_SCORE, 0, 20);
        List<Tuple> offlineFriend = socialRedisTemplate.zrevrangeByScoreWithScores(buildIntimateFriendKey(userId), INTIMATE_ONLINE_SCORE - 1, 0, 0, 20);
        if (ListUtils.isEmpty(onlineFriend) && ListUtils.isEmpty(offlineFriend)) {
            return null;
        }
        List<RankUserInfoVO> rankList = new ArrayList<>();
        for (Tuple tuple : onlineFriend) {
            RankUserInfoVO vo = new RankUserInfoVO();
            vo.setUserId(Long.parseLong(tuple.getElement()));
            vo.setScore((long) (tuple.getScore() - INTIMATE_ONLINE_SCORE));
            rankList.add(vo);
        }
        for (Tuple tuple : offlineFriend) {
            RankUserInfoVO vo = new RankUserInfoVO();
            vo.setUserId(Long.parseLong(tuple.getElement()));
            vo.setScore((long) (tuple.getScore()));
            rankList.add(vo);
        }
        rankList.sort(Comparator.comparing(RankUserInfoVO::getScore).reversed());
        return rankList.stream().map(RankUserInfoVO::getUserId).collect(Collectors.toSet());
    }

    /**
     * 获取达到一定亲密值的密友
     *
     * @return
     */
    public List<String> getIntimateFriendIds(Long userId, Long intimateNum) {
        this.initIntimateFriendCache(userId);
        // 前十
        List<String> onlineFriend = socialRedisTemplate.zrevrangeByScore(buildIntimateFriendKey(userId), Integer.MAX_VALUE, INTIMATE_ONLINE_SCORE + intimateNum, 0, -1);
        List<String> offlineFriend = socialRedisTemplate.zrevrangeByScore(buildIntimateFriendKey(userId), INTIMATE_ONLINE_SCORE - 1, intimateNum, 0, -1);
        if (onlineFriend == null) {
            onlineFriend = new ArrayList<>();
        }
        if (offlineFriend == null) {
            offlineFriend = new ArrayList<>();
        }
        onlineFriend.addAll(offlineFriend);
        return onlineFriend;
    }


    /**
     * 上下线刷新密友的列表分值
     *
     * @param userId
     * @param onlineStatus
     */
    public void refreshUserIntimateList(Long userId, OnlineStatus onlineStatus) {
        // 获取所有密友
        List<String> friendIds = socialRedisTemplate.zrevrangeByIndex(buildIntimateFriendKey(userId), 0, -1);
        for (String id : friendIds) {
            Long friendId = Long.valueOf(id);
            RedisKey friendIdsKey = buildIntimateFriendKey(friendId);
            // 密友的列表不存在,跳过处理
            if (!socialRedisTemplate.exists(friendIdsKey)) {
                continue;
            }
            // 刷新密友userId的分值
            Double scoreD = socialRedisTemplate.zscore(friendIdsKey, userId.toString());
            scoreD = null == scoreD ? 0D : scoreD;
            if (OnlineStatus.Online == onlineStatus && scoreD < INTIMATE_ONLINE_SCORE) {
                scoreD += INTIMATE_ONLINE_SCORE;
            } else if (scoreD > INTIMATE_ONLINE_SCORE) {
                scoreD -= INTIMATE_ONLINE_SCORE;
            }
            socialRedisTemplate.zadd(friendIdsKey, scoreD, userId.toString());
            socialRedisTemplate.expire(friendIdsKey);
        }

    }

    /**
     * 获取男用户亲密好友
     * 备注：亲密度值大于6（用作系统匹配过滤）
     */
    public List<String> getMaleIntimateFriendForSc(Long maleUserId){
        return socialRedisTemplate.zrangeByIndex(buildIntimateFriendKey(maleUserId), 0, -1);
    }

    /**
     * 是否为缘分匹配有固定对象的用户
     *
     * @param userId
     * @return
     */
    public boolean isFateMatchFixUser(Long userId) {
        // 判断近3天是否有亲密度增加
        if (!socialRedisTemplate.exists(buildIntimateIncrMarkKey(userId))) {
            return false;
        }
        // 判断是否有1人亲密度值大于等于20
        RedisKey redisKey = buildIntimateFriendKey(userId);
        List<Tuple> friendList = socialRedisTemplate.zrevrangeWithScores(redisKey, 0, 100);
        if (ListUtils.isEmpty(friendList)) {
            return false;
        }
        for (Tuple tuple : friendList) {
            Long score = (long) tuple.getScore();
            if (score >= INTIMATE_ONLINE_SCORE) {
                score = score - INTIMATE_ONLINE_SCORE;
            }
            if (score >= 20) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否有密友在线
     *
     * @param userId
     * @return
     */
    public boolean intimateOnline(Long userId) {
        RedisKey intimateKey = buildIntimateFriendKey(userId);
        if (socialRedisTemplate.exists(intimateKey)) {
            List<String> friendIds = socialRedisTemplate.zrangeByScore(intimateKey, INTIMATE_ONLINE_SCORE, Long.MAX_VALUE);
            return friendIds.size() > 0;
        }
        return false;
    }

    /**
     * 今日密友变化值
     *
     * @param relationId
     */
    private void saveIntimateTodayChange(String relationId, Long addValue) {
        socialRedisTemplate.set(buildIntimateDayChangeKey(relationId), addValue.toString());
    }

    /**
     * 亲密度变化日缓存
     *
     * @return
     */
    private RedisKey buildIntimateDayChangeKey(String relationId) {
        return RedisKey.create(SocialKeyDefine.IntimateTodayChange, relationId, DateUtils.toString(new Date(), DatePattern.YMD));
    }

    /**
     * 亲密度三天内有变化标记
     *
     * @param userId
     * @return
     */
    private RedisKey buildIntimateIncrMarkKey(Long userId) {
        return RedisKey.create(SocialKeyDefine.IntimateIncrMark, userId);
    }

    /**
     * 获取今日亲密度变化值
     *
     * @param relationId
     * @return
     */
    public Long getIntimateTodayChangeNum(String relationId) {
        return socialRedisTemplate.getLong(buildIntimateDayChangeKey(relationId));
    }

    /**
     * 获取亲密等级列表
     *
     * @param basic
     * @param friendBasic
     * @return
     */
    public List<IntimateLevelDTO> getIntimateLevelList(UUserBasic basic, UUserBasic friendBasic, UUserMore friendMore, Integer intimateLevel, Integer nextIntimateLevel) {
        List<IntimateLevelDTO> intimateLevelList = new ArrayList<>();
        IntimatePrivilege current = IntimatePrivilege.getIntimatePrivilege(intimateLevel);
        IntimatePrivilege next = IntimatePrivilege.getNext(current);
        for (IntimatePrivilege privilege : IntimatePrivilege.values()) {
            if (!privilege.isOpen()) {
                continue;
            }
            // 选中(当前等级)
            boolean select = privilege == current;
            // 下个等级选中
            boolean nextSelect = privilege == next;
            IntimateStatus status = IntimateStatus.getInstance(privilege, intimateLevel);
            IntimateLevelDTO dto = new IntimateLevelDTO(privilege, select, nextSelect, status);
            // 1级和完成了 不需要图片
            if(IntimatePrivilege.CWMY == privilege || IntimateStatus.Finish == status){
                dto.setIcon(null);
            }
            intimateLevelList.add(dto);
        }
        return intimateLevelList;
    }
}
