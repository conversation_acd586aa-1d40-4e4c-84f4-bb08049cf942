package com.tuowan.yeliao.social.comp.friend;

import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.ListUtils;
import com.easyooo.framework.common.util.MsgUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.config.enums.MiniLockType;
import com.tuowan.yeliao.commons.config.lock.MiniLockTemplate;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.social.data.dto.friend.DayMgRankDTO;
import com.tuowan.yeliao.social.data.dto.friend.HisMgRankDTO;
import com.tuowan.yeliao.social.data.dto.friend.MgFriendDTO;
import com.tuowan.yeliao.social.data.entity.FMindGiftStat;
import com.tuowan.yeliao.social.data.persistence.FMindGiftStatMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 心意礼物组件
 */
@Component
public class MindGiftComponent {
    @Autowired
    private FMindGiftStatMapper mindGiftStatMapper;
    @Autowired
    private SocialRedisTemplate socialRedisTemplate;
    @Autowired
    private MiniLockTemplate miniLockTemplate;

    /**
     * 删除我的榜单前三用户缓存
     */
    public void delMyMgRankThreeFriendKey(Long userId){
        CallbackAfterTransactionUtil.send(() -> {
            socialRedisTemplate.del(buildMyMgRankThreeFriendKey(userId));
        });
    }

    /**
     * 心意礼物收礼记录
     */
    public void saveMindGiftRecord(Long userId, Long friendId, Integer giftId, Integer beans, String mgMessage, Date createTime, Long tid){
        FMindGiftStat stat = new FMindGiftStat();
        stat.setUserId(userId);
        stat.setFriendId(friendId);
        stat.setGiftId(giftId);
        stat.setBeans(beans);
        stat.setMgMessage(mgMessage);
        stat.setCreateTime(createTime);
        stat.setTid(tid);
        mindGiftStatMapper.insert(stat);
    }

    /**
     * Ta的前三心意榜单用户
     */
    public List<Long> queryMgFriendsThree(Long userId){
        RedisKey redisKey = buildMyMgRankThreeFriendKey(userId);
        List<String> list = socialRedisTemplate.zrevrangeByIndex(redisKey, 0, -1);
        if(ListUtils.isEmpty(list)){
            return miniLockTemplate.execute(MiniLockType.Common, "initMgFriendsThree", () -> {
                // 为了避免并发操作 我们来到这里我们先去删除缓存
                socialRedisTemplate.del(redisKey);
                // 从数据库中获取 并且 加载到缓存
                List<MgFriendDTO> rankDTOS = queryMgFriends(userId, 0, 3);
                if(ListUtils.isEmpty(rankDTOS)){
                    socialRedisTemplate.zadd(redisKey, 0D, GlobalConstant.EMPTY_VALUE);
                    socialRedisTemplate.expire(redisKey);
                    return Collections.emptyList();
                }else{
                    socialRedisTemplate.zadd(redisKey, rankDTOS.stream().collect(Collectors.toMap(k -> String.valueOf(k.getFriendId()), v -> Double.valueOf(v.getMindBeans()))));
                    socialRedisTemplate.expire(redisKey);
                    return rankDTOS.stream().sorted(Comparator.comparing(MgFriendDTO::getMindBeans).reversed()).map(MgFriendDTO::getFriendId).collect(Collectors.toList());
                }
            });
        }
        list.remove(GlobalConstant.EMPTY_VALUE);
        return list.stream().map(Long::valueOf).collect(Collectors.toList());
    }

    /**
     * 给Ta送心意礼物的好友列表
     * 备注：总价值从高到低排列
     */
    public List<MgFriendDTO> queryMgFriends(Long userId, Integer offset, Integer limit){
        return mindGiftStatMapper.queryMgFriends(userId, offset, limit);
    }

    /**
     * 我收到的心意礼物流水
     * 备注：根据送礼时间降序排列
     */
    public List<FMindGiftStat> queryMyMgRecord(Long userId, Integer offset, Integer limit){
        return mindGiftStatMapper.queryMgRecord(userId, offset, limit);
    }

    /**
     * 获取今日榜单
     */
    public List<DayMgRankDTO> queryTodayMgRank(Date now){
        return mindGiftStatMapper.queryDayMgRank(DateUtils.getStartOfDay(now), DateUtils.getEndOfDay(now), 50);
    }

    /**
     * 获取昨日榜首
     */
    public Long queryYesterdayMgRankFirst(Date now){
        List<Long> list = queryYesterdayMgRank(now);
        if(ListUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    /**
     * 昨天榜首的大哥
     */
    public Long queryYesterdayMgRankFirstEldest(Date now, Long firstUserId){
        RedisKey redisKey = buildMyMgRankEldestBrotherKey(DateUtils.toString(now, DatePattern.YMD2), firstUserId);
        String cacheValue = socialRedisTemplate.get(redisKey);
        if(StringUtils.isEmpty(cacheValue)){
            return miniLockTemplate.execute(MiniLockType.Common, "initYesterdayMgRankFirstEldest", () -> {
                // 为了避免并发操作 我们来到这里我们先去删除缓存
                socialRedisTemplate.del(redisKey);
                // 从数据库中获取 并且 加载到缓存
                Long eldestBrother = mindGiftStatMapper.queryMgRankUserEldestBrother(DateUtils.plusDays(DateUtils.getStartOfDay(now), -1), DateUtils.plusDays(DateUtils.getEndOfDay(now), -1), firstUserId);
                if(Objects.isNull(eldestBrother)){
                    socialRedisTemplate.set(redisKey, GlobalConstant.EMPTY_VALUE);
                    return null;
                }else{
                    socialRedisTemplate.set(redisKey, String.valueOf(eldestBrother));
                    return eldestBrother;
                }
            });
        }
        return GlobalConstant.EMPTY_VALUE.equals(cacheValue) ? null : Long.valueOf(cacheValue);
    }

    /**
     * 获取昨日榜单
     */
    public List<Long> queryYesterdayMgRank(Date now){
        // 从缓存中获取
        RedisKey redisKey = buildYesterdayMgRankKey(DateUtils.toString(now, DatePattern.YMD2));
        List<String> list = socialRedisTemplate.zrevrangeByIndex(redisKey, 0, -1);
        if(ListUtils.isEmpty(list)){
            return miniLockTemplate.execute(MiniLockType.Common, "initYesterdayMgRank", () -> {
                // 为了避免并发操作 我们来到这里我们先去删除缓存
                socialRedisTemplate.del(redisKey);
                // 从数据库中获取 并且 加载到缓存
                List<DayMgRankDTO> rankDTOS = mindGiftStatMapper.queryDayMgRank(DateUtils.plusDays(DateUtils.getStartOfDay(now), -1), DateUtils.plusDays(DateUtils.getEndOfDay(now), -1), 50);
                if(ListUtils.isEmpty(rankDTOS)){
                    socialRedisTemplate.zadd(redisKey, 0D, GlobalConstant.EMPTY_VALUE);
                    socialRedisTemplate.expire(redisKey);
                    return Collections.emptyList();
                }else{
                    socialRedisTemplate.zadd(redisKey, rankDTOS.stream().collect(Collectors.toMap(k -> String.valueOf(k.getUserId()), v -> Double.valueOf(v.getBeans()))));
                    socialRedisTemplate.expire(redisKey);
                    return rankDTOS.stream().sorted(Comparator.comparing(DayMgRankDTO::getBeans).reversed()).map(DayMgRankDTO::getUserId).collect(Collectors.toList());
                }
            });
        }
        list.remove(GlobalConstant.EMPTY_VALUE);
        return list.stream().map(Long::valueOf).collect(Collectors.toList());
    }

    /**
     * 获取历史榜单
     */
    public List<String> queryHisMgRank(Date now){
        RedisKey redisKey = buildHisMgRankKey(DateUtils.toString(now, DatePattern.YMD2));
        List<String> list = socialRedisTemplate.zrevrangeByIndex(redisKey, 0, -1);
        if(ListUtils.isEmpty(list)){
            return miniLockTemplate.execute(MiniLockType.Common, "initHisMgRank", () -> {
                // 为了避免并发操作 我们来到这里再次去进行判断
                List<String> list01 = socialRedisTemplate.zrevrangeByIndex(redisKey, 0, -1);
                if(ListUtils.isNotEmpty(list01)){
                    return list01.stream().filter(f -> !GlobalConstant.EMPTY_VALUE.equals(f)).collect(Collectors.toList());
                }
                // 从数据库中获取 并且 加载到缓存
                List<HisMgRankDTO> rankDTOS = mindGiftStatMapper.queryHisMgRank(null, DateUtils.plusDays(DateUtils.getEndOfDay(now), -1), 60);
                if(ListUtils.isEmpty(rankDTOS)){
                    socialRedisTemplate.zadd(redisKey, 0D, GlobalConstant.EMPTY_VALUE);
                    socialRedisTemplate.expire(redisKey);
                    return Collections.emptyList();
                }else{
                    socialRedisTemplate.zadd(redisKey, rankDTOS.stream().collect(Collectors.toMap(k -> MsgUtils.format("{}_{}",  k.getUserId(), k.getDate().getTime()), v -> (double)v.getDate().getTime())));
                    socialRedisTemplate.expire(redisKey);
                    return rankDTOS.stream().sorted(Comparator.comparing(HisMgRankDTO::getDate).reversed()).map(m -> MsgUtils.format("{}_{}", m.getUserId(), m.getDate().getTime())).collect(Collectors.toList());
                }
            });
        }
        return list.stream().filter(f -> !GlobalConstant.EMPTY_VALUE.equals(f)).collect(Collectors.toList());
    }

    /**
     * 昨日榜单缓存
     */
    public RedisKey buildYesterdayMgRankKey(String nowDateStr){
        return RedisKey.create(SocialKeyDefine.YesterdayMgRank, nowDateStr);
    }

    /**
     * 历史榜单缓存
     */
    public RedisKey buildHisMgRankKey(String nowDateStr){
        return RedisKey.create(SocialKeyDefine.HisMgRank, nowDateStr);
    }

    /**
     * 我的前三榜单用户
     */
    public RedisKey buildMyMgRankThreeFriendKey(Long userId){
        return RedisKey.create(SocialKeyDefine.MyMgRankThreeFriend, userId);
    }

    /**
     * 心意榜大哥
     */
    public RedisKey buildMyMgRankEldestBrotherKey(String nowDateStr, Long userId){
        return RedisKey.create(SocialKeyDefine.MyMgRankEldestBrother, nowDateStr, userId);
    }
}
