package com.tuowan.yeliao.social.comp.friend;

import com.alibaba.nacos.common.utils.Pair;
import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.comp.consume.ConsumePresentComponent;
import com.tuowan.yeliao.commons.comp.consume.dto.ConsumePresentDTO;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.comp.operate.UserOperateComponent;
import com.tuowan.yeliao.commons.comp.user.UserBusiComponent;
import com.tuowan.yeliao.commons.comp.user.UserSocialBagComponent;
import com.tuowan.yeliao.commons.config.configuration.impl.AppConfig;
import com.tuowan.yeliao.commons.config.lock.ObjectLockTemplate;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.BusiRedisTemplate;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.context.GlobalDataUtils;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.core.constant.FieldConstant;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.constant.VersionConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BackCodeDefine;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.business.SocialGoodsDefine;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.redis.BusiKeyDefine;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.ComponentException;
import com.tuowan.yeliao.commons.core.exception.GeneralException;
import com.tuowan.yeliao.commons.data.dto.common.RedisShardDTO;
import com.tuowan.yeliao.commons.data.dto.user.BeansConsumeDTO;
import com.tuowan.yeliao.commons.data.dto.user.DiscountDTO;
import com.tuowan.yeliao.commons.data.dto.user.UserBagDTO;
import com.tuowan.yeliao.commons.data.dto.user.UserBusiDTO;
import com.tuowan.yeliao.commons.data.entity.config.TFemaleLevelNum;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGoods;
import com.tuowan.yeliao.commons.data.entity.user.UMessageSetting;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserExt;
import com.tuowan.yeliao.commons.data.enums.config.NoticeSysType;
import com.tuowan.yeliao.commons.data.enums.config.ProdGoodsType;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.enums.general.RedisShardType;
import com.tuowan.yeliao.commons.data.enums.social.ChatContentType;
import com.tuowan.yeliao.commons.data.enums.social.NetCallType;
import com.tuowan.yeliao.commons.data.enums.user.OnlineStatus;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.enums.user.UserCashSourceType;
import com.tuowan.yeliao.commons.data.manager.commons.SeqManager;
import com.tuowan.yeliao.commons.data.manager.config.NewsManager;
import com.tuowan.yeliao.commons.data.manager.config.SocialProdManager;
import com.tuowan.yeliao.commons.data.manager.user.*;
import com.tuowan.yeliao.commons.data.persistence.config.TProdSocialGoodsMapper;
import com.tuowan.yeliao.commons.data.support.config.impl.FemaleLevelNumConfig;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.commons.data.utils.AppVersionUtils;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.data.utils.RedisShardUtils;
import com.tuowan.yeliao.commons.mq.producer.MessageQueueProducer;
import com.tuowan.yeliao.log.data.entity.CAudioRecord;
import com.tuowan.yeliao.log.data.enums.social.AudioType;
import com.tuowan.yeliao.social.comp.chatmaster.ChatMasterComponent;
import com.tuowan.yeliao.social.comp.friend.dto.CallBeanConsumeDTO;
import com.tuowan.yeliao.social.comp.friend.dto.NetCallCloudRecordingDTO;
import com.tuowan.yeliao.social.comp.home.HomeVideoChatComponent;
import com.tuowan.yeliao.social.comp.log.LogComponent;
import com.tuowan.yeliao.social.comp.lottery.TurnTableLotteryComponent;
import com.tuowan.yeliao.social.data.dto.friend.ChatConsumeDTO;
import com.tuowan.yeliao.social.data.dto.friend.PresentCashDTO;
import com.tuowan.yeliao.social.data.dto.friend.RelationBasicDTO;
import com.tuowan.yeliao.social.data.dto.friend.RelationDTO;
import com.tuowan.yeliao.social.data.dto.friend.netcall.*;
import com.tuowan.yeliao.social.data.entity.FChatNetCall;
import com.tuowan.yeliao.social.data.entity.FChatNetCallFee;
import com.tuowan.yeliao.social.data.enums.friend.*;
import com.tuowan.yeliao.social.data.manager.friend.ChatManager;
import com.tuowan.yeliao.social.data.manager.friend.FriendRelationManager;
import com.tuowan.yeliao.social.data.persistence.FChatNetCallFeeMapper;
import com.tuowan.yeliao.social.data.persistence.FChatNetCallMapper;
import com.tuowan.yeliao.social.data.utils.agora.AgoraSdkUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.resps.Tuple;

import javax.print.attribute.standard.Finishings;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 音视频通话业务组件
 *
 * <AUTHOR>
 * @date 2020/7/4 20:58
 */
@Component
public class NetCallComponent {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    /**
     * 余额提醒剩余分钟数
     */
    public static final int BALANCE_REMIND_LESS_MINUTE = 2;
    /**
     * 每分钟第50秒开始扣下一分钟的费用
     */
    public static final int DEDUCT_TIME_IN_MINUTES = 50;
    /**
     * 通话超时时间(用户心跳为30秒一次，1分钟超时，正常情况客户端异常结束由声网处理)
     */
    private final long TIME_OUT = 60 * 1000L;
    /**
     * 视频免费券-每分钟奖励（元|积分）
     */
    private final double VIDEO_FREE_CARD_AWARD = 1.25;
    /**
     * 语音免费券-每分钟奖励（元|积分）
     */
    private final double VOICE_FREE_CARD_AWARD = 0.5;

    @Autowired
    private FChatNetCallMapper fChatNetCallMapper;
    @Autowired
    private FChatNetCallFeeMapper fChatNetCallFeeMapper;
    @Autowired
    private BusiRedisTemplate busiRedisTemplate;
    @Autowired
    private SocialRedisTemplate socialRedisTemplate;
    @Autowired
    private FriendRelationManager friendRelationManager;
    @Autowired
    private ActionStatManager actionStatManager;
    @Autowired
    private SocialProdManager socialProdManager;
    @Autowired
    private ChatManager chatManager;
    @Autowired
    private UserBusiComponent userBusiComponent;
    @Autowired
    private IntimateComponent intimateComponent;
    @Autowired
    private MessageSettingManager messageSettingManager;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private SeqManager seqManager;
    @Autowired
    private ChatConsumeComponent chatConsumeComponent;
    @Autowired
    private TurnTableLotteryComponent lotteryComponent;
    @Autowired
    private UserSocialBagComponent userSocialBagComponent;
    @Autowired
    private TProdSocialGoodsMapper socialGoodsMapper;
    @Autowired
    private UserOperateComponent userOperateComponent;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private HomeVideoChatComponent videoChatComponent;
    @Autowired
    private NewsManager newsManager;
    @Autowired
    private NoticeComponent noticeComponent;
    @Autowired
    private NetCallMatchComponent netCallMatchComponent;
    @Autowired
    private ConsumePresentComponent consumePresentComponent;
    @Autowired
    private TProdSocialGoodsMapper tProdSocialGoodsMapper;
    @Autowired
    private LogComponent logComponent;
    @Autowired
    private MessageQueueProducer messageQueueProducer;
    @Autowired
    private UserVisitManager userVisitManager;
    @Autowired
    private ObjectLockTemplate objectLockTemplate;
    @Autowired
    private ChatMasterManager chatMasterManager;
    @Autowired
    private ChatMasterComponent chatMasterComponent;
    @Autowired
    private FriendRelationComponent friendRelationComponent;

    /**
     * 判断当前视频通话画面违规摄像头是否关闭
     * @param callId 通话标识id
     * @param userId 画面违规方id
     */
    public boolean checkVideoViolationCameraOff(Long callId, Long userId){
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        if(SexType.Male == userBasic.getSex()){
            // 违规方是男用户 直接关闭
            return true;
        }
        RedisKey redisKey = buildVideoViolationCameraOffRecord(callId, userId);
        return socialRedisTemplate.incr(redisKey) == 1;
    }

    /**
     * 视频通话无人脸次数记录
     */
    public long incrVideoCallNoFaceTimes(Long callId){
        return socialRedisTemplate.incr(buildVideoCallNoFaceTimesRecord(callId));
    }

    /**
     * 视频通话无人脸次数记录
     */
    public long clearVideoCallNoFaceTimes(Long callId){
        return socialRedisTemplate.decrby(buildVideoCallNoFaceTimesRecord(callId), 10000L);
    }

    /**
     * 通话
     * @param finishDTO
     */
    public void setFinishExtValue(NetCallFinishDTO finishDTO) {
        GlobalUtils.extValue(BusinessDataKey.CallId, finishDTO.getCallId());
        GlobalUtils.extValue(BusinessDataKey.HangUpId, finishDTO.getHangUpId());
        GlobalUtils.extValue(BusinessDataKey.BillUserId, finishDTO.getBillUserId());
        GlobalUtils.extValue(BusinessDataKey.Duration, finishDTO.getDuration());
        GlobalUtils.extValue(BusinessDataKey.TotalBeans, finishDTO.getTotalBeans());
        GlobalUtils.extValue(BusinessDataKey.ConsumePlatformBeans, finishDTO.getPlatformBeans());
        GlobalUtils.extValue(BusinessDataKey.MatchNetCall, BoolType.valueOf(finishDTO.getSourceType() == NetCallSourceType.VideoMate));
        // 用于客户端展示
        if (finishDTO.getPresentCash() > 0) {
            GlobalUtils.extValue(BusinessDataKey.PresentCash, BusiUtils.cashToYuanSimplifyStr(finishDTO.getPresentCash(), 4));
        }
        GlobalUtils.extValue(BusinessDataKey.FinishType, finishDTO.getFinishType());
        GlobalUtils.extValue(BusinessDataKey.NetCallSourceType, finishDTO.getSourceType());

        GlobalUtils.busiValue(BusinessDataKey.CallUserId, finishDTO.getUserId());
        GlobalUtils.busiValue(BusinessDataKey.ReceiveUserId, finishDTO.getFriendId());
        // 用于分成计算
        GlobalUtils.busiValue(BusinessDataKey.PresentCash, finishDTO.getPresentCash());
        // 此通电话是否使用了物品券
        GlobalUtils.extValue(BusinessDataKey.UseProd, BoolType.valueOf(Objects.nonNull(finishDTO.getDiscountProdId())));
    }

    /**
     * 判断通话评价影响女用户服务分次数
     * 备注：一天仅允许评价三次
     */
    public boolean checkCallEvaluateAboutScore(Date now, Long maleUserId, Long femaleUserId){
        RedisKey redisKey = buildTodayCallEvaluateTimes(DateUtils.toString(now, DatePattern.YMD2));
        Integer times = socialRedisTemplate.hgetInt(redisKey, BusiUtils.generateRelationId(maleUserId, femaleUserId), true);
        return times < 3;
    }

    /**
     * 记录通话评价影响女用户服务分次数
     */
    public void recordCallEvaluateAboutScore(Date now, Long maleUserId, Long femaleUserId){
        CallbackAfterTransactionUtil.send(() -> {
            RedisKey redisKey = buildTodayCallEvaluateTimes(DateUtils.toString(now, DatePattern.YMD2));
            socialRedisTemplate.hincrBy(redisKey, BusiUtils.generateRelationId(maleUserId, femaleUserId), 1L);
            socialRedisTemplate.expire(redisKey);
        });
    }

    /**
     * 获取《视频匹配》《视频邀请》《私聊视频推荐》的视频通话相关券信息
     * 备注：使用优先级是 1、免费券 > 折扣券；2、力度大的 > 力度小的；3、快过期的 > 晚过期的
     * @return 返回券的 id
     */
    public Pair<Integer, ProdGoodsType> getVideoCallUseTicketInfo(Long userId){
        // 优先判断 免费券
        List<UserBagDTO> goodsInfoList = userSocialBagComponent.findGoodsInfoList(userId, ProdGoodsType.SocialVideoFreeTicket);
        if(ListUtils.isNotEmpty(goodsInfoList)) {
            // 免费券 按照免费通话时间从大到小 过期时间从近到远 排序
            goodsInfoList = goodsInfoList.stream().sorted(Comparator.comparing(UserBagDTO::getOptValue).reversed().thenComparing(UserBagDTO::getExpTime)).collect(Collectors.toList());
            return Pair.with(goodsInfoList.get(0).getProdId(), ProdGoodsType.SocialVideoFreeTicket);
        }
        // 其次判断折扣券
        goodsInfoList = userSocialBagComponent.findGoodsInfoList(userId, ProdGoodsType.SocialVideoDiscountTicket);
        if(ListUtils.isNotEmpty(goodsInfoList)) {
            // 折扣券 按照折扣力度从大到小 过期时间从近到远 排序
            goodsInfoList = goodsInfoList.stream().sorted(Comparator.comparing(UserBagDTO::getOptValue).reversed().thenComparing(UserBagDTO::getExpTime)).collect(Collectors.toList());
            return Pair.with(goodsInfoList.get(0).getProdId(), ProdGoodsType.SocialVideoDiscountTicket);
        }
        // 无可使用券的信息
        return null;
    }

    /**
     * 将通话信息加入视频画面违规队列
     */
    public void addVideoFrameViolationQueue(Long callId, Long userId){
        double score = DateUtils.plusSeconds(new Date(), 5).getTime();
        socialRedisTemplate.zadd(buildVideoCallFrameViolationQueue(), score, MsgUtils.format("{}_{}", callId, userId));
    }

    /**
     * 获取视频画面违规队列中的数据
     */
    public List<String> getVideoFrameViolationData(Date now){
        return socialRedisTemplate.zrangeByScore(buildVideoCallFrameViolationQueue(), 0, now.getTime());
    }

    /**
     * 将视频通话画面违规数据从队列中移除
     */
    public void removeVideoFrameViolationQueue(String member){
        socialRedisTemplate.zrem(buildVideoCallFrameViolationQueue(), member);
    }

    /**
     * 修改女用户今日主动拨打视频次数
     */
    public void updateFemaleActCallTimes(Date now, Long femaleUserId, Long num){
        socialRedisTemplate.incrby(buildFemaleActCallTimesKey(DateUtils.toString(now, DatePattern.YMD2), femaleUserId), num);
    }

    /**
     * 获取女用户今日主动拨打视频次数
     */
    public int getFemaleActCallTimes(Date now, Long femaleUserId){
        return socialRedisTemplate.getInt(buildFemaleActCallTimesKey(DateUtils.toString(now, DatePattern.YMD2), femaleUserId));
    }

    /**
     * 将女用户保存进正在通话队列
     */
    public void saveFemaleInCurrentCall(Long femaleUserId){
        socialRedisTemplate.zadd(buildCurrentCallingKey(SexType.Female), (double)System.currentTimeMillis(), String.valueOf(femaleUserId));
    }

    /**
     * 将女用户从正在通话队列中移除
     */
    public void removeFemaleInCurrentCall(Long femaleUserId){
        socialRedisTemplate.zrem(buildCurrentCallingKey(SexType.Female), String.valueOf(femaleUserId));
    }

    /**
     * 获取正在通话的女用户
     * 备注：怕有BUG 又考虑到拨打30分钟的概率较小，我们获取分数为当前时间向前移30分钟的数据
     */
    public List<String> queryCurrentCallingFemale(Date now){
        long minScore = DateUtils.plusMinutes(now, -30).getTime();
        long maxScore = now.getTime();

        return socialRedisTemplate.zrangeByScore(buildCurrentCallingKey(SexType.Female), minScore, maxScore);
    }

    /**
     * 获取视频通话摄像头初始话配置
     * 备注：仅供 /create /info 这两个通话相关接口使用
     * @param callId
     * @param callType
     * @param currentSex（当前请求者的性别）
     * @param friendSex
     * @return
     */
    public VideoCallCameraCtrDTO getVideoCallCameraInitCfg(Long callId, NetCallType callType, Long currentUserId, SexType currentSex, Long friendId, SexType friendSex){
        // 如果不是视频通话 直接返回
        if(NetCallType.Video != callType){
            return VideoCallCameraCtrDTO.buildDefault();
        }
        try {
            // 获取配置
            UrlParamsMap cfgMap = UrlParamsMap.build(getVideoCallCameraInitCfg(callId));

            // 判断自己这方该功能是否开启
            BoolType currentCameraState = null;
            if(cfgMap.getEnumById(MsgUtils.format("{}_switch", currentSex.getId()), BoolType.class).boolValue()){
                // 判断有没有设置值
                UMessageSetting setting = messageSettingManager.getSetting(currentUserId);
                if(Objects.nonNull(setting.getCamera())){
                    currentCameraState = setting.getCamera();
                }else{
                    currentCameraState = cfgMap.getEnumById(MsgUtils.format("{}_initState", currentSex.getId()), BoolType.class);
                }
            }
            BoolType friendCameraState = null;
            if(cfgMap.getEnumById(MsgUtils.format("{}_switch", friendSex.getId()), BoolType.class).boolValue()){
                // 判断有没有设置值
                UMessageSetting setting = messageSettingManager.getSetting(friendId);
                if(Objects.nonNull(setting.getCamera())){
                    friendCameraState = setting.getCamera();
                }else {
                    friendCameraState = cfgMap.getEnumById(MsgUtils.format("{}_initState", friendSex.getId()), BoolType.class);
                }
            }
            return VideoCallCameraCtrDTO.build(currentCameraState, friendCameraState);
        }catch (Exception e){
            LOG.error("getVideoCallCameraCfg-error e:", e);
        }
        return VideoCallCameraCtrDTO.buildDefault();
    }

    /**
     * 获取视频通话摄像头控制
     */
    private String getVideoCallCameraInitCfg(Long callId){
        // 先从缓存中获取
        /*String cacheCfgStr = socialRedisTemplate.get(buildVideoCallCameraCfg(callId));
        if(StringUtils.isNotEmpty(cacheCfgStr)){
            return cacheCfgStr;
        }*/
        // 从数据库中获取
        String dbCfgStr = SettingsConfig.getString(SettingsType.VideoCallCameraCfg);
        // 缓存配置
       /* CallbackAfterTransactionUtil.send(() -> {
            socialRedisTemplate.set(buildVideoCallCameraCfg(callId), dbCfgStr);
        });*/
        return dbCfgStr;
    }

    /**
     * 获取音视频通话语音违规次数
     * 备注：
     * 1.是已经增加了此次的一个次数
     * 2.缓存保存时间为 1个小时，如果我们配置的违规次数是5次
     * 就意味着在这一个小时内 能违规5次，后面又会从新计数
     */
    public Long getNetCallAudioIllegalTimes(Long callId){
        return socialRedisTemplate.incr(buildCallAudioIllegalRecord(callId));
    }

    /**
     * 获取视频通话画面违规次数
     * 备注：
     * 1.是已经增加了此次的一个次数
     * 2.缓存保存时间为 1个小时，如果我们配置的违规次数是5次
     * 就意味着在这一个小时内 能违规5次，后面又会从新计数
     */
    public Long getVideoCallFrameIllegalTimes(Long callId){
        return socialRedisTemplate.incr(buildVideoCallFrameIllegalRecord(callId));
    }

    /**
     * 获取每分钟通话单价
     *
     * @param friendId
     * @param callType
     * @return
     */
    public Integer getBeansPerMin(Long friendId, NetCallType callType) {
        // 视频速配由于不知道接收方是谁 所以没办法知道单价，这里我们给一个默认值200
        /*if(AppConfig.VIDEO_MATCH_RECEIVER.equals(friendId)){
            return GlobalConstant.MATCH_FIRST_MINUTE_PRICE;
        }*/
        UMessageSetting setting = messageSettingManager.getSetting(friendId);
        if (NetCallType.Voice == callType) {
            return setting.getVoiceFee();
        }
        if (NetCallType.Video == callType) {
            return setting.getVideoFee();
        }
        throw new ComponentException(ErrCodeType.InvalidArguments);
    }

    /**
     * 判断是否可以发起语音通话
     *
     * @param userBasic 拨打方
     * @param receiver 接收方
     * @param callType 通话类型
     */
    public void canStartNetCall(UUserBasic userBasic, UUserBasic receiver, NetCallType callType, RelationBasicDTO relationInfo, NetCallSourceType callSourceType) {
        Long userId = userBasic.getUserId();
        Long friendId = receiver.getUserId();
        // 同性别拨打电话直接抛出异常信息
        if(userBasic.getSex() == receiver.getSex()){
            throw new BusiException("对方正忙请稍后重试！");
        }
        // 对拨打方是女用户做特殊校验
        if(SexType.Female == userBasic.getSex()){
            if(chatMasterManager.getActCallIntimate(userId) > relationInfo.getIntimateNum()){
                throw new BusiException("亲密度不够无法主动拨打！");
            }
            // 女用户拨打频率限制(女用户主动给同一个男用户3分钟内连续拨打超过5次)
            if(socialRedisTemplate.getInt(buildFemaleActCallFrequencyLimit(userId, friendId)) >= 5){
                throw new BusiException("当前对同一人拨打过多请稍后再试");
            }
            if(!checkFemaleToMaleCall(userId, friendId)){
                throw new BusiException(ErrCodeType.NotRightCall);
            }
            CallbackAfterTransactionUtil.send(() -> {
                // 记录女用户主动拨打频率
                socialRedisTemplate.incr(buildFemaleActCallFrequencyLimit(userId, friendId));
            });
        }
        // 对拨打方是男用户做特殊校验
        if(SexType.Male == userBasic.getSex()){
            // 男用户主动拨打需要亲密度值 大于100
            if(NetCallSourceType.VideoMate != callSourceType && relationInfo.getIntimateNum() < GlobalConstant.MALE_ACT_CALL_NEED_INTIMATE){
                throw new BusiException(ErrCodeType.NotRightCall);
            }
        }
        // 【判断主叫方】 是否开启了音视频权限
        if (NetCallType.Voice == callType && !messageSettingManager.hasVoiceCall(userId)) {
            throw new ComponentException("您已关闭了语音拨打服务");
        }
        if (NetCallType.Video == callType && !messageSettingManager.hasVideoCall(userId)) {
            throw new ComponentException("您已关闭了视频拨打服务");
        }
        // 【判断被叫方】 是否开启了音视频权限
        if (NetCallType.Voice == callType && !messageSettingManager.hasVoiceAnswer(friendId)) {
            throw new ComponentException("对方关闭了语音接听服务");
        }
        if (NetCallType.Video == callType && !messageSettingManager.hasVideoAnswer(friendId)) {
            throw new ComponentException("对方关闭了视频接听服务");
        }

        RelationDTO relation = friendRelationManager.getRelation(userId, friendId);
        if (relation != null) {
            // 判断拉黑关系
            if (relation.getMyBlacklistTime() != null) {
                throw new BusiException("解除黑名单后才能通话哦");
            }
            if (relation.getTargetBlacklistTime() != null) {
                throw new BusiException("对方已将你拉黑，通话失败");
            }
        }
    }

    /**
     * 检查女用户是否有对男用户发起音视频通话的权限
     */
    public boolean checkFemaleToMaleCall(Long femaleUserId, Long maleUserId){
        String dateStr = DateUtils.toString(new Date(), DatePattern.YMD2);
        RedisKey redisKey = buildFemaleActCallRecordKey(dateStr, femaleUserId);
        if(socialRedisTemplate.sismember(redisKey, String.valueOf(maleUserId))){
            return true;
        }
        return socialRedisTemplate.scard(redisKey) < chatMasterManager.getActCallLimit(femaleUserId);
    }

    /**
     * 获取女用户已主动拨打人数
     */
    public Long getFemaleActCallUsersNum(String dateStr, Long femaleUserId){
        RedisKey redisKey = buildFemaleActCallRecordKey(dateStr, femaleUserId);
        return socialRedisTemplate.scard(redisKey);
    }

    /**
     * 免费检测
     *
     * @param userId
     * @param callType
     * @param friendId
     * @param bean 通话单价
     * @return
     */
    public NetCallWaiverType getWaiverType(Long userId, Long friendId, NetCallType callType, Integer bean) {
        if (bean <= 0) {
            // 自己价格设置的免费
            return NetCallWaiverType.SelfFree;
        }
        // 亲密度特权免费
        /*if (privilegeNetCallFreeByType(userId, friendId, callType)) {
            return NetCallWaiverType.IntimateFree;
        }*/
        // 付费
        return NetCallWaiverType.NotFree;
    }

    /**
     * 新增通话记录
     *
     * @param userId
     * @param friendId
     * @param billUserId
     * @param callType
     * @param sourceType
     * @param beans
     * @return
     */
    public FChatNetCall add(Long userId, Long friendId, Long billUserId, NetCallType callType, NetCallSourceType sourceType,
                            Integer beans, NetCallWaiverType waiverType, TProdSocialGoods goods) {
        FChatNetCall chatNetCall = new FChatNetCall();
        Date now = new Date(GlobalUtils.reqTime());
        chatNetCall.setTid(GlobalUtils.tid());
        chatNetCall.setCallType(callType);
        chatNetCall.setUserId(userId);
        chatNetCall.setFriendId(friendId);
        chatNetCall.setBillUserId(billUserId);
        chatNetCall.setCallTime(now);
        chatNetCall.setBeans(beans);
        chatNetCall.setStatus(NetCallStatus.Wait);
        chatNetCall.setSourceType(sourceType);
        chatNetCall.setProdId(goods == null ? null : goods.getGoodsId());
        chatNetCall.setUseBagCount(goods == null ? 0 : 1);
        chatNetCall.setWaiverType(waiverType);
        fChatNetCallMapper.insert(chatNetCall);
        CallbackAfterTransactionUtil.send(() -> {
            // 折扣券信息存储
            saveDiscountInfoCache(chatNetCall.getCallId(), goods);
        });
        return chatNetCall;
    }

    /**
     * 查询音频背包折扣券
     *
     * @param userId
     * @param callType
     * @return
     */
    public DiscountDTO getBagDiscountByCallType(Long userId, NetCallType callType, Integer beans) {
        if (null == callType || null == beans || beans <= 0) {
            return null;
        }
        ProdGoodsType goodsType = NetCallType.Voice == callType ? ProdGoodsType.SocialVoiceDiscountTicket : ProdGoodsType.SocialVideoDiscountTicket;
        // 查询背包折扣券
        Map<Integer, UserBagDTO> bagMap = userSocialBagComponent.findGoodsInfoMap(userId, goodsType);
        // 是否有对应类型的折扣券 直接比较ID
        if (null == bagMap || bagMap.size() <= 0) {
            return null;
        }
        List<DiscountDTO> discountList = new ArrayList<>();
        // 对应类型折扣券
        List<TProdSocialGoods> socialGoods = socialGoodsMapper.selectByGoodsType(new TProdSocialGoods(goodsType));
        Map<Integer, TProdSocialGoods> goodMap = socialGoods.stream().collect(Collectors.toMap(TProdSocialGoods::getGoodsId, Function.identity(), (k1, k2) -> k1));
        for (UserBagDTO dto : bagMap.values()) {
            TProdSocialGoods goods = goodMap.get(dto.getProdId());
            if (null == goods) {
                continue;
            }
            discountList.add(new DiscountDTO(dto, goods));
        }
        // 折扣升序,失效升序
        discountList = discountList.stream().sorted(Comparator.comparing(DiscountDTO::getDiscount).thenComparing(DiscountDTO::getExpTime)).collect(Collectors.toList());
        if (ListUtils.isEmpty(discountList)) {
            return null;
        }
        // 最低折扣,最先失效的返回
        DiscountDTO first = discountList.get(0);
        return null == first.getDiscount() ? null : first;
    }

    /**
     * 存储通话折扣券信息
     *
     * @param callId
     * @param goods
     */
    private void saveDiscountInfoCache(Long callId, TProdSocialGoods goods) {
        if (null == callId || null == goods) {
            return;
        }
        if (ProdGoodsType.SocialVoiceDiscountTicket != goods.getGoodsType() && ProdGoodsType.SocialVideoDiscountTicket != goods.getGoodsType()){
            return;
        }
        socialRedisTemplate.set(buildNetCallDiscountInfoKey(callId), JsonUtils.seriazileAsString(goods));
    }

    /**
     * 获取通话折扣券信息
     *
     * @param callId
     */
    public TProdSocialGoods getDiscountInfoCache(Long callId) {
        String json = socialRedisTemplate.get(buildNetCallDiscountInfoKey(callId));
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        return JsonUtils.deserializeAsObject(json, TProdSocialGoods.class);
    }

    /**
     * 当前用户是否有待接通的
     *
     * @param friendId
     */
    public boolean hasWaitAcceptUser(Long friendId) {
        RedisKey waitAcceptKey = buildNetCallWaitAcceptKey();
        return socialRedisTemplate.sismember(waitAcceptKey, friendId.toString());
    }

    /**
     * 存储待接通用户,用于待接通前业务判断
     *
     * @param friendId
     */
    private void cacheWaitAcceptUser(Long friendId) {
        RedisKey waitAcceptKey = buildNetCallWaitAcceptKey();
        socialRedisTemplate.sadd(waitAcceptKey, friendId.toString());
    }

    /**
     * 移除待接通用户
     *
     * @param friendId
     */
    private void removeWaitAcceptUser(Long friendId) {
        RedisKey waitAcceptKey = buildNetCallWaitAcceptKey();
        socialRedisTemplate.srem(waitAcceptKey, friendId.toString());
    }

    /**
     * 扣除通话费用
     *
     * @param netCall
     * @param beans        每分钟所需金币
     * @param nowTime
     * @param expireTime 此次扣费允许通话到多久
     */
    public void deductBeans(FChatNetCall netCall, Integer beans, Long surplusBeans, Date nowTime, Long expireTime) {
        // 付费用户ID
        Long billUserId = netCall.getBillUserId();
        // 扣除金币
        if(beans != 0){
            BeansConsumeDTO beansConsumeInfo = userBusiComponent.addBeans(billUserId, -beans * 1L);
            surplusBeans = beansConsumeInfo.getTotalBeans();
            // 增加预扣费记录
            FChatNetCallFee fee = new FChatNetCallFee();
            fee.setCallId(netCall.getCallId());
            fee.setUserId(billUserId);
            fee.setBeans(beans);
            fee.setPlatformBeans(beansConsumeInfo.getConsumePlatformBeans().intValue());
            fee.setDeductTime(nowTime);
            fChatNetCallFeeMapper.insert(fee);
        }
        // 不付费用户ID
        Long freeUserId = billUserId.equals(netCall.getUserId()) ? netCall.getFriendId() : netCall.getUserId();
        // 加入余额提醒队列(如果需要)
        putToBalanceRemindQueue(netCall.getCallId(), billUserId, freeUserId, surplusBeans, nowTime.getTime(), expireTime, netCall.getBeans());
    }

    /**
     * 付费用户券失效提醒操作
     *
     * @param netCall
     */
    private void sendBillUserInvalidFreeTime(FChatNetCall netCall) {
        Long billUserId = netCall.getBillUserId();
        Long callId = netCall.getCallId();
        // 不付费用户ID
        Long freeUserId = null;
        if (ClientType.Android == GlobalUtils.clientType()) {
            freeUserId = billUserId.equals(netCall.getUserId()) ? netCall.getFriendId() : netCall.getUserId();
        }
        Map<BusinessDataKey, Object> extMap = new HashMap<>();
        extMap.put(BusinessDataKey.CallId, callId);
        if (null == freeUserId) {
            messageComponent.sendMsgToUserInContext(BusiCodeDefine.NetCallCancel, extMap, billUserId);
        } else {
            messageComponent.sendMsgToUserInContext(BusiCodeDefine.NetCallCancel, extMap, billUserId, freeUserId);
        }
        // 消息toast
        messageComponent.sendCommonToastMsg("通话免费券已失效，拨通失败", billUserId);
    }

    /**
     * 挂断通话
     * <p>
     * 用户主动挂断通话时调用
     *
     * @param chatNetCall
     * @param clientDuration
     * @param reqTime
     */
    public NetCallFinishDTO hangUp(Long hangUpId, FChatNetCall chatNetCall, Integer clientDuration, Long reqTime, NetCallFinishType finishType) {
        return finishCall(hangUpId, chatNetCall, clientDuration, reqTime, finishType, null);
    }

    /**
     * 断开通话
     * <p>
     * 由声网通知而结束的通话
     *
     * @param hangUpId
     * @param chatNetCall
     * @param reqTime
     * @param reason
     * @return
     */
    public NetCallFinishDTO disconnect(Long hangUpId, FChatNetCall chatNetCall, Long reqTime, String reason) {
        return finishCall(hangUpId, chatNetCall, -1, reqTime, NetCallFinishType.Notify, reason);
    }


    /**
     * 被动挂断
     * <p>
     * 例如以下情况
     * 1、余额不足
     * 2、心跳超时
     * 3、通话违规
     *
     * @param callId
     * @param finishType
     * @param disconnectTime
     * @return
     */
    public NetCallFinishDTO savePassiveDisconnect(Long callId, NetCallFinishType finishType, Long disconnectTime) {
        // 第一步先将数据从缓存队列中移除
        removeDisconnectQueue(callId, finishType);

        FChatNetCall chatNetCall = getChatNetCall(callId);
        if (null == chatNetCall || NetCallStatus.Accept != chatNetCall.getStatus()) {
            return null;
        }
        boolean callFree = chatNetCall.getBeans() <= 0;
        NetCallFinishDTO finishDTO;
        if (callFree) {
            finishDTO = disconnect(callId, finishType, disconnectTime);
        } else {
            // 获取付费信息
            NetCallBillDTO billDTO = getBillInfo(callId);
            if (billDTO == null) {
                // 通话已挂断，不做任何处理
                return null;
            }
            finishDTO = disconnect(callId, finishType, disconnectTime);
        }
        // 发送通话中断消息
        sendDisconnectMsg(finishDTO);
        return finishDTO;
    }

    public void addInvitePresentAndIntimate(NetCallFinishDTO finishDTO){
        // 增加邀友分成和亲密度
        addInvitePresentAndIntimate(finishDTO.getUserId(), finishDTO.getFriendId(), finishDTO.getBillUserId(), finishDTO.getTotalBeans(), finishDTO.getPresentCash());
    }

    /**
     * 发送通话中断消息
     *
     * @param finishDTO
     */
    private void sendDisconnectMsg(NetCallFinishDTO finishDTO) {
        Map<BusinessDataKey, Object> extMap = new HashMap<>();
        extMap.put(BusinessDataKey.CallId, finishDTO.getCallId());
        extMap.put(BusinessDataKey.HangUpId, finishDTO.getHangUpId());
        extMap.put(BusinessDataKey.BillUserId, finishDTO.getBillUserId());
        extMap.put(BusinessDataKey.Duration, finishDTO.getDuration());
        extMap.put(BusinessDataKey.TotalBeans, finishDTO.getTotalBeans());
        extMap.put(BusinessDataKey.UseProd, BoolType.valueOf(Objects.nonNull(finishDTO.getDiscountProdId())));
        if (finishDTO.getPresentCash() > 0) {
            extMap.put(BusinessDataKey.PresentCash, BusiUtils.cashToYuanStr(finishDTO.getPresentCash()));
        } else {
            // 免费通话的样式
            String freeText = getResultFreeText(finishDTO.getCallType());
            extMap.put(BusinessDataKey.PresentCash, "0.00");
            extMap.put(BusinessDataKey.DiscountInfo, freeText);
            // 这里无付费用户，默认设置为发起用户，防止无付费用户情况下发起方展示+0.00元
            extMap.put(BusinessDataKey.BillUserId, finishDTO.getUserId());
        }
        extMap.put(BusinessDataKey.FinishType, finishDTO.getFinishType());
        Long[] targetIds = new Long[]{finishDTO.getUserId(), finishDTO.getFriendId()};
        messageComponent.sendMsgToUserInContext(BusiCodeDefine.NetCallDisconnect, extMap, targetIds);
    }


    /**
     * 断开通话
     * <p>
     * 以下情况会调用
     * 1、余额不足
     * 2、心跳超时
     * 3、通话违规
     *
     * @param callId
     * @param finishType
     * @param reqTime
     */
    private NetCallFinishDTO disconnect(Long callId, NetCallFinishType finishType, Long reqTime) {
        FChatNetCall chatNetCall = getChatNetCall(callId);
        return finishCall(GlobalConstant.SYS_USER_ID, chatNetCall, -1, reqTime, finishType, null);
    }

    /**
     * 增加邀友分成和亲密度
     *
     * @param userId
     * @param friendId
     * @param billUserId
     * @param totalBeans
     * @param presentCash
     */
    public void addInvitePresentAndIntimate(Long userId, Long friendId, Long billUserId, Long totalBeans, Long presentCash) {
        // 增加亲密度
        intimateComponent.addIntimateWithBeans(userId, friendId, totalBeans);
    }

    /**
     * 修改通话状态
     *
     * @param callId
     * @param status
     */
    public void updateStatus(Long callId, NetCallStatus status, Long reqTime) {
        updateStatus(callId, status, null, reqTime);
    }

    /**
     * 修改通话信息
     */
    public void updateChatNetCall(FChatNetCall call){
        fChatNetCallMapper.updateByPrimaryKeySelective(call);
    }

    /**
     * 修改通话状态
     *
     * @param callId
     * @param status
     * @param statusRemark
     * @param reqTime
     */
    public void updateStatus(Long callId, NetCallStatus status, String statusRemark, Long reqTime) {
        if (NetCallStatus.Finish == status) {
            throw new ComponentException("此方法不支持该状态修改，状态：{}", status);
        }
        // 清除当前时间的毫秒数，防止通话时长计算错误
        Date nowTime = DateUtils.truncMillis(reqTime);
        FChatNetCall chatNetCall = new FChatNetCall(callId);
        switch (status) {
            case Ring:
                chatNetCall.setRingTime(nowTime);
                break;
            case Accept:
                chatNetCall.setConnectTime(nowTime);
                break;
            default:
        }
        chatNetCall.setStatus(status);
        chatNetCall.setStatusRemark(statusRemark);
        chatNetCall.setStatusTime(nowTime);
        fChatNetCallMapper.updateByPrimaryKeySelective(chatNetCall);
        // 移除待接通用户
        FChatNetCall netCall = getChatNetCall(callId);
        CallbackAfterTransactionUtil.send(() -> {
            removeWaitAcceptUser(netCall.getFriendId());
        });
    }

    /**
     * 获取通话信息
     *
     * @param callId
     * @return
     */
    public FChatNetCall getChatNetCall(Long callId) {
        return fChatNetCallMapper.selectByPrimaryKey(new FChatNetCall(callId));
    }


    /**
     * 获取指定状态的通话详情
     *
     * @param callId
     * @param userId
     * @param statuses
     * @return
     */
    public FChatNetCall getChatNetCallWithStatus(Long callId, Long userId, NetCallStatus... statuses) {
        FChatNetCall chatNetCall = getChatNetCall(callId);
        if (chatNetCall == null) {
            LOG.info("通话信息不存在，通话ID：{}，用户ID：{}", callId, userId);
            return null;
        }
        if (!chatNetCall.getUserId().equals(userId) && !chatNetCall.getFriendId().equals(userId)) {
            LOG.info("没有权限访问当前通话，通话ID：{}，用户ID：{}", callId, userId);
            return null;
        }
        if (statuses == null || statuses.length == 0) {
            return chatNetCall;
        }
        if (!ArrayUtils.contains(statuses, chatNetCall.getStatus())) {
            LOG.info("通话状态错误，当前状态：{}，通话ID：{}，用户ID：{}", chatNetCall.getStatus(), callId, userId);
            return null;
        }
        return chatNetCall;
    }

    /**
     * 获取待扣费的通话队列
     *
     * @param jobShardIndex
     * @param jobShardTotal
     * @return
     */
    public Set<RedisShardDTO> getWaitDeductQueue(int jobShardIndex, int jobShardTotal) {
        List<Integer> shardIndexes = RedisShardUtils.getSharedIndexes(jobShardIndex, jobShardTotal, RedisShardType.NetCallDeductQueue);
        if (ListUtils.isEmpty(shardIndexes)) {
            return null;
        }
        long nowUnixTime = DateUtils.nowUnixTime();
        Set<RedisShardDTO> values = new HashSet<>();
        for (int shardIndex : shardIndexes) {
            RedisKey redisKey = buildNetCallDeductQueueKey(shardIndex);
            List<String> members = socialRedisTemplate.zrangeByScore(redisKey, 0, nowUnixTime);
            if (ListUtils.isNotEmpty(members)) {
                values.add(RedisShardDTO.build(shardIndex, members));
            }
        }
        return values;
    }

    /**
     * 将通话加入扣费队列
     * <p>
     * 通话开始时调用
     *
     * @param chatNetCall 通话ID
     * @param startTime   通话开始时间
     * @param nextTime   下一次预扣时间
     */
    public void putToDeductQueue(FChatNetCall chatNetCall, Long startTime, Long nextTime, Long expireTime) {
        // 事务提交之后再加入扣费队列
        CallbackAfterTransactionUtil.send(() -> {
            int shardIndex = RedisShardUtils.getShardIndex(chatNetCall.getCallId(), RedisShardType.NetCallDeductQueue);
            // 下一次预扣时间
            RedisKey redisKey = buildNetCallDeductQueueKey(shardIndex);
            socialRedisTemplate.zadd(redisKey, nextTime * 1D, String.valueOf(chatNetCall.getCallId()));

            Map<String, String> callInfoMap = new HashMap<>();
            callInfoMap.put(FieldConstant.CALL_TYPE, chatNetCall.getCallType().name());
            callInfoMap.put(FieldConstant.BILL_USER_ID, String.valueOf(chatNetCall.getBillUserId()));
            callInfoMap.put(FieldConstant.START_TIME, String.valueOf(startTime));
            callInfoMap.put(FieldConstant.BEANS, String.valueOf(chatNetCall.getBeans()));
            callInfoMap.put(FieldConstant.NEXT_TIME, String.valueOf(nextTime));
            callInfoMap.put(FieldConstant.EXPIRE_TIME, String.valueOf(expireTime));
            redisKey = buildNetCallDeductInfoKey(chatNetCall.getCallId());
            socialRedisTemplate.hmset(redisKey, callInfoMap);
            socialRedisTemplate.expire(redisKey);
        });
    }

    /**
     * 更新下一次预扣时间
     *
     * @param shardIndex
     * @param callId
     * @return
     */
    public void updateDeductTime(int shardIndex, Long callId, Long nextTime, Long expireTime) {
        CallbackAfterTransactionUtil.send(() -> {
            RedisKey redisKey = buildNetCallDeductInfoKey(callId);
            Map<String, String> map = new HashMap<>();
            map.put(FieldConstant.NEXT_TIME, String.valueOf(nextTime)); // 下一次扣费时间
            map.put(FieldConstant.EXPIRE_TIME, String.valueOf(expireTime)); // 通话截止时间
            socialRedisTemplate.hmset(redisKey, map);
            socialRedisTemplate.expire(redisKey);
            // 设置下一次预扣时间
            socialRedisTemplate.zadd(buildNetCallDeductQueueKey(shardIndex), nextTime * 1D, String.valueOf(callId));
        });
    }

    /**
     * 将通话从预扣队列中移除
     *
     * @param callId
     */
    public void removeDeductQueue(Long callId) {
        int shardIndex = RedisShardUtils.getShardIndex(callId, RedisShardType.NetCallDeductQueue);
        CallbackAfterTransactionUtil.send(() -> {
            RedisKey redisKey = buildNetCallDeductQueueKey(shardIndex);
            socialRedisTemplate.zrem(redisKey, String.valueOf(callId));

            redisKey = buildNetCallDeductInfoKey(callId);
            socialRedisTemplate.del(redisKey);
        });
    }

    /**
     * 将通话从预扣费队列中移除
     * 备注：这里只从预扣费队列中删除 不去删除通话信息
     */
    public void onlyRemoveDeductQueue(Long callId) {
        int shardIndex = RedisShardUtils.getShardIndex(callId, RedisShardType.NetCallDeductQueue);
        CallbackAfterTransactionUtil.send(() -> {
            RedisKey redisKey = buildNetCallDeductQueueKey(shardIndex);
            socialRedisTemplate.zrem(redisKey, String.valueOf(callId));
        });
    }

    /**
     * 获取通话付费信息
     *
     * @param callId
     * @return
     */
    public NetCallBillDTO getBillInfo(Long callId) {
        RedisKey redisKey = buildNetCallDeductInfoKey(callId);
        List<String> values = socialRedisTemplate.hmget(redisKey, FieldConstant.CALL_TYPE, FieldConstant.START_TIME, FieldConstant.BILL_USER_ID, FieldConstant.BEANS, FieldConstant.NEXT_TIME, FieldConstant.EXPIRE_TIME);
        return NetCallBillDTO.create(callId, values);
    }

    /**
     * 加入强制结束队列
     *
     * @param callId
     * @param finishType
     */
    public void putToDisconnectQueue(Long callId, NetCallFinishType finishType) {
        putToDisconnectQueue(callId, finishType, DateUtils.nowUnixTime());
    }

    /**
     * 加入强制结束队列
     *
     * @param callId
     * @param finishType
     * @param nowUnixTime
     */
    public void putToDisconnectQueue(Long callId, NetCallFinishType finishType, long nowUnixTime) {
        double finalExpTime = nowUnixTime * 1D;
        CallbackAfterTransactionUtil.send(() -> {
            RedisKey redisKey = buildNetCallDisconnectQueueKey();
            socialRedisTemplate.zadd(redisKey, finalExpTime, NetCallDisconnectDTO.create(callId, finishType).toKey());
        });
    }

    /**
     * 获取待强制结束的队列
     *
     * @return
     */
    public List<Tuple> getWaitDisconnectQueue() {
        RedisKey redisKey = buildNetCallDisconnectQueueKey();
        return socialRedisTemplate.zrangeByScoreWithScores(redisKey, 0, DateUtils.nowUnixTime());
    }

    /**
     * 判断是否在强制结束队列中
     *
     * @param callId
     * @param finishType
     * @return
     */
    public boolean isInDisconnectQueue(Long callId, NetCallFinishType finishType) {
        RedisKey redisKey = buildNetCallDisconnectQueueKey();
        return socialRedisTemplate.zscore(redisKey, NetCallDisconnectDTO.create(callId, finishType).toKey()) != null;
    }

    /**
     * 将通话从待强制结束队列中移除
     *
     * @param callId
     */
    public void removeDisconnectQueue(Long callId, NetCallFinishType finishType) {
        CallbackAfterTransactionUtil.send(() -> {
            RedisKey redisKey = buildNetCallDisconnectQueueKey();
            socialRedisTemplate.zrem(redisKey, NetCallDisconnectDTO.create(callId, finishType).toKey());
        });
    }

    /**
     * 获取待返还预扣费用队列
     *
     * @return
     */
    public List<String> getWaitReturnBeansQueue() {
        RedisKey redisKey = buildNetCallReturnBeansQueueKey();
        return socialRedisTemplate.zrangeByScore(redisKey, 0, System.currentTimeMillis());
    }

    /**
     * 移除待返还预扣队列
     *
     * @param member
     */
    public boolean removeReturnBeansQueue(String member) {
        return socialRedisTemplate.zrem(buildNetCallReturnBeansQueueKey(), member) > 0;
    }

    /**
     * 判断用户是否在通话中
     *
     * @param userId
     * @return
     */
    public boolean isInCall(Long userId) {
        return getCallId(userId) != null;
    }

    /**
     * 根据用户ID获取正在通话的ID
     *
     * @param userId
     * @return
     */
    public Long getCallId(Long userId) {
        RedisKey redisKey = buildNetCallUserInfoKey(userId);
        return busiRedisTemplate.getLong(redisKey, false);
    }

    /**
     * 维护通话用户心跳
     * <p>
     * 用户心跳接口调用
     *
     * @param userId
     */
    public void processUserAlive(Long userId) {
        Integer shardIndex = RedisShardUtils.getShardIndex(userId, RedisShardType.NetCallTimeoutQueue);
        RedisKey redisKey = buildNetCallTimeoutQueueKey(shardIndex);
        String member = String.valueOf(userId);
        // 如果是用户心跳，需判断缓存是否存在
        if (socialRedisTemplate.zscore(redisKey, member) == null) {
            return;
        }
        socialRedisTemplate.zadd(redisKey, System.currentTimeMillis() * 1D, member);
        RedisKey userKey = buildNetCallUserInfoKey(userId);
        // 用户心跳，延长缓存有效期
        busiRedisTemplate.expire(userKey);
    }

    /**
     * 加入待超时队列
     * <p>
     * 通话接受或者用户心跳时更新
     *
     * @param userId
     * @param callId
     */
    public void putToTimeoutQueue(Long userId, Long callId) {
        Integer shardIndex = RedisShardUtils.getShardIndex(userId, RedisShardType.NetCallTimeoutQueue);
        RedisKey redisKey = buildNetCallTimeoutQueueKey(shardIndex);
        CallbackAfterTransactionUtil.send(() -> {
            socialRedisTemplate.zadd(redisKey, System.currentTimeMillis() * 1D, String.valueOf(userId));
            RedisKey userKey = buildNetCallUserInfoKey(userId);
            // 接受通话，设置通话ID
            busiRedisTemplate.set(userKey, String.valueOf(callId));
        });
    }

    /**
     * 获取待超时队列
     *
     * @param jobShardIndex
     * @param jobShardTotal
     * @return
     */
    public Set<RedisShardDTO> getWaitTimeoutQueue(int jobShardIndex, int jobShardTotal) {
        List<Integer> shardIndexes = RedisShardUtils.getSharedIndexes(jobShardIndex, jobShardTotal, RedisShardType.NetCallTimeoutQueue);
        if (ListUtils.isEmpty(shardIndexes)) {
            return null;
        }
        long expTime = System.currentTimeMillis() - TIME_OUT;
        Set<RedisShardDTO> values = new HashSet<>();
        for (int shardIndex : shardIndexes) {
            RedisKey redisKey = buildNetCallTimeoutQueueKey(shardIndex);
            List<String> members = socialRedisTemplate.zrangeByScore(redisKey, 0, expTime);
            if (ListUtils.isNotEmpty(members)) {
                values.add(RedisShardDTO.build(shardIndex, members));
            }
        }
        return values;
    }

    /**
     * 将用户从超时队列中移除
     *
     * @param userId
     */
    public void removeTimeoutQueue(Long userId) {
        Integer shardIndex = RedisShardUtils.getShardIndex(userId, RedisShardType.NetCallTimeoutQueue);
        CallbackAfterTransactionUtil.send(() -> {
            RedisKey redisKey = buildNetCallTimeoutQueueKey(shardIndex);
            socialRedisTemplate.zrem(redisKey, String.valueOf(userId));

            redisKey = buildNetCallUserInfoKey(userId);
            busiRedisTemplate.del(redisKey);
        });
    }

    /**
     * 将通话ID加入待剔除频道队列中
     * <p>
     * 放入队列中的目的，是确保剔出频道能调成功
     *
     * @param channelId
     */
    public void putToKickChannelQueue(String channelId) {
        CallbackAfterTransactionUtil.send(() -> {
            RedisKey redisKey = buildNetCallKickChannelQueueKey();
            socialRedisTemplate.zadd(redisKey, System.currentTimeMillis() * 1D, channelId);
        });
    }

    /**
     * 获取等待剔除频道的队列
     *
     * @return
     */
    public List<String> getWaitKickChannelQueue() {
        RedisKey redisKey = buildNetCallKickChannelQueueKey();
        return socialRedisTemplate.zrangeByScore(redisKey, 0, System.currentTimeMillis());
    }

    /**
     * 将通话ID从待剔除频道队列中移除
     *
     * @param channelId
     */
    public void removeKickChannelQueue(String channelId) {
        CallbackAfterTransactionUtil.send(() -> {
            RedisKey redisKey = buildNetCallKickChannelQueueKey();
            socialRedisTemplate.zrem(redisKey, channelId);
        });
    }

    /**
     * 加入余额提醒队列
     *
     * @param callId       通话ID
     * @param billUserId   付费用户ID
     * @param freeUserId   免费用户ID
     * @param surplusBeans 所剩余额
     * @param nowTime      当前时间
     * @param expireTime  此次扣款之后、允许通话时间
     * @param beans        通话单价
     *
     * 备注：该方法是扣款之后进行调用的
     */
    public void putToBalanceRemindQueue(Long callId, Long billUserId, Long freeUserId, Long surplusBeans, Long nowTime, Long expireTime, Integer beans) {
        // 此次扣款之后、付费方剩余金币还能通话的分钟数
        long minutes = beans <= 0 ? Long.MAX_VALUE : surplusBeans / beans;
        if (minutes >= BALANCE_REMIND_LESS_MINUTE) {
            // 如果还能通话的分钟数 大于等于 2分钟，则返回
            return;
        }
        // 通话结束时间
        long endTime = (expireTime + minutes * 60) * 1000;
        NetCallRemindDTO dto = NetCallRemindDTO.create(billUserId, endTime, freeUserId);
        // 备注：存储信息一定要在 放入集合的前面
        CallbackAfterTransactionUtil.send(() -> {
            // 储存信息
            RedisKey redisKey = buildNetCallBalanceRemindInfoKey(callId);
            socialRedisTemplate.set(redisKey, JsonUtils.seriazileAsString(dto));
            // 放入待提醒集合
            redisKey = buildNetCallBalanceRemindQueueKey();
            socialRedisTemplate.zadd(redisKey, (double)nowTime, String.valueOf(callId));
        });
    }

    /**
     * 获取等待余额提醒的队列
     *
     * @return
     */
    public List<String> getWaitBalanceRemindQueue() {
        RedisKey redisKey = buildNetCallBalanceRemindQueueKey();
        return socialRedisTemplate.zrangeByScore(redisKey, 0, System.currentTimeMillis());
    }

    /**
     * 移除余额提醒队列
     *
     * @param callId
     */
    public void removeBalanceRemindQueue(String callId) {
        RedisKey redisKey = buildNetCallBalanceRemindQueueKey();
        socialRedisTemplate.zrem(redisKey, callId);

        redisKey = buildNetCallBalanceRemindInfoKey(callId);
        socialRedisTemplate.del(redisKey);
    }

    /**
     * 加入录制等待队列
     */
    public void putToRecordingQueue(String channelId) {
        CallbackAfterTransactionUtil.send(() -> {
            RedisKey redisKey = buildNetCallRecordingQueueKey();
            socialRedisTemplate.sadd(redisKey, channelId);
        });
    }

    /**
     * 加入录制结束队列
     */
    public void putToRecordingEndQueue(String channelId) {
        RedisKey redisKey = buildNetCallRecordingEndQueueKey();
        socialRedisTemplate.sadd(redisKey, channelId);
    }

    /**
     * 获取等待音频录制的通话频道队列
     */
    public Set<String> getWaitRecordingQueue() {
        return socialRedisTemplate.spops(buildNetCallRecordingQueueKey());
    }

    /**
     * 获取等待结束语音通话录制的队列
     */
    public Set<String> getWaitRecordingEndQueue() {
        return socialRedisTemplate.spops(buildNetCallRecordingEndQueueKey());
    }

    /**
     * 获取余额提醒信息
     *
     * @param callId
     * @return
     */
    public NetCallRemindDTO getBalanceRemindInfo(String callId) {
        RedisKey redisKey = buildNetCallBalanceRemindInfoKey(callId);
        return socialRedisTemplate.getObject(redisKey, NetCallRemindDTO.class);
    }

    /**
     * 结束通话
     *
     * @param hangUpId
     * @param call
     * @param clientDuration
     * @param reqTime
     * @param finishType
     */
    private NetCallFinishDTO finishCall(Long hangUpId, FChatNetCall call, Integer clientDuration, Long reqTime, NetCallFinishType finishType, String statusRemark) {
        Date nowTime = DateUtils.truncMillis(reqTime);
        Long callId = call.getCallId();
        String popContent = null;
        // 付费用户
        Long billUserId = call.getBillUserId();

        // 计算实际通话时长
        int duration = (int) ((reqTime / 1000 - call.getConnectTime().getTime() / 1000));
        // 通话总金币
        Integer durationBean = getPayTotalBeans(billUserId, duration, call.getBeans(), call.getProdId());
        // 预扣总金币
        NetCallFeeDTO preBeansInfo = getPreTotalBeans(billUserId, callId);
        // 实扣总金币
        Long actTotalBeans = Math.min(durationBean, preBeansInfo.getBeans());
        // 如果此通电话是因为无人脸挂断的 则不扣费
        if(NetCallFinishType.NoFace == finishType){
            popContent = actTotalBeans <= 0 ? null : MsgUtils.format("您结束了本次视频，消耗的{}金币已退回", actTotalBeans);
            actTotalBeans = 0L;
        }
        // 计算 实际消耗、实际退回
        CallBeanConsumeDTO dto = calculateCallBeanConsume(actTotalBeans, preBeansInfo);

        ChatConsumeDTO consumeDTO = buildGoodsConsumeDTO(call.getCallType(), call.getUserId(), call.getFriendId(), billUserId, dto.getConsumeTotalBean(), dto.getConsumePlatformBean(), call.getProdId(), call.getUseBagCount(), nowTime);

        // 将状态修改为“已结束”
        updateStatusToFinish(callId, hangUpId, duration, clientDuration, actTotalBeans.intValue(), preBeansInfo.getBeans().intValue(), actTotalBeans, consumeDTO.getTargetPresentCash(), finishType, statusRemark, nowTime);

        // 增加消费记录
        String relationId = BusiUtils.generateRelationId(call.getUserId(), call.getFriendId());
        chatManager.addChatConsumeLog(consumeDTO, relationId);
        // 如果是付费通话，返回多扣的金币，并移除相关缓存数据
        if (billUserId != null) {
            // 异步返还多扣的金币
            // 判断退回的充值金币和赠送金币各多少
            returnPreBeans(billUserId, callId, dto.getReturnTotalBean().intValue(), dto.getReturnRechargeBean().intValue(), dto.getReturnPlatformBean().intValue());
            // 将通话从预扣队列中移除
            removeDeductQueue(callId);
            // 增加待提成记录表
            addNetCallPresent(call, consumeDTO, nowTime);
        }
        // 将主叫和被叫从超时队列中移除
        removeTimeoutQueue(call.getUserId());
        removeTimeoutQueue(call.getFriendId());
        // 将通话从强制结束队列中移除
        removeDisconnectQueue(callId, finishType);

        // 通话结束后，调用声网将通话双方踢出频道
        putToKickChannelQueue(AgoraSdkUtils.buildNetCallChannelId(callId));
        // 结束录制
        this.putToRecordingEndQueue(AgoraSdkUtils.buildNetCallChannelId(callId));
        // 通话记录缓存
        call.setDuration(duration);
        this.saveCallListCache(call);
        // 增加访客相关互动标记
        userVisitManager.saveInteractSign(BusiUtils.generateRelationId(call.getUserId(), call.getFriendId()));
        return NetCallFinishDTO.create(call, hangUpId, billUserId, duration, actTotalBeans, consumeDTO.getPlatformBeans(), consumeDTO.getTargetPresentCash(), finishType, popContent);
    }

    /**
     * 计算 通话具体消耗、需要回退的具体数量
     */
    private CallBeanConsumeDTO calculateCallBeanConsume(Long actTotalBeans, NetCallFeeDTO preBeansInfo){
        // 因为实际消耗金币 是取的最小值、所以只存在预扣总金币大于等于实际消耗金币的可能
        if(actTotalBeans.equals(preBeansInfo.getBeans())){
            return CallBeanConsumeDTO.build(preBeansInfo.getBeans(), preBeansInfo.getBeans() - preBeansInfo.getPlatformBeans(), preBeansInfo.getPlatformBeans(), 0L, 0L, 0L);
        }
        // 因为我们是优先使用的 充值金币 所以我们要优先退赠送金币
        Long returnTotalBean = preBeansInfo.getBeans() - actTotalBeans;
        Long returnRechargeBean = 0L;
        Long returnPlatformBean = 0L;
        Long consumeRechargeBean = preBeansInfo.getBeans() - preBeansInfo.getPlatformBeans();
        Long consumePlatformBean = preBeansInfo.getPlatformBeans();
        if(preBeansInfo.getPlatformBeans() >= returnTotalBean){
            // 全部返回平台金币
            returnPlatformBean = returnTotalBean;
            consumePlatformBean = preBeansInfo.getPlatformBeans() - returnTotalBean;
        }else if(preBeansInfo.getPlatformBeans() > 0){
            // 返回部分平台金币 + 部分充值金币
            returnPlatformBean = preBeansInfo.getPlatformBeans();
            returnRechargeBean = returnTotalBean - returnPlatformBean;

            consumePlatformBean = consumePlatformBean - returnPlatformBean;
            consumeRechargeBean = consumeRechargeBean - returnRechargeBean;
        }else{
            // 全部返回充值金币
            returnRechargeBean = returnTotalBean;
            consumeRechargeBean = consumeRechargeBean - returnTotalBean;
        }
        return CallBeanConsumeDTO.build(actTotalBeans, consumeRechargeBean, consumePlatformBean, returnTotalBean, returnRechargeBean, returnPlatformBean);
    }

    /**
     * 通话记录缓存
     *
     * @param call
     */
    public void saveCallListCache(FChatNetCall call) {
        // 如果接听方是匹配替代用户 我们不插入记录
        if(AppConfig.VIDEO_MATCH_RECEIVER.equals(call.getFriendId())){
            return;
        }
        saveCallListCache(call.getUserId(), call.getFriendId(), call.getDuration(), call.getCallType(), NetCallSourceType.Chat, call.getCallTime());
    }


    /**
     * 获取通话记录
     *
     * @param userId
     * @param offset
     * @param limit
     * @return
     */
    public List<NetCallLogDTO> getCallLogList(Long userId, Integer offset, Integer limit) {
        UUserBasic selfBasic = userInfoManager.getUserBasic(userId);
        List<NetCallLogDTO> logList = new ArrayList<>();
        RedisKey callLogKey = buildUserNetCallLogRecentNewKey(userId);
        List<Tuple> tuples = socialRedisTemplate.zrevrangeWithScores(callLogKey, offset, (offset + limit) - 1);
        for (Tuple tuple : tuples) {
            Long friendId = Long.valueOf(tuple.getElement());
            RedisKey recentKey = buildUserNetCallLogNewKey(userId, friendId);
            String recentJson = socialRedisTemplate.get(recentKey);
            if (StringUtils.isEmpty(recentJson)) {
                socialRedisTemplate.zrem(callLogKey, tuple.getElement());
            }
            // 好友信息
            UUserBasic friendBasic = userInfoManager.getUserBasic(friendId);
            String friendNotes = friendRelationComponent.getFriendNotes(userId, friendId);
            UserBusiDTO busiDTO = GlobalDataUtils.busi(friendId);
            // 亲密度
            Integer intimateLevel = 0;
            RelationDTO relation = friendRelationManager.getRelation(userId, friendId);
            if (null != relation) {
                intimateLevel = relation.getIntimateLevel();
            }
            NetCallFriendLogDTO friendDTO = JsonUtils.deserializeAsObject(recentJson, NetCallFriendLogDTO.class);
            // 有情提示
            String tip = buildTip(selfBasic, friendBasic);
            // 好友在线状态
            OnlineStatus friendOnlineStatus = userInfoManager.getOnlineStatus(friendId, friendBasic.getSex());
            logList.add(new NetCallLogDTO(friendDTO, friendBasic, busiDTO, intimateLevel,
                    (long) tuple.getScore(), tip, friendOnlineStatus, friendNotes));
        }
        return logList;
    }

    /**
     * 删除通话记录
     *
     * @param userId
     * @param friendId
     */
    public void deleteCallLogList(Long userId, Long friendId) {
        RedisKey callLogKey = buildUserNetCallLogRecentNewKey(userId);
        socialRedisTemplate.zrem(callLogKey, friendId.toString());
        RedisKey recentKey = buildUserNetCallLogNewKey(userId, friendId);
        socialRedisTemplate.del(recentKey);
    }

    /**
     * 通话记录缓存
     *
     * @param userId     主叫用户
     * @param friendId   被叫用户
     * @param duration
     * @param callType
     * @param createTime 通话创建时间
     */
    public void saveCallListCache(Long userId, Long friendId, Integer duration, NetCallType callType, NetCallSourceType sourceType, Date createTime) {
        Long time = createTime.getTime();
        // 来源类型为空,则标记为普通处理
        if (null == sourceType) {
            sourceType = NetCallSourceType.Chat;
        }
        switch (sourceType) {
            case Invite:
                // 主叫 = 被邀请人; 被叫 = 邀请人，此时邀请人标记为 发起人
                NetCallLogType inviteCallLogType = NetCallType.Video == callType ? NetCallLogType.InviteVideo : NetCallLogType.InviteVoice;
                saveUserCallLogCache(userId, friendId, duration, callType, inviteCallLogType, time, BoolType.True);
                saveUserCallLogCache(friendId, userId, duration, callType, inviteCallLogType, time, BoolType.False);
                // 被邀请人标记红点
                saveNetCallLogMark(userId);
                break;
            case VideoMate:
                // 主叫 = 男性用户; 被叫 = 女性用户，此时被叫记为 发起人
                saveUserCallLogCache(userId, friendId, duration, callType, NetCallLogType.MatchVideo, time, BoolType.False);
                // 男性用户 = 主叫，标记红点
                saveNetCallLogMark(userId);
                break;
            // 其他主动发起通话处理
            default:
                // 主叫记录类型
                NetCallLogType callLogType = NetCallType.Video == callType ? NetCallLogType.CallVideo : NetCallLogType.CallVoice;
                // 被叫记录类型
                NetCallLogType beCallLogType = NetCallType.Video == callType ? NetCallLogType.BeCallVideo : NetCallLogType.BeCallVoice;
                saveUserCallLogCache(userId, friendId, duration, callType, callLogType, time, BoolType.True);
                saveUserCallLogCache(friendId, userId, duration, callType, beCallLogType, time, BoolType.False);
                break;
        }

    }

    /**
     * 封装单独用户通话记录缓存处理
     *
     * @param userId      用户ID
     * @param friendId    好友ID
     * @param duration    通话时长
     * @param callType
     * @param callLogType 记录类型
     * @param time
     * @param hasCaller   是否记录为发起者
     */
    private void saveUserCallLogCache(Long userId, Long friendId, Integer duration, NetCallType callType, NetCallLogType callLogType, Long time, BoolType hasCaller) {
        try {
            if (null == callLogType) {
                return;
            }
            // 新的数据写入新的实例
            RedisKey callLogKey = buildUserNetCallLogRecentNewKey(userId);
            RedisKey recentKey = buildUserNetCallLogNewKey(userId, friendId);
            String recentJson = JsonUtils.seriazileAsString(new NetCallFriendLogDTO(callType, callLogType, duration, hasCaller));
            socialRedisTemplate.set(recentKey, recentJson);

            socialRedisTemplate.zadd(callLogKey, (double) time, friendId.toString());
            socialRedisTemplate.expire(callLogKey);
            // 获取100条之后
            List<Tuple> tuples = socialRedisTemplate.zrevrangeWithScores(callLogKey, 101, -1);
            // 删除对应信息缓存
            for (Tuple tuple : tuples) {
                Long friend = Long.valueOf(tuple.getElement());
                socialRedisTemplate.del(buildUserNetCallLogNewKey(userId, friend));
            }
            if (ListUtils.isNotEmpty(tuples)) {
                List<String> friendIs = tuples.stream().map(Tuple::getElement).collect(Collectors.toList());
                // 截取保留前100名
                socialRedisTemplate.zrem(callLogKey, ListUtils.toStringArray(friendIs));
            }
        } catch (Exception e) {
            LOG.error("保存用户通话记录缓存错误,userId:{},friendId:{},callLogType:{}", userId, friendId, callLogType);
        }
    }

    /**
     * 将通话状态改成“已结束”
     *
     * @param callId
     * @param hangUpId
     * @param duration
     * @param clientDuration
     * @param totalBeans
     * @param preTotalBeans
     * @param actTotalBeans
     * @param presentCash
     * @param finishType
     * @param statusRemark
     * @param nowTime
     */
    private void updateStatusToFinish(Long callId, Long hangUpId, Integer duration, Integer clientDuration, Integer totalBeans, Integer preTotalBeans, Long actTotalBeans, Long presentCash, NetCallFinishType finishType, String statusRemark, Date nowTime) {
        FChatNetCall netCall = new FChatNetCall(callId);
        netCall.setFinishTime(nowTime);
        netCall.setFinishType(finishType);
        netCall.setHangUpId(hangUpId);
        netCall.setDuration(duration);
        netCall.setClientDuration(clientDuration);
        netCall.setTotalBeans(totalBeans);
        netCall.setPreTotalBeans(preTotalBeans);
        netCall.setActTotalBeans(actTotalBeans.intValue());
        netCall.setPresentCash(presentCash.intValue());
        netCall.setStatus(NetCallStatus.Finish);
        netCall.setStatusTime(nowTime);
        netCall.setStatusRemark(statusRemark);
        // 系统根据结束状态自动审核
        fChatNetCallMapper.updateByPrimaryKeySelective(netCall);
    }

    /**
     * 获取通话总费用
     *
     * @param beans 消耗总金币
     * @return
     *
     */
    private Integer getPayTotalBeans(Long billUserId, Integer duration, Integer beans, Integer prodId) {
        // 通话免费或者通话时长小于10秒不收费《如果用户使用免费视频券、则把券回收掉》
        if (billUserId == null) { //  || duration < 10
            return 0;
        }
        //  如果使用了【免费视频券】则把计算金币的秒数 扣减掉
        if (Objects.nonNull(prodId)) {
            TProdSocialGoods goods = socialGoodsMapper.selectByPrimaryKey(new TProdSocialGoods(prodId));
            if(goods.getGoodsType() == ProdGoodsType.SocialVoiceFreeTicket || goods.getGoodsType() == ProdGoodsType.SocialVideoFreeTicket){
                duration = Math.max(0, duration - JsonUtils.toSimpleMap(goods.getExtJsonCfg()).getIntValue("second"));
            }
        }
        int minutes;
        if (duration % 60 == 0) {
            minutes = duration / 60;
        } else {
            minutes = duration / 60 + 1;
        }
        return Math.max(0, minutes * beans);
    }

    /**
     * 获取通话预扣总金币
     *
     * @param billUserId
     * @param callId
     * @return
     */
    private NetCallFeeDTO getPreTotalBeans(Long billUserId, Long callId) {
        if (billUserId == null) {
            return new NetCallFeeDTO(0L, 0L);
        }
        return fChatNetCallFeeMapper.queryPreTotalBeans(callId);
    }


    /**
     * 开始语音通话录制
     *
     * @param callId
     * @param userId
     * @param friendId
     */
    public void startRecording(Long callId, AudioType audioType, String channelId, Long userId, Long friendId, NetCallType callType) {

        if (callType == NetCallType.Video) {
            audioType = AudioType.Video;
        }
        String[] subscribeAudioUids = new String[]{userId.toString(), friendId.toString()};
        Long uid = generateCloudRecordUid(subscribeAudioUids);

        // 获取录制资源
        String resourceId = AgoraSdkUtils.getResourceId(channelId, uid);
        if (StringUtils.isEmpty(resourceId)) {
            return;
        }

        // 开始录制
        String sid = AgoraSdkUtils.startCloudRecording(channelId, uid, resourceId, subscribeAudioUids, callType);
        if (StringUtils.isEmpty(sid)) {
            CAudioRecord record = new CAudioRecord();
            record.setCallId(callId);
            record.setRecordStatus(BoolType.False);
            record.setCreateTime(DateUtils.nowTime());
            record.setAudioType(audioType);
            logComponent.saveAudioRecord(record);
            return;
        }
        // 缓存云录制信息
        NetCallCloudRecordingDTO dto = new NetCallCloudRecordingDTO(channelId, uid, resourceId, sid, audioType);
        socialRedisTemplate.hset(buildNetCallRecordingInfoKey(), channelId, JsonUtils.seriazileAsString(dto));
    }

    /**
     * 是否特权通话免费
     *
     * @param userId
     * @param friendId
     * @param callType
     * @return
     */
    public boolean privilegeNetCallFreeByType(Long userId, Long friendId, NetCallType callType) {
        IntimatePrivilege privilege = NetCallType.Video == callType ? IntimatePrivilege.SPMF : IntimatePrivilege.YYMF;
        if (!privilege.isOpen()) {
            return false;
        }
        RelationDTO dto = friendRelationManager.getRelation(userId, friendId);
        if (privilege.getLevel() <= dto.getIntimateLevel()) {
            return true;
        }
        return false;
    }

    /**
     * 特权文本样式
     *
     * @param callType
     * @return
     */
    public String getFreeText(Long userId, Long billUserId, Integer bean, NetCallType callType, Integer prodId) {
        try {
            // 如果没有付费方 或者 没有使用物品 不做任何显示
            if(Objects.isNull(billUserId) || Objects.isNull(prodId)){
                return null;
            }
            // 如果付费方不是接听方不做任何显示
            if(!userId.equals(billUserId)){
                return null;
            }
            // 查询物品
            TProdSocialGoods goods = tProdSocialGoodsMapper.selectByPrimaryKey(new TProdSocialGoods(prodId));
            if(Objects.isNull(goods)){
                return null;
            }
            // 判断使用物品是否是免费券
            if(goods.getGoodsType() == ProdGoodsType.SocialVideoFreeTicket || goods.getGoodsType() == ProdGoodsType.SocialVoiceFreeTicket){
                Map<String, Object> map = JsonUtils.toJsonMap(goods.getExtJsonCfg());
                return MsgUtils.format("{}金币/分钟(前{}分钟免费)", bean, map.get("minu"));
            }
            // 判断使用物品是否是折扣券
            if(goods.getGoodsType() == ProdGoodsType.SocialVideoDiscountTicket || goods.getGoodsType() == ProdGoodsType.SocialVoiceDiscountTicket){
                return MsgUtils.format("{}金币/分钟({})", bean, "使用折扣券接听");
            }
            return null;
        }catch (Exception e){
            LOG.error("NetCallComponent-getFreeText-error e:", e);
        }
        return null;
    }

    /**
     * 特权文本样式
     *
     * @param callType
     * @return
     */
    public String getResultFreeText(NetCallType callType) {
        IntimatePrivilege privilege = NetCallType.Video == callType ? IntimatePrivilege.SPMF : IntimatePrivilege.YYMF;
        if (!privilege.isOpen()) {
            return null;
        }
        String audioText = NetCallType.Video == callType ? "视频" : "语音";
        return MsgUtils.format("亲密等级LV.{}{}免费特权", privilege.getLevel(), audioText);
    }

    /**
     * 生成一个云录制频道UID, 不能和频道内的任何一个UID重复
     *
     * @param subscribeAudioUids
     * @return
     */
    private Long generateCloudRecordUid(String[] subscribeAudioUids) {
        for (int i = 0; i < subscribeAudioUids.length; i++) {
            Long uid = seqManager.getCloudRecordingUid();
            if (!ArrayUtils.contains(subscribeAudioUids, uid.toString())) {
                return uid;
            }
        }
        return seqManager.getCloudRecordingUid();
    }

    /**
     * 返还预扣多余的费用
     *
     * @param billUserId
     */
    private void returnPreBeans(Long billUserId, Long callId, Integer returnTotalBeans, Integer returnRechargeBeans, Integer returnPlatformBeans) {
        if (returnTotalBeans <= 0) {
            return;
        }
        // 加入待返还队列
        RedisKey redisKey = buildNetCallReturnBeansQueueKey();
        if (returnPlatformBeans > 0) {
            String memberKey = NetCallReturnBeansDTO.create(callId, billUserId, returnPlatformBeans, BoolType.False).toKey();
            socialRedisTemplate.zadd(redisKey, System.currentTimeMillis() * 1D, memberKey);
        }
        if (returnRechargeBeans > 0) {
            String memberKey = NetCallReturnBeansDTO.create(callId, billUserId, returnRechargeBeans, BoolType.True).toKey();
            socialRedisTemplate.zadd(redisKey, System.currentTimeMillis() * 1D, memberKey);
        }
    }

    /**
     * 构造一个物品消费DTO
     *
     * @param callType
     * @param userId
     * @param friendId
     * @param billUserId
     * @param totalBeans
     * @param nowTime
     * @return
     */
    private ChatConsumeDTO buildGoodsConsumeDTO(NetCallType callType, Long userId, Long friendId, Long billUserId, Long totalBeans, Long platformBeans, Integer prodId, Integer prodCnt, Date nowTime) {
        ChatConsumeDTO dto = new ChatConsumeDTO();
        dto.setTid(GlobalUtils.tid());
        dto.setTotalBeans(totalBeans);
        dto.setPlatformBeans(platformBeans);
        dto.setFromUserId(userId);
        dto.setToUserId(friendId);
        dto.setSourceType(ChatSourceType.Voice);
        dto.setContentType(ChatContentType.Voice);
        if (callType == NetCallType.Video) {
            dto.setSourceType(ChatSourceType.Video);
            dto.setContentType(ChatContentType.Video);
        }
        dto.setFeeUserId(billUserId);
        dto.setProdId(prodId);
        dto.setProdCnt(prodCnt);
        dto.setConsumeTime(nowTime);

        // 计算各方提成
        Long presentUserId = billUserId.equals(userId) ? friendId : userId;
        PresentCashDTO presentDTO = chatConsumeComponent.calcPresentCash(totalBeans, platformBeans, GlobalConstant.RESOURCE_PRESENT_RATE, userInfoManager.getUserBasic(presentUserId), prodId);

        // 直接提成用户
        dto.setTargetPresentUserId(presentDTO.getPresentUserId());
        dto.setTargetPresentCash(presentDTO.getTargetPresentCash());
        dto.setTargetPlatformCash(presentDTO.getTargetPlatformCash());

        // 直接邀请人提成
        dto.setInvitorUserId(presentDTO.getInvitorUserId());
        dto.setInvitorPresentCash(presentDTO.getInvitorPresentCash());
        dto.setInvitorPlatformCash(presentDTO.getInvitorPlatformCash());

        return dto;
    }

    /**
     * 通话记录红点标记
     *
     * @param userId
     */
    public void saveNetCallLogMark(Long userId) {
        RedisKey markKey = buildUserNetCallMark(userId);
        socialRedisTemplate.set(markKey, BoolType.True.name());
        messageComponent.sendMsgToUserInContext(BackCodeDefine.CallMarkChange, null, userId);
    }

    /**
     * 是否有通话红点
     *
     * @param userId
     * @return
     */
    public boolean getNetCallMark(Long userId) {
        return socialRedisTemplate.exists(buildUserNetCallMark(userId));
    }

    /**
     * 每天首次错过电话发送系统消息
     *
     * @param userId
     * @param callId
     * @param nickname
     */
    public void sendDayMissCallMsg(Long userId, Long callId, String nickname) {
        UMessageSetting setting = messageSettingManager.getSetting(userId);
        // 用户开启推送提醒跳过
        if (BoolType.True == setting.getPushEnable()) {
            return;
        }
        RedisKey dayKey = buildUserDayInfo(userId);
        if (socialRedisTemplate.hexists(dayKey, FieldConstant.DAY_FIRST_MISS_CALL_ID)) {
            return;
        }
        socialRedisTemplate.hset(dayKey, FieldConstant.DAY_FIRST_MISS_CALL_ID, callId.toString());
        socialRedisTemplate.expire(dayKey);
        // 每天首次错过电话发送系统消息
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("nickname", nickname);
        noticeComponent.sendSystemNotice(userId, NoticeSysType.MissNetCallVideo, paramMap);
    }

    /**
     * 清除通话记录红点标记
     *
     * @param userId
     */
    public void removeNetCallLogMark(Long userId) {
        RedisKey markKey = buildUserNetCallMark(userId);
        socialRedisTemplate.del(markKey);
    }

    /**
     * 是否有视频免费券
     *
     * @param userId
     * @return
     */
    public boolean hasFreeTimeVideoTicketBagCnt(Long userId) {
        return getFreeTimeVideoTicketBagCnt(userId) > 0;
    }

    /**
     * 获取视频免费券数量
     *
     * @param userId
     * @return
     */
    public Integer getFreeTimeVideoTicketBagCnt(Long userId) {
        return userSocialBagComponent.getGoodsCnt(userId, ProdGoodsType.SocialVideoFreeTicket, SocialGoodsDefine.VideoTicket60.getGoodsId());
    }

    /**
     * 保存通话遮罩信息
     *
     * @param callId
     */
    public void saveNetCallMaskCache(Long callId, Long userId) {
        socialRedisTemplate.sadd(buildNetCallMaskKey(callId), userId.toString());
    }

    /**
     * 当前通话是否处于遮罩
     *
     * @param callId
     */
    public boolean existNetCallMaskCache(Long callId) {
        return socialRedisTemplate.exists(buildNetCallMaskKey(callId));
    }

    /**
     * 获取涉黄用户ID
     *
     * @param callId
     */
    public Set<String> getNetCallMaskUser(Long callId) {
        return socialRedisTemplate.smembers(buildNetCallMaskKey(callId));
    }

    /**
     * 清除通话遮罩信息
     *
     * @param callId
     */
    public void removeNetCallMask(Long callId) {
        RedisKey markKey = buildNetCallMaskKey(callId);
        socialRedisTemplate.del(markKey);
    }

    /**
     * 增加通话提成记录
     *
     * @param call
     * @param dto
     * @param nowTime
     */
    private void addNetCallPresent(FChatNetCall call, ChatConsumeDTO dto, Date nowTime) {
        // 设置扩展参数
        Map<String, Object> extCfg = new HashMap<>();
        if (call.getProdId() != null) {
            TProdSocialGoods goods = tProdSocialGoodsMapper.selectByPrimaryKey(new TProdSocialGoods(call.getProdId()));
            if (goods != null) {
                extCfg.put("discountInfo", goods.getGoodsName());
            }
        }

        // 增加收益用户待提成记录表
        UserCashSourceType sourceType = NetCallType.Voice == call.getCallType() ? UserCashSourceType.PayVoice : UserCashSourceType.PayVideo;
        if(Objects.nonNull(dto.getTargetPresentUserId())){
            ConsumePresentDTO presentDTO = ConsumePresentDTO.builder(sourceType)
                    .setPaidUserId(dto.getFeeUserId())
                    .setConsumeBeans(dto.getTotalBeans())
                    .setConsumePlatformBeans(dto.getPlatformBeans())
                    .setPresentUserId(dto.getTargetPresentUserId())
                    .setPresentCash(dto.getTargetPresentCash())
                    .setPresentPlatformCash(dto.getTargetPlatformCash())
                    .setCreateTime(dto.getConsumeTime());
            consumePresentComponent.addPresent(presentDTO, extCfg);
        }

        // 直接邀请人待提成记录表
        if(Objects.nonNull(dto.getInvitorUserId())){
            sourceType = NetCallType.Voice == call.getCallType() ? UserCashSourceType.IvtPayVoice : UserCashSourceType.IvtPayVideo;
            ConsumePresentDTO presentDTO1 = ConsumePresentDTO.builder(sourceType)
                    .setPaidUserId(dto.getFeeUserId())
                    .setConsumeBeans(dto.getTotalBeans())
                    .setConsumePlatformBeans(dto.getPlatformBeans())
                    .setPresentUserId(dto.getInvitorUserId())
                    .setPresentCash(dto.getInvitorPresentCash())
                    .setPresentPlatformCash(dto.getInvitorPlatformCash())
                    .setCreateTime(dto.getConsumeTime());
            consumePresentComponent.addPresent(presentDTO1, MapUtils.gmap("beInviteUserId", dto.getTargetPresentUserId()));
        }
    }

    /**
     * 构建提示消息
     */
    private String buildTip(UUserBasic selfBasic, UUserBasic friendBasic) {
        Integer friendAge = BusiUtils.getAgeByDate(friendBasic.getBirthDate());
        return MsgUtils.format("[{}]", friendAge);
    }

    private RedisKey buildNetCallDeductQueueKey(int shardIndex) {
        return RedisKey.create(SocialKeyDefine.NetCallDeductQueue, shardIndex);
    }

    private RedisKey buildNetCallDeductInfoKey(Long callId) {
        return RedisKey.create(SocialKeyDefine.NetCallDeductInfo, callId);
    }

    private RedisKey buildNetCallDisconnectQueueKey() {
        return RedisKey.create(SocialKeyDefine.NetCallDisconnectQueue);
    }

    private RedisKey buildNetCallReturnBeansQueueKey() {
        return RedisKey.create(SocialKeyDefine.NetCallReturnBeansQueue);
    }

    private RedisKey buildNetCallTimeoutQueueKey(int shardIndex) {
        return RedisKey.create(SocialKeyDefine.NetCallTimeoutQueue, shardIndex);
    }

    private RedisKey buildNetCallUserInfoKey(Long userId) {
        return RedisKey.create(BusiKeyDefine.NetCallUserInfo, userId);
    }

    private RedisKey buildNetCallKickChannelQueueKey() {
        return RedisKey.create(SocialKeyDefine.NetCallKickChannelQueue);
    }

    private RedisKey buildNetCallBalanceRemindQueueKey() {
        return RedisKey.create(SocialKeyDefine.NetCallBalanceRemindQueue);
    }

    private RedisKey buildNetCallBalanceRemindInfoKey(Object callId) {
        return RedisKey.create(SocialKeyDefine.NetCallBalanceRemindInfo, callId);
    }

    private RedisKey buildNetCallRecordingQueueKey() {
        return RedisKey.create(SocialKeyDefine.NetCallRecordingQueue);
    }

    private RedisKey buildNetCallRecordingEndQueueKey() {
        return RedisKey.create(SocialKeyDefine.NetCallRecordingEndQueue);
    }

    private RedisKey buildNetCallRecordingInfoKey() {
        return RedisKey.create(SocialKeyDefine.NetCallRecordingInfo);
    }

    private RedisKey buildNetCallDiscountInfoKey(Long callId) {
        return RedisKey.create(SocialKeyDefine.NetCallDiscountInfo, callId);
    }

    private RedisKey buildUserNetCallLogNewKey(Long userId, Long friendId) {
        return RedisKey.create(SocialKeyDefine.UserCallFriendRecentLog, userId, friendId);
    }

    private RedisKey buildUserNetCallLogRecentNewKey(Long userId) {
        return RedisKey.create(SocialKeyDefine.UserCallLog, userId);
    }

    private RedisKey buildUserNetCallMark(Long userId) {
        return RedisKey.create(SocialKeyDefine.UserCallMark, userId);
    }

    private RedisKey buildUserDayInfo(Long userId) {
        return RedisKey.create(SocialKeyDefine.UserCallDayInfo, userId);
    }

    private RedisKey buildUserWeekInfo(Long userId) {
        return RedisKey.create(SocialKeyDefine.UserCallWeekInfo, userId);
    }

    private RedisKey buildNetCallMaskKey(Long callId) {
        return RedisKey.create(SocialKeyDefine.NetCallMask, callId);
    }

    private RedisKey buildNetCallWaitAcceptKey() {
        return RedisKey.create(SocialKeyDefine.NetCallWait);
    }


    /**
     * 音视频通话语音违规次数记录
     */
    private RedisKey buildCallAudioIllegalRecord(Long callId) {
        return RedisKey.create(SocialKeyDefine.CallAudioIllegalRecord, callId);
    }

    /**
     * 视频通话画面违规次数记录
     */
    private RedisKey buildVideoCallFrameIllegalRecord(Long callId) {
        return RedisKey.create(SocialKeyDefine.VideoCallFrameIllegalRecord, callId);
    }

    /**
     * 正在通话女用户保存队列
     */
    private RedisKey buildCurrentCallingKey(SexType sexType){
        return RedisKey.create(SocialKeyDefine.CurrentCalling, sexType.name());
    }

    /**
     * 女用户主动拨打记录
     */
    private RedisKey buildFemaleActCallRecordKey(String timeStr, Long femaleUserId){
        return RedisKey.create(SocialKeyDefine.FemaleActCallRecord, timeStr, femaleUserId);
    }

    /**
     * 女用户主动拨打次数
     */
    private RedisKey buildFemaleActCallTimesKey(String dateStr, Long femaleUserId){
        return RedisKey.create(SocialKeyDefine.FemaleActCallTimes, dateStr, femaleUserId);
    }

    /**
     * 新版视频通话画面违规复查队列
     */
    private RedisKey buildVideoCallFrameViolationQueue(){
        return RedisKey.create(SocialKeyDefine.VideoCallFrameViolationQueue);
    }

    /**
     * 构建通话评价缓存次数记录缓存
     */
    private RedisKey buildTodayCallEvaluateTimes(String dateStr){
        return RedisKey.create(SocialKeyDefine.TodayCallEvaluateTimes, dateStr);
    }

    /**
     * 视频通话人脸检测不通过次数记录
     */
    private RedisKey buildVideoCallNoFaceTimesRecord(Long callId){
        return RedisKey.create(SocialKeyDefine.VideoCallNoFaceTimesRecord, callId);
    }

    /**
     * 视频通话画面违规摄像头关掉记录
     */
    private RedisKey buildVideoViolationCameraOffRecord(Long callId, Long userId){
        return RedisKey.create(SocialKeyDefine.VideoViolationCameraOffRecord, callId, userId);
    }

    /**
     * 女用户主动拨打频率限制
     */
    private RedisKey buildFemaleActCallFrequencyLimit(Long userId, Long friendId){
        return RedisKey.create(SocialKeyDefine.FemaleActCallFrequencyLimit, userId, friendId);
    }
}
