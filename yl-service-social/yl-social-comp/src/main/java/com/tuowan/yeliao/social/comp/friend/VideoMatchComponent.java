package com.tuowan.yeliao.social.comp.friend;

import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.comp.user.UserSocialBagComponent;
import com.tuowan.yeliao.commons.config.enums.MiniLockType;
import com.tuowan.yeliao.commons.config.lock.MiniLockTemplate;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.context.GlobalDataUtils;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.constant.FieldConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BackCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.InternalException;
import com.tuowan.yeliao.commons.data.dto.common.ObjectTimeDTO;
import com.tuowan.yeliao.commons.data.dto.user.DiscountDTO;
import com.tuowan.yeliao.commons.data.entity.user.UIdentityAuth;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserLocation;
import com.tuowan.yeliao.commons.data.enums.config.TextUseType;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.enums.social.MatchType;
import com.tuowan.yeliao.commons.data.enums.social.NetCallType;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.manager.config.CommonTextManager;
import com.tuowan.yeliao.commons.data.manager.config.PicManager;
import com.tuowan.yeliao.commons.data.manager.user.ActionStatManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.log.data.entity.CVideoMatchLog;
import com.tuowan.yeliao.log.data.enums.social.VideoMatchStatus;
import com.tuowan.yeliao.social.comp.log.LogComponent;
import com.tuowan.yeliao.social.data.dto.friend.TouchDTO;
import com.tuowan.yeliao.social.data.dto.friend.VideoMatchInfoDTO;
import com.tuowan.yeliao.social.data.dto.friend.VideoMatchScoreDTO;
import com.tuowan.yeliao.social.data.dto.friend.VideoMatchUserDTO;
import com.tuowan.yeliao.social.data.entity.FChatNetCall;
import com.tuowan.yeliao.social.data.enums.friend.NetCallWaiverType;
import com.tuowan.yeliao.social.data.manager.family.FamilyLiveManager;
import com.tuowan.yeliao.social.data.utils.agora.AgoraSdkUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.resps.Tuple;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频匹配组件，注意区分 NetCallMatchComponent
 * 一个池子，多用户循环匹配
 *
 * <AUTHOR>
 * @date 2022/1/6 17:14
 */
@Component
public class VideoMatchComponent {


    private Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private SocialRedisTemplate socialRedisTemplate;
    @Autowired
    private NetCallComponent netCallComponent;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private MiniLockTemplate miniLockTemplate;
    @Autowired
    private FamilyLiveManager familyLiveManager;
    @Autowired
    private CommonTextManager commonTextManager;
    @Autowired
    private UserSocialBagComponent socialBagComponent;
    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private PicManager picManager;
    @Autowired
    private LogComponent logComponent;
    @Autowired
    private FateMatchComponent fateMatchComponent;
    @Autowired
    private ActionStatManager actionStatManager;

    /**
     * 匹配用户
     * 备注：视频速配 我们一次性筛选3个用户
     */
    public List<Long> matchUsers(Long userId, SexType sex, Date now){
        if(SexType.Male != sex){
            throw new InternalException("性别错误！");
        }
        List<String> list = fateMatchComponent.maleMatchFemale(now, userId, 3, MatchType.VM);
        if(ListUtils.isEmpty(list)){
            throw new BusiException(ErrCodeType.VideoMatchUserNotNotEnough);
        }
        String dateStr = DateUtils.toString(now, DatePattern.YMD2);
        CallbackAfterTransactionUtil.send(() -> {
            // 记录女用户【视频速配】曝光次数
            list.forEach(item -> {
                actionStatManager.incrFemaleByType(Long.valueOf(item), 1L, dateStr, FieldConstant.FEMALE_VM_BG_NUM);
            });
        });
        return list.stream().map(Long::valueOf).collect(Collectors.toList());
    }

    /**
     * 记录callId对应的匹配用户
     */
    public void recordMatchUsersForCallId(Long callId, Long userId, List<Long> friendIds){
        CallbackAfterTransactionUtil.send(() -> {
            RedisKey redisKey = buildVideoMatchInfoPlus(callId);
            socialRedisTemplate.hset(redisKey, FieldConstant.VIDEO_MATCH_USER, userId.toString());
            socialRedisTemplate.hset(redisKey, FieldConstant.VIDEO_MATCH_FRIENDS, BusiUtils.collToStr(friendIds, ","));
            socialRedisTemplate.expire(redisKey);
        });
    }

    /**
     * 检查用户是否是匹配用户
     */
    public boolean checkUserIsMatchFriends(Long callId, Long userId){
        Map<String, String> matchInfo = getVideoMatchInfo(callId);
        if(Objects.isNull(matchInfo) || matchInfo.isEmpty()){
            return false;
        }
        List<String> friends = BusiUtils.strToList(matchInfo.get(FieldConstant.VIDEO_MATCH_FRIENDS), ",");
        return friends.contains(userId.toString());
    }

    /**
     * 获取视频速配信息
     */
    public Map<String, String> getVideoMatchInfo(Long callId){
        return socialRedisTemplate.hgetAll(buildVideoMatchInfoPlus(callId));
    }

    /**
     * 是否在视频匹配中
     *
     * @param userId
     * @return
     */
    public boolean inVideoMatch(Long userId) {
        return inVideoMatch(userId, SexType.Female) || inVideoMatch(userId, SexType.Male);
    }

    /**
     * 是否在视频匹配中
     *
     * @param userId
     * @param sexType
     * @return
     */
    public boolean inVideoMatch(Long userId, SexType sexType) {
        Double score = socialRedisTemplate.zscore(buildVideoMatchQueueKey(sexType), userId.toString());
        return null != score && score > 0;
    }

    /**
     * 获取虚拟用户列表
     *
     * @param userId
     * @return
     */
    public List<VideoMatchUserDTO> getFriendList(Long userId, SexType sexType) {
        String userCity = null;
        UUserLocation location = userInfoManager.getUserLocation(userId);
        if (null != location) {
            userCity = location.getCity();
        }
        // 女性查询男性头像库 ,男性查询女性视频匹配专用头像库
        List<String> headPics = SexType.Female == sexType ? picManager.getRandomPics(SexType.Male, 16) : commonTextManager.getRandomText(TextUseType.MatchPic, SexType.Female, 16);
        // 随机城市列表
        List<String> cityList = new ArrayList<>();
        if (StringUtils.isEmpty(userCity)) {
            cityList = commonTextManager.getRandomText(TextUseType.City, headPics.size());
        }
        UUserBasic basic = userInfoManager.getUserBasic(userId);
        Integer age = BusiUtils.getAgeByDate(basic.getBirthDate());
        List<VideoMatchUserDTO> friendList = new ArrayList<>();
        for (String headPic : headPics) {
            String city = userCity;
            if (StringUtils.isEmpty(city)) {
                Random random = new Random();
                int n = random.nextInt(cityList.size());
                city = cityList.get(n);
            }
            Integer randomAge = SexType.Female == sexType ? RandomUtils.getInt(28, 45) : RandomUtils.getInt(25, 35);
            friendList.add(new VideoMatchUserDTO(headPic, randomAge, city));
        }
        return friendList;
    }

    /**
     * 获取实名点击信息
     *
     * @param userId
     * @return
     */
    public TouchDTO getRealNameTouchInfo(Long userId, SexType sexType) {
        boolean realName = userInfoManager.hasRealName(userId);
        if (realName) {
            return null;
        }
        TouchDTO touchDTO = new TouchDTO(null, ClientTouchType.RealName, null);
        String touchText = SexType.Male == sexType ? "实名认证后显示视频画面" : "实名认证后进行视频匹配";
        touchDTO.setTouchText(touchText);
        return touchDTO;
    }

    /**
     * 获取今日剩余次数
     *
     * @param userId
     * @return
     */
    public Long getDaySurplusTimes(Long userId, SexType sexType) {
        Long maxTimes = SexType.Male == sexType ? 3L : 99L;
        return maxTimes - socialRedisTemplate.getLong(buildVideoMatchTimesKey(userId));
    }


    /**
     * 开始匹配校验
     *
     * @param userId
     * @param sexType
     */
    public void checkMatch(Long userId, SexType sexType) {
        // 使用次数
        Long dayTimes = socialRedisTemplate.getLong(buildVideoMatchTimesKey(userId));
        Long maxTimes = SexType.Male == sexType ? 3L : 99L;
        if (dayTimes >= maxTimes) {
            throw new BusiException("今日次数已用完，明天再来吧");
        }
        if (SexType.Female == sexType) {
            boolean realName = userInfoManager.hasRealName(userId);
            if (!realName) {
                throw new BusiException("请先完成实名认证");
            }
        }
        // 是否正在通话
        boolean inCall = netCallComponent.isInCall(userId);
        if (inCall) {
            throw new BusiException("通话过程中，不可进行视频匹配");
        }
        // 是否有待接听的通话
        boolean wait = netCallComponent.hasWaitAcceptUser(userId);
        if (wait) {
            throw new BusiException("有待处理的通话，不可进行视频匹配");
        }
        boolean living = familyLiveManager.isLiving(userId);
        if (living) {
            throw new BusiException("当前上麦中，不可匹配");
        }
    }

    /**
     * 加入视频匹配队列
     *
     * @param userId
     */
    public void putToMatchQueue(Long userId) {
        UUserBasic basic = userInfoManager.getUserBasic(userId);
        SexType sexType = basic.getSex();
        Long now = System.currentTimeMillis();
        // 加入队列
        socialRedisTemplate.zadd(buildVideoMatchQueueKey(sexType), (double) now, userId.toString());
        // 用户信息存储
        UUserLocation location = userInfoManager.getUserLocation(userId);
        Integer age = findUserAge(basic);
        // 匹配记录
        String logJson = saveVideoMatchLog(userId);
        VideoMatchInfoDTO infoDTO = new VideoMatchInfoDTO(location, age, logJson);
        socialRedisTemplate.set(buildVideoMatchInfoKey(userId), JsonUtils.seriazileAsString(infoDTO));
    }

    /**
     * 获取用户的年龄
     * 1，优先实名认证信息中的年龄
     * 2，最后用户资料中填写的用户年龄
     */
    private Integer findUserAge(UUserBasic basic) {
        UIdentityAuth auth = userInfoManager.getUserIdentityAuth(basic.getUserId());
        if (auth != null && StringUtils.isNotEmpty(auth.getIdentityNo())) {
            return BusiUtils.getAgeByIdNumber(BusiUtils.decrypt(auth.getIdentityNo()));
        }
        return BusiUtils.getAgeByDate(basic.getBirthDate());
    }

    /**
     * 移除视频匹配队列
     *
     * @param userId
     */
    public boolean removeMatchQueue(Long userId, SexType sexType, VideoMatchStatus matchStatus) {
        return removeMatchQueue(userId, sexType, matchStatus, null, null);
    }

    /**
     * 移除视频匹配队列
     *
     * @param userId
     */
    public boolean removeMatchQueue(Long userId, VideoMatchStatus matchStatus) {
        return removeMatchQueue(userId, null, matchStatus, null, null);
    }


    /**
     * 移除视频匹配队列
     *
     * @param userId
     */
    public boolean removeMatchQueue(Long userId, SexType sexType, VideoMatchStatus matchStatus, Long friendId, Long callId) {
        if (null == sexType) {
            UUserBasic basic = userInfoManager.getUserBasic(userId);
            sexType = basic.getSex();
        }
        Long line = socialRedisTemplate.zrem(buildVideoMatchQueueKey(sexType), userId.toString());
        updateVideoMatchLog(userId, matchStatus, friendId, callId);
        socialRedisTemplate.del(buildVideoMatchInfoKey(userId));
        return line > 0;
    }

    /**
     * 匹配超时消息
     *
     * @param userIds
     */
    public void saveTimeOut(List<Long> userIds) {
        // 队列移除
        Iterator<Long> iterator = userIds.iterator();
        while (iterator.hasNext()) {
            Long userId = iterator.next();
            boolean removeSuccess = miniLockTemplate.execute(MiniLockType.NetCall, userId, () -> {
                // 从队列移除
                return removeMatchQueue(userId, VideoMatchStatus.TimeOut);
            });
            if (!removeSuccess) {
                iterator.remove();
            }
        }
        // 超时消息
        messageComponent.sendMsgToUser(BackCodeDefine.VideoMatchTimeOut, null, ListUtils.toLongArray(userIds));
    }

    /**
     * 获取等待匹配队列
     *
     * @return
     */
    public List<Tuple> getVideoMatchMales(SexType sexType) {
        // 时间由小到大
        return socialRedisTemplate.zrangeWithScores(buildVideoMatchQueueKey(sexType), 0L, -1L);
    }

    /**
     * 开始视频匹配
     *
     * @param tuple 男性用户信息
     */
    public void startVideoMatch(Tuple tuple) {
        Long userId = Long.parseLong(tuple.getElement());
        // 其他情况移除队列，不可继续匹配
        if (!canMatch(userId)) {
            return;
        }
        Long now = System.currentTimeMillis();
        Long score = (long) tuple.getScore();
        // 开启用户锁
        miniLockTemplate.execute(MiniLockType.NetCall, userId.toString(), () -> {
            // 如果超时，下一个
            if (timeOut(now, score)) {
                if (removeMatchQueue(userId, VideoMatchStatus.TimeOut)) {
                    // 超时消息
                    messageComponent.sendMsgToUserInContext(BackCodeDefine.VideoMatchTimeOut, null, userId);
                }
                return null;
            }
            // 是否还在队列中，如果不在，可能被其他业务移除，匹配失败
            if (!inVideoMatch(userId)) {
                return null;
            }
            // 寻找匹配的女性用户
            VideoMatchScoreDTO scoreDTO = findVideoMatchUser(userId, score);
            // 没有找到，下次继续
            if (null == scoreDTO) {
                return null;
            }
            Long friendId = scoreDTO.getUserId();
            miniLockTemplate.execute(MiniLockType.NetCall, friendId.toString(), () -> {
                // 是否还在队列中，如果不在，可能被其他业务移除，匹配失败
                if (!inVideoMatch(friendId)) {
                    return null;
                }
                // 执行匹配
                UUserBasic basic = userInfoManager.getUserBasic(userId);
                UUserBasic friendBasic = userInfoManager.getUserBasic(friendId);
                // 创建通话
                FChatNetCall netCall = saveCreateVideoCall(basic, friendBasic, now);
                // 匹配成功，移除队列
                removeMatchQueue(userId, basic.getSex(), VideoMatchStatus.Success, friendId, netCall.getCallId());
                removeMatchQueue(friendId, friendBasic.getSex(), VideoMatchStatus.Success, userId, netCall.getCallId());
                // 发送匹配成功消息
                Map<BusinessDataKey, Object> extMap = new HashMap<>();
                extMap.put(BusinessDataKey.CallId, netCall.getCallId());
                messageComponent.sendMsgToUserInContext(BackCodeDefine.VideoMatchSuccess, extMap, userId, friendId);
                // 累计匹配次数
                socialRedisTemplate.incr(buildVideoMatchTimesKey(userId));
                socialRedisTemplate.incr(buildVideoMatchTimesKey(friendId));
                return null;
            });
            return null;
        });
    }

    /**
     * 是否可以继续匹配
     *
     * @param userId
     * @return
     */
    private boolean canMatch(Long userId) {
        // 如果已离线，将用户从等待队列中移除
        if (!userInfoManager.isOnline(userId)) {
            removeMatchQueue(userId, VideoMatchStatus.Cancel);
            LOG.info("视频匹配 - 当前用户已离线，放弃匹配，userId：{}", userId);
            return false;
        }
        return true;
    }

    /**
     * 匹配到之后创建通话
     *
     * @param basic
     * @param friendBasic
     */
    private FChatNetCall saveCreateVideoCall(UUserBasic basic, UUserBasic friendBasic, Long now) {
        Long userId = basic.getUserId();
        Long friendId = friendBasic.getUserId();
        // 免费检测
        NetCallWaiverType waiverType = netCallComponent.getWaiverType(userId, friendId, NetCallType.Video, null);
        // 通话是否收费
        boolean charge = NetCallWaiverType.NotFree == waiverType;
        // 付费人是男方用戶
        Long billUserId = charge ? getBillUserId(basic, friendBasic) : null;
        // 获取通话单价
        Integer beans = charge ? netCallComponent.getBeansPerMin(friendId, NetCallType.Video) : 0;
        // 折扣券信息
        DiscountDTO discount = null;
        // 付费用券检查
        if (charge) {
            discount = netCallComponent.getBagDiscountByCallType(billUserId, NetCallType.Video, beans);
            if (null != discount) {
                // 折扣后的单价
                Long discountBeans = Math.round(discount.getDiscount() / 100D * beans);
                beans = discountBeans.intValue();
            }
        }
        // 新增通话记录 模拟使用1min免费券通话功能，useBagCnt = 1
        FChatNetCall chatNetCall = null; //netCallComponent.add(userId, friendId, billUserId, NetCallType.Video, NetCallSourceType.VideoMatch, beans, waiverType, null);
        if (null != billUserId) {
            // 给付费方用户加用户锁
            userLockTemplate.acquireTransactionLock(billUserId);
        }
        // 扣除背包折扣券
        if (null != discount && null != billUserId) {
            socialBagComponent.reduceGoods(billUserId, discount.getGoodsId(), 1);
        }
        boolean useBag = null != chatNetCall.getUseBagCount() && chatNetCall.getUseBagCount() > 0;
        if (null != chatNetCall.getBillUserId()) {
            // 预扣付费方第一分钟通话的金币
            netCallComponent.deductBeans(chatNetCall, chatNetCall.getBeans(), GlobalDataUtils.beans(chatNetCall.getBillUserId()), null, null);
            // 将通话加入扣费队列
            // netCallComponent.putToDeductQueue(chatNetCall, now / 1000);
        }
        // 将主叫和被叫加入待超时队列
        netCallComponent.putToTimeoutQueue(chatNetCall.getUserId(), chatNetCall.getCallId());
        netCallComponent.putToTimeoutQueue(chatNetCall.getFriendId(), chatNetCall.getCallId());
        // 通话接通，加入等待录制队列
        netCallComponent.putToRecordingQueue(AgoraSdkUtils.buildNetCallChannelId(chatNetCall.getCallId()));
        return chatNetCall;
    }

    /**
     * 获取付费用户
     *
     * @param basic
     * @param friendBasic
     * @return
     */
    private Long getBillUserId(UUserBasic basic, UUserBasic friendBasic) {
        if (SexType.Male == basic.getSex()) {
            return basic.getUserId();
        }
        return friendBasic.getUserId();
    }

    /**
     * 是否超时
     *
     * @param now
     * @param score
     * @return
     */
    public boolean timeOut(Long now, Long score) {
        return (now - score) / 1000L > 10 * 60;
    }

    /**
     * 查询匹配用户
     *
     * @param userId
     * @param score
     * @return
     */
    private VideoMatchScoreDTO findVideoMatchUser(Long userId, Long score) {
        VideoMatchInfoDTO userInfo = getMatchInfo(userId);
        if (null == userInfo) {
            return null;
        }
        List<VideoMatchScoreDTO> friendIdList = new ArrayList<>();
        List<Tuple> tuples = getVideoMatchMales(SexType.Female);
        // 计算当前用户和其他用户的分值
        for (Tuple tuple : tuples) {
            Long friendId = Long.valueOf(tuple.getElement());
            VideoMatchInfoDTO friendInfo = getMatchInfo(friendId);
            if (null == friendInfo) {
                continue;
            }
            friendIdList.add(calculateScore(userInfo, friendInfo, friendId, userId));
        }
        friendIdList = friendIdList.stream().sorted(Comparator.comparing(VideoMatchScoreDTO::getScore).reversed()).collect(Collectors.toList());
        // <120秒 匹配高分用户
        boolean maxScore = (System.currentTimeMillis() - score) / 1000L < 120L;
        // 仅获取6分以上用户
        if (maxScore) {
            friendIdList = friendIdList.stream().filter(t -> t.getScore() > 6).collect(Collectors.toList());
        }
        if (friendIdList.size() > 0) {
            VideoMatchScoreDTO matchScoreDTO = friendIdList.get(0);
            LOG.info(MsgUtils.format("视频匹配成功,匹配时长(秒):{}！！！匹配方信息-{}:{},被匹配方信息-{}:{},匹配分值:{}", (System.currentTimeMillis() - score) / 1000, userId, socialRedisTemplate.get(buildVideoMatchInfoKey(userId)), matchScoreDTO.getUserId(), socialRedisTemplate.get(buildVideoMatchInfoKey(matchScoreDTO.getUserId())), matchScoreDTO.getScore()));
            return friendIdList.get(0);
        }
        return null;
    }

    /**
     * 获取用户匹配计算信息
     *
     * @param userId
     * @return
     */
    private VideoMatchInfoDTO getMatchInfo(Long userId) {
        String userJson = socialRedisTemplate.get(buildVideoMatchInfoKey(userId));
        return JsonUtils.deserializeAsObject(userJson, VideoMatchInfoDTO.class);
    }

    /**
     * 计算分值
     *
     * @param userInfo
     * @param friendInfo
     * @param friendId
     * @return
     */
    private VideoMatchScoreDTO calculateScore(VideoMatchInfoDTO userInfo, VideoMatchInfoDTO friendInfo, Long friendId, Long userId) {
        // 距离分
        Long distanceScore = getDistanceScore(userInfo, friendInfo, friendId, userId);
        // 年龄分
        Long ageScore = getAgeScore(userInfo, friendInfo);
        return new VideoMatchScoreDTO(friendId, distanceScore + ageScore);
    }

    /**
     * 获取距离分
     *
     * @param userInfo
     * @param friendInfo
     * @return
     */
    private Long getDistanceScore(VideoMatchInfoDTO userInfo, VideoMatchInfoDTO friendInfo, Long friendId, Long userId) {
        Long distance = BusiUtils.getDistance(userInfo.getLat(), userInfo.getLng(), friendInfo.getLat(), friendInfo.getLng());
        // 无法计算 1分
        if (null == distance) {
            return 1L;
        }
        // 大于300km 2分
        if (distance > 300 * 1000) {
            return 2L;
        }
        // 100-300km 3分
        if (distance > 100 * 1000) {
            return 3L;
        }
        // 1-100km 5分
        return 5L;
    }

    /**
     * 获取年龄分
     *
     * @param friendInfo
     * @return
     */
    private Long getAgeScore(VideoMatchInfoDTO userInfo, VideoMatchInfoDTO friendInfo) {
        if (null == userInfo.getAge() || null == friendInfo.getAge()) {
            return 1L;
        }
        Integer diffAge = userInfo.getAge() - friendInfo.getAge();
        // 大于15 2分
        if (diffAge > 15) {
            return 2L;
        }
        // 11至15 3分
        if (diffAge > 10) {
            return 3L;
        }
        // -5至10 3分
        if (diffAge > -5) {
            return 5L;
        }
        // 小于等于-5 1分
        return 1L;
    }

    /**
     * 匹配记录日志
     *
     * @param userId
     * @return
     */
    private String saveVideoMatchLog(Long userId) {
        CVideoMatchLog matchLog = new CVideoMatchLog();
        matchLog.setUserId(userId);
        matchLog.setStatus(VideoMatchStatus.Wait);
        Date now = new Date();
        matchLog.setStatusTime(now);
        matchLog.setCreateTime(DateUtils.truncMillis(System.currentTimeMillis()));
        logComponent.saveVideoMatchLog(matchLog);
        return ObjectTimeDTO.toKey(matchLog.getLogId(), matchLog.getCreateTime());
    }

    /**
     * 更新匹配记录日志
     *
     * @param userId
     * @param matchStatus
     * @param friendId
     * @param callId
     */
    private void updateVideoMatchLog(Long userId, VideoMatchStatus matchStatus, Long friendId, Long callId) {
        VideoMatchInfoDTO matchInfo = getMatchInfo(userId);
        if (null == matchInfo) {
            return;
        }
        updateVideoMatchLog(matchInfo.getLogInfo(), matchStatus, friendId, callId);
    }

    /**
     * 更新匹配记录日志
     *
     * @param logInfo
     * @param matchStatus
     * @param friendId
     * @param callId
     */
    private void updateVideoMatchLog(String logInfo, VideoMatchStatus matchStatus, Long friendId, Long callId) {
        ObjectTimeDTO dto = ObjectTimeDTO.build(logInfo);
        CVideoMatchLog matchLog = new CVideoMatchLog(dto.getObjectId(), new Date(dto.getTimeMs()));
        matchLog.setStatus(matchStatus);
        matchLog.setStatusTime(new Date());
        matchLog.setFriendId(friendId);
        matchLog.setCallId(callId);
        logComponent.updateVideoMatchLog(matchLog);
    }


    /**
     * 匹配次数Key
     *
     * @param userId
     * @return
     */
    private RedisKey buildVideoMatchTimesKey(Long userId) {
        return RedisKey.create(SocialKeyDefine.VideoMatchTimes, userId);
    }


    /**
     * 匹配队列Key
     *
     * @param sexType
     * @return
     */
    private RedisKey buildVideoMatchQueueKey(SexType sexType) {
        return RedisKey.create(SocialKeyDefine.VideoMatchQueue, sexType);
    }

    /**
     * 匹配计算信息Key
     *
     * @param userId
     * @return
     */
    private RedisKey buildVideoMatchInfoKey(Long userId) {
        return RedisKey.create(SocialKeyDefine.VideoMatchInfo, userId);
    }

    /**
     * 匹配信息
     * @return
     */
    private RedisKey buildVideoMatchInfoPlus(Long callId) {
        return RedisKey.create(SocialKeyDefine.VideoMatchInfoPlus, callId);
    }
}
