package com.tuowan.yeliao.social.comp.friend.config;

import java.util.Arrays;
import java.util.List;

/**
 * 自动搭讪分发配置
 *
 * <AUTHOR>
 * @date 2020/9/19 20:54
 */
public class ChatupDispatchConfig {

    /**
     * 男用户打开APP被搭讪时间间隔
     */
    private static final List<Integer> CHAT_UP_INTERVALS = Arrays.asList(1, 10, 20, 30, 60, 90, 120, 150, 180, 210, 250, 360, 420, 480, 540);

    private static final List<Integer> CHAT_UP_PURE_MODE_INTERVALS = Arrays.asList(60, 180);

    // 搭讪匹配达标总金额金额
    public static final Long STANDARD_INCOME_CASH = 100_0000L;
    // 大神匹配人均收益判断门槛金额，达标才计算人均收益
    public static final Long BASE_AVG_INCOME_LIMIT = 10_0000L;
    // 搭讪匹配达标人均金额
    public static final Long STANDARD_AVG_INCOME = 1800L;
    // 匹配时间间隔，两天内不再重复匹配
    public static final Long MATCH_INTERVAL = 2 * 24 * 3600 * 1000L;
    // 每小时搭讪流量上限
    public static final Long HOUR_CHAT_UP_LIMIT = 60L;

    // 匹配等级，1,2,3,4 分别表示第1-3层漏斗，和匹配失败
    public static final Integer MATCH_LEVEL_1 = 1;
    public static final Integer MATCH_LEVEL_2 = 2;
    public static final Integer MATCH_LEVEL_3 = 3;
    public static final Integer MATCH_LEVEL_4 = 4;
    public static final Integer MATCH_LEVEL_5 = 5;


    /**
     * 获取不同性别红包间隔时间
     */
    public static List<Integer> getRedPacketTaskIntervals() {
        return CHAT_UP_INTERVALS;
    }
}
