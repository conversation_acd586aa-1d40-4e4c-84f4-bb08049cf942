package com.tuowan.yeliao.social.comp.lottery.enums;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 消费获得抽奖券类型
 *
 * <AUTHOR>
 * @date 2021/4/9 09:57
 */
public enum ConsumeLotteryType implements EnumUtils.IDEnum {

    // 正式配置
    High("AwardLottery_11", "高级奖励", 1000L, Long.MAX_VALUE),
    Middle("AwardLottery_10", "中级奖励", 200L, 1000L),
    Low("AwardLottery_9", "低级奖励", 0L, 200L);

    /**
     * 奖励code
     */
    private String id;
    private String desc;
    /**
     * 最小值 单位金币
     */
    private Long min;
    /**
     * 最大值 单位金币
     */
    private Long max;

    ConsumeLotteryType(String id, String desc, Long min, Long max) {
        this.id = id;
        this.desc = desc;
        this.min = min;
        this.max = max;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public Long getMin() {
        return min;
    }

    public Long getMax() {
        return max;
    }

    public static String getAwardCode(Long beans) {
        for (ConsumeLotteryType type : ConsumeLotteryType.values()) {
            if (beans >= type.getMin() && beans < type.getMax()) {
                return type.getId();
            }
        }
        return null;
    }
}
