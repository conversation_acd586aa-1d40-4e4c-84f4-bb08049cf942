package com.tuowan.yeliao.social.data.dto.friend;


import com.tuowan.yeliao.commons.data.enums.social.ChatContentType;
import com.tuowan.yeliao.social.data.enums.friend.ChatSourceType;

import java.util.Date;

/**
 * 聊天消费信息封装
 *
 * <AUTHOR>
 * @date 2020/7/4 11:22
 */
public class ChatConsumeDTO {

    /**
     * 业务流水号
     */
    private Long tid;
    /**
     * 送礼人 ID
     */
    private Long fromUserId;
    /**
     * 收礼人ID
     */
    private Long toUserId;
    /**
     * 付费用户ID
     */
    private Long feeUserId;
    /**
     * 使用总金币数量（充值金币+赠送金币）
     */
    private Long totalBeans;
    /**
     * 使用的平台金币数量
     */
    private Long platformBeans;
    /**
     * 使用总银币数量
     */
    private Long totalSilver;
    /**
     * 礼物：礼物标识id
     * 私聊：折扣券信息
     * 音视频通话：折扣券信息
     */
    private Integer prodId;
    /**
     * 礼物：礼物数量
     * 私聊：所用折扣券个数
     * 音视频：使用音视频券个数
     */
    private Integer prodCnt;
    /**
     * 消息来源类型
     */
    private ChatSourceType sourceType;
    /**
     * 聊天内容类型
     */
    private ChatContentType contentType;
    /**
     * 提成用户ID
     */
    private Long targetPresentUserId;
    /**
     * 提成积分
     */
    private Long targetPresentCash;
    /**
     * 提成积分(赠送金币部分)
     */
    private Long targetPlatformCash;

    /**
     * 邀请人信息
     */
    private Long invitorUserId;
    /**
     * 邀请人提成积分
     */
    private Long invitorPresentCash;
    /**
     * 邀请人提成积分(赠送金币部分)
     */
    private Long invitorPlatformCash;

    /**
     * 消费时间
     */
    private Date consumeTime;

    public ChatConsumeDTO() {
    }

    public ChatConsumeDTO(Long totalBeans, Integer prodId, Integer prodCnt, Long targetPresentCash) {
        this.totalBeans = totalBeans;
        this.prodId = prodId;
        this.prodCnt = prodCnt;
        this.targetPresentCash = targetPresentCash;
    }

    public Long getTid() {
        return tid;
    }

    public void setTid(Long tid) {
        this.tid = tid;
    }

    public Long getFromUserId() {
        return fromUserId;
    }

    public void setFromUserId(Long fromUserId) {
        this.fromUserId = fromUserId;
    }

    public Long getToUserId() {
        return toUserId;
    }

    public void setToUserId(Long toUserId) {
        this.toUserId = toUserId;
    }

    public Long getTotalBeans() {
        return totalBeans;
    }

    public Long getFeeUserId() {
        return feeUserId;
    }

    public void setFeeUserId(Long feeUserId) {
        this.feeUserId = feeUserId;
    }

    public void setTotalBeans(Long totalBeans) {
        this.totalBeans = totalBeans;
    }

    public Integer getProdId() {
        return prodId;
    }

    public void setProdId(Integer prodId) {
        this.prodId = prodId;
    }

    public Integer getProdCnt() {
        return prodCnt;
    }

    public void setProdCnt(Integer prodCnt) {
        this.prodCnt = prodCnt;
    }

    public ChatContentType getContentType() {
        return contentType;
    }

    public void setContentType(ChatContentType contentType) {
        this.contentType = contentType;
    }

    public Long getPlatformBeans() {
        return platformBeans;
    }

    public void setPlatformBeans(Long platformBeans) {
        this.platformBeans = platformBeans;
    }

    public Long getTotalSilver() {
        return totalSilver;
    }

    public void setTotalSilver(Long totalSilver) {
        this.totalSilver = totalSilver;
    }

    public Long getTargetPresentUserId() {
        return targetPresentUserId;
    }

    public void setTargetPresentUserId(Long targetPresentUserId) {
        this.targetPresentUserId = targetPresentUserId;
    }

    public Long getTargetPresentCash() {
        return targetPresentCash;
    }

    public void setTargetPresentCash(Long targetPresentCash) {
        this.targetPresentCash = targetPresentCash;
    }

    public Date getConsumeTime() {
        return consumeTime;
    }

    public void setConsumeTime(Date consumeTime) {
        this.consumeTime = consumeTime;
    }

    public Long getTargetPlatformCash() {
        return targetPlatformCash;
    }

    public void setTargetPlatformCash(Long targetPlatformCash) {
        this.targetPlatformCash = targetPlatformCash;
    }

    public Long getInvitorPresentCash() {
        return invitorPresentCash;
    }

    public void setInvitorPresentCash(Long invitorPresentCash) {
        this.invitorPresentCash = invitorPresentCash;
    }

    public Long getInvitorPlatformCash() {
        return invitorPlatformCash;
    }

    public void setInvitorPlatformCash(Long invitorPlatformCash) {
        this.invitorPlatformCash = invitorPlatformCash;
    }

    public ChatSourceType getSourceType() {
        return sourceType;
    }

    public void setSourceType(ChatSourceType sourceType) {
        this.sourceType = sourceType;
    }

    public Long getInvitorUserId() {
        return invitorUserId;
    }

    public void setInvitorUserId(Long invitorUserId) {
        this.invitorUserId = invitorUserId;
    }
}
