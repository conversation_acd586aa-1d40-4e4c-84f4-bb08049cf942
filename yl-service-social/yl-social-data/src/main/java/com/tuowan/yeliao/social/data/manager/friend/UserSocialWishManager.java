package com.tuowan.yeliao.social.data.manager.friend;


import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.social.data.entity.USocialWishConfig;
import com.tuowan.yeliao.social.data.persistence.USocialWishConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 用户社交意愿数据封装
 *
 * <AUTHOR>
 * @date 2022/4/14 13:24
 */
@Component
public class UserSocialWishManager {

    @Autowired
    private USocialWishConfigMapper uSocialWishConfigMapper;
    @Autowired
    private UserInfoManager userInfoManager;

    /**
     * 获取用户查询配置
     *
     * @param userId
     * @return
     */
    public USocialWishConfig getUserSearConfig(Long userId) {
        // 筛选配置
        USocialWishConfig config = uSocialWishConfigMapper.selectByPrimaryKey(new USocialWishConfig(userId));
        if (Objects.nonNull(config)) {
            return config;
        }

        /*try {
            // 男用户看比自己年龄最多大5岁的女用户；女用户看比自己年龄最多大10岁的男用户
            UUserBasic userBasic = userInfoManager.getUserBasic(userId);
            Integer age = BusiUtils.getAgeByDate(userBasic.getBirthDate());
            USocialWishConfig config = new USocialWishConfig();
            config.setUserId(userId);
            config.setMinAge(GlobalConstant.SOCIAL_WISH_MIN_AGE);
            config.setMaxAge(SexType.Male == userBasic.getSex() ? age + 5 : age + 10);
            return config;
        }catch (Exception e){
            // do nothing
        }*/
        return USocialWishConfig.createDefault(userId);
    }
}
