package com.tuowan.yeliao.social.data.manager.home;

import com.tuowan.yeliao.social.data.entity.UUserVideoChatApply;
import com.tuowan.yeliao.social.data.persistence.UUserVideoChatApplyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class VideoChatManager {

    @Autowired
    private UUserVideoChatApplyMapper userVideoChatApplyMapper;

    /**
     * 保存用户视频聊申请记录
     */
    public void saveUserVideoChatApply(UUserVideoChatApply apply){
        userVideoChatApplyMapper.insert(apply);
    }

    /**
     * 获取用户视频聊申请记录
     */
    public UUserVideoChatApply getUserVideoChatApply(Long userId){
        return userVideoChatApplyMapper.selectByPrimaryKey(new UUserVideoChatApply(userId));
    }

    /**
     * 修改用户视频聊申请记录
     */
    public void updateUserVideoChatApply(UUserVideoChatApply update){
        userVideoChatApplyMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 删除用户视频聊申请记录
     */
    public void deleteUserVideoChatApply(Long userId){
        userVideoChatApplyMapper.deleteByPrimaryKey(new UUserVideoChatApply(userId));
    }
}
