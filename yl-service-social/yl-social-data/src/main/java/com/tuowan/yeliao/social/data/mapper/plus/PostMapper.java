package com.tuowan.yeliao.social.data.mapper.plus;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tuowan.yeliao.commons.data.mapper.plus.BasePlusMapper;
import com.tuowan.yeliao.social.entity.plus.Post;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 动态 Mapper 接口
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Mapper
public interface PostMapper extends BasePlusMapper<Post> {

    /**
     * 根据用户ID查询动态列表
     */
    @Select("SELECT * FROM s_post_plus_demo WHERE user_id = #{userId} AND deleted = 0 ORDER BY create_time DESC")
    List<Post> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和动态类型查询
     */
    @Select("SELECT * FROM s_post_plus_demo WHERE user_id = #{userId} AND post_type = #{postType} AND deleted = 0 ORDER BY create_time DESC")
    List<Post> selectByUserIdAndType(@Param("userId") Long userId, @Param("postType") Integer postType);

    /**
     * 查询热门动态（根据互动数排序）
     */
    @Select("SELECT * FROM s_post_plus_demo " +
            "WHERE deleted = 0 AND status = 1 AND audit_status = 1 " +
            "ORDER BY (praise_num * 0.5 + comment_num * 0.3 + share_num * 0.2) DESC " +
            "LIMIT #{limit}")
    List<Post> selectHotPosts(@Param("limit") Integer limit);

    /**
     * 查询最新动态
     */
    @Select("SELECT * FROM s_post_plus_demo " +
            "WHERE deleted = 0 AND status = 1 AND audit_status = 1 " +
            "ORDER BY create_time DESC " +
            "LIMIT #{limit}")
    List<Post> selectLatestPosts(@Param("limit") Integer limit);

    /**
     * 根据关键词搜索动态
     */
    @Select("SELECT * FROM s_post_plus_demo " +
            "WHERE (title LIKE CONCAT('%', #{keyword}, '%') OR content LIKE CONCAT('%', #{keyword}, '%')) " +
            "AND deleted = 0 AND status = 1 AND audit_status = 1 " +
            "ORDER BY create_time DESC")
    List<Post> searchPosts(@Param("keyword") String keyword);

    /**
     * 统计用户动态数量
     */
    @Select("SELECT COUNT(*) FROM s_post_plus_demo WHERE user_id = #{userId} AND deleted = 0")
    Long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户各类型动态数量
     */
    @Select("SELECT post_type, COUNT(*) as count FROM s_post_plus_demo " +
            "WHERE user_id = #{userId} AND deleted = 0 " +
            "GROUP BY post_type")
    List<java.util.Map<String, Object>> countByUserIdGroupByType(@Param("userId") Long userId);

    /**
     * 增加点赞数
     */
    @Update("UPDATE s_post_plus_demo SET praise_num = praise_num + 1, update_time = NOW() WHERE id = #{id}")
    int increasePraiseNum(@Param("id") Long id);

    /**
     * 减少点赞数
     */
    @Update("UPDATE s_post_plus_demo SET praise_num = GREATEST(praise_num - 1, 0), update_time = NOW() WHERE id = #{id}")
    int decreasePraiseNum(@Param("id") Long id);

    /**
     * 增加评论数
     */
    @Update("UPDATE s_post_plus_demo SET comment_num = comment_num + 1, update_time = NOW() WHERE id = #{id}")
    int increaseCommentNum(@Param("id") Long id);

    /**
     * 减少评论数
     */
    @Update("UPDATE s_post_plus_demo SET comment_num = GREATEST(comment_num - 1, 0), update_time = NOW() WHERE id = #{id}")
    int decreaseCommentNum(@Param("id") Long id);

    /**
     * 增加分享数
     */
    @Update("UPDATE s_post_plus_demo SET share_num = share_num + 1, update_time = NOW() WHERE id = #{id}")
    int increaseShareNum(@Param("id") Long id);

    /**
     * 分页查询用户动态
     */
    default IPage<Post> selectUserPostPage(Page<Post> page, Long userId) {
        QueryWrapper<Post> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("deleted", 0)
                   .orderByDesc("create_time");
        return selectPage(page, queryWrapper);
    }

    /**
     * 分页查询用户指定类型动态
     */
    default IPage<Post> selectUserPostPageByType(Page<Post> page, Long userId, Integer postType) {
        QueryWrapper<Post> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("post_type", postType)
                   .eq("deleted", 0)
                   .orderByDesc("create_time");
        return selectPage(page, queryWrapper);
    }

    /**
     * 分页查询热门动态
     */
    default IPage<Post> selectHotPostPage(Page<Post> page) {
        QueryWrapper<Post> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0)
                   .eq("status", 1)
                   .eq("audit_status", 1)
                   .orderByDesc("(praise_num * 0.5 + comment_num * 0.3 + share_num * 0.2)");
        return selectPage(page, queryWrapper);
    }

    /**
     * 分页搜索动态
     */
    default IPage<Post> searchPostPage(Page<Post> page, String keyword) {
        QueryWrapper<Post> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(wrapper -> wrapper
                    .like("title", keyword)
                    .or()
                    .like("content", keyword))
                   .eq("deleted", 0)
                   .eq("status", 1)
                   .eq("audit_status", 1)
                   .orderByDesc("create_time");
        return selectPage(page, queryWrapper);
    }

    /**
     * 根据动态类型查询
     */
    default List<Post> selectByPostType(Integer postType) {
        QueryWrapper<Post> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("post_type", postType)
                   .eq("deleted", 0)
                   .eq("status", 1)
                   .eq("audit_status", 1)
                   .orderByDesc("create_time");
        return selectList(queryWrapper);
    }

    /**
     * 查询待审核动态
     */
    default List<Post> selectPendingAuditPosts() {
        QueryWrapper<Post> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("audit_status", 0)
                   .eq("deleted", 0)
                   .orderByAsc("create_time");
        return selectList(queryWrapper);
    }

    /**
     * 查询用户最近的动态
     */
    default List<Post> selectUserRecentPosts(Long userId, Integer limit) {
        QueryWrapper<Post> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("deleted", 0)
                   .eq("status", 1)
                   .eq("audit_status", 1)
                   .orderByDesc("create_time")
                   .last("LIMIT " + limit);
        return selectList(queryWrapper);
    }
}
