package com.tuowan.yeliao.social.data.mapper.plus;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tuowan.yeliao.commons.data.mapper.plus.BasePlusMapper;
import com.tuowan.yeliao.social.entity.plus.SPostPlus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 动态表 Mapper 接口（MyBatis-Plus 版本）
 * 演示 MyBatis-Plus 的使用方式
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Mapper
public interface SPostPlusMapper extends BasePlusMapper<SPostPlus> {

    /**
     * 根据用户ID查询动态列表
     * 使用 MyBatis-Plus 的注解方式
     */
    @Select("SELECT * FROM s_post WHERE user_id = #{userId} AND deleted = 0 ORDER BY create_time DESC")
    List<SPostPlus> selectByUserId(@Param("userId") Long userId);

    /**
     * 分页查询用户动态
     * 使用 MyBatis-Plus 的分页功能
     */
    default IPage<SPostPlus> selectUserPostPage(Page<SPostPlus> page, Long userId) {
        QueryWrapper<SPostPlus> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("deleted", 0)
                   .orderByDesc("create_time");
        return selectPage(page, queryWrapper);
    }

    /**
     * 根据动态类型查询
     */
    default List<SPostPlus> selectByPostType(Integer postType) {
        QueryWrapper<SPostPlus> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("post_type", postType)
                   .eq("deleted", 0)
                   .orderByDesc("create_time");
        return selectList(queryWrapper);
    }

    /**
     * 查询热门动态
     * 可以使用自定义 SQL
     */
    @Select("SELECT * FROM s_post WHERE deleted = 0 AND status = 1 " +
            "ORDER BY (praise_num * 0.5 + comment_num * 0.3 + share_num * 0.2) DESC " +
            "LIMIT #{limit}")
    List<SPostPlus> selectHotPosts(@Param("limit") Integer limit);

    /**
     * 统计用户动态数量
     */
    default Long countByUserId(Long userId) {
        QueryWrapper<SPostPlus> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("deleted", 0);
        return selectCount(queryWrapper);
    }
}
