/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.dto.friend.netcall.NetCallFeeDTO;
import com.tuowan.yeliao.social.data.entity.FChatNetCallFee;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "F_CHAT_NET_CALL_FEE", schema = "YL_SOCIAL")
public interface FChatNetCallFeeMapper {
    int deleteByPrimaryKey(FChatNetCallFee record);

    int insert(FChatNetCallFee record);

    FChatNetCallFee selectByPrimaryKey(FChatNetCallFee record);

    int updateByPrimaryKeySelective(FChatNetCallFee record);

    int updateByPrimaryKey(FChatNetCallFee record);

    /**
     * 查询预扣总费用
     *
     * @param callId
     * @return
     */
    NetCallFeeDTO queryPreTotalBeans(Long callId);
}