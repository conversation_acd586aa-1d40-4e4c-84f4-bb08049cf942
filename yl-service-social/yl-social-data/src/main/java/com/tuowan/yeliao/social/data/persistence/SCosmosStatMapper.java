/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.SCosmosStat;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "S_COSMOS_STAT", schema = "YL_SOCIAL")
public interface SCosmosStatMapper {
    int deleteByPrimaryKey(SCosmosStat record);

    int insert(SCosmosStat record);

    SCosmosStat selectByPrimaryKey(SCosmosStat record);

    int updateByPrimaryKeySelective(SCosmosStat record);

    int updateByPrimaryKey(SCosmosStat record);

    // 获取指定宇宙的访问数量
    int countCosmosWatch(@Param("cosmosId") Long cosmosId);
}