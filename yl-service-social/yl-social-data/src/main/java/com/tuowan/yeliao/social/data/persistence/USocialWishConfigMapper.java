/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.USocialWishConfig;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "U_SOCIAL_WISH_CONFIG", schema = "YL_SOCIAL")
public interface USocialWishConfigMapper {
    int deleteByPrimaryKey(USocialWishConfig record);

    int insert(USocialWishConfig record);

    USocialWishConfig selectByPrimaryKey(USocialWishConfig record);

    int updateByPrimaryKeySelective(USocialWishConfig record);

    int updateByPrimaryKey(USocialWishConfig record);
}