/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence;

import com.easyooo.framework.cache.annotations.GroupStrategy;
import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.UUserExclusiveChatup;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "U_USER_EXCLUSIVE_CHATUP", schema = "YL_SOCIAL")
public interface UUserExclusiveChatupMapper {
    int deleteByPrimaryKey(UUserExclusiveChatup record);

    int insert(UUserExclusiveChatup record);

    UUserExclusiveChatup selectByPrimaryKey(UUserExclusiveChatup record);

    int updateByPrimaryKeySelective(UUserExclusiveChatup record);

    int updateByPrimaryKey(UUserExclusiveChatup record);

    @GroupStrategy
    List<UUserExclusiveChatup> selectByUserId(UUserExclusiveChatup record);
}