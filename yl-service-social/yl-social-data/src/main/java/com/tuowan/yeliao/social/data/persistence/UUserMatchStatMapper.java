/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.UUserMatchStat;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "u_user_match_stat", schema = "YL_SOCIAL")
public interface UUserMatchStatMapper {
    int deleteByPrimaryKey(UUserMatchStat record);

    int insert(UUserMatchStat record);

    UUserMatchStat selectByPrimaryKey(UUserMatchStat record);

    int updateByPrimaryKeySelective(UUserMatchStat record);

    int updateByPrimaryKey(UUserMatchStat record);

    int insertBatch(List<UUserMatchStat> list);
}