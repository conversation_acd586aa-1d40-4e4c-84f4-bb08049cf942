/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence.act;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.act.ALuckyWheelTimes;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "A_LUCKY_WHEEL_TIMES", schema = "YL_SOCIAL")
public interface ALuckyWheelTimesMapper {
    int deleteByPrimaryKey(ALuckyWheelTimes record);

    int insert(ALuckyWheelTimes record);

    ALuckyWheelTimes selectByPrimaryKey(ALuckyWheelTimes record);

    int updateByPrimaryKeySelective(ALuckyWheelTimes record);

    int updateByPrimaryKey(ALuckyWheelTimes record);
}