/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence.act;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.act.APigbankReceive;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "A_PIGBANK_RECEIVE", schema = "YL_SOCIAL")
public interface APigbankReceiveMapper {
    int deleteByPrimaryKey(APigbankReceive record);

    int insert(APigbankReceive record);

    APigbankReceive selectByPrimaryKey(APigbankReceive record);

    int updateByPrimaryKeySelective(APigbankReceive record);

    int updateByPrimaryKey(APigbankReceive record);
}