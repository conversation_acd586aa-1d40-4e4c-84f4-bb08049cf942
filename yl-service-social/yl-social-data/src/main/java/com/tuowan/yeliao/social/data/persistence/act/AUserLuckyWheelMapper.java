/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence.act;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.act.AUserLuckyWheel;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "A_USER_LUCKY_WHEEL", schema = "YL_SOCIAL")
public interface AUserLuckyWheelMapper {
    AUserLuckyWheel selectByPrimaryKey(AUserLuckyWheel record);

    int insert(AUserLuckyWheel record);

    int updateByPrimaryKeySelective(AUserLuckyWheel record);
}