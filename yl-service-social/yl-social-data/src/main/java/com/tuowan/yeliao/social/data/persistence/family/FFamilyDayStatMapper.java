/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence.family;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.family.FFamilyDayStat;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
@Table(value = "F_FAMILY_DAY_STAT", schema = "YL_SOCIAL")
public interface FFamilyDayStatMapper {
    int deleteByPrimaryKey(FFamilyDayStat record);

    int insert(FFamilyDayStat record);

    FFamilyDayStat selectByPrimaryKey(FFamilyDayStat record);

    int updateByPrimaryKeySelective(FFamilyDayStat record);

    int updateByPrimaryKey(FFamilyDayStat record);

    Long selectCashByWeek(@Param("familyId") Integer familyId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);
}