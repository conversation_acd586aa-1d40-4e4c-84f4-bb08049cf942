/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence.post;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.post.SPostHeatChangeLog;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "S_POST_HEAT_CHANGE_LOG", schema = "YL_SOCIAL")
public interface SPostHeatChangeLogMapper {
    int deleteByPrimaryKey(SPostHeatChangeLog record);

    int insert(SPostHeatChangeLog record);

    SPostHeatChangeLog selectByPrimaryKey(SPostHeatChangeLog record);

    int updateByPrimaryKeySelective(SPostHeatChangeLog record);

    int updateByPrimaryKey(SPostHeatChangeLog record);
}