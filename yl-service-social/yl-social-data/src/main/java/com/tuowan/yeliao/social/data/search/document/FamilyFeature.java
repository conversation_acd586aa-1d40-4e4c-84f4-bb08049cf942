package com.tuowan.yeliao.social.data.search.document;

import com.easyooo.framework.support.elasticsearch.Document;
import com.easyooo.framework.support.elasticsearch.ESMapping;
import com.easyooo.framework.support.elasticsearch.MappingType;
import com.tuowan.yeliao.social.data.entity.family.FFamily;

/**
 * 家族特征信息，包括地理位置等
 * 用于同城家族筛选
 *
 * <AUTHOR>
 * @date 2021/12/14 19:31
 */
@Document(index = "family-feature", keyProperty = "familyId")
public class FamilyFeature {

    /**
     * 用户ID
     */
    public Integer familyId;

    /**
     * 用户地理位置信息， 维度,经度
     */
    @ESMapping(type = MappingType.GeoPoint)
    public String location;

    /**
     * 推荐分
     */
    public Long recomScore;


    public FamilyFeature() {
    }


    public FamilyFeature(FFamily family, Long recomScore) {
        this.familyId = family.getFamilyId();
        this.location = family.getLat() + "," + family.getLng();
        this.recomScore = recomScore;
    }

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }


    public Long getRecomScore() {
        return recomScore;
    }

    public void setRecomScore(Long recomScore) {
        this.recomScore = recomScore;
    }
}
