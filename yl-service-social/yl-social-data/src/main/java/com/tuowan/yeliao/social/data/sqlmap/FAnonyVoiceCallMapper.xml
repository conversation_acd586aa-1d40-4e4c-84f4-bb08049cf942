<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.FAnonyVoiceCallMapper">
  <sql id="Base_Column_List">
    call_id, user_id, friend_id, match_time, connect_time, finish_time, hang_up_id, duration
  </sql>
  <select id="selectByPrimaryKey" parameterType="FAnonyVoiceCall" resultType="FAnonyVoiceCall">
    select 
    <include refid="Base_Column_List" />
    from f_anony_voice_call
    where call_id = #{callId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="FAnonyVoiceCall">
    delete from f_anony_voice_call
    where call_id = #{callId}
  </delete>
  <insert id="insert" parameterType="FAnonyVoiceCall" useGeneratedKeys="true" keyProperty="callId">
    insert into f_anony_voice_call (call_id, user_id, friend_id, match_time, connect_time, finish_time, 
      hang_up_id, duration)
    values (#{callId}, #{userId}, #{friendId}, #{matchTime}, #{connectTime}, #{finishTime}, 
      #{hangUpId}, #{duration})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="FAnonyVoiceCall">
    update f_anony_voice_call
    <set>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="friendId != null">
        friend_id = #{friendId},
      </if>
      <if test="matchTime != null">
        match_time = #{matchTime},
      </if>
      <if test="connectTime != null">
        connect_time = #{connectTime},
      </if>
      <if test="finishTime != null">
        finish_time = #{finishTime},
      </if>
      <if test="hangUpId != null">
        hang_up_id = #{hangUpId},
      </if>
      <if test="duration != null">
        duration = #{duration},
      </if>
    </set>
    where call_id = #{callId}
  </update>
  <update id="updateByPrimaryKey" parameterType="FAnonyVoiceCall">
    update f_anony_voice_call
    set user_id = #{userId},
      friend_id = #{friendId},
      match_time = #{matchTime},
      connect_time = #{connectTime},
      finish_time = #{finishTime},
      hang_up_id = #{hangUpId},
      duration = #{duration}
    where call_id = #{callId}
  </update>
</mapper>