<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.FChatConsumeMapper">
  <sql id="Base_Column_List">
    create_time, tid, relation_id, from_user_id, to_user_id, source_type, content_type,
    bill_user_id, prod_id, prod_cnt, total_beans, platform_beans, total_silver
  </sql>
  <select id="selectByPrimaryKey" parameterType="FChatConsume" resultType="FChatConsume">
    select 
    <include refid="Base_Column_List" />
    from f_chat_consume
    where create_time = #{createTime}
      and tid = #{tid}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="FChatConsume">
    delete from f_chat_consume
    where create_time = #{createTime}
      and tid = #{tid}
  </delete>
  <insert id="insert" parameterType="FChatConsume">
    insert into f_chat_consume (create_time, tid, relation_id, from_user_id, to_user_id, source_type, content_type,
      bill_user_id, prod_id, prod_cnt, total_beans, platform_beans, total_silver
      )
    values (#{createTime}, #{tid}, #{relationId}, #{fromUserId}, #{toUserId}, #{sourceType}, #{contentType},
      #{billUserId}, #{prodId}, #{prodCnt}, #{totalBeans}, #{platformBeans}, #{totalSilver}
      )
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="FChatConsume">
    update f_chat_consume
    <set>
      <if test="relationId != null">
        relation_id = #{relationId},
      </if>
      <if test="fromUserId != null">
        from_user_id = #{fromUserId},
      </if>
      <if test="toUserId != null">
        to_user_id = #{toUserId},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType},
      </if>
      <if test="contentType != null">
        content_type = #{contentType},
      </if>
      <if test="billUserId != null">
        bill_user_id = #{billUserId},
      </if>
      <if test="prodId != null">
        prod_id = #{prodId},
      </if>
      <if test="prodCnt != null">
        prod_cnt = #{prodCnt},
      </if>
      <if test="totalBeans != null">
        total_beans = #{totalBeans},
      </if>
      <if test="platformBeans != null">
        platform_beans = #{platformBeans},
      </if>
      <if test="totalSilver != null">
        total_silver = #{totalSilver},
      </if>
    </set>
    where create_time = #{createTime}
      and tid = #{tid}
  </update>
  <update id="updateByPrimaryKey" parameterType="FChatConsume">
    update f_chat_consume
    set relation_id = #{relationId},
      from_user_id = #{fromUserId},
      to_user_id = #{toUserId},
      source_type = #{sourceType},
      content_type = #{contentType},
      bill_user_id = #{billUserId},
      prod_id = #{ProdId},
      prod_cnt = #{prodCnt},
      total_beans = #{totalBeans},
      platform_beans = #{platformBeans},
      total_silver = #{totalSilver}
    where create_time = #{createTime}
      and tid = #{tid}
  </update>
</mapper>