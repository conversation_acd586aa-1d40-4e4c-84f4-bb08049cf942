<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.FRelationCpMomentHisMapper">
  <sql id="Base_Column_List">
    log_id, moment_id, relation_id, type, content, create_time, log_create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="FRelationCpMomentHis" resultType="FRelationCpMomentHis">
    select 
    <include refid="Base_Column_List" />
    from f_relation_cp_moment_his
    where log_id = #{logId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="FRelationCpMomentHis">
    delete from f_relation_cp_moment_his
    where log_id = #{logId}
  </delete>
  <insert id="insert" parameterType="FRelationCpMomentHis">
    insert into f_relation_cp_moment_his (log_id, moment_id, relation_id, type, content, create_time, 
      log_create_time)
    values (#{logId}, #{momentId}, #{relationId}, #{type}, #{content}, #{createTime}, 
      #{logCreateTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="FRelationCpMomentHis">
    update f_relation_cp_moment_his
    <set>
      <if test="momentId != null">
        moment_id = #{momentId},
      </if>
      <if test="relationId != null">
        relation_id = #{relationId},
      </if>
      <if test="type != null">
        type = #{type},
      </if>
      <if test="content != null">
        content = #{content},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="logCreateTime != null">
        log_create_time = #{logCreateTime},
      </if>
    </set>
    where log_id = #{logId}
  </update>
  <update id="updateByPrimaryKey" parameterType="FRelationCpMomentHis">
    update f_relation_cp_moment_his
    set moment_id = #{momentId},
      relation_id = #{relationId},
      type = #{type},
      content = #{content},
      create_time = #{createTime},
      log_create_time = #{logCreateTime}
    where log_id = #{logId}
  </update>

  <insert id="insertBatch" parameterType="java.lang.String">
  INSERT INTO f_relation_cp_moment_his ( moment_id, relation_id, type, content, create_time, log_create_time )
  SELECT
  moment_id,
  relation_id,
  type,
  content,
  create_time,
  NOW()
  FROM
  f_relation_cp_moment
  WHERE
  relation_id = #{relationId}
  </insert>

</mapper>