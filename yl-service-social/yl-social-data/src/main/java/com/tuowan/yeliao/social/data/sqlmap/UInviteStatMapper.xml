<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.UInviteStatMapper">
  <sql id="Base_Column_List">
    user_id, total_invite, total_cash, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="UInviteStat" resultType="UInviteStat">
    select 
    <include refid="Base_Column_List" />
    from u_invite_stat
    where user_id = #{userId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="UInviteStat">
    delete from u_invite_stat
    where user_id = #{userId}
  </delete>
  <insert id="insert" parameterType="UInviteStat">
    insert into u_invite_stat (user_id, total_invite, total_cash, create_time)
    values (#{userId}, #{totalInvite}, #{totalCash}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="UInviteStat">
    update u_invite_stat
    <set>
      <if test="totalInvite != null">
        total_invite = total_invite + #{totalInvite},
      </if>
      <if test="totalCash != null">
        total_cash = total_cash + #{totalCash},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where user_id = #{userId}
  </update>
  <update id="updateByPrimaryKey" parameterType="UInviteStat">
    update u_invite_stat
    set total_invite = #{totalInvite},
      total_cash = #{totalCash},
      create_time = #{createTime}
    where user_id = #{userId}
  </update>

  <select id="queryTotalCashRank" resultType="UInviteStat">
    select
    <include refid="Base_Column_List" />
    from u_invite_stat
    order by total_cash desc
    limit #{offset},#{limit}
  </select>
</mapper>