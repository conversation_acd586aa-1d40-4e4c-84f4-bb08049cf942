<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.family.CFamilyEnterLogMapper">
  <sql id="Base_Column_List">
    create_time, tid, family_id, user_id, my_family_id, source_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="CFamilyEnterLog" resultType="CFamilyEnterLog">
    select 
    <include refid="Base_Column_List" />
    from c_family_enter_log
    where create_time = #{createTime}
      and tid = #{tid}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="CFamilyEnterLog">
    delete from c_family_enter_log
    where create_time = #{createTime}
      and tid = #{tid}
  </delete>
  <insert id="insert" parameterType="CFamilyEnterLog">
    insert into c_family_enter_log (create_time, tid, family_id, user_id, my_family_id, source_type
      )
    values (#{createTime}, #{tid}, #{familyId}, #{userId}, #{myFamilyId}, #{sourceType}
      )
  </insert>
    <update id="updateByPrimaryKeySelective" parameterType="CFamilyEnterLog">
    update c_family_enter_log
    <set>
      <if test="familyId != null">
        family_id = #{familyId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="myFamilyId != null">
        my_family_id = #{myFamilyId},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType},
      </if>
    </set>
    where create_time = #{createTime}
      and tid = #{tid}
  </update>
  <update id="updateByPrimaryKey" parameterType="CFamilyEnterLog">
    update c_family_enter_log
    set family_id = #{familyId},
      user_id = #{userId},
      my_family_id = #{myFamilyId},
      source_type = #{sourceType}
    where create_time = #{createTime}
      and tid = #{tid}
  </update>
  <insert id="insertBatch">
    insert into c_family_enter_log
    (create_time, tid, family_id, user_id, my_family_id, source_type )
    values
    <foreach collection ="list" item="t" index= "index" separator =",">
      (#{t.createTime}, #{t.tid}, #{t.familyId}, #{t.userId}, #{t.myFamilyId}, #{t.sourceType} )
    </foreach >
  </insert>

</mapper>