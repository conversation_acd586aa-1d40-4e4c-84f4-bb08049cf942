<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.family.FFamilyApplyMapper">
  <sql id="Base_Column_List">
    log_id, family_id, apply_type, family_name, family_desc, cover_pic,city,lat,lng, status, audit_user_id,
    audit_time, audit_reason, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="FFamilyApply" resultType="FFamilyApply">
    select 
    <include refid="Base_Column_List" />
    from f_family_apply
    where log_id = #{logId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="FFamilyApply">
    delete from f_family_apply
    where log_id = #{logId}
  </delete>
  <insert id="insert" parameterType="FFamilyApply">
    insert into f_family_apply (log_id, family_id, apply_type, family_name, family_desc, cover_pic, 
     city,lat,lng, status, audit_user_id, audit_time, audit_reason, creator, create_time
      )
    values (#{logId}, #{familyId}, #{applyType}, #{familyName}, #{familyDesc}, #{coverPic}, 
     #{city},#{lat},#{lng}, #{status}, #{auditUserId}, #{auditTime}, #{auditReason}, #{creator}, #{createTime}
      )
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="FFamilyApply">
    update f_family_apply
    <set>
      <if test="familyId != null">
        family_id = #{familyId},
      </if>
      <if test="applyType != null">
        apply_type = #{applyType},
      </if>
      <if test="familyName != null">
        family_name = #{familyName},
      </if>
      <if test="familyDesc != null">
        family_desc = #{familyDesc},
      </if>
      <if test="coverPic != null">
        cover_pic = #{coverPic},
      </if>
      <if test="city != null">
        city = #{city},
      </if>
      <if test="lat != null">
        lat = #{lat},
      </if>
      <if test="lng != null">
        lng = #{lng},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="auditUserId != null">
        audit_user_id = #{auditUserId},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime},
      </if>
      <if test="auditReason != null">
        audit_reason = #{auditReason},
      </if>
      <if test="creator != null">
        creator = #{creator},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where log_id = #{logId}
  </update>
  <update id="updateByPrimaryKey" parameterType="FFamilyApply">
    update f_family_apply
    set family_id = #{familyId},
      apply_type = #{applyType},
      family_name = #{familyName},
      family_desc = #{familyDesc},
      cover_pic = #{coverPic},
      city = #{city},
      lat = #{lat},
      lng = #{lng},
      status = #{status},
      audit_user_id = #{auditUserId},
      audit_time = #{auditTime},
      audit_reason = #{auditReason},
      creator = #{creator},
      create_time = #{createTime}
    where log_id = #{logId}
  </update>

  <select id="selectWaitByUserId" parameterType="map" resultType="FFamilyApply">
    select
    <include refid="Base_Column_List" />
    from f_family_apply
    where creator = #{userId}
    AND status = 'W'
  </select>

  <select id="selectWaitByFamilyId" parameterType="map" resultType="FFamilyApply">
    select
    <include refid="Base_Column_List" />
    from f_family_apply
    where family_id = #{familyId}
    AND status = 'W'
  </select>

</mapper>