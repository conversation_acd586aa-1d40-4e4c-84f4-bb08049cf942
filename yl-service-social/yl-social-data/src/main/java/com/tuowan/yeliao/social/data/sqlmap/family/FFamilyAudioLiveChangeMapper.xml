<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.family.FFamilyAudioLiveChangeMapper">
    <sql id="Base_Column_List">
        log_id, show_tid, family_id, action_type, show_title, create_time, creator
    </sql>
    <select id="selectByPrimaryKey" parameterType="FFamilyAudioLiveChange" resultType="FFamilyAudioLiveChange">
        select
        <include refid="Base_Column_List"/>
        from f_family_audio_live_change
        where log_id = #{logId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="FFamilyAudioLiveChange">
        delete from f_family_audio_live_change
        where log_id = #{logId}
    </delete>
    <insert id="insert" parameterType="FFamilyAudioLiveChange">
        insert into f_family_audio_live_change (log_id, show_tid, family_id, action_type, show_title, create_time,
        creator)
        values (#{logId}, #{showTid}, #{familyId}, #{actionType}, #{showTitle}, #{createTime},
        #{creator})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="FFamilyAudioLiveChange">
        update f_family_audio_live_change
        <set>
            <if test="showTid != null">
                show_tid = #{showTid},
            </if>
            <if test="familyId != null">
                family_id = #{familyId},
            </if>
            <if test="actionType != null">
                action_type = #{actionType},
            </if>
            <if test="showTitle != null">
                show_title = #{showTitle},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="creator != null">
                creator = #{creator},
            </if>
        </set>
        where log_id = #{logId}
    </update>
    <update id="updateByPrimaryKey" parameterType="FFamilyAudioLiveChange">
        update f_family_audio_live_change
        set show_tid = #{showTid},
        family_id = #{familyId},
        action_type = #{actionType},
        show_title = #{showTitle},
        create_time = #{createTime},
        creator = #{creator}
        where log_id = #{logId}
    </update>
</mapper>