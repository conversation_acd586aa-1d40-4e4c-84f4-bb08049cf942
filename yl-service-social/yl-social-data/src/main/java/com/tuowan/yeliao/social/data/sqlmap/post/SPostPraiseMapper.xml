<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.post.SPostPraiseMapper">
  <sql id="Base_Column_List">
    post_id, user_id, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="SPostPraise" resultType="SPostPraise">
    select 
    <include refid="Base_Column_List" />
    from s_post_praise
    where post_id = #{postId}
      and user_id = #{userId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="SPostPraise">
    delete from s_post_praise
    where post_id = #{postId}
      and user_id = #{userId}
  </delete>
  <insert id="insert" parameterType="SPostPraise">
    insert into s_post_praise (post_id, user_id, create_time)
    values (#{postId}, #{userId}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="SPostPraise">
    update s_post_praise
    <set>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where post_id = #{postId}
      and user_id = #{userId}
  </update>
  <update id="updateByPrimaryKey" parameterType="SPostPraise">
    update s_post_praise
    set create_time = #{createTime}
    where post_id = #{postId}
    and user_id = #{userId}
  </update>
  <select id="selectByUserId" parameterType="SPostPraise" resultType="SPostPraise">
    select post_id
    from s_post_praise
    where user_id = #{userId}
  </select>
  <select id="selectByPostId" resultType="SPostPraise">
    select
    <include refid="Base_Column_List"/>
      from s_post_praise
      where post_id = #{postId}
      order by create_time desc,user_id desc limit #{offset}, #{limit}
  </select>
  <select id="selectFirstPraise" parameterType="SPostPraise" resultType="SPostPraise">
      select
      <include refid="Base_Column_List"/>
      from s_post_praise
      where post_id = #{postId}
      order by create_time asc
      limit 1
  </select>
  <select id="selectPraisePostIds" resultType="long">
      select post_id from s_post_praise
      where user_id = #{userId} and post_id in
      <foreach collection="postIds" item="postId" open="(" separator="," close=")">
          #{postId}
      </foreach>
  </select>

</mapper>