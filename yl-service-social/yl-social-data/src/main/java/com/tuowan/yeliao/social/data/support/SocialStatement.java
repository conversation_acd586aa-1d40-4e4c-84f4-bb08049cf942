package com.tuowan.yeliao.social.data.support;

import com.tuowan.yeliao.commons.config.mybatis.dispatch.Statement;
import com.tuowan.yeliao.social.data.persistence.FChatConsumeWaitMapper;
import com.tuowan.yeliao.social.data.persistence.FRelationBasicMapper;
import com.tuowan.yeliao.social.data.persistence.query.CommQueryMapper;

/**
 * 社交分页查询枚举定义
 *
 * <AUTHOR>
 * @date 2022/4/18 13:43
 */
public enum SocialStatement implements Statement {

    /** 查询过期的聊天待提成记录 */
    ChatConsumeWaitExpired(FChatConsumeWaitMapper.class, "selectExpiredConsumeWait"),
    /** 查询亲密度降级的用户 */
    IntimateDown(FRelationBasicMapper.class, "selectNeedIntimateDownResults"),

    /** 获取聊主昨日收益信息，用于判断聊主等级晋升 */
    SelectChatMasterIncome(CommQueryMapper.class, "selectChatMasterIncome"),
    /** 获取能升级C级聊主的用户 */
    SelectChatMasterLevelToCUsers(CommQueryMapper.class, "selectChatMasterLevelToCUsers"),
    /** 查询需要进行聊主等级下降的用户ID */
    SelectNeedChatMasterLevelDownUsers(CommQueryMapper.class, "selectNeedChatMasterLevelDownUsers"),
    ;

    private String selectId;
    private Class<?> mapperClass;

    SocialStatement(Class<?> mapperClass, String selectId) {
        this.mapperClass = mapperClass;
        this.selectId = selectId;
    }

    @Override
    public String getSelectId() {
        return this.selectId;
    }

    @Override
    public Class<?> getMapperClass() {
        return this.mapperClass;
    }
}
