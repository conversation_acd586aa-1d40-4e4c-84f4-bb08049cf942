package com.tuowan.yeliao.social.data;

import com.tuowan.tools.mybatis.YCMyBatisGenerator;
import com.tuowan.tools.mybatis.exception.GeneratorException;
import org.mybatis.generator.logging.LogFactory;

import java.io.InputStream;

/**
 * Mybatis生成器
 *
 * <AUTHOR>
 * @date 2018/6/29 11:33
 */
public class MybatisGenerator {
    static final String DEFAULT_CONFIGURATION_PATH = "generatorConfiguration.xml";
    static final String DEFAULT_CONFIGURATION_TABLES_PATH = "generatorConfiguration-tables.xml";

    static final String DEFAULT_CLASSPATH = "/";


    private static void runIt() throws GeneratorException {

        InputStream mybatisStream = MybatisGenerator.class.getResourceAsStream(DEFAULT_CLASSPATH
                + DEFAULT_CONFIGURATION_PATH);
        InputStream ycStream = MybatisGenerator.class.getResourceAsStream(DEFAULT_CLASSPATH
                + DEFAULT_CONFIGURATION_TABLES_PATH);

        try {
            new YCMyBatisGenerator().generator(mybatisStream, ycStream, true);
        } catch (GeneratorException e) {
            throw new RuntimeException("Generator Modules failure.", e);
        }
    }


    public static void main(String[] args) {
        try {
            runIt();
            LogFactory.getLog(MybatisGenerator.class).warn("completed.");
        } catch (GeneratorException e) {
            e.printStackTrace();
        }
    }
}
