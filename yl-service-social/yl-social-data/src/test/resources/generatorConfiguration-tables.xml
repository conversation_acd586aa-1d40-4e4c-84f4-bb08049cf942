<?xml version="1.0" encoding="UTF-8"?>

<!--  Add Tables Group for MyBatis Generator Configuration,
	No DTD check -->
<generatorConfiguration>

    <project path="./yl-service-social/yl-social-data"/>

    <queryTables>show tables</queryTables>

    <properties>
        <propery name="example" value="false"/>
    </properties>

    <target type="mapper" sources="/src/main/java"
            package="com.tuowan.yeliao.social.data.persistence"/>

    <target type="domain" sources="./yl-service-social/yl-social-entity/src/main/java"
            package="com.tuowan.yeliao.social.data.entity" appendPath="false"/>

    <target type="sqlmap" sources="/src/main/java"
            package="com.tuowan.yeliao.social.data.sqlmap"/>

    <!--
        The table element equals match
        The exclude element equals isn't match
        The include element startWith match
     -->
    <modules>
        <!-- lm-config -->
        <module subPackage="">
            <!--<include startWith="" />-->
            <table name="g_animal_principal"></table>
            <table name="g_animal_principal_log"></table>
        </module>
    </modules>
</generatorConfiguration>