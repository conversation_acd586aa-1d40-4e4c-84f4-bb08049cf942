/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FAnonyVoiceLog")
public class FAnonyVoiceLog {
    /** 通话ID */
    @KeyProperty
    private Long callId;

    /** 用户ID */
    private Long userId;

    /** 好友ID */
    private Long friendId;

    /** 匹配时间 */
    private Date matchTime;

    /** 接通时间 */
    private Date connectTime;

    /** 结束时间 */
    private Date finishTime;

    /** 挂断人ID */
    private Long hangUpId;

    /** 通话时长(秒) */
    private Integer duration;

    public FAnonyVoiceLog() {
        
    }

    /** 根据主键初始化实例 **/
    public FAnonyVoiceLog(Long callId) {
        this.callId = callId;
    }

    public Long getCallId() {
        return callId;
    }

    public void setCallId(Long callId) {
        this.callId = callId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }

    public Date getMatchTime() {
        return matchTime;
    }

    public void setMatchTime(Date matchTime) {
        this.matchTime = matchTime;
    }

    public Date getConnectTime() {
        return connectTime;
    }

    public void setConnectTime(Date connectTime) {
        this.connectTime = connectTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Long getHangUpId() {
        return hangUpId;
    }

    public void setHangUpId(Long hangUpId) {
        this.hangUpId = hangUpId;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }
}