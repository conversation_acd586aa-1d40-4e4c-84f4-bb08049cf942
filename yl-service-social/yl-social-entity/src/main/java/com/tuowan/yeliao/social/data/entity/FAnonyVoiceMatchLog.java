/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FAnonyVoiceMatchLog")
public class FAnonyVoiceMatchLog {
    /** 日志ID */
    @KeyProperty
    private Long logId;

    /** 匹配时间 */
    private Date matchTime;

    /** 用户ID */
    private Long userId;

    /** 通话ID */
    private Long callId;

    /** 接通时间 */
    private Date connectTime;

    public FAnonyVoiceMatchLog() {
        
    }

    /** 根据主键初始化实例 **/
    public FAnonyVoiceMatchLog(Long logId) {
        this.logId = logId;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Date getMatchTime() {
        return matchTime;
    }

    public void setMatchTime(Date matchTime) {
        this.matchTime = matchTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getCallId() {
        return callId;
    }

    public void setCallId(Long callId) {
        this.callId = callId;
    }

    public Date getConnectTime() {
        return connectTime;
    }

    public void setConnectTime(Date connectTime) {
        this.connectTime = connectTime;
    }
}