/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.data.enums.social.NetCallType;
import com.tuowan.yeliao.social.data.enums.call.CallEvaluateType;
import com.tuowan.yeliao.social.data.enums.friend.NetCallFinishType;
import com.tuowan.yeliao.social.data.enums.friend.NetCallSourceType;
import com.tuowan.yeliao.social.data.enums.friend.NetCallStatus;
import com.tuowan.yeliao.social.data.enums.friend.NetCallWaiverType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FChatNetCall")
public class FChatNetCall {
    /**
     * 通话ID
     */
    @KeyProperty
    private Long callId;

    /**
     * 业务流水号
     */
    private Long tid;

    /**
     * 通话类型 NetCallType
     * A(语音通话)
     * V(视频通话)
     */
    private NetCallType callType;

    /**
     * 主叫ID
     */
    private Long userId;

    /**
     * 被叫ID
     */
    private Long friendId;

    /**
     * 付费用户ID
     */
    private Long billUserId;

    /**
     * 呼叫时间
     */
    private Date callTime;

    /**
     * 响铃时间(被叫)
     */
    private Date ringTime;

    /**
     * 接通时间
     */
    private Date connectTime;

    /**
     * 结束时间
     */
    private Date finishTime;

    /**
     * 结束类型 NetCallFinishType
     * N(正常结束)
     * H(心跳超时)
     * B(余额不足)
     * I(通话违规)
     */
    private NetCallFinishType finishType;

    /**
     * 挂断用户ID
     */
    private Long hangUpId;

    /**
     * 通话单价(金币)
     */
    private Integer beans;

    /**
     * 通话时长(秒)
     */
    private Integer duration;

    /**
     * 客户端通话时长
     */
    private Integer clientDuration;

    /**
     * 通话总金币
     */
    private Integer totalBeans;

    /**
     * 预扣总金币
     */
    private Integer preTotalBeans;

    /**
     * 实扣总金币
     */
    private Integer actTotalBeans;

    /**
     * 零钱提成
     */
    private Integer presentCash;

    /**
     * 通话状态 NetCallStatus
     * CT(已创建)
     * WT(待接通)
     * CA(呼叫取消)
     * TM(呼叫超时)
     * RG(被叫响铃)
     * RF(被叫拒绝)
     * AP(被叫接受)
     * FS(已结束)
     */
    private NetCallStatus status;

    /**
     * 通话状态备注
     */
    private String statusRemark;

    /**
     * 通话状态时间
     */
    private Date statusTime;

    /**
     * 通话来源
     */
    private NetCallSourceType sourceType;

    /**
     * 商品Id
     */
    private Integer prodId;

    /**
     * 使用了几张免费券
     */
    private Integer useBagCount;

    /**
     * 通话费用免除类型
     */
    private NetCallWaiverType waiverType;

    /**
     * 通话评分
     */
    private CallEvaluateType evaluateType;

    public FChatNetCall() {

    }

    /**
     * 根据主键初始化实例
     **/
    public FChatNetCall(Long callId) {
        this.callId = callId;
    }

    public Long getCallId() {
        return callId;
    }

    public void setCallId(Long callId) {
        this.callId = callId;
    }

    public Long getTid() {
        return tid;
    }

    public void setTid(Long tid) {
        this.tid = tid;
    }

    public NetCallType getCallType() {
        return callType;
    }

    public void setCallType(NetCallType callType) {
        this.callType = callType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }

    public Long getBillUserId() {
        return billUserId;
    }

    public void setBillUserId(Long billUserId) {
        this.billUserId = billUserId;
    }

    public Date getCallTime() {
        return callTime;
    }

    public void setCallTime(Date callTime) {
        this.callTime = callTime;
    }

    public Date getRingTime() {
        return ringTime;
    }

    public void setRingTime(Date ringTime) {
        this.ringTime = ringTime;
    }

    public Date getConnectTime() {
        return connectTime;
    }

    public void setConnectTime(Date connectTime) {
        this.connectTime = connectTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public NetCallFinishType getFinishType() {
        return finishType;
    }

    public void setFinishType(NetCallFinishType finishType) {
        this.finishType = finishType;
    }

    public Long getHangUpId() {
        return hangUpId;
    }

    public void setHangUpId(Long hangUpId) {
        this.hangUpId = hangUpId;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getClientDuration() {
        return clientDuration;
    }

    public void setClientDuration(Integer clientDuration) {
        this.clientDuration = clientDuration;
    }

    public Integer getTotalBeans() {
        return totalBeans;
    }

    public void setTotalBeans(Integer totalBeans) {
        this.totalBeans = totalBeans;
    }

    public Integer getPreTotalBeans() {
        return preTotalBeans;
    }

    public void setPreTotalBeans(Integer preTotalBeans) {
        this.preTotalBeans = preTotalBeans;
    }

    public Integer getActTotalBeans() {
        return actTotalBeans;
    }

    public void setActTotalBeans(Integer actTotalBeans) {
        this.actTotalBeans = actTotalBeans;
    }

    public Integer getPresentCash() {
        return presentCash;
    }

    public void setPresentCash(Integer presentCash) {
        this.presentCash = presentCash;
    }

    public NetCallStatus getStatus() {
        return status;
    }

    public void setStatus(NetCallStatus status) {
        this.status = status;
    }

    public String getStatusRemark() {
        return statusRemark;
    }

    public void setStatusRemark(String statusRemark) {
        this.statusRemark = statusRemark;
    }

    public Date getStatusTime() {
        return statusTime;
    }

    public void setStatusTime(Date statusTime) {
        this.statusTime = statusTime;
    }

    public NetCallSourceType getSourceType() {
        return sourceType;
    }

    public void setSourceType(NetCallSourceType sourceType) {
        this.sourceType = sourceType;
    }

    public Integer getProdId() {
        return prodId;
    }

    public void setProdId(Integer prodId) {
        this.prodId = prodId;
    }

    public Integer getUseBagCount() {
        return useBagCount;
    }

    public void setUseBagCount(Integer useBagCount) {
        this.useBagCount = useBagCount;
    }

    public NetCallWaiverType getWaiverType() {
        return waiverType;
    }

    public void setWaiverType(NetCallWaiverType waiverType) {
        this.waiverType = waiverType;
    }

    public CallEvaluateType getEvaluateType() {
        return evaluateType;
    }

    public void setEvaluateType(CallEvaluateType evaluateType) {
        this.evaluateType = evaluateType;
    }
}