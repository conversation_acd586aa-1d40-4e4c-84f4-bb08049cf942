/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.data.enums.social.NetCallType;
import com.tuowan.yeliao.social.data.enums.friend.NetCallMatchStatus;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FChatNetCallMatch")
public class FChatNetCallMatch {
    /**
     * 记录ID
     */
    @KeyProperty
    private Long logId;

    /**
     * 创建时间
     */
    @KeyProperty
    private Date createTime;

    /**
     * 通话类型
     */
    private NetCallType callType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 好友ID
     */
    private Long friendId;

    /**
     * 派单时间
     */
    private Date dispatchTime;

    /**
     * 通话ID
     */
    private Long callId;

    /**
     * 呼叫时间
     */
    private Date callTime;

    /**
     * 接通时间
     */
    private Date connectTime;

    /**
     * 匹配状态
     * NetCallMatchStatus
     * W(等待接受)
     * T(匹配超时)
     * R(拒绝邀请)
     * A(接受邀请)
     * S(接通成功)
     */
    private NetCallMatchStatus status;

    /**
     * 状态时间
     */
    private Date statusTime;

    public FChatNetCallMatch() {

    }

    /**
     * 根据主键初始化实例
     **/
    public FChatNetCallMatch(Long logId, Date createTime) {
        this.logId = logId;
        this.createTime = createTime;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public NetCallType getCallType() {
        return callType;
    }

    public void setCallType(NetCallType callType) {
        this.callType = callType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }

    public Date getDispatchTime() {
        return dispatchTime;
    }

    public void setDispatchTime(Date dispatchTime) {
        this.dispatchTime = dispatchTime;
    }

    public Long getCallId() {
        return callId;
    }

    public void setCallId(Long callId) {
        this.callId = callId;
    }

    public Date getCallTime() {
        return callTime;
    }

    public void setCallTime(Date callTime) {
        this.callTime = callTime;
    }

    public Date getConnectTime() {
        return connectTime;
    }

    public void setConnectTime(Date connectTime) {
        this.connectTime = connectTime;
    }

    public NetCallMatchStatus getStatus() {
        return status;
    }

    public void setStatus(NetCallMatchStatus status) {
        this.status = status;
    }

    public Date getStatusTime() {
        return statusTime;
    }

    public void setStatusTime(Date statusTime) {
        this.statusTime = statusTime;
    }
}