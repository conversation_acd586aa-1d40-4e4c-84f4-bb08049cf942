/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.Group;
import com.easyooo.framework.cache.annotations.KeyProperty;
import java.util.Date;

import com.tuowan.yeliao.social.data.enums.friend.RedPacketStatus;
import com.tuowan.yeliao.social.data.enums.friend.RedPacketType;
import org.apache.ibatis.type.Alias;

@Alias("FChatRedPacket")
@Cache(expire = 24 * 3600)
public class FChatRedPacket {
    /** 记录ID */
    @KeyProperty
    private Long id;

    /** 标识 */
    @Group
    private String sign;

    /** 红包类型 */
    private RedPacketType type;

    /** 发送方 */
    private Long userId;

    /** 总金币 */
    private Long totalBeans;

    /** 进奖池金币 */
    private Long pondBeans;

    /** 剩余金币 */
    private Long surplusBeans;

    /** 退回金币 */
    private Long returnBeans;

    /** 红包总个数 */
    private Integer totalNum;

    /** 红包剩余个数 */
    private Integer surplusNum;

    /** 红包文案 */
    private String desc;

    /** 对象用户 */
    private Long objId;

    /** 红包状态 */
    private RedPacketStatus status;

    /** 状态时间 */
    private Date statusTime;

    /** 可领取时间 */
    private Date receiveTime;

    /** 过期时间 */
    private Date expireTime;

    /** 创建时间 */
    private Date createTime;

    public FChatRedPacket() {
        
    }

    /** 根据主键初始化实例 **/
    public FChatRedPacket(Long id) {
        this.id = id;
    }

    public FChatRedPacket(String sign) {
        this.sign = sign;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign == null ? null : sign.trim();
    }

    public RedPacketType getType() {
        return type;
    }

    public void setType(RedPacketType type) {
        this.type = type;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getTotalBeans() {
        return totalBeans;
    }

    public void setTotalBeans(Long totalBeans) {
        this.totalBeans = totalBeans;
    }

    public Long getSurplusBeans() {
        return surplusBeans;
    }

    public void setSurplusBeans(Long surplusBeans) {
        this.surplusBeans = surplusBeans;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public Integer getSurplusNum() {
        return surplusNum;
    }

    public void setSurplusNum(Integer surplusNum) {
        this.surplusNum = surplusNum;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc == null ? null : desc.trim();
    }

    public Long getObjId() {
        return objId;
    }

    public void setObjId(Long objId) {
        this.objId = objId;
    }

    public RedPacketStatus getStatus() {
        return status;
    }

    public void setStatus(RedPacketStatus status) {
        this.status = status;
    }

    public Date getStatusTime() {
        return statusTime;
    }

    public void setStatusTime(Date statusTime) {
        this.statusTime = statusTime;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getPondBeans() {
        return pondBeans;
    }

    public void setPondBeans(Long pondBeans) {
        this.pondBeans = pondBeans;
    }

    public Long getReturnBeans() {
        return returnBeans;
    }

    public void setReturnBeans(Long returnBeans) {
        this.returnBeans = returnBeans;
    }
}