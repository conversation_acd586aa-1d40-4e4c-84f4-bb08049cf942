/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.KeyProperty;
import java.util.Date;
import org.apache.ibatis.type.Alias;

@Alias("FChatRedPacketLog")
@Cache(expire = 600)
public class FChatRedPacketLog {
    /** 红包标识id */
    @KeyProperty
    private Long id;

    /** 领取用户 */
    @KeyProperty
    private Long userId;

    /** 领取到的金币 */
    private Long beans;

    /** 金币对应的积分 */
    private Long cash;

    /** 领取时间 */
    private Date createTime;

    public FChatRedPacketLog() {
        
    }

    /** 根据主键初始化实例 **/
    public FChatRedPacketLog(Long id, Long userId) {
        this.id = id;
        this.userId = userId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getBeans() {
        return beans;
    }

    public void setBeans(Long beans) {
        this.beans = beans;
    }

    public Long getCash() {
        return cash;
    }

    public void setCash(Long cash) {
        this.cash = cash;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}