/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FCpClock")
public class FCpClock {
    /**
     * 情侣关系ID
     */
    @KeyProperty
    private String relationId;

    /**
     * 打卡日期
     */
    @KeyProperty
    private Date clockDate;

    /**
     * 男性用户打卡时间
     */
    private Date maleClockTime;

    /**
     * 女性用户打卡时间
     */
    private Date femaleClockTime;

    public FCpClock() {

    }

    /**
     * 根据主键初始化实例
     **/
    public FCpClock(String relationId, Date clockDate) {
        this.relationId = relationId;
        this.clockDate = clockDate;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public Date getClockDate() {
        return clockDate;
    }

    public void setClockDate(Date clockDate) {
        this.clockDate = clockDate;
    }

    public Date getMaleClockTime() {
        return maleClockTime;
    }

    public void setMaleClockTime(Date maleClockTime) {
        this.maleClockTime = maleClockTime;
    }

    public Date getFemaleClockTime() {
        return femaleClockTime;
    }

    public void setFemaleClockTime(Date femaleClockTime) {
        this.femaleClockTime = femaleClockTime;
    }
}