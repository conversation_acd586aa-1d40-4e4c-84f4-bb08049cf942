/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.easyooo.framework.common.util.DateUtils;
import com.tuowan.yeliao.social.data.enums.friend.LoveSpaceStatus;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FRelationCp")
public class FRelationCp {
    /**
     * 情侣关系ID,空间ID
     */
    @KeyProperty
    private String relationId;

    /**
     * 邀请人
     */
    private Long inviteUserId;

    /**
     * 接收人
     */
    private Long acceptUserId;

    /**
     * 空间状态，W（Wait,等待接受），S(Success,创建成功)
     */
    private LoveSpaceStatus status;

    /**
     * 邀请时间
     */
    private Date inviteTime;

    /**
     * 接受时间
     */
    private Date acceptTime;

    /**
     * 甜蜜值
     */
    private Integer sweetValue;

    public FRelationCp() {

    }

    /**
     * 根据主键初始化实例
     **/
    public FRelationCp(String relationId) {
        this.relationId = relationId;
    }

    public static FRelationCp create(String relationId, Long userId, Long friendId) {
        FRelationCp cp = new FRelationCp(relationId);
        cp.setInviteUserId(userId);
        cp.setAcceptUserId(friendId);
        cp.setInviteTime(DateUtils.nowTime());
        cp.setStatus(LoveSpaceStatus.WaitAccept);
        cp.setSweetValue(0);
        return cp;
    }

    public static FRelationCp createAuto(String relationId, Long userId, Long friendId) {
        FRelationCp cp = new FRelationCp(relationId);
        cp.setInviteUserId(userId);
        cp.setAcceptUserId(friendId);
        cp.setInviteTime(DateUtils.nowTime());
        cp.setStatus(LoveSpaceStatus.Open);
        cp.setSweetValue(0);
        cp.setInviteTime(new Date());
        cp.setAcceptTime(new Date());
        return cp;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public Long getInviteUserId() {
        return inviteUserId;
    }

    public void setInviteUserId(Long inviteUserId) {
        this.inviteUserId = inviteUserId;
    }

    public Long getAcceptUserId() {
        return acceptUserId;
    }

    public void setAcceptUserId(Long acceptUserId) {
        this.acceptUserId = acceptUserId;
    }

    public LoveSpaceStatus getStatus() {
        return status;
    }

    public void setStatus(LoveSpaceStatus status) {
        this.status = status;
    }

    public Date getInviteTime() {
        return inviteTime;
    }

    public void setInviteTime(Date inviteTime) {
        this.inviteTime = inviteTime;
    }

    public Date getAcceptTime() {
        return acceptTime;
    }

    public void setAcceptTime(Date acceptTime) {
        this.acceptTime = acceptTime;
    }

    public Integer getSweetValue() {
        return sweetValue;
    }

    public void setSweetValue(Integer sweetValue) {
        this.sweetValue = sweetValue;
    }
}