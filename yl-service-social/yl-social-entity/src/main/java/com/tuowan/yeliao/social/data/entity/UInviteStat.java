/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("UInviteStat")
public class UInviteStat {
    /** 邀请人用户标识 */
    @KeyProperty
    private Long userId;

    /** 邀请总人数 */
    private Long totalInvite;

    /** 总收入 单位毫 */
    private Long totalCash;

    /** 创建时间 */
    private Date createTime;

    public static UInviteStat init(Long userId){
        UInviteStat stat = new UInviteStat(userId);
        stat.setTotalInvite(0L);
        stat.setTotalCash(0L);
        stat.setCreateTime(new Date());
        return stat;
    }


    public UInviteStat() {
        
    }

    /** 根据主键初始化实例 **/
    public UInviteStat(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getTotalInvite() {
        return totalInvite;
    }

    public void setTotalInvite(Long totalInvite) {
        this.totalInvite = totalInvite;
    }

    public Long getTotalCash() {
        return totalCash;
    }

    public void setTotalCash(Long totalCash) {
        this.totalCash = totalCash;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}