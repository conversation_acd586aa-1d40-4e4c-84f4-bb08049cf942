/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import org.apache.ibatis.type.Alias;

import java.util.Date;
import java.util.Objects;

@Alias("USocialWishConfig")
@Cache(expire = 24 * 3600)
public class USocialWishConfig {
    /** 用户ID */
    @KeyProperty
    private Long userId;

    /** 筛选的最小年龄 */
    private Integer minAge;

    /** 筛选的最大年龄 */
    private Integer maxAge;

    /** 创建时间 */
    private Date createTime;

    public USocialWishConfig() {

    }

    /** 根据主键初始化实例 **/
    public USocialWishConfig(Long userId) {
        this.userId = userId;
    }

    public static USocialWishConfig createConfigDefault(Long userId) {
        USocialWishConfig config = new USocialWishConfig();
        config.setUserId(userId);
        config.setMinAge(GlobalConstant.SOCIAL_WISH_MIN_AGE);
        config.setMaxAge(GlobalConstant.SOCIAL_WISH_MAX_AGE);
        return config;
    }

    public static USocialWishConfig createDefault(Long userId) {
        USocialWishConfig config = new USocialWishConfig();
        config.setUserId(userId);
        config.setMinAge(GlobalConstant.SOCIAL_WISH_MIN_AGE);
        config.setMaxAge(GlobalConstant.SOCIAL_WISH_REAL_MAX_AGE);
        return config;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getMinAge() {
        return minAge;
    }

    public void setMinAge(Integer minAge) {
        this.minAge = minAge;
    }

    public Integer getMaxAge() {
        return maxAge;
    }

    public void setMaxAge(Integer maxAge) {
        this.maxAge = maxAge;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 是否默认配置
     *
     * @return
     */
    public boolean isDefaultConfig() {
        return Objects.equals(this.minAge, GlobalConstant.SOCIAL_WISH_MIN_AGE) && Objects.equals(this.maxAge, GlobalConstant.SOCIAL_WISH_MAX_AGE);
    }
}