/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.social.data.enums.user.GiftBagType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("UUserGiftBag")
public class UUserGiftBag {
    /**
     * 用户ID
     */
    @KeyProperty
    private Long userId;

    /**
     * 礼包类型
     */
    @KeyProperty
    private GiftBagType giftBagType;

    /**
     * 礼包对应的奖励代码
     */
    private String awardCode;

    /**
     * 领取状态
     */
    private BoolType receiveStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 领取时间
     */
    private Date receiveTime;

    public UUserGiftBag() {

    }

    /**
     * 根据主键初始化实例
     **/
    public UUserGiftBag(Long userId, GiftBagType giftBagType) {
        this.userId = userId;
        this.giftBagType = giftBagType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public GiftBagType getGiftBagType() {
        return giftBagType;
    }

    public void setGiftBagType(GiftBagType giftBagType) {
        this.giftBagType = giftBagType;
    }

    public String getAwardCode() {
        return awardCode;
    }

    public void setAwardCode(String awardCode) {
        this.awardCode = awardCode == null ? null : awardCode.trim();
    }

    public BoolType getReceiveStatus() {
        return receiveStatus;
    }

    public void setReceiveStatus(BoolType receiveStatus) {
        this.receiveStatus = receiveStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }
}