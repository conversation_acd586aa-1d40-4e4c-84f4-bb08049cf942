/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import java.util.Date;

import com.tuowan.yeliao.commons.data.enums.social.MatchType;
import org.apache.ibatis.type.Alias;

@Alias("UUserMatchStat")
public class UUserMatchStat {
    /** 主键 */
    @KeyProperty
    private Long logId;

    /** 男用户 */
    private Long maleUser;

    /** 女用户 */
    private Long femaleUser;

    /** 男用户数字等级 */
    private String mNumLevel;

    /** 女用户数字等级 */
    private String fNumLevel;

    /** 女用户字符等级 */
    private String fCharLevel;

    /** 类型 */
    private MatchType matchType;

    /** 搭讪时间 */
    private Date createTime;

    public UUserMatchStat() {
        
    }

    public static UUserMatchStat build(Long maleUser, Long femaleUser, String mNumLevel, String fNumLevel, String fCharLevel, MatchType matchType, Date createTime){
        UUserMatchStat stat = new UUserMatchStat();
        stat.setMaleUser(maleUser);
        stat.setFemaleUser(femaleUser);
        stat.setmNumLevel(mNumLevel);
        stat.setfNumLevel(fNumLevel);
        stat.setfCharLevel(fCharLevel);
        stat.setMatchType(matchType);
        stat.setCreateTime(createTime);
        return stat;
    }

    /** 根据主键初始化实例 **/
    public UUserMatchStat(Long logId) {
        this.logId = logId;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Long getMaleUser() {
        return maleUser;
    }

    public void setMaleUser(Long maleUser) {
        this.maleUser = maleUser;
    }

    public Long getFemaleUser() {
        return femaleUser;
    }

    public void setFemaleUser(Long femaleUser) {
        this.femaleUser = femaleUser;
    }

    public String getmNumLevel() {
        return mNumLevel;
    }

    public void setmNumLevel(String mNumLevel) {
        this.mNumLevel = mNumLevel == null ? null : mNumLevel.trim();
    }

    public String getfNumLevel() {
        return fNumLevel;
    }

    public void setfNumLevel(String fNumLevel) {
        this.fNumLevel = fNumLevel == null ? null : fNumLevel.trim();
    }

    public String getfCharLevel() {
        return fCharLevel;
    }

    public void setfCharLevel(String fCharLevel) {
        this.fCharLevel = fCharLevel == null ? null : fCharLevel.trim();
    }

    public MatchType getMatchType() {
        return matchType;
    }

    public void setMatchType(MatchType matchType) {
        this.matchType = matchType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}