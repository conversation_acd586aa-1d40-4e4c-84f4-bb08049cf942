/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity.family;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("CFamilySignLog")
public class CFamilySignLog {
    /**
     * 签到时间
     */
    @KeyProperty
    private Date createTime;

    /**
     * 流水号
     */
    @KeyProperty
    private Long tid;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 家族ID
     */
    private Integer familyId;

    public CFamilySignLog() {

    }

    /**
     * 根据主键初始化实例
     **/
    public CFamilySignLog(Date createTime, Long tid) {
        this.createTime = createTime;
        this.tid = tid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getTid() {
        return tid;
    }

    public void setTid(Long tid) {
        this.tid = tid;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }
}