/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity.family;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.easyooo.framework.cache.annotations.MiniTable;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.social.data.enums.family.FamilyJoinCondition;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FFamily")
@Cache(expire = 3 * 24 * 3600)
@MiniTable
public class FFamily {
    /**
     * 家族ID
     */
    @KeyProperty
    private Integer familyId;

    /**
     * 家族名称
     */
    private String familyName;

    /**
     * 家族等级
     */
    private Integer familyLevel;

    /**
     * 总贡献值
     */
    private Long totalContribution;

    /**
     * 最近一次贡献值刷新时间
     */
    private Date lastContributionTime;

    /**
     * 家族简介
     */
    private String familyDesc;

    /**
     * 家族封面
     */
    private String coverPic;

    /**
     * 成员数量
     */
    private Integer memberNum;

    /**
     * 加入的条件
     */
    private FamilyJoinCondition joinCondition;

    /**
     * 族长ID
     */
    private Long leaderId;

    /** 禁言状态 */
    private Status allMuteStatus;

    /** 家族标签ID */
    private String tagIds;

    /**
     * 创建时间
     */
    private Date createTime;

    /** 热度值 */
    private Long heatValue;

    /** 基础热度值 */
    private Long baseHeatValue;

    /** 基础热度值调整时间，用于计算基础热度值 */
    private Date baseHeatTime;

    /** 创建人 */
    private Long creator;

    /** 家族公告 */
    private String announcement;

    /** 家族所在城市 */
    private String city;

    /** 纬度值 */
    private String lat;

    /** 经度值 */
    private String lng;

    /** 冻结状态：T 冻结，F 未冻结 */
    private BoolType freeze;


    public FFamily() {

    }

    /**
     * 根据主键初始化实例
     **/
    public FFamily(Integer familyId) {
        this.familyId = familyId;
    }

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }

    public String getFamilyName() {
        return familyName;
    }

    public void setFamilyName(String familyName) {
        this.familyName = familyName == null ? null : familyName.trim();
    }

    public String getFamilyDesc() {
        return familyDesc;
    }

    public void setFamilyDesc(String familyDesc) {
        this.familyDesc = familyDesc == null ? null : familyDesc.trim();
    }

    public Integer getFamilyLevel() {
        return familyLevel;
    }

    public void setFamilyLevel(Integer familyLevel) {
        this.familyLevel = familyLevel;
    }

    public String getCoverPic() {
        return coverPic;
    }

    public void setCoverPic(String coverPic) {
        this.coverPic = coverPic == null ? null : coverPic.trim();
    }

    public Integer getMemberNum() {
        return memberNum;
    }

    public void setMemberNum(Integer memberNum) {
        this.memberNum = memberNum;
    }

    public FamilyJoinCondition getJoinCondition() {
        return joinCondition;
    }

    public void setJoinCondition(FamilyJoinCondition joinCondition) {
        this.joinCondition = joinCondition;
    }

    public Long getLeaderId() {
        return leaderId;
    }

    public void setLeaderId(Long leaderId) {
        this.leaderId = leaderId;
    }

    public Status getAllMuteStatus() {
        return allMuteStatus;
    }

    public void setAllMuteStatus(Status allMuteStatus) {
        this.allMuteStatus = allMuteStatus;
    }

    public String getTagIds() {
        return tagIds;
    }

    public void setTagIds(String tagIds) {
        this.tagIds = tagIds;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getTotalContribution() {
        return totalContribution;
    }

    public void setTotalContribution(Long totalContribution) {
        this.totalContribution = totalContribution;
    }

    public Date getLastContributionTime() {
        return lastContributionTime;
    }

    public void setLastContributionTime(Date lastContributionTime) {
        this.lastContributionTime = lastContributionTime;
    }

    public Long getHeatValue() {
        return heatValue;
    }

    public void setHeatValue(Long heatValue) {
        this.heatValue = heatValue;
    }

    public Long getBaseHeatValue() {
        return baseHeatValue;
    }

    public void setBaseHeatValue(Long baseHeatValue) {
        this.baseHeatValue = baseHeatValue;
    }

    public Date getBaseHeatTime() {
        return baseHeatTime;
    }

    public void setBaseHeatTime(Date baseHeatTime) {
        this.baseHeatTime = baseHeatTime;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public String getAnnouncement() {
        return announcement;
    }

    public void setAnnouncement(String announcement) {
        this.announcement = announcement;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public BoolType getFreeze() {
        return freeze;
    }

    public void setFreeze(BoolType freeze) {
        this.freeze = freeze;
    }
}