/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity.family;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.social.data.enums.family.FamilyActionType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FFamilyAudioLiveChange")
public class FFamilyAudioLiveChange {
    /**
     * 记录ID
     */
    @KeyProperty
    private Integer logId;

    /**
     * 开播流水号
     */
    private Long showTid;

    /**
     * 家族ID
     */
    private Integer familyId;

    /**
     * 家族动作
     */
    private FamilyActionType actionType;

    /**
     * 直播标题
     */
    private String showTitle;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Long creator;

    public FFamilyAudioLiveChange() {

    }

    /**
     * 根据主键初始化实例
     **/
    public FFamilyAudioLiveChange(Integer logId) {
        this.logId = logId;
    }

    public FFamilyAudioLiveChange(FFamilyAudioLive live, Long creator) {
        this.showTid = live.getShowTid();
        this.familyId = live.getFamilyId();
        this.actionType = live.getActionType();
        this.showTitle = live.getShowTitle();
        this.createTime = new Date();
        this.creator = creator;
    }

    public Integer getLogId() {
        return logId;
    }

    public void setLogId(Integer logId) {
        this.logId = logId;
    }

    public Long getShowTid() {
        return showTid;
    }

    public void setShowTid(Long showTid) {
        this.showTid = showTid;
    }

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }

    public FamilyActionType getActionType() {
        return actionType;
    }

    public void setActionType(FamilyActionType actionType) {
        this.actionType = actionType;
    }

    public String getShowTitle() {
        return showTitle;
    }

    public void setShowTitle(String showTitle) {
        this.showTitle = showTitle == null ? null : showTitle.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }
}