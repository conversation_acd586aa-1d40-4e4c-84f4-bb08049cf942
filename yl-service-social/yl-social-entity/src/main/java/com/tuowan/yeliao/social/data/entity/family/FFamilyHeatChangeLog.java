/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity.family;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FFamilyHeatChangeLog")
public class FFamilyHeatChangeLog {
    /**
     * 修改记录日志ID
     */
    @KeyProperty
    private Integer logId;

    /**
     * 处理人
     */
    private Long userId;

    /**
     * 家族ID
     */
    private Integer familyId;

    /**
     * 调整的热度值
     */
    private Long baseHeatValue;

    /**
     * 调整时间
     */
    private Date createTime;

    public FFamilyHeatChangeLog() {

    }

    /**
     * 根据主键初始化实例
     **/
    public FFamilyHeatChangeLog(Integer logId) {
        this.logId = logId;
    }

    public Integer getLogId() {
        return logId;
    }

    public void setLogId(Integer logId) {
        this.logId = logId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }

    public Long getBaseHeatValue() {
        return baseHeatValue;
    }

    public void setBaseHeatValue(Long baseHeatValue) {
        this.baseHeatValue = baseHeatValue;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}