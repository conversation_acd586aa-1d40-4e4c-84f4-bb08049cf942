/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity.post;

import com.easyooo.framework.cache.annotations.KeyProperty;
import java.util.Date;

import com.tuowan.yeliao.social.data.enums.post.PostShareType;
import org.apache.ibatis.type.Alias;

@Alias("SPostShare")
public class SPostShare {
    /** 记录ID */
    @KeyProperty
    private Long logId;

    /** 分享类型 */
    private PostShareType type;

    /** 动态ID */
    private Long postId;

    /** 用户ID */
    private Long userId;

    /** 分享时间 */
    private Date createTime;

    public SPostShare() {
        
    }

    /** 根据主键初始化实例 **/
    public SPostShare(Long logId) {
        this.logId = logId;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public PostShareType getType() {
        return type;
    }

    public void setType(PostShareType type) {
        this.type = type;
    }

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}