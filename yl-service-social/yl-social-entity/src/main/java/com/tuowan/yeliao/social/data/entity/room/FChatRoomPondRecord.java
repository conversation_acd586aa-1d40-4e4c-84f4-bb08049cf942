/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity.room;

import com.easyooo.framework.cache.annotations.KeyProperty;
import java.util.Date;

import com.tuowan.yeliao.social.data.enums.room.PondBeanType;
import org.apache.ibatis.type.Alias;

@<PERSON>as("FChatRoomPondRecord")
public class FChatRoomPondRecord {
    /** 主键 */
    @KeyProperty
    private Long id;

    /** 聊天室ID */
    private Long roomId;

    /** 聊天室奖池轮次 */
    private Integer pondRound;

    /** 来源类型 */
    private PondBeanType type;

    /** 额外信息 */
    private String extInfo;

    /** 金币 */
    private Long beans;

    /** 创建时间 */
    private Date createTime;

    public FChatRoomPondRecord() {
        
    }

    /** 根据主键初始化实例 **/
    public FChatRoomPondRecord(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRoomId() {
        return roomId;
    }

    public void setRoomId(Long roomId) {
        this.roomId = roomId;
    }

    public Integer getPondRound() {
        return pondRound;
    }

    public void setPondRound(Integer pondRound) {
        this.pondRound = pondRound;
    }

    public PondBeanType getType() {
        return type;
    }

    public void setType(PondBeanType type) {
        this.type = type;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo == null ? null : extInfo.trim();
    }

    public Long getBeans() {
        return beans;
    }

    public void setBeans(Long beans) {
        this.beans = beans;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}