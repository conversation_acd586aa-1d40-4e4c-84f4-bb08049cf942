package com.tuowan.yeliao.social.entity.plus;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tuowan.yeliao.commons.entity.plus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 动态实体类
 * 对应数据库表：s_post_plus_demo（去掉 s_ 前缀）
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@ApiModel(value = "Post", description = "动态实体")
@TableName("s_post_plus_demo")
public class Post extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 动态业务ID
     */
    @ApiModelProperty(value = "动态业务ID", example = "1001")
    @TableField("post_id")
    private Long postId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", required = true, example = "1001")
    @TableField("user_id")
    private Long userId;

    /**
     * 动态标题
     */
    @ApiModelProperty(value = "动态标题", example = "今天天气真好")
    @TableField("title")
    private String title;

    /**
     * 动态内容
     */
    @ApiModelProperty(value = "动态内容", required = true, example = "今天天气真好，适合出去走走")
    @TableField("content")
    private String content;

    /**
     * 动态类型
     * 1-文字 2-图片 3-视频 4-链接
     */
    @ApiModelProperty(value = "动态类型", notes = "1-文字 2-图片 3-视频 4-链接", example = "1")
    @TableField("post_type")
    private Integer postType;

    /**
     * 审核状态
     * 0-待审核 1-审核通过 2-审核拒绝
     */
    @ApiModelProperty(value = "审核状态", notes = "0-待审核 1-审核通过 2-审核拒绝", example = "1")
    @TableField("audit_status")
    private Integer auditStatus;

    /**
     * 状态
     * 0-禁用 1-启用
     */
    @ApiModelProperty(value = "状态", notes = "0-禁用 1-启用", example = "1")
    @TableField("status")
    private Integer status;

    /**
     * 点赞数
     */
    @ApiModelProperty(value = "点赞数", example = "10")
    @TableField("praise_num")
    private Integer praiseNum;

    /**
     * 评论数
     */
    @ApiModelProperty(value = "评论数", example = "5")
    @TableField("comment_num")
    private Integer commentNum;

    /**
     * 分享数
     */
    @ApiModelProperty(value = "分享数", example = "2")
    @TableField("share_num")
    private Integer shareNum;

    // ==================== 枚举定义 ====================

    /**
     * 动态类型枚举
     */
    public enum PostType {
        TEXT(1, "文字"),
        IMAGE(2, "图片"),
        VIDEO(3, "视频"),
        LINK(4, "链接");

        private final Integer code;
        private final String desc;

        PostType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static PostType getByCode(Integer code) {
            for (PostType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 审核状态枚举
     */
    public enum AuditStatus {
        PENDING(0, "待审核"),
        APPROVED(1, "审核通过"),
        REJECTED(2, "审核拒绝");

        private final Integer code;
        private final String desc;

        AuditStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static AuditStatus getByCode(Integer code) {
            for (AuditStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 状态枚举
     */
    public enum Status {
        DISABLED(0, "禁用"),
        ENABLED(1, "启用");

        private final Integer code;
        private final String desc;

        Status(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static Status getByCode(Integer code) {
            for (Status status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    // ==================== Getter and Setter ====================

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getPostType() {
        return postType;
    }

    public void setPostType(Integer postType) {
        this.postType = postType;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getPraiseNum() {
        return praiseNum;
    }

    public void setPraiseNum(Integer praiseNum) {
        this.praiseNum = praiseNum;
    }

    public Integer getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(Integer commentNum) {
        this.commentNum = commentNum;
    }

    public Integer getShareNum() {
        return shareNum;
    }

    public void setShareNum(Integer shareNum) {
        this.shareNum = shareNum;
    }

    // ==================== 便利方法 ====================

    /**
     * 获取动态类型描述
     */
    public String getPostTypeDesc() {
        PostType type = PostType.getByCode(this.postType);
        return type != null ? type.getDesc() : "未知";
    }

    /**
     * 获取审核状态描述
     */
    public String getAuditStatusDesc() {
        AuditStatus status = AuditStatus.getByCode(this.auditStatus);
        return status != null ? status.getDesc() : "未知";
    }

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        Status status = Status.getByCode(this.status);
        return status != null ? status.getDesc() : "未知";
    }

    /**
     * 是否已审核通过
     */
    public boolean isApproved() {
        return AuditStatus.APPROVED.getCode().equals(this.auditStatus);
    }

    /**
     * 是否启用状态
     */
    public boolean isEnabled() {
        return Status.ENABLED.getCode().equals(this.status);
    }

    /**
     * 获取互动总数（点赞+评论+分享）
     */
    public Integer getTotalInteraction() {
        int praise = this.praiseNum != null ? this.praiseNum : 0;
        int comment = this.commentNum != null ? this.commentNum : 0;
        int share = this.shareNum != null ? this.shareNum : 0;
        return praise + comment + share;
    }

    @Override
    public String toString() {
        return "Post{" +
                "postId=" + postId +
                ", userId=" + userId +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", postType=" + postType +
                ", auditStatus=" + auditStatus +
                ", status=" + status +
                ", praiseNum=" + praiseNum +
                ", commentNum=" + commentNum +
                ", shareNum=" + shareNum +
                "} " + super.toString();
    }
}
