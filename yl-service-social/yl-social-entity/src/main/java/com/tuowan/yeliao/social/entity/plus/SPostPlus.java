package com.tuowan.yeliao.social.entity.plus;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tuowan.yeliao.commons.entity.plus.BaseEntity;

/**
 * 动态表实体类（MyBatis-Plus 版本）
 * 用于演示新的 MyBatis-Plus 方案
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@TableName("s_post")
public class SPostPlus extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 动态ID
     */
    @TableField("post_id")
    private Long postId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 动态标题
     */
    @TableField("title")
    private String title;

    /**
     * 动态内容
     */
    @TableField("content")
    private String content;

    /**
     * 动态类型
     */
    @TableField("post_type")
    private Integer postType;

    /**
     * 审核状态
     */
    @TableField("audit_status")
    private Integer auditStatus;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 点赞数
     */
    @TableField("praise_num")
    private Integer praiseNum;

    /**
     * 评论数
     */
    @TableField("comment_num")
    private Integer commentNum;

    /**
     * 分享数
     */
    @TableField("share_num")
    private Integer shareNum;

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getPostType() {
        return postType;
    }

    public void setPostType(Integer postType) {
        this.postType = postType;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getPraiseNum() {
        return praiseNum;
    }

    public void setPraiseNum(Integer praiseNum) {
        this.praiseNum = praiseNum;
    }

    public Integer getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(Integer commentNum) {
        this.commentNum = commentNum;
    }

    public Integer getShareNum() {
        return shareNum;
    }

    public void setShareNum(Integer shareNum) {
        this.shareNum = shareNum;
    }

    @Override
    public String toString() {
        return "SPostPlus{" +
                "postId=" + postId +
                ", userId=" + userId +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", postType=" + postType +
                ", auditStatus=" + auditStatus +
                ", status=" + status +
                ", praiseNum=" + praiseNum +
                ", commentNum=" + commentNum +
                ", shareNum=" + shareNum +
                "} " + super.toString();
    }
}
