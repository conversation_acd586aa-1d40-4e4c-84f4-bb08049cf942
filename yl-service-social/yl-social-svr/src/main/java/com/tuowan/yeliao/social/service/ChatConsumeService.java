package com.tuowan.yeliao.social.service;

import com.alibaba.nacos.common.utils.Pair;
import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.extend.SimpleMap;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.comp.chat.RemindComponent;
import com.tuowan.yeliao.commons.comp.consume.ConsumePresentComponent;
import com.tuowan.yeliao.commons.comp.grant.GrantComponent;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.comp.operate.UserOperateComponent;
import com.tuowan.yeliao.commons.comp.task.TaskComponent;
import com.tuowan.yeliao.commons.comp.user.BothComponent;
import com.tuowan.yeliao.commons.comp.user.UserGuardComponent;
import com.tuowan.yeliao.commons.config.enums.ObjectLockType;
import com.tuowan.yeliao.commons.config.lock.ObjectLockTemplate;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.BusiRedisTemplate;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.core.constant.FieldConstant;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BackCodeDefine;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.redis.BusiKeyDefine;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.data.dto.common.TextStyleDTO;
import com.tuowan.yeliao.commons.data.dto.social.ChatTipsInfoDTO;
import com.tuowan.yeliao.commons.data.entity.config.TPoolBox;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGift;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGoods;
import com.tuowan.yeliao.commons.data.entity.user.*;
import com.tuowan.yeliao.commons.data.enums.acct.WealthType;
import com.tuowan.yeliao.commons.data.enums.config.NoticeSysType;
import com.tuowan.yeliao.commons.data.enums.config.ProdGoodsType;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.enums.config.SocialGiftGroup;
import com.tuowan.yeliao.commons.data.enums.general.ChatFilterType;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.enums.social.*;
import com.tuowan.yeliao.commons.data.enums.user.*;
import com.tuowan.yeliao.commons.data.manager.commons.BusiManager;
import com.tuowan.yeliao.commons.data.manager.config.SocialProdManager;
import com.tuowan.yeliao.commons.data.manager.user.*;
import com.tuowan.yeliao.commons.data.persistence.config.TPoolBoxMapper;
import com.tuowan.yeliao.commons.data.persistence.config.TProdSocialGiftMapper;
import com.tuowan.yeliao.commons.data.persistence.user.UChatMasterVoiceMapper;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.open.yidun.YidunSupport;
import com.tuowan.yeliao.commons.open.yidun.dto.YidunResultDTO;
import com.tuowan.yeliao.commons.open.yidun.enums.YidunResult;
import com.tuowan.yeliao.commons.open.yidun.enums.YidunReviewType;
import com.tuowan.yeliao.commons.web.proxy.config.MQInvoke;
import com.tuowan.yeliao.social.comp.activity.LuckyWheelComponent;
import com.tuowan.yeliao.social.comp.chatmaster.ChatMasterComponent;
import com.tuowan.yeliao.social.comp.friend.*;
import com.tuowan.yeliao.social.comp.friend.dto.ChatConsumeResultDTO;
import com.tuowan.yeliao.social.comp.home.VideoChatComponent;
import com.tuowan.yeliao.social.comp.log.LogComponent;
import com.tuowan.yeliao.social.data.config.activity.EcuGiftConfig;
import com.tuowan.yeliao.social.data.dto.friend.chat.GroupLuckyGiftDTO;
import com.tuowan.yeliao.social.data.entity.FChatConsumeWait;
import com.tuowan.yeliao.social.data.entity.FChatNetCall;
import com.tuowan.yeliao.social.data.entity.FRelationBasic;
import com.tuowan.yeliao.social.data.entity.room.FChatRoom;
import com.tuowan.yeliao.social.data.entity.room.FChatRoomPondRecord;
import com.tuowan.yeliao.social.data.enums.call.CallEvaluateType;
import com.tuowan.yeliao.social.data.enums.friend.*;
import com.tuowan.yeliao.social.data.enums.room.PondBeanType;
import com.tuowan.yeliao.social.data.manager.friend.ChatGuideManager;
import com.tuowan.yeliao.social.data.manager.friend.ChatManager;
import com.tuowan.yeliao.social.data.manager.friend.GiftWallManager;
import com.tuowan.yeliao.social.data.manager.friend.GroupLuckyGiftManager;
import com.tuowan.yeliao.social.data.manager.room.RoomManager;
import com.tuowan.yeliao.social.data.persistence.FChatConsumeWaitMapper;
import com.tuowan.yeliao.social.data.persistence.FRelationBasicMapper;
import com.tuowan.yeliao.social.data.persistence.UUserQuickReplyMapper;
import com.tuowan.yeliao.social.data.search.document.UserFeature;
import com.tuowan.yeliao.social.data.search.repository.UserFeatureRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.resps.Tuple;

import java.util.*;

import static com.tuowan.yeliao.commons.context.GlobalUtils.extValue;

/**
 * 聊天消费业务实现
 *
 * <AUTHOR>
 * @date 2022/4/18 12:00
 */
@Service
public class ChatConsumeService {

    private static final Logger LOG = LoggerFactory.getLogger(ChatConsumeService.class);

    /**
     * 待回复聊天记录过期时间
     */
    private static final Long CHAT_WAIT_REPLY_EXPIRE_TIME = 15 * 60 * 1000L;
    private static final String SEND_GIFT_LIMIT_FORMAT = "{}_{}";
    private static final String ECU_GIFT_ACTIVITY = "ECU:GIFT:ACTIVITY:{}";
    private static final String MALE_FIRST_USE_VIDEO_TICKET_EXT_AWARD_FORMAT = "MALE:FUVTEAF:{}";

    @Autowired
    private ChatConsumeComponent chatConsumeComponent;
    @Autowired
    private SocialRedisTemplate socialRedisTemplate;
    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private FChatConsumeWaitMapper fChatConsumeWaitMapper;
    @Autowired
    private IntimateComponent intimateComponent;
    @Autowired
    private NetCallComponent netCallComponent;
    @Autowired
    private GiftWallManager giftWallManager;
    @Autowired
    private ChatGuideManager chatGuideManager;
    @Autowired
    private UserGuardComponent userGuardComponent;
    @Autowired
    private UChatMasterVoiceMapper uChatMasterVoiceMapper;
    @Autowired
    private ActionStatManager actionStatManager;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private UUserQuickReplyMapper uUserQuickReplyMapper;
    @Autowired
    private LogComponent logComponent;
    @Autowired
    private TaskComponent taskComponent;
    @Autowired
    private ChatManager chatManager;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private MessageSettingManager messageSettingManager;
    @Autowired
    private LuckyWheelComponent luckyWheelComponent;
    @Autowired
    private ChatMasterManager chatMasterManager;
    @Autowired
    private UserOperateComponent userOperateComponent;
    @Autowired
    private BusiRedisTemplate busiRedisTemplate;
    @Autowired
    private UserKeyMarkManager userKeyMarkManager;
    @Autowired
    private VideoChatComponent videoChatComponent;
    @Autowired
    private BothComponent bothComponent;
    @Autowired
    private RemindComponent remindComponent;
    @Autowired
    private ChatMasterComponent chatMasterComponent;
    @Autowired
    private FateMatchComponent fateMatchComponent;
    @Autowired
    private FriendRelationComponent friendRelationComponent;
    @Autowired
    private TProdSocialGiftMapper tProdSocialGiftMapper;
    @Autowired
    private BusiManager busiManager;
    @Autowired
    private GrantComponent grantComponent;
    @Autowired
    private NoticeComponent noticeComponent;
    @Autowired
    private ObjectLockTemplate objectLockTemplate;
    @Autowired
    private FRelationBasicMapper fRelationBasicMapper;
    @Autowired
    private UserFeatureRepository userFeatureRepository;
    @Autowired
    private RoomManager roomManager;
    @Autowired
    private ConsumePresentComponent consumePresentComponent;
    @Autowired
    private SocialProdManager socialProdManager;
    @Autowired
    private MindGiftComponent mindGiftComponent;
    @Autowired
    private TPoolBoxMapper poolBoxMapper;
    @Autowired
    private GroupLuckyGiftManager groupLuckyGiftManager;

    /**
     * 保存送礼达到金额限制
     */
    public void saveSendChatGiftArriveLimit(Long userId, Long friendId){
        chatConsumeComponent.updateSendGiftIsLimit(MsgUtils.format(SEND_GIFT_LIMIT_FORMAT, userId, friendId));
    }

    /**
     * 男女用户每天首次消息交互逻辑处理
     */
    public void saveUserFirstMsgDayDeal(SexType sexType, Long userId, Long friendId, FemaleMsg type){
        chatConsumeComponent.userFirstMsgDayDeal(sexType, userId, friendId, type.getType());
    }

    /**
     * 私聊发消息任务处理
     */
    public void saveMsgChatTaskProcess(Long userId, Long friendId){
        taskComponent.saveProcessTask(BusiCodeDefine.SendChatMsg, userId, friendId);
    }

    /**
     * 女用户首次私聊建立时间
     */
    public void saveFemaleFfActMsgTime(Long userId,  Long friendId){
        friendRelationComponent.recordUserFccTime(userId, friendId);
    }

    /**
     * 聊天室发消息任务处理
     */
    public void saveMsgChatRoomTaskProcess(Long userId, Long friendId){
        taskComponent.saveProcessTask(BusiCodeDefine.SendChatRoomMsg, userId, friendId);
    }

    /**
     * 私聊消息
     * 亲密度增加
     */
    public void saveIntimateDeal(Long userId, Long friendId, Long realBeans){
        if(realBeans <= 0){
            return;
        }
        // 增加亲密度（亲密度的增加根据消耗走，不需要等回复）
        intimateComponent.addIntimateWithBeans(userId, friendId, realBeans);
    }

    /**
     * 私聊消息
     * 聊天内容保存
     */
    public void saveChatContent(Long userId, SexType userSex, Long friendId, SexType friendSex){
        // 1. 保存聊天内容
        saveChatContent(userId, friendId);
        // 3. 如果消息接收方第一次收到消息 且 消息接收方设置了私聊价格 则进行提示
        // 3.1 判断双方是否是付费私聊
        ChatFeeWaiverType type = GlobalUtils.extEnum(BusinessDataKey.ChatFeeWaiverType, ChatFeeWaiverType.class);
        if(ChatFeeWaiverType.NotFree != type){
            return;
        }
        // 3.2 判断双方是否第一次 私聊
        if(bothComponent.existMark(userId, friendId, UserBothKeyMark.FirstPrivateChat)){
            return;
        }
        remindComponent.saveDayRemind(friendId, userId, ChatTipsType.FirstReceiveChatMsg);
        bothComponent.saveMark(userId, friendId, UserBothKeyMark.FirstPrivateChat, "T");
    }

    /**
     * 群聊消息
     * 聊天内容保存
     */
    public void saveChatRoomContent(){

    }

    /**
     * 聊天室聊天热度处理
     * 备注：基础版聊天室列表排序 根据聊天室最近10分钟
     */
    public void saveCrHotForSendMsg(){
        Long chatRoomId = GlobalUtils.extLong(BusinessDataKey.ChatRoomId);

    }

    /**
     * 私聊消息
     * 处理待提成的聊天记录
     */
    public ChatConsumeResultDTO saveChatWaitConsumeAndIntimate(Long userId, Long friendId, SexType userSex, SexType friendSex, Long realBeans) {
        // 处理待消费记录,提成方加锁
        userLockTemplate.acquireTransactionLock(userId);
        RedisKey waitReplyKey = chatConsumeComponent.buildWaitReplayKey(userId, friendId);
        List<Tuple> waitList = socialRedisTemplate.zrangeWithScores(waitReplyKey, 0, -1);
        List<String> elements = new ArrayList<>(waitList.size());
        Long totalBeans = 0L; // 消耗方消耗
        Long presentTotalCash = 0L; // 收益方提成
        long validReplyNum = 0;
        for (Tuple w : waitList) {
            ChatConsumeResultDTO result = doChatWaitConsumeAndIntimate(w, userId, friendId);
            totalBeans += result.getTotalBeans();
            presentTotalCash += result.getPresentTotalCash();
            elements.add(w.getElement());
            if(result.isValidTimeReply()){
                validReplyNum++;
            }
        }
        if (elements.size() > 0) {
            // 移除待回复记录
            socialRedisTemplate.zrem(waitReplyKey, elements.toArray(new String[]{}));
            // 统计每次文字聊天亲密度，用于触发私信提醒
            chatGuideManager.processChatTipsAfterChat(userId, GlobalUtils.sexType(), friendId, totalBeans);
        }
        String dateStr = DateUtils.toString(new Date(), DatePattern.YMD2);
        // 统计女用户收消息数
        if(SexType.Female == friendSex){
            actionStatManager.incrFemaleByType(friendId, 1L, dateStr, FieldConstant.FEMALE_RECEIVE_MSG_NUM);
        }
        // 统计女用户有效回复消息数
        if(SexType.Female == userSex && validReplyNum > 0){
            actionStatManager.incrFemaleByType(userId, validReplyNum, dateStr, FieldConstant.FEMALE_VALID_REPLY_NUM);
        }
        return new ChatConsumeResultDTO(totalBeans, presentTotalCash);
    }

    /**
     * 保存聊天内容
     *
     * @param userId
     * @param friendId
     */
    private boolean saveChatContent(Long userId, Long friendId) {
        // 保存聊天内容
        ChatContentType contentType = EnumUtils.byName(GlobalUtils.formValue("contentType"), ChatContentType.class);
        String content = GlobalUtils.extValue(BusinessDataKey.Content);
        // Integer voiceId = GlobalUtils.formValue("voiceId");
        // 私信快捷回复ID
        // Long replyId = GlobalUtils.formLong("replyId");
        String quickReplay = GlobalUtils.formValue("quickReply");
        ChatFeeWaiverType chatFeeWaiverType = GlobalUtils.extEnum(BusinessDataKey.ChatFeeWaiverType, ChatFeeWaiverType.class);
        ChatSourceType sourceType = EnumUtils.byName(GlobalUtils.formValue("sourceType"), ChatSourceType.class);
        ChatFilterType filterType = EnumUtils.byName(GlobalUtils.extValue(BusinessDataKey.ChatFilterType), ChatFilterType.class);
        try {
            Map<String, Object> extMap = new HashMap<>();
            // 语音消息时长
            Integer duration = GlobalUtils.formValue("duration");
            if (duration != null) {
                extMap.put("duration", duration);
            }
            if (filterType != null) {
                extMap.put("filterType", filterType.name());
            }
            /*if (voiceId != null) {
                extMap.put("voiceId", voiceId);
                UChatMasterVoice voice = uChatMasterVoiceMapper.selectByPrimaryKey(new UChatMasterVoice(voiceId));
                if (voice != null) {
                    voice.setUsedTimes(voice.getUsedTimes() + 1);
                    uChatMasterVoiceMapper.updateByPrimaryKeySelective(voice);
                }
            }*/
            /*if (replyId != null) {
                extMap.put("replyId", replyId);
                // 女性快捷回复限制次数
                if (SexType.Female == GlobalUtils.sexType() && replyId > 0) {
                    UUserQuickReply textReply = uUserQuickReplyMapper.selectByPrimaryKey(new UUserQuickReply(replyId));
                    if (textReply != null) {
                        Integer times = null == textReply.getUsedTimes() ? 0 : textReply.getUsedTimes();
                        textReply.setUsedTimes(times + 1);
                        if (textReply.getUsedTimes() >= GlobalConstant.QUICK_TEXT_MAX_USE_TIMES) {
                            // 达到使用上限，失效
                            textReply.setStatus(Status.Disable);
                        }
                        try {
                            uUserQuickReplyMapper.updateByPrimaryKeySelective(textReply);
                        } catch (Exception e) {
                            String causeBy = e.getCause() != null ? e.getCause().getMessage() : null;
                            LOG.error(MsgUtils.format("修改快捷回复次数失败，原因： causeBy: {}", causeBy), e);
                        }
                    }
                }
            }*/
            if (StringUtils.isNotEmpty(quickReplay)) {
                extMap.put("quickReply", quickReplay);
            }
            logComponent.saveChatContent(GlobalUtils.tid(), GlobalUtils.reqTime(), userId, friendId, contentType, content, chatFeeWaiverType, sourceType, extMap);
        } catch (Exception e) {
            LOG.error(MsgUtils.format("保存聊天内容出错,userId:{}, friendId:{}, content:{}, contentType:{}", userId, friendId, content, contentType), e);
        }
        return true;
    }

    /**
     * 处理音视频通话主播分成和亲密度
     */
    public void saveNetCallInvitePresentAndIntimate() {
        long totalBeans = GlobalUtils.extLong(BusinessDataKey.TotalBeans, 0L);
        if (totalBeans == 0) {
            return;
        }
        Long billUserId = GlobalUtils.extLong(BusinessDataKey.BillUserId);
        Long userId = GlobalUtils.busiLong(BusinessDataKey.CallUserId);
        Long friendId = GlobalUtils.busiLong(BusinessDataKey.ReceiveUserId);
        Long presentCash = GlobalUtils.busiLong(BusinessDataKey.PresentCash);
        netCallComponent.addInvitePresentAndIntimate(userId, friendId, billUserId, totalBeans, presentCash);
    }

    /**
     * 充值任务
     */
    public void saveRechargeTaskProcess(){
        Long targetUserId = GlobalUtils.extLong(BusinessDataKey.TargetUserId);
        taskComponent.saveProcessTask(BusiCodeDefine.UpdateRechargeResult, targetUserId, null);
    }


    /**
     * 礼物排行榜处理
     * 备注：礼物上电视墙规则（消耗金币总量 大于199上普通墙 大于520上特殊墙）
     */
    public void saveSendGiftTvWallDeal(Long userId, Long friendId){
        Long totalBeans = GlobalUtils.extLong(BusinessDataKey.ConsumerTotalBeans);
        if(Objects.isNull(totalBeans)){
            return;
        }
        // 判断双方是不是有不上榜的
        UMessageSetting userSetting = messageSettingManager.getSetting(userId);
        /*UMessageSetting friendSetting = messageSettingManager.getSetting(friendId);
        if(OnGiftWallType.No == friendSetting.getOnGiftWallType()){
            return;
        }*/
        Date now = new Date();
        Integer chatGiftId = GlobalUtils.formValue("chatGiftId");
        Integer count = GlobalUtils.formValue("count");
        // 如果是无收益礼物 则不上榜
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(chatGiftId));
        if(Objects.isNull(gift) || !gift.getGroupCode().isHaveCash()){
            return;
        }
        if(totalBeans >= 199){
            GiftWallDpType type = totalBeans >= 520 ? GiftWallDpType.BigAmount : GiftWallDpType.Common;
            // 上普通墙
            chatConsumeComponent.saveGiftInCommonTvWall(GlobalUtils.tid(), userId, friendId, chatGiftId, count, totalBeans, now, type);
        }
        if(totalBeans >= 520){
            // 上大额墙
            chatConsumeComponent.saveGiftInBaTvWall(GlobalUtils.tid(), userId, friendId, chatGiftId, count, totalBeans, now);
        }
    }

    /**
     * 聊天室送礼奖池处理
     */
    public void saveSendGiftInCrPondDeal(Long userId){
        ChatSourceType sourceType = ChatSourceType.valueOf(GlobalUtils.formValue("sourceType"));
        if(ChatSourceType.ChatRoom != sourceType){
            return;
        }
        Long roomId = GlobalUtils.formLong("roomId");
        if(Objects.isNull(roomId)){
            return;
        }
        objectLockTemplate.acquireTransactionLock(ObjectLockType.ChatRoomPond, roomId);
        Long consumerTotalBeans = GlobalUtils.extLong(BusinessDataKey.ConsumerTotalBeans);
        if(Objects.isNull(consumerTotalBeans) || consumerTotalBeans <= 0){
            return;
        }
        Long pondBeans = (long)Math.ceil(GlobalConstant.CHAT_ROOM_RED_PACKET_POND_PROPORTION * consumerTotalBeans);
        FChatRoom room = roomManager.getRoom(roomId);
        // 累计聊天室奖池此轮奖池金币
        FChatRoom update = new FChatRoom(roomId);
        update.setPondBeans(room.getPondBeans() + pondBeans);
        roomManager.updateChatRoom(update);
        // 聊天室奖池记录
        FChatRoomPondRecord record = new FChatRoomPondRecord();
        record.setRoomId(roomId);
        record.setPondRound(room.getPondRound());
        record.setType(PondBeanType.Gift);
        record.setExtInfo(JsonUtils.seriazileAsString(MapUtils.gmap("roomId", roomId, "userId", userId)));
        record.setBeans(pondBeans);
        record.setCreateTime(new Date());
        roomManager.saveChatRoomPondRecord(record);
    }

    /**
     * 幸运礼物抽奖
     */
    public void saveLuckyGiftDraw(){
        // 幸运礼物目前仅支持聊天室内送礼
        Long roomId = GlobalUtils.formLong("roomId");
        if(Objects.isNull(roomId)){
            return;
        }
        ChatSourceType sourceType = EnumUtils.byName(GlobalUtils.formValue("sourceType"), ChatSourceType.class);
        if(ChatSourceType.ChatRoom != sourceType){
            return;
        }
        Long userId = GlobalUtils.uid();
        Integer giftId = GlobalUtils.formValue("chatGiftId");
        Integer count = GlobalUtils.formValue("count");
        Long friendId = GlobalUtils.formLong("friendId");
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(giftId));
        // 如果不是幸运礼物 不做处理
        if(SocialGiftGroup.Lucky != gift.getGroupCode()){
            return;
        }
        // 如果该礼物没有配置 抽奖池Code不做处理
        SimpleMap simpleMap = JsonUtils.toSimpleMap(gift.getSpecialParams());
        if(!simpleMap.containsKey("boxPoolCode")){
            return;
        }
        List<TPoolBox> tPoolBoxes = poolBoxMapper.selectByCode(new TPoolBox(simpleMap.getString("boxPoolCode")));
        if(ListUtils.isEmpty(tPoolBoxes)){
            return;
        }
        userLockTemplate.acquireTransactionLock(userId);
        tPoolBoxes.sort(Comparator.comparing(TPoolBox::getPoolId));
        int totalDrawBeans = 0;
        int multiple = 0;
        int maxMultiple = 0;
        // 送多少个就抽多少次奖
        for(int i=0; i<count; i++){
            int random = RandomUtils.getInt(100000);
            int base = 0;
            for(TPoolBox item : tPoolBoxes){ // 备注 remark里面存储的是倍数
                int mid = base + item.getCount();
                if(base <= random && random < mid){
                    totalDrawBeans += gift.getBeans() * Integer.parseInt(item.getRemark());
                    multiple += Integer.parseInt(item.getRemark());
                    maxMultiple = Math.max(maxMultiple, Integer.parseInt(item.getRemark()));
                    break;
                }
                base = mid;
            }
        }

        FChatRoom room = roomManager.getRoom(roomId);
        GroupLuckyGiftDTO dto = null;
        String sign = groupLuckyGiftManager.getContinuousSendSign(room.getRoomId(), userId, giftId);
        if(StringUtils.isNotEmpty(sign)){ // 表示是连续送礼
            dto = groupLuckyGiftManager.getGroupLuckyGiftDTO(sign);
            dto.setTimes(dto.getTimes() + count);
            dto.setMultiple(dto.getMultiple() + multiple);
            dto.setAwardBeans(dto.getAwardBeans() + totalDrawBeans);
            dto.setMaxMultiple(Math.max(maxMultiple, dto.getMaxMultiple()));
        }else{
            sign = StringUtils.getUUID();
            dto = new GroupLuckyGiftDTO();
            dto.setUserId(userId);
            dto.setFriendId(friendId);
            dto.setSignRoomId(room.createSignRoomId());
            dto.setGiftId(giftId);
            dto.setTimes(count);
            dto.setMultiple(multiple);
            dto.setMaxMultiple(maxMultiple);
            dto.setAwardBeans(totalDrawBeans);
            dto.setFirstSendTm(GlobalUtils.reqTime());
        }

        // 记录连送缓存标识
        groupLuckyGiftManager.recordContinuousSendSign(roomId, userId, giftId, sign);
        // 记录连送缓存信息
        groupLuckyGiftManager.recordGroupLuckyGiftDTO(sign, dto);
        // 加入礼物消息待发送队列
        groupLuckyGiftManager.addWaitSendLgMsgQueue(sign, DateUtils.plusSeconds(new Date(), 3).getTime());
        // 中奖消息发送
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        GlobalUtils.extValue(BusinessDataKey.GroupMsgSendType, "ev");
        GlobalUtils.extValue(BusinessDataKey.UserId, userBasic.getUserId());
        GlobalUtils.extValue(BusinessDataKey.Nickname, userBasic.getNickname());
        GlobalUtils.extValue(BusinessDataKey.HeadPic, userBasic.getHeadPic());
        GlobalUtils.extValue(BusinessDataKey.ConsumerStreamTimes, dto.getTimes());
        GlobalUtils.extValue(BusinessDataKey.ConsumerGiftPic, gift.getPic());
        GlobalUtils.extValue(BusinessDataKey.Level, dto.getMultiple() > 500 ? 3 : dto.getMultiple() > 200 ? 2 : 1);
        GlobalUtils.extValue(BusinessDataKey.Multiple, dto.getMultiple());
        GlobalUtils.extValue(BusinessDataKey.MaxMultiple, dto.getMaxMultiple());
        GlobalUtils.extValue(BusinessDataKey.RoomId, room.getRoomId());
        GlobalUtils.extValue(BusinessDataKey.ChatRoomId, room.createSignRoomId());
        GlobalUtils.extValue(BusinessDataKey.SignRoomId, room.createSignRoomId());
        GlobalUtils.extValue(BusinessDataKey.OverwriteBusiCode, BackCodeDefine.LuckyGiftSendInfo.getCodeName());

        GlobalUtils.extValue(BusinessDataKey.Time, GlobalUtils.reqTime());
        GlobalUtils.extValue(BusinessDataKey.AwardBeans, totalDrawBeans);
        GlobalUtils.extValue(BusinessDataKey.TriggerType, multiple >= 100 ? "Top" : "Bottom");
        messageComponent.sendMatchMsgs();
        // 奖励发放
        if(totalDrawBeans > 0) {
            grantComponent.saveGrantBeans("LuckyGiftDraw", userId, totalDrawBeans, "幸运礼物中奖");
        }
    }

    /**
     * 亲密度礼物统计
     */
    public void saveMindGiftStat(){
        Integer giftId = GlobalUtils.formValue("chatGiftId");
        Integer count = GlobalUtils.formValue("count");
        Long friendId = GlobalUtils.formLong("friendId");
        String mgMessage = GlobalUtils.formValue("mgMessage");
        // 如果参数不准备 不做处理
        if(Objects.isNull(giftId) || Objects.isNull(count) || Objects.isNull(friendId)){
            return;
        }
        // 如果带有寄语 我们需要易盾审核
        if(StringUtils.isNotEmpty(mgMessage)){
            UUserBasic userBasic = userInfoManager.getUserBasic(GlobalUtils.uid());
            YidunResultDTO.CommonResult commonResult = YidunSupport.syncSubmitText(YidunReviewType.MindGiftMessage, userBasic, giftId.longValue(), mgMessage);
            if(YidunResult.Pass != commonResult.getResult()){
                mgMessage = "请收下我对你的一点点心意~";
            }
        }
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(giftId));
        // 如果礼物不存在、或者不是心意礼物不做处理
        if(Objects.isNull(gift) || SocialGiftGroup.Mind != gift.getGroupCode()){
            return;
        }
        // 心意礼物收礼记录 备注：一次性送多个 需要分开记录
        for(int i = 0; i<count; i++){
            mindGiftComponent.saveMindGiftRecord(friendId, GlobalUtils.uid(), giftId, gift.getBeans(), mgMessage, new Date(GlobalUtils.reqTime()), GlobalUtils.tid());
            mindGiftComponent.delMyMgRankThreeFriendKey(friendId);
        }
    }

    /**
     * 女用户收礼指标数据统计
     */
    public void saveFemaleReceiveGiftDataStat(Long userId){
        if(GlobalUtils.sexType() != SexType.Male){
            return;
        }
        String dateStr = DateUtils.toString(new Date(), DatePattern.YMD2);
        Long friendId = GlobalUtils.formLong("friendId");
        Long giftCount = GlobalUtils.formLong("count");
        Integer giftId = GlobalUtils.formValue("chatGiftId");
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(giftId));
        if(WealthType.Bean == gift.getGroupCode().getType()){
            actionStatManager.incrFemaleByType(friendId, giftCount, dateStr, FieldConstant.FEMALE_BEAN_GIFT_NUM);
        }
        if(WealthType.Silver == gift.getGroupCode().getType()){
            actionStatManager.incrFemaleByType(friendId, giftCount, dateStr, FieldConstant.FEMALE_SILVER_GIFT_NUM);
        }
    }

    /**
     * 送礼活动处理
     * @Param friendId 代表收礼方ID
     */
    public void saveSendEcuGiftActivityDeal(Long friendId){
        Integer giftId = GlobalUtils.formValue("chatGiftId");
        Integer count = GlobalUtils.formValue("count");
        Date sendTime = new Date(GlobalUtils.reqTime());
        // 1、先判断活动时间
        Pair<Date, Date> dateRange = SettingsConfig.getDateRange(SettingsType.EcuGiftActTimes, "~");
        if(Objects.isNull(dateRange) || sendTime.before(dateRange.getFirst()) || sendTime.after(dateRange.getSecond())){
            return;
        }
        // 2、先判断该礼物是否参加了专属礼物活动
        EcuGiftConfig.Detail detail = EcuGiftConfig.getDetail(giftId);
        if(Objects.isNull(detail)){
            return;
        }
        // 3、男用户不参加该活动
        UUserBasic friendBasic = userInfoManager.getUserBasic(friendId);
        if(SexType.Female != friendBasic.getSex()){
            return;
        }
        // 4、由于跟钱相关，怕出现重复消费的情况 我们这里把tid拼接起来做幂等
        if(!busiManager.saveIdempotentMapper(MsgUtils.format(ECU_GIFT_ACTIVITY, GlobalUtils.tid()), new Date())){
            return;
        }

        // 5、增加活动额外奖励
        GlobalUtils.extValue(BusinessDataKey.TAwardDetailExt, "source=" + UserCashSourceType.EcuGiftAct.getId());
        Long totalAwardCash = BusiUtils.yuanToCash(detail.extAward * count);
        grantComponent.saveGrantPlatformCash("ECU_GIFT_ACTIVITY", friendId, totalAwardCash.intValue(), "专属礼物活动奖励");
        // 6、发送系统通知告知女用户
        Map<String, Object> params = new HashMap<>();
        params.put("count", count);
        params.put("extAward", detail.extAward * count);
        noticeComponent.sendSystemNotice(friendId, NoticeSysType.ActivityEcuGift, params);
    }

    /**
     * 送礼任务
     */
    public void saveSendGiftTaskProcess(Long userId){
        taskComponent.saveProcessTask(BusiCodeDefine.SendChatGift, userId, null);
    }

    /**
     * 收礼物任务
     */
    public void saveReceiveGiftTaskProcess(Long userId){
        taskComponent.saveProcessTask(BusiCodeDefine.ReceiveChatGift, userId, null);
    }

    /**
     * 主播转化率处理
     */
    public void saveDealAnchorTransformRate(){
        // 充值用户id
        Long targetUserId = GlobalUtils.extLong(BusinessDataKey.TargetUserId);
        chatMasterComponent.dealAnchorTransferRate(targetUserId);
    }

    /**
     * 处理送礼亲密度
     */
    public void saveSendGiftIntimate(Long friendId) {
        Long userId = GlobalUtils.uid();
        Integer giftId = GlobalUtils.formValue("chatGiftId");
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(giftId));
        // 如果礼物是幸运礼物则不增长亲密度
        if(SocialGiftGroup.Lucky == gift.getGroupCode()){
            return;
        }
        Long totalBeans = GlobalUtils.extLong(BusinessDataKey.ConsumerTotalBeans);
        if (totalBeans != null && totalBeans > 0) {
            intimateComponent.addIntimateWithBeans(userId, friendId, totalBeans);
        }
    }

    /**
     * 处理送礼礼物亲密度
     */
    public void saveSendGiftOfGiftIntimate(Long friendId) {
        Long userId = GlobalUtils.uid();
        Long totalBeans = GlobalUtils.extLong(BusinessDataKey.ConsumerTotalBeans);
        if (totalBeans != null && totalBeans > 0) {
            intimateComponent.addGiftIntimate(userId, friendId, totalBeans);
        }
    }

    /**
     * 统计用户收礼
     */
    public void saveStatUserReceiveGift(Long friendId){
        Integer chatGiftId = GlobalUtils.formValue("chatGiftId");
        Integer count = GlobalUtils.formValue("count");
        giftWallManager.saveUserReceiveGift(friendId, chatGiftId, count);
    }

    /**
     * 开通守护
     *
     * @param userId
     * @param friendId
     * @param giftId
     * @param count
     */
    public void saveOpenGuard(Long userId, Long friendId, Integer giftId, Integer count) {
        userLockTemplate.acquireTransactionLock(userId);
        userGuardComponent.saveOpenGuard(userId, friendId, giftId, count);
    }

    /**
     * 通话结束 视频聊业务处理
     */
    public void saveFinishCallProcess(){
        Long callUserId = GlobalUtils.busiLong(BusinessDataKey.CallUserId);
        Long receiveUserId = GlobalUtils.busiLong(BusinessDataKey.ReceiveUserId);
        UUserBasic userBasic = userInfoManager.getUserBasic(callUserId);
        UUserBasic friendBasic = userInfoManager.getUserBasic(receiveUserId);
        Long femaleUserId = null;
        if(SexType.Female == userBasic.getSex()){
            femaleUserId = userBasic.getUserId();
        }else if(SexType.Female == friendBasic.getSex()){
            femaleUserId = friendBasic.getUserId();
        }
        // 拨打方 视频聊分数2更新
        // videoChatComponent.updateVideoChatScore2(userBasic.getUserId(), true);
        // 接收方 视频聊分数2更新
        // videoChatComponent.updateVideoChatScore2(friendBasic.getUserId(), true);
        if(Objects.nonNull(femaleUserId)){
            // 通话结束 更新女用户视频聊分数1
            videoChatComponent.updateVideoChatScore1(femaleUserId);
        }
    }

    /**
     * 接受通话业务处理
     */
    public void saveAcceptCallProcess(){
        Long callId = GlobalUtils.formLong("callId");
        if (null == callId) {
            return;
        }
        FChatNetCall chatNetCall = netCallComponent.getChatNetCall(callId);
        if (null == chatNetCall) {
            return;
        }
        Long femaleUserId = null;
        UUserBasic userBasic = userInfoManager.getUserBasic(chatNetCall.getUserId());
        UUserBasic friendBasic = userInfoManager.getUserBasic(chatNetCall.getFriendId());
        if(SexType.Female == userBasic.getSex()){
            femaleUserId = userBasic.getUserId();
        }else if(SexType.Female == friendBasic.getSex()){
            femaleUserId = friendBasic.getUserId();
        }
        // 女用户视频聊更新分数1
        if(Objects.nonNull(femaleUserId)){
            videoChatComponent.updateVideoChatScore1(femaleUserId);
        }
    }

    /**
     * 结束电话
     * 同步用户通话空闲状态到ES
     */
    public void syncUserCallFreeStatusToEs(){
        Long userId = GlobalUtils.busiLong(BusinessDataKey.CallUserId);
        Long friendId = GlobalUtils.busiLong(BusinessDataKey.ReceiveUserId);
        // 判断波大方是否是在通话中
        if(!netCallComponent.isInCall(userId)){
            UserFeature update = new UserFeature(userId);
            update.setInCall(0);
            userFeatureRepository.updateSelective(update);
        }
        // 判断接收方是否在通话中
        if(!netCallComponent.isInCall(friendId)){
            UserFeature update = new UserFeature(friendId);
            update.setInCall(0);
            userFeatureRepository.updateSelective(update);
        }
    }

    /**
     * 通话结束
     * 将女用户从正在通话队列中移除
     */
    public void deleteCallingQueueForFemale(){
        Long femaleUserId;
        Long callId = GlobalUtils.extLong(BusinessDataKey.CallId);
        FChatNetCall netCall = netCallComponent.getChatNetCall(callId);
        if(Objects.isNull(netCall) || Objects.isNull(netCall.getFinishTime())){
            // 通话信息不存在，或者还未结束
            return;
        }
        UUserBasic receive = userInfoManager.getUserBasic(netCall.getFriendId());
        if(SexType.Female == receive.getSex()){
            femaleUserId = netCall.getFriendId();
        }else{
            femaleUserId = netCall.getUserId();
        }
        // 从通话中队列移除
        netCallComponent.removeFemaleInCurrentCall(femaleUserId);
    }

    /**
     * 通话结束
     * 女用户音视频指标统计
     */
    public void saveFemaleCallInfo(){
        String dateStr = DateUtils.toString(new Date(), DatePattern.YMD2);
        Long callUserId = GlobalUtils.busiLong(BusinessDataKey.CallUserId);
        Long receiveUserId = GlobalUtils.busiLong(BusinessDataKey.ReceiveUserId);
        Long maleUserId = callUserId;
        Long femaleUserId = receiveUserId;
        if(userInfoManager.getUserBasic(callUserId).getSex().isFemale()){
            maleUserId = receiveUserId;
            femaleUserId = callUserId;
        }
        NetCallType callType = EnumUtils.byName(extValue(BusinessDataKey.CallType), NetCallType.class);
        NetCallSourceType sourceType = EnumUtils.byName(extValue(BusinessDataKey.NetCallSourceType), NetCallSourceType.class);
        //主动挂断次数
        if(BusiCodeDefine.NetCallHangUp == GlobalUtils.busiCodeName() && GlobalUtils.uid().equals(femaleUserId)){
            NetCallEndType endType = EnumUtils.byName(GlobalUtils.formValue("endType"), NetCallEndType.class);
            if(NetCallEndType.ManualHangUp == endType){
                actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_ACT_HU_TIMES);
            }
        }
        // 总通话时长（秒）
        Long duration = GlobalUtils.extLong(BusinessDataKey.Duration, 0L);
        // 通话时长小于10秒统计
        if(duration < 10){
            actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_LOW_10S_CALL_TIMES);
        }
        // 通话时长小于60秒统计
        if(duration < 60){
            actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_LOW_60S_CALL_TIMES);
        }
        if(NetCallType.Video == callType){
            if(NetCallSourceType.VideoMate == sourceType){
                actionStatManager.incrFemaleByType(femaleUserId, duration, dateStr, FieldConstant.FEMALE_VM_DURATION);
            }else if(NetCallSourceType.Invite== sourceType){
                actionStatManager.incrFemaleByType(femaleUserId, duration, dateStr, FieldConstant.FEMALE_VI_DURATION);
            }else if(NetCallSourceType.VideoFate== sourceType){
                actionStatManager.incrFemaleByType(femaleUserId, duration, dateStr, FieldConstant.FEMALE_VF_DURATION);
            }else{
                actionStatManager.incrFemaleByType(femaleUserId, duration, dateStr, FieldConstant.FEMALE_VC_DURATION);
            }
        }
        if(NetCallType.Voice == callType){
            if(NetCallSourceType.Invite== sourceType){
                actionStatManager.incrFemaleByType(femaleUserId, duration, dateStr, FieldConstant.FEMALE_OI_DURATION);
            }else{
                actionStatManager.incrFemaleByType(femaleUserId, duration, dateStr, FieldConstant.FEMALE_OC_DURATION);
            }
        }
    }

    /**
     * 女用户视频时长任务处理
     */
    public void saveFemaleVideoTaskProcess(){
        Long femaleUserId = GlobalUtils.busiLong(BusinessDataKey.CallUserId);
        if(userInfoManager.getUserBasic(femaleUserId).getSex().isMale()){
            femaleUserId = GlobalUtils.busiLong(BusinessDataKey.ReceiveUserId);
        }
        Integer duration = GlobalUtils.extInt(BusinessDataKey.Duration, 0);
        taskComponent.saveProcessTask(BackCodeDefine.FemaleVideoTask, femaleUserId, null, (long)(duration / 60));
    }

    /**
     * 男用户视频拨打任务处理
     */
    public void saveMaleVideoTaskProcess(){
        Long maleUserId = GlobalUtils.busiLong(BusinessDataKey.CallUserId);
        if(userInfoManager.getUserBasic(maleUserId).getSex().isFemale()){
            maleUserId = GlobalUtils.busiLong(BusinessDataKey.ReceiveUserId);
        }
        taskComponent.saveProcessTask(BackCodeDefine.MaleVideoTask, maleUserId, null);
    }

    /**
     * 接受电话
     * 同步用户通话忙碌状态到ES
     */
    public void syncUserCallBusyStatusToEs(){
        Long callId = GlobalUtils.extLong(BusinessDataKey.CallId);
        Long userId = GlobalUtils.extLong(BusinessDataKey.CallUserId);
        Long friendId = GlobalUtils.extLong(BusinessDataKey.ReceiveUserId);
        // 判断波大方是否是在通话中
        Long userCallId = netCallComponent.getCallId(userId);
        if(Objects.nonNull(userCallId) && userCallId.equals(callId)){
            UserFeature update = new UserFeature(userId);
            update.setInCall(1);
            userFeatureRepository.updateSelective(update);
        }
        // 判断接收方是否在通话中
        Long friendCallId = netCallComponent.getCallId(friendId);
        if(Objects.nonNull(friendCallId) && friendCallId.equals(callId)){
            UserFeature update = new UserFeature(friendId);
            update.setInCall(1);
            userFeatureRepository.updateSelective(update);
        }
    }

    /**
     * 接受电话
     * 将女用户保存进通话中队列
     */
    public void saveCallingQueueForFemale(){
        Long femaleUserId = GlobalUtils.uid();
        Long callUserId = GlobalUtils.extLong(BusinessDataKey.CallUserId);
        if(GlobalUtils.sexType() == SexType.Male){
            femaleUserId = callUserId;
        }
        // 保存进通话中队列
        netCallComponent.saveFemaleInCurrentCall(femaleUserId);
    }

    /**
     * 接受通话
     * 女用户音视频通话数据保存
     */
    public void saveFemaleCallInfoForAccept(){
        Long callUserId = GlobalUtils.extLong(BusinessDataKey.CallUserId);
        Long receiveUserId = GlobalUtils.extLong(BusinessDataKey.ReceiveUserId);
        Long femaleUserId = receiveUserId;
        Long maleUserId = callUserId;
        if(GlobalUtils.sexType() == SexType.Male){
            femaleUserId = callUserId;
            maleUserId = receiveUserId;
        }
        String dateStr = DateUtils.toString(new Date(), DatePattern.YMD2);
        NetCallType callType = EnumUtils.byName(extValue(BusinessDataKey.CallType), NetCallType.class);
        NetCallSourceType sourceType = EnumUtils.byName(extValue(BusinessDataKey.NetCallSourceType), NetCallSourceType.class);
        if(NetCallType.Video == callType){
            if(NetCallSourceType.VideoMate == sourceType){
                actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_VM_SUC_TIMES);
                actionStatManager.addFemaleByTypeForUsers(femaleUserId, FieldConstant.FEMALE_VM_USERS, dateStr, maleUserId);
            }else if(NetCallSourceType.Invite== sourceType){
                actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_VI_SUC_TIMES);
                actionStatManager.addFemaleByTypeForUsers(femaleUserId, FieldConstant.FEMALE_VI_USERS, dateStr, maleUserId);
            }else if(NetCallSourceType.VideoFate== sourceType){
                actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_VF_SUC_TIMES);
                actionStatManager.addFemaleByTypeForUsers(femaleUserId, FieldConstant.FEMALE_VF_USERS, dateStr, maleUserId);
            }else{
                actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_VC_SUC_TIMES);
                actionStatManager.addFemaleByTypeForUsers(femaleUserId, FieldConstant.FEMALE_VC_USERS, dateStr, maleUserId);
            }
        }
        if(NetCallType.Voice == callType){
            if(NetCallSourceType.Invite== sourceType){
                actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_OI_SUC_TIMES);
                actionStatManager.addFemaleByTypeForUsers(femaleUserId, FieldConstant.FEMALE_OI_USERS, dateStr, maleUserId);
            }else{
                actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_OC_SUC_TIMES);
                actionStatManager.addFemaleByTypeForUsers(femaleUserId, FieldConstant.FEMALE_OC_USERS, dateStr, maleUserId);
            }
        }
    }

    /**
     * 接受通话
     * 男用户首次使用视频免费券处理
     * 1、标记记录
     * 2、提示女用户
     */
    public void saveMaleFirstUseVideoTicketDeal(){
        Long callId = GlobalUtils.extLong(BusinessDataKey.CallId);
        FChatNetCall call = netCallComponent.getChatNetCall(callId);
        if(Objects.isNull(call) || Objects.isNull(call.getBillUserId()) || Objects.isNull(call.getProdId())){
            return;
        }
        // 如果券的类型不是视频免费券 我们不做处理
        TProdSocialGoods goods = socialProdManager.getSocialGood(call.getProdId());
        if(Objects.isNull(goods) || ProdGoodsType.SocialVideoFreeTicket != goods.getGoodsType()){
            return;
        }
        // 如果男用户不是首次使用视频免费券通话 我们不做处理
        if(userKeyMarkManager.exists(call.getBillUserId(), UserBusiKeyMark.MaleFirstUseVideoTicketCall)){
            return;
        }
        // 给非付费方发送额外奖励提示
        Long presentUserId = call.getUserId().equals(call.getBillUserId()) ? call.getFriendId() : call.getUserId();
        messageComponent.sendCallPublicInfo(presentUserId, callId, ChatTipsInfoDTO.create("此通视频通话，通话结束后对方好评你将额外获得1元奖励，加油哦~"));
        // 打上标记
        CallbackAfterTransactionUtil.send(() -> {
            userKeyMarkManager.saveOrUpdateUserMark(call.getBillUserId(), UserBusiKeyMark.MaleFirstUseVideoTicketCall, String.valueOf(callId));
        });
    }

    /**
     * 接受通话
     * 男用户没有首充 拨打视频通话 给额外提示【完成首充将获得10%奖励】
     */
    public void maleNoFirstRechargeVideoTips(){
        Long callId = GlobalUtils.extLong(BusinessDataKey.CallId);
        FChatNetCall call = netCallComponent.getChatNetCall(callId);
        if(Objects.isNull(call.getBillUserId())){
            return;
        }
        Long presentUserId = call.getUserId().equals(call.getBillUserId()) ? call.getFriendId() : call.getUserId();
        UUserExt billUserExt = userInfoManager.getUserExt(call.getBillUserId());
        if(Objects.nonNull(billUserExt.getFirstRechargeTime()) || presentUserId.equals(billUserExt.getInviteUserId())){
            return;
        }
        messageComponent.sendCallPublicInfo(presentUserId, callId, ChatTipsInfoDTO.create("此通视频通话，对方在视频通话中完成充值，你将额外获得对方充值金额的10%奖励"));
    }

    /**
     * 通话结束
     * 匹配通话处理
     */
    public void saveVideoMatchDealForAccept(){
        BoolType matchCall = EnumUtils.byName(extValue(BusinessDataKey.MatchNetCall), BoolType.class);
        // 如果不是视频匹配直接返回
        if(BoolType.True != matchCall){
            return;
        }
        Long maleUserId = GlobalUtils.busiLong(BusinessDataKey.CallUserId);
        Long femaleUserId = GlobalUtils.busiLong(BusinessDataKey.ReceiveUserId);
        // 匹配完成后续处理
        fateMatchComponent.maleMatchFinishDeal(new Date(), maleUserId, Collections.singletonList(String.valueOf(femaleUserId)), MatchType.VM);
    }

    /**
     * 通话结束
     * 社交行为最近交互时间记录
     */
    public void saveSocialLastlyInteractiveTime(){
        Long userId = GlobalUtils.busiLong(BusinessDataKey.CallUserId);
        Long friendId = GlobalUtils.busiLong(BusinessDataKey.ReceiveUserId);
        // 备注：通话是双向的 所以这里需要记录两遍
        chatConsumeComponent.saveSocialLastlyInteractiveTime(String.valueOf(userId), String.valueOf(friendId));
        chatConsumeComponent.saveSocialLastlyInteractiveTime(String.valueOf(friendId), String.valueOf(userId));
    }

    /**
     * 通话结束
     * 记录消息状态为已回复
     */
    public void saveRecordMsgReplyStatus(){
        Long userId = GlobalUtils.busiLong(BusinessDataKey.CallUserId);
        Long friendId = GlobalUtils.busiLong(BusinessDataKey.ReceiveUserId);
        // 备注：通话是双向的 所以这里需要记录两遍【只需要记录已回复】
        chatConsumeComponent.recordReplyData(userId, friendId, false, true);
        chatConsumeComponent.recordReplyData(friendId, userId, false, true);
    }

    /**
     * 通话结束
     * 记录双方首次交互建立时间
     */
    public void saveFccTimeForCall(){
        Long userId = GlobalUtils.busiLong(BusinessDataKey.CallUserId);
        Long friendId = GlobalUtils.busiLong(BusinessDataKey.ReceiveUserId);
        friendRelationComponent.recordUserFccTime(userId, friendId);
    }

    /**
     * 拒绝通话
     * 女用户音视频通话数据保存
     */
    public void saveFemaleCallInfoForRefuse(){
        String dateStr = DateUtils.toString(new Date(), DatePattern.YMD2);
        Long femaleUserId = GlobalUtils.extLong(BusinessDataKey.CallUserId);
        if(userInfoManager.getUserBasic(femaleUserId).getSex().isMale()){
            femaleUserId = GlobalUtils.extLong(BusinessDataKey.ReceiveUserId);
        }
        // 音视频通话接通次数
        actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_RF_CALL_TIMES);
    }

    /**
     * 拒绝通话
     * 漏接电话【超时漏接】
     * 自动关闭视频接听功能处理
     */
    public void saveAutoOffVideoAnswerForCall(UserBusiKeyMark userBusiKeyMark){
        Date now = new Date();
        String dateStr = DateUtils.toString(now, DatePattern.YMD2);
        if(UserBusiKeyMark.FemaleMissCallTimes == userBusiKeyMark){
            // 如果不是男用户取消 则不做处理
            if(GlobalUtils.sexType() != SexType.Male){
                return;
            }
            Long callId = GlobalUtils.formLong("callId");
            FChatNetCall chatNetCall = netCallComponent.getChatNetCall(callId);
            // 如果不是视频通话不做处理 没有响铃时间不做处理
            if(NetCallType.Video != chatNetCall.getCallType() || Objects.isNull(chatNetCall.getRingTime())){
                return;
            }
            // 如果响铃时间小于15秒不做处理
            if(DateUtils.getDiffSeconds(chatNetCall.getRingTime(), now) < 15){
                return;
            }
            UUserBasic receiver = userInfoManager.getUserBasic(chatNetCall.getFriendId());
            if(SexType.Female != receiver.getSex()){
                return;
            }
            ////// 对苹果用户做适配-暂时不做处理
            UUserExt iosUserExt = userInfoManager.getUserExt(receiver.getUserId());
            if(ClientType.Android != iosUserExt.getLastClientType()){
                return;
            }
            //////
            // 统计女用户 漏接 次数
            RedisKey redisKey = buildFemaleRefuseOrMissCallTimes(dateStr, receiver.getUserId(), UserBusiKeyMark.FemaleMissCallTimes.name());
            Long refuseOrMissCallTimes = socialRedisTemplate.incr(redisKey);
            if(refuseOrMissCallTimes >= 5){ // 如果 漏接 超5次
                UUserKeyMark keyMark = userKeyMarkManager.getKeyMark(receiver.getUserId(), UserBusiKeyMark.FemaleMissCallTimes);
                int violationTimes = Objects.isNull(keyMark) ? 1 : Integer.parseInt(keyMark.getBusiValue()) + 1;
                Pair<Long, String> banHourForVoTimes = getBanHourForVoTimes(violationTimes, now);
                // redis 记录
                messageSettingManager.updateVideoAnswer(receiver.getUserId(), SexType.Female, BoolType.False, banHourForVoTimes.getFirst());
                // 系统关闭 视频接听 标记
                userKeyMarkManager.saveOrUpdateUserMark(receiver.getUserId(), UserBusiKeyMark.SysOffVideoAnswer, String.valueOf(banHourForVoTimes.getFirst()));
                // 系统通知 + 弹窗通知
                noticeComponent.sendSystemNotice(receiver.getUserId(), NoticeSysType.CommonNotice, MapUtils.gmap("overrideTouchType", ClientTouchType.MsgFeeSetPage, "content",
                        MsgUtils.format("当前您已累计{}次响铃15秒未接视频，已被关闭视频接听权限{}! \n如不方便接听 可点击：关闭视频接听前往关闭>>>", refuseOrMissCallTimes, banHourForVoTimes.getSecond())));
                messageComponent.sendCommonPopupMsg("漏接提醒", MsgUtils.format("当前您已累计{}次响铃15秒未接视频，已被关闭视频接听权限{}! \n如不方便接听可前往关闭视频接听", refuseOrMissCallTimes, banHourForVoTimes.getSecond()),
                        "前往关闭", ClientTouchType.MsgFeeSetPage, null, "取消", receiver.getUserId());
                // 处罚之后 重置次数
                socialRedisTemplate.del(redisKey);
                // 处罚之后 记录违规次数
                userKeyMarkManager.saveOrUpdateUserMark(receiver.getUserId(), UserBusiKeyMark.FemaleMissCallTimes, String.valueOf(violationTimes));
            }else{
                // 还未达到处罚次数 需要给系统通知和弹窗提示
                noticeComponent.sendSystemNotice(receiver.getUserId(), NoticeSysType.CommonNotice, MapUtils.gmap("overrideTouchType", ClientTouchType.MsgFeeSetPage, "content",
                        MsgUtils.format("您在{}有1个响铃15秒未接视频，当天已累计{}个响铃15秒未接视频，已减少流量曝光10%。一日内累计达到5次响铃15秒未接视频，系统将关闭您视频权限； \n如不方便接听 可点击：前往关闭视频接听权限>>>", DateUtils.toString(now, DatePattern.YMD_HM3), refuseOrMissCallTimes)));
                messageComponent.sendCommonPopupMsg("漏接提醒", MsgUtils.format("您在{}有1个响铃15秒未接视频，当天已累计{}个响铃15秒未接视频，已减少流量曝光10%。一日内累计达到5次响铃15秒未接视频，系统将关闭您视频权限； \n如不方便接听可前往关闭视频接听", DateUtils.toString(now, DatePattern.YMD_HM3), refuseOrMissCallTimes),
                        "前往关闭", ClientTouchType.MsgFeeSetPage, null, "取消", receiver.getUserId());
            }
        }
        if(UserBusiKeyMark.FemaleRefuseCallTimes == userBusiKeyMark){
            // 如果不是女用户主动挂断 则不做处理
            if(GlobalUtils.sexType() != SexType.Female){
                return;
            }
            Long callId = GlobalUtils.formLong("callId");
            FChatNetCall chatNetCall = netCallComponent.getChatNetCall(callId);
            // 如果不是视频通话不做处理
            if(NetCallType.Video != chatNetCall.getCallType()){
                return;
            }
            ////// 对苹果用户做适配-暂时不做处理
            UUserExt iosUserExt = userInfoManager.getUserExt(GlobalUtils.uid());
            if(ClientType.Android != iosUserExt.getLastClientType()){
                return;
            }
            //////
            // 统计女用户 拒接 次数
            RedisKey redisKey = buildFemaleRefuseOrMissCallTimes(dateStr, GlobalUtils.uid(), UserBusiKeyMark.FemaleRefuseCallTimes.name());
            Long refuseOrMissCallTimes = socialRedisTemplate.incr(redisKey);
            if(refuseOrMissCallTimes >= 3){ // 如果 拒接 超过3次
                UUserKeyMark keyMark = userKeyMarkManager.getKeyMark(GlobalUtils.uid(), UserBusiKeyMark.FemaleRefuseCallTimes);
                int violationTimes = Objects.isNull(keyMark) ? 1 : Integer.parseInt(keyMark.getBusiValue()) + 1;
                Pair<Long, String> banHourForVoTimes = getBanHourForVoTimes(violationTimes, now);
                // redis 记录
                messageSettingManager.updateVideoAnswer(GlobalUtils.uid(), SexType.Female, BoolType.False, banHourForVoTimes.getFirst());
                // 系统关闭 视频接听 标记
                userKeyMarkManager.saveOrUpdateUserMark(GlobalUtils.uid(), UserBusiKeyMark.SysOffVideoAnswer, String.valueOf(banHourForVoTimes.getFirst()));
                // 系统通知 + 弹窗通知
                noticeComponent.sendSystemNotice(GlobalUtils.uid(), NoticeSysType.CommonNotice, MapUtils.gmap("overrideTouchType", ClientTouchType.MsgFeeSetPage, "content",
                        MsgUtils.format("当前您已累计{}次通话拒绝接听，已被关闭视频权限{}! \n如不方便接听 可点击：关闭视频接听前往关闭>>>", refuseOrMissCallTimes, banHourForVoTimes.getSecond())));
                messageComponent.sendCommonPopupMsg("拒接提醒", MsgUtils.format("当前您已累计{}次通话拒绝接听，已被关闭视频权限{}! \n如不方便接听可前往关闭视频接听", refuseOrMissCallTimes, banHourForVoTimes.getSecond()),
                        "前往关闭", ClientTouchType.MsgFeeSetPage, null, "取消", GlobalUtils.uid());
                // 处罚之后 重置次数
                socialRedisTemplate.del(redisKey);
                // 处罚之后 记录违规次数
                userKeyMarkManager.saveOrUpdateUserMark(GlobalUtils.uid(), UserBusiKeyMark.FemaleRefuseCallTimes, String.valueOf(violationTimes));
            }else{
                // 还未达到处罚次数 需要给系统通知和弹窗提示
                noticeComponent.sendSystemNotice(GlobalUtils.uid(), NoticeSysType.CommonNotice, MapUtils.gmap("overrideTouchType", ClientTouchType.MsgFeeSetPage, "content",
                        MsgUtils.format("您在{}有1个通话拒绝接听，当天已累计{}个拒绝接听，已减少流量曝光10%。一日内达到3次拒绝接听，系统将关闭您视频权限；\n如不方便接听 可点击：前往关闭视频接听权限>>>", DateUtils.toString(now, DatePattern.YMD_HM3), refuseOrMissCallTimes)));
                messageComponent.sendCommonPopupMsg("拒接提醒", MsgUtils.format("您在{}有1个通话拒绝接听，当天已累计{}个拒绝接听，已减少流量曝光10%。一日内达到3次拒绝接听，系统将关闭您视频权限；\n如不方便接听可前往关闭视频接听", DateUtils.toString(now, DatePattern.YMD_HM3), refuseOrMissCallTimes),
                        "前往关闭", ClientTouchType.MsgFeeSetPage, null, "取消", GlobalUtils.uid());
            }
        }
        if(UserBusiKeyMark.FemaleNoFaceCallTimes == userBusiKeyMark){
            // 统计女用户 视频不露脸 次数
            RedisKey redisKey = buildFemaleRefuseOrMissCallTimes(dateStr, GlobalUtils.uid(), UserBusiKeyMark.FemaleNoFaceCallTimes.name());
            Long refuseOrMissCallTimes = socialRedisTemplate.incr(redisKey);
            if(refuseOrMissCallTimes >= 3){ // 如果 视频不露脸 超过3次
                UUserKeyMark keyMark = userKeyMarkManager.getKeyMark(GlobalUtils.uid(), UserBusiKeyMark.FemaleNoFaceCallTimes);
                int violationTimes = Objects.isNull(keyMark) ? 1 : Integer.parseInt(keyMark.getBusiValue()) + 1;
                Pair<Long, String> banHourForVoTimes = getBanHourForVoTimes(violationTimes, now);
                // redis 记录
                messageSettingManager.updateVideoAnswer(GlobalUtils.uid(), SexType.Female, BoolType.False, banHourForVoTimes.getFirst());
                // 系统关闭 视频接听 标记
                userKeyMarkManager.saveOrUpdateUserMark(GlobalUtils.uid(), UserBusiKeyMark.SysOffVideoAnswer, String.valueOf(banHourForVoTimes.getFirst()));
                // 系统通知 + 弹窗通知
                noticeComponent.sendSystemNotice(GlobalUtils.uid(), NoticeSysType.CommonNotice, MapUtils.gmap("overrideTouchType", ClientTouchType.MsgFeeSetPage, "content",
                        MsgUtils.format("当前您已累计{}次无人脸音视频通话，将会被关闭视频权限{}! \n如不方便接听 可点击：关闭视频接听前往关闭>>>", refuseOrMissCallTimes, banHourForVoTimes.getSecond())));
                messageComponent.sendCommonToastMsg(MsgUtils.format("当前您已累计{}次无人脸视频通话，将会被关闭视频权限{}! ", refuseOrMissCallTimes, banHourForVoTimes.getSecond()), GlobalUtils.uid());
                // 处罚之后 重置次数
                socialRedisTemplate.del(redisKey);
                // 处罚之后 记录违规次数
                userKeyMarkManager.saveOrUpdateUserMark(GlobalUtils.uid(), UserBusiKeyMark.FemaleNoFaceCallTimes, String.valueOf(violationTimes));
            }else{
                // 还未达到处罚次数 需要给系统通知和弹窗提示
                noticeComponent.sendSystemNotice(GlobalUtils.uid(), NoticeSysType.CommonNotice, MapUtils.gmap("overrideTouchType", ClientTouchType.MsgFeeSetPage, "content",
                        MsgUtils.format("您在{}有1个视频无人脸，当天已累计{}个视频无人脸，已减少流量曝光10%。一日内达到3次视频无人脸，系统将关闭您音视频权限；\n如不方便接听 可点击：前往关闭视频接听权限>>>", DateUtils.toString(now, DatePattern.YMD_HM3), refuseOrMissCallTimes)));
                messageComponent.sendCommonToastMsg(MsgUtils.format("您在{}有1个视频无人脸，当天已累计{}个视频无人脸，已减少流量曝光10%。一日内达到3次视频无人脸，系统将关闭您视频权限。", DateUtils.toString(now, DatePattern.YMD_HM3), refuseOrMissCallTimes), GlobalUtils.uid());
            }
        }
    }

    /**
     * 根据视频表现违规次数 获取封禁小时
     */
    private Pair<Long, String> getBanHourForVoTimes(int voTimes, Date time){
        if(voTimes == 1){
            return Pair.with(DateUtils.plusHours(time, 3).getTime(), "3小时");
        }else if(voTimes == 2){
            return Pair.with(DateUtils.plusHours(time, 12).getTime(), "12小时");
        }else if(voTimes == 3){
            return Pair.with(DateUtils.plusHours(time, 24).getTime(), "24小时");
        }else{
            return Pair.with(GlobalConstant.FOREVER_TIME_STAMP, "永久");
        }
    }

    /**
     * 查询通话信息
     * 女用户音视频通话数据保存
     */
    public void saveFemaleCallInfoForInfo(){
        String dateStr = DateUtils.toString(new Date(), DatePattern.YMD2);
        Long femaleUserId = GlobalUtils.extLong(BusinessDataKey.ReceiveUserId);
        if(userInfoManager.getUserBasic(femaleUserId).getSex().isMale()){
            femaleUserId = GlobalUtils.extLong(BusinessDataKey.CallUserId);
        }
        NetCallType callType = EnumUtils.byName(extValue(BusinessDataKey.CallType), NetCallType.class);
        NetCallSourceType sourceType = EnumUtils.byName(extValue(BusinessDataKey.NetCallSourceType), NetCallSourceType.class);
        if(NetCallType.Video == callType){
            if(NetCallSourceType.VideoMate == sourceType){
                actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_VM_TIMES);
            }else if(NetCallSourceType.Invite== sourceType){
                actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_VI_TIMES);
            }else if(NetCallSourceType.VideoFate== sourceType){
                actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_VF_TIMES);
            }else{
                actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_VC_TIMES);
            }
        }
        if(NetCallType.Voice == callType){
            if(NetCallSourceType.Invite== sourceType){
                actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_OI_TIMES);
            }else{
                actionStatManager.incrFemaleByType(femaleUserId, 1L, dateStr, FieldConstant.FEMALE_OC_TIMES);
            }
        }
    }

    /**
     * 视频通话好评
     * 女用户获得视频免费券额外奖励
     * 备注：只有好评才有奖励
     */
    @MQInvoke
    public void saveCallEvaluateVideoTicketExtAward(){
        // 不是男用户的评价不作处理
        if(SexType.Male != GlobalUtils.sexType()){
            return;
        }
        // 判断是不是好评
        CallEvaluateType type = CallEvaluateType.valueOf(GlobalUtils.formValue("type"));
        if(CallEvaluateType.Good != type){
            return;
        }
        Long userId = GlobalUtils.uid(); // 评价用户
        // 判断有没有用视频券
        UUserKeyMark mark = userKeyMarkManager.getKeyMark(userId, UserBusiKeyMark.MaleFirstUseVideoTicketCall);
        if(Objects.isNull(mark)){
            return;
        }
        // 判断首次用视频券的通话ID是否和此次评价通话ID一致
        Long callId = GlobalUtils.formLong("callId");
        if(!callId.equals(Long.valueOf(mark.getBusiValue()))){
            return;
        }
        // 幂等控制 （一个男用户只有一次给女用户额外奖励的机会 用幂等控制避免超发）
        if(!busiManager.saveIdempotentMapper(MsgUtils.format(MALE_FIRST_USE_VIDEO_TICKET_EXT_AWARD_FORMAT, userId), new Date(GlobalUtils.reqTime()))){
            return;
        }
        FChatNetCall chatNetCall = netCallComponent.getChatNetCall(callId);
        Long awardUserId = userId.equals(chatNetCall.getUserId()) ? chatNetCall.getFriendId() : chatNetCall.getUserId();
        // 给女用户额外奖励
        Long extAwardCash = BusiUtils.yuanToCash(2);
        consumePresentComponent.addPresent(UserCashSourceType.MaleFirstUseVtGoodEvaluate, userId, awardUserId, extAwardCash, extAwardCash);
    }

    /**
     * 漏接电话系统提醒消息
     */
    public void saveSendDayMissCallMsg() {
        Long callId = GlobalUtils.formLong("callId");
        if (null == callId) {
            return;
        }
        FChatNetCall chatNetCall = netCallComponent.getChatNetCall(callId);
        if (null == chatNetCall) {
            return;
        }
        UUserBasic basic = userInfoManager.getUserBasic(chatNetCall.getFriendId());
        if (SexType.Male == basic.getSex()) {
            // 接听方是男用户直接return
            return;
        }
        // 获取备注名
        String friendNotes = friendRelationComponent.getFriendNotes(chatNetCall.getFriendId(), chatNetCall.getUserId());
        String nickname = StringUtils.isEmpty(friendNotes) ? GlobalUtils.nickname() : friendNotes;
        // 给女接听方发送漏接通知
        netCallComponent.sendDayMissCallMsg(chatNetCall.getFriendId(), chatNetCall.getCallId(), nickname);
        // 女接听方漏接视频聊扣减分数2
        /*NetCallCancelType cancelType = NetCallCancelType.valueOf(GlobalUtils.formValue("cancelType"));
        if(NetCallCancelType.Timeout == cancelType){
            videoChatComponent.updateVideoChatScore2(chatNetCall.getFriendId(), false);
        }*/
    }

    /**
     * 处理聊天消息消费提成、亲密度、财富等级计算逻辑
     *
     * @param w
     * @param userId
     * @param friendId
     */
    private ChatConsumeResultDTO doChatWaitConsumeAndIntimate(Tuple w, Long userId, Long friendId) {
        long sendTime = (long) w.getScore();
        if (sendTime < System.currentTimeMillis() - CHAT_WAIT_REPLY_EXPIRE_TIME) {
            // 本条已过期
            return new ChatConsumeResultDTO(0L, 0L, false);
        }
        FChatConsumeWait wait = new FChatConsumeWait(DateUtils.truncMillis(sendTime), Long.parseLong(w.getElement()));
        wait = fChatConsumeWaitMapper.selectByPrimaryKey(wait);
        if (wait == null) {
            return new ChatConsumeResultDTO(0L, 0L, false);
        }
        // 处理消费提成
        chatConsumeComponent.doChatWithDraw(userId, friendId, wait);
        Long waitTotalBeans = wait.getTotalBeans().longValue();
        Long waitPresentCash = null == wait.getPresentCash() ? 0L : wait.getPresentCash();
        return new ChatConsumeResultDTO(waitTotalBeans, waitPresentCash, true);
    }

    /**
     * 第一次聊天付费提示
     * @Param userId 发送方
     * @Param friendId 接收方
     */
    public void firstChatTipsInfo(Long userId, Long friendId){
        try {
            // 根据全局消息判断是否是搭讪
            if(ChatSourceType.ChatUp.name().equals(GlobalUtils.formValue("sourceType"))) {
                return;
            }
            // 男用户才会收到消息
            UUserBasic basic = userInfoManager.getUserBasic(userId);
            if (SexType.Male != basic.getSex()) {
                return;
            }
            // 判断开关是否开启
            boolean checkTips = SettingsConfig.getBoolean(SettingsType.FirstChatSendTips);
            if (!checkTips) {
                return;
            }
            RedisKey key = buildChatChargeTipsKey();
            //布隆过滤器
            String value = new StringBuilder().append(userId).append(friendId).toString();
            Long checkResult = socialRedisTemplate.pfadd(key ,value);
            if (checkResult.equals(0L)) {
                return;
            }
            //第一次聊天发送提示消息
            UUserBasic friendBasic = userInfoManager.getUserBasic(friendId);
            //查询对面用户的收费金额
            UMessageSetting friendSetting = messageSettingManager.getSetting(friendId);
            //设置提示样式
            String textTpl = "对方设置了消息收费"+friendSetting.getMsgFee()+"金币/条，${colorText}，祝遇有缘人";
            Map<String, Object> textParam = new HashMap<>();
            textParam.put("${colorText}", "亲密等级达到14级可免费聊天");
            List<TextStyleDTO> textStyleList = new ArrayList<>();
            textStyleList.add(TextStyleDTO.Builder.create().setStyleType("basic").setEl("${colorText}").setColor("#16EBFF").setUnderLineColor("#16EBFF").build());
            messageComponent.sendFirstChatTipsMsg(friendId, userId, ChatTipsInfoDTO.createFroSender(textTpl, textParam, textStyleList));
        }  catch (Exception e) {
            LOG.error("ChatConsumeComponent-firstChatTipsInfo-error reason:", e);
        }
    }

    /**
     * 检查用户发言是否有违规关键词
     * @Param userId 发送方
     * @Param content 消息内容
     */
    public void updateIllegallyKeyword(Long userId, String content){
        // 判断开关是否开启
        //先判断封号开关是否开启
        boolean checkOpen = SettingsConfig.getBoolean(SettingsType.OpenBanUserIllegalWordCheck);
        if (!checkOpen) {
            return;
        }
        // 女用户才进行判断
        UUserBasic basic = userInfoManager.getUserBasic(userId);
        if (SexType.Female != basic.getSex()) {
            return;
        }
        try {
            // 获取嘉宾等级
            ChatMasterLevel chatMasterLevel = chatMasterManager.getChatMasterLevel(userId);
            // 获取用户魅力等级
            UUserLevel userLevel = userInfoManager.getUserLevel(userId);
            Integer userCharmLevel = userLevel.getCharmLevel();
            RedisKey keywordsSetKey = buildKeywordSetKey();
            //拿到所有违禁的词
            List<Tuple> tuples = busiRedisTemplate.zrangeByScoreWithScores(keywordsSetKey, 0D, 4D);
            for (Tuple keyword : tuples) {
                String keywordName = String.valueOf(keyword.getElement());
                if (content.contains(keywordName)) {
                    // 今日当前用户的当前关键词违禁次数+1
                    Long illegalTimes = saveDayIllegalTimes(userId, keywordName);
                    // 查询当前用户的关键词封禁次数
                    UUserKeyMark keyMark = userKeyMarkManager.getKeyMark(userId, UserBusiKeyMark.FemaleChatHitKwBan);
                    Integer banTimes = keyMark == null ? 0 : Integer.parseInt(keyMark.getBusiValue());
                    //根据嘉宾等级（ABCDN S）、魅力等级、关键词处罚次数判断封禁天数
                    putIntoQueue(userId, banTimes, userCharmLevel, chatMasterLevel, keywordName);
                }
            }
            // 从redis里拿取值 分数
        }  catch (Exception e) {
            LOG.error("ChatConsumeComponent-checkIllegallyKeyword-error reason:", e);
        }
    }

    /**
     * 根据用户嘉宾等级、魅力等级判断需要封禁多久、并存入redis等待定时任务扫描封禁
     * @Param userId 用户id
     * @Param banTimes 用户关键词违规次数
     * @Param userCharmLevel 用户魅力等级
     * @Param chatMasterLevel 嘉宾等级
     */
    private void putIntoQueue(Long userId ,Integer banTimes, Integer userCharmLevel,ChatMasterLevel chatMasterLevel,String keyword) {
        // 封禁秒数（ -1: 永久）
        String banSeconds = "";
        if (StringUtils.isEmpty(banSeconds)) {
            return;
        }
        Date score = DateUtils.plusMinutes(new Date(), 5);
        RedisKey redisKey = buildKeywordBanUserKey();
        Map<String, Object> params = new HashMap<>();
        params.put("u", userId);
        params.put("w", keyword);
        params.put("s", Integer.valueOf(banSeconds));
        params.put("d", DateUtils.toString(new Date(), DatePattern.YMD_HMS));
        busiRedisTemplate.zadd(redisKey, (double) score.getTime(), String.valueOf(userId));
        userOperateComponent.recordUserHotKwDetail(userId, UrlParamsMap.toUrlString(params));
    }

    /**
     * 男用户第一次聊天 付费提示缓存 （pfadd）
     */
    private RedisKey buildChatChargeTipsKey(){
        return RedisKey.create(SocialKeyDefine.ChatChargeTips);
    }

    private RedisKey buildKeywordBanUserKey(){
        return RedisKey.create(BusiKeyDefine.IllegalKeyWordBanQueue);
    }

    private RedisKey buildKeywordSetKey(){
        return RedisKey.create(BusiKeyDefine.IllegalKeyWordsSet);
    }

    private RedisKey buildDayIllegalTimesKey(Long userId, String keyword){
        return RedisKey.create(BusiKeyDefine.DayIllegalTimes, userId, keyword);
    }

    public Long saveDayIllegalTimes(Long userId,String keyword) {
        return busiRedisTemplate.incrby(buildDayIllegalTimesKey( userId, keyword ), 1L);
    }

    /**
     * 女用户 拒接、漏接 次数缓存
     */
    public RedisKey buildFemaleRefuseOrMissCallTimes(String dateStr, Long femaleUserId, String keyMark){
        return RedisKey.create(SocialKeyDefine.FemaleRefuseOrMissCallTimes, dateStr, femaleUserId, keyMark);
    }
}
