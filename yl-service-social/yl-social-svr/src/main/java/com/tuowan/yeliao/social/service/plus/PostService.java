package com.tuowan.yeliao.social.service.plus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tuowan.yeliao.social.data.mapper.plus.PostMapper;
import com.tuowan.yeliao.social.entity.plus.Post;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 动态服务类
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
@ConditionalOnProperty(name = "mybatis-plus.enabled", havingValue = "true", matchIfMissing = false)
public class PostService extends ServiceImpl<PostMapper, Post> {

    // ==================== 基础 CRUD 操作 ====================

    /**
     * 创建动态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createPost(Post post) {
        // 设置默认值
        if (post.getPostType() == null) {
            post.setPostType(Post.PostType.TEXT.getCode());
        }
        if (post.getAuditStatus() == null) {
            post.setAuditStatus(Post.AuditStatus.PENDING.getCode());
        }
        if (post.getStatus() == null) {
            post.setStatus(Post.Status.ENABLED.getCode());
        }
        if (post.getPraiseNum() == null) {
            post.setPraiseNum(0);
        }
        if (post.getCommentNum() == null) {
            post.setCommentNum(0);
        }
        if (post.getShareNum() == null) {
            post.setShareNum(0);
        }
        
        return save(post);
    }

    /**
     * 根据ID获取动态
     */
    public Post getPostById(Long id) {
        return getById(id);
    }

    /**
     * 根据业务ID获取动态
     */
    public Post getPostByPostId(Long postId) {
        LambdaQueryWrapper<Post> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Post::getPostId, postId);
        return getOne(queryWrapper);
    }

    /**
     * 更新动态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePost(Post post) {
        return updateById(post);
    }

    /**
     * 删除动态（逻辑删除）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePost(Long id) {
        return removeById(id);
    }

    /**
     * 批量删除动态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeletePosts(List<Long> ids) {
        return removeByIds(ids);
    }

    // ==================== 查询操作 ====================

    /**
     * 根据用户ID查询动态列表
     */
    public List<Post> getPostsByUserId(Long userId) {
        return baseMapper.selectByUserId(userId);
    }

    /**
     * 根据用户ID和动态类型查询
     */
    public List<Post> getPostsByUserIdAndType(Long userId, Integer postType) {
        return baseMapper.selectByUserIdAndType(userId, postType);
    }

    /**
     * 分页查询用户动态
     */
    public Page<Post> getUserPostPage(Long userId, int current, int size) {
        Page<Post> page = new Page<>(current, size);
        return baseMapper.selectUserPostPage(page, userId);
    }

    /**
     * 分页查询用户指定类型动态
     */
    public Page<Post> getUserPostPageByType(Long userId, Integer postType, int current, int size) {
        Page<Post> page = new Page<>(current, size);
        return baseMapper.selectUserPostPageByType(page, userId, postType);
    }

    /**
     * 查询热门动态
     */
    public List<Post> getHotPosts(Integer limit) {
        return baseMapper.selectHotPosts(limit);
    }

    /**
     * 分页查询热门动态
     */
    public Page<Post> getHotPostPage(int current, int size) {
        Page<Post> page = new Page<>(current, size);
        return baseMapper.selectHotPostPage(page);
    }

    /**
     * 查询最新动态
     */
    public List<Post> getLatestPosts(Integer limit) {
        return baseMapper.selectLatestPosts(limit);
    }

    /**
     * 搜索动态
     */
    public List<Post> searchPosts(String keyword) {
        return baseMapper.searchPosts(keyword);
    }

    /**
     * 分页搜索动态
     */
    public Page<Post> searchPostPage(String keyword, int current, int size) {
        Page<Post> page = new Page<>(current, size);
        return baseMapper.searchPostPage(page, keyword);
    }

    /**
     * 根据动态类型查询
     */
    public List<Post> getPostsByType(Integer postType) {
        return baseMapper.selectByPostType(postType);
    }

    /**
     * 查询待审核动态
     */
    public List<Post> getPendingAuditPosts() {
        return baseMapper.selectPendingAuditPosts();
    }

    /**
     * 查询用户最近的动态
     */
    public List<Post> getUserRecentPosts(Long userId, Integer limit) {
        return baseMapper.selectUserRecentPosts(userId, limit);
    }

    // ==================== 统计操作 ====================

    /**
     * 统计用户动态数量
     */
    public Long countUserPosts(Long userId) {
        return baseMapper.countByUserId(userId);
    }

    /**
     * 统计用户各类型动态数量
     */
    public List<Map<String, Object>> countUserPostsByType(Long userId) {
        return baseMapper.countByUserIdGroupByType(userId);
    }

    /**
     * 统计总动态数量
     */
    public Long countTotalPosts() {
        LambdaQueryWrapper<Post> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Post::getDeleted, 0);
        return count(queryWrapper);
    }

    // ==================== 互动操作 ====================

    /**
     * 点赞动态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean praisePost(Long id) {
        return baseMapper.increasePraiseNum(id) > 0;
    }

    /**
     * 取消点赞
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean unpraisePost(Long id) {
        return baseMapper.decreasePraiseNum(id) > 0;
    }

    /**
     * 增加评论数
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean increaseCommentNum(Long id) {
        return baseMapper.increaseCommentNum(id) > 0;
    }

    /**
     * 减少评论数
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean decreaseCommentNum(Long id) {
        return baseMapper.decreaseCommentNum(id) > 0;
    }

    /**
     * 分享动态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean sharePost(Long id) {
        return baseMapper.increaseShareNum(id) > 0;
    }

    // ==================== 状态管理 ====================

    /**
     * 审核通过
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean approvePost(Long id) {
        LambdaUpdateWrapper<Post> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Post::getId, id)
                    .set(Post::getAuditStatus, Post.AuditStatus.APPROVED.getCode());
        return update(updateWrapper);
    }

    /**
     * 审核拒绝
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean rejectPost(Long id) {
        LambdaUpdateWrapper<Post> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Post::getId, id)
                    .set(Post::getAuditStatus, Post.AuditStatus.REJECTED.getCode());
        return update(updateWrapper);
    }

    /**
     * 启用动态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean enablePost(Long id) {
        LambdaUpdateWrapper<Post> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Post::getId, id)
                    .set(Post::getStatus, Post.Status.ENABLED.getCode());
        return update(updateWrapper);
    }

    /**
     * 禁用动态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean disablePost(Long id) {
        LambdaUpdateWrapper<Post> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Post::getId, id)
                    .set(Post::getStatus, Post.Status.DISABLED.getCode());
        return update(updateWrapper);
    }

    /**
     * 批量更新状态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<Long> postIds, Integer status) {
        LambdaUpdateWrapper<Post> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(Post::getId, postIds)
                    .set(Post::getStatus, status);
        return update(updateWrapper);
    }

    /**
     * 批量审核
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAudit(List<Long> postIds, Integer auditStatus) {
        LambdaUpdateWrapper<Post> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(Post::getId, postIds)
                    .set(Post::getAuditStatus, auditStatus);
        return update(updateWrapper);
    }

    // ==================== 复杂查询 ====================

    /**
     * 查询用户热门动态
     */
    public List<Post> getUserHotPosts(Long userId, Integer minPraise) {
        LambdaQueryWrapper<Post> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Post::getUserId, userId)
                   .ge(Post::getPraiseNum, minPraise)
                   .eq(Post::getDeleted, 0)
                   .eq(Post::getStatus, Post.Status.ENABLED.getCode())
                   .eq(Post::getAuditStatus, Post.AuditStatus.APPROVED.getCode())
                   .orderByDesc(Post::getPraiseNum)
                   .last("LIMIT 10");
        return list(queryWrapper);
    }

    /**
     * 查询用户指定时间范围内的动态
     */
    public List<Post> getUserPostsInDateRange(Long userId, String startDate, String endDate) {
        LambdaQueryWrapper<Post> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Post::getUserId, userId)
                   .ge(Post::getCreateTime, startDate)
                   .le(Post::getCreateTime, endDate)
                   .eq(Post::getDeleted, 0)
                   .orderByDesc(Post::getCreateTime);
        return list(queryWrapper);
    }
}
