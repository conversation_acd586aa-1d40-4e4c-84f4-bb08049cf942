package com.tuowan.yeliao.social.service.plus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tuowan.yeliao.social.data.mapper.plus.SPostPlusMapper;
import com.tuowan.yeliao.social.entity.plus.SPostPlus;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 动态服务类（MyBatis-Plus 版本）
 * 演示 MyBatis-Plus 的 Service 层使用方式
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
@ConditionalOnProperty(name = "mybatis-plus.enabled", havingValue = "true", matchIfMissing = false)
public class SPostPlusService extends ServiceImpl<SPostPlusMapper, SPostPlus> {

    /**
     * 创建动态
     */
    public boolean createPost(SPostPlus post) {
        return save(post);
    }

    /**
     * 根据ID获取动态
     */
    public SPostPlus getPostById(Long id) {
        return getById(id);
    }

    /**
     * 更新动态
     */
    public boolean updatePost(SPostPlus post) {
        return updateById(post);
    }

    /**
     * 删除动态（逻辑删除）
     */
    public boolean deletePost(Long id) {
        return removeById(id);
    }

    /**
     * 根据用户ID查询动态列表
     */
    public List<SPostPlus> getPostsByUserId(Long userId) {
        LambdaQueryWrapper<SPostPlus> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SPostPlus::getUserId, userId)
                   .orderByDesc(SPostPlus::getCreateTime);
        return list(queryWrapper);
    }

    /**
     * 分页查询用户动态
     */
    public Page<SPostPlus> getUserPostPage(Long userId, int current, int size) {
        Page<SPostPlus> page = new Page<>(current, size);
        return baseMapper.selectUserPostPage(page, userId);
    }

    /**
     * 根据动态类型查询
     */
    public List<SPostPlus> getPostsByType(Integer postType) {
        return baseMapper.selectByPostType(postType);
    }

    /**
     * 查询热门动态
     */
    public List<SPostPlus> getHotPosts(Integer limit) {
        return baseMapper.selectHotPosts(limit);
    }

    /**
     * 统计用户动态数量
     */
    public Long countUserPosts(Long userId) {
        return baseMapper.countByUserId(userId);
    }

    /**
     * 增加点赞数
     */
    public boolean increasePraiseNum(Long postId) {
        LambdaUpdateWrapper<SPostPlus> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SPostPlus::getId, postId)
                    .setSql("praise_num = praise_num + 1");
        return update(updateWrapper);
    }

    /**
     * 增加评论数
     */
    public boolean increaseCommentNum(Long postId) {
        LambdaUpdateWrapper<SPostPlus> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SPostPlus::getId, postId)
                    .setSql("comment_num = comment_num + 1");
        return update(updateWrapper);
    }

    /**
     * 批量更新状态
     */
    public boolean batchUpdateStatus(List<Long> postIds, Integer status) {
        LambdaUpdateWrapper<SPostPlus> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(SPostPlus::getId, postIds)
                    .set(SPostPlus::getStatus, status);
        return update(updateWrapper);
    }

    /**
     * 复杂查询示例：查询指定用户的热门动态
     */
    public List<SPostPlus> getUserHotPosts(Long userId, Integer minPraise) {
        LambdaQueryWrapper<SPostPlus> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SPostPlus::getUserId, userId)
                   .ge(SPostPlus::getPraiseNum, minPraise)
                   .orderByDesc(SPostPlus::getPraiseNum)
                   .last("LIMIT 10");
        return list(queryWrapper);
    }
}
