# MyBatis-Plus 配置文件
# 用于启用 MyBatis-Plus 功能

# 启用 MyBatis-Plus
mybatis-plus:
  enabled: true
  # 配置 Mapper XML 文件位置
  mapper-locations: classpath*:mapper/plus/**/*Mapper.xml
  # 配置实体类别名包路径
  type-aliases-package: com.tuowan.yeliao.**.entity.plus
  # 全局配置
  global-config:
    # 数据库配置
    db-config:
      # 主键类型（AUTO 自增）
      id-type: auto
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
      # 字段验证策略
      insert-strategy: not_null
      update-strategy: not_null
      select-strategy: not_empty
  # 配置
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: true
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
