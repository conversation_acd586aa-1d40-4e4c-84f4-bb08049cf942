package com.tuowan.yeliao.social.service.plus;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tuowan.yeliao.social.entity.plus.Post;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * MyBatis-Plus 动态服务测试类
 * 用于验证 MyBatis-Plus 集成是否正常工作
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@SpringBootTest
@ActiveProfiles({"dev", "mybatis-plus"})
public class PostServiceTest {

    @Autowired(required = false)
    private PostService postService;

    @Test
    public void testCreatePost() {
        if (postService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 创建测试动态
        Post post = new Post();
        post.setPostId(System.currentTimeMillis()); // 使用时间戳作为业务ID
        post.setUserId(1001L);
        post.setTitle("测试动态标题");
        post.setContent("这是一个使用 MyBatis-Plus 创建的测试动态，去掉了前缀");
        post.setPostType(Post.PostType.TEXT.getCode());
        post.setAuditStatus(Post.AuditStatus.APPROVED.getCode());
        post.setStatus(Post.Status.ENABLED.getCode());

        boolean result = postService.createPost(post);
        System.out.println("创建动态结果: " + result);
        System.out.println("动态ID: " + post.getId());
        System.out.println("业务ID: " + post.getPostId());
        System.out.println("动态类型: " + post.getPostTypeDesc());
        System.out.println("审核状态: " + post.getAuditStatusDesc());
    }

    @Test
    public void testGetPostById() {
        if (postService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 假设存在ID为1的动态
        Post post = postService.getPostById(1L);
        if (post != null) {
            System.out.println("查询到动态: " + post.toString());
            System.out.println("互动总数: " + post.getTotalInteraction());
        } else {
            System.out.println("未找到ID为1的动态");
        }
    }

    @Test
    public void testGetPostByPostId() {
        if (postService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 根据业务ID查询
        Post post = postService.getPostByPostId(1001L);
        if (post != null) {
            System.out.println("根据业务ID查询到动态: " + post.getTitle());
        } else {
            System.out.println("未找到业务ID为1001的动态");
        }
    }

    @Test
    public void testGetPostsByUserId() {
        if (postService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 查询用户动态
        List<Post> posts = postService.getPostsByUserId(1001L);
        System.out.println("用户1001的动态数量: " + posts.size());
        posts.forEach(post -> {
            System.out.println("动态: " + post.getTitle() +
                             ", 类型: " + post.getPostTypeDesc() +
                             ", 状态: " + post.getStatusDesc());
        });
    }

    @Test
    public void testGetUserPostPage() {
        if (postService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 分页查询用户动态
        Page<Post> page = postService.getUserPostPage(1001L, 1, 5);
        System.out.println("总记录数: " + page.getTotal());
        System.out.println("总页数: " + page.getPages());
        System.out.println("当前页记录数: " + page.getRecords().size());
        page.getRecords().forEach(post ->
            System.out.println("动态: " + post.getTitle() + ", 是否已审核: " + post.isApproved())
        );
    }

    @Test
    public void testCountUserPosts() {
        if (postService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 统计用户动态数量
        Long count = postService.countUserPosts(1001L);
        System.out.println("用户1001的动态总数: " + count);
    }

    @Test
    public void testCountUserPostsByType() {
        if (postService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 统计用户各类型动态数量
        List<Map<String, Object>> typeCount = postService.countUserPostsByType(1001L);
        System.out.println("用户1001各类型动态统计:");
        typeCount.forEach(map ->
            System.out.println("类型: " + map.get("post_type") + ", 数量: " + map.get("count"))
        );
    }

    @Test
    public void testPraisePost() {
        if (postService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 点赞动态
        boolean result = postService.praisePost(1L);
        System.out.println("点赞结果: " + result);
    }

    @Test
    public void testSearchPosts() {
        if (postService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 搜索动态
        List<Post> posts = postService.searchPosts("测试");
        System.out.println("搜索到的动态数量: " + posts.size());
        posts.forEach(post -> System.out.println("搜索结果: " + post.getTitle()));
    }

    @Test
    public void testGetHotPosts() {
        if (postService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 查询热门动态
        List<Post> hotPosts = postService.getHotPosts(5);
        System.out.println("热门动态数量: " + hotPosts.size());
        hotPosts.forEach(post ->
            System.out.println("热门动态: " + post.getTitle() +
                             ", 互动总数: " + post.getTotalInteraction())
        );
    }

    @Test
    public void testBatchUpdateStatus() {
        if (postService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 批量更新状态
        List<Long> postIds = Arrays.asList(1L, 2L, 3L);
        boolean result = postService.batchUpdateStatus(postIds, Post.Status.ENABLED.getCode());
        System.out.println("批量更新状态结果: " + result);
    }

    @Test
    public void testGetUserHotPosts() {
        if (postService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 查询用户热门动态
        List<Post> hotPosts = postService.getUserHotPosts(1001L, 5);
        System.out.println("用户1001的热门动态数量: " + hotPosts.size());
        hotPosts.forEach(post ->
            System.out.println("热门动态: " + post.getTitle() +
                             ", 点赞数: " + post.getPraiseNum() +
                             ", 是否启用: " + post.isEnabled())
        );
    }

    @Test
    public void testAuditOperations() {
        if (postService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 测试审核操作
        Long postId = 1L;

        // 审核通过
        boolean approveResult = postService.approvePost(postId);
        System.out.println("审核通过结果: " + approveResult);

        // 查询待审核动态
        List<Post> pendingPosts = postService.getPendingAuditPosts();
        System.out.println("待审核动态数量: " + pendingPosts.size());
    }

    @Test
    public void testEnumUsage() {
        System.out.println("=== 测试枚举使用 ===");

        // 测试动态类型枚举
        System.out.println("动态类型枚举:");
        for (Post.PostType type : Post.PostType.values()) {
            System.out.println(type.getCode() + " - " + type.getDesc());
        }

        // 测试审核状态枚举
        System.out.println("审核状态枚举:");
        for (Post.AuditStatus status : Post.AuditStatus.values()) {
            System.out.println(status.getCode() + " - " + status.getDesc());
        }

        // 测试状态枚举
        System.out.println("状态枚举:");
        for (Post.Status status : Post.Status.values()) {
            System.out.println(status.getCode() + " - " + status.getDesc());
        }
    }
}
