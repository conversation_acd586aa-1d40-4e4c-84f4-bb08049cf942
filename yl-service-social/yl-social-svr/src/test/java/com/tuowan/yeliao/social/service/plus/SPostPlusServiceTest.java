package com.tuowan.yeliao.social.service.plus;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tuowan.yeliao.social.entity.plus.SPostPlus;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

/**
 * MyBatis-Plus 服务测试类
 * 用于验证 MyBatis-Plus 集成是否正常工作
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@SpringBootTest
@ActiveProfiles({"dev", "mybatis-plus"})
public class SPostPlusServiceTest {

    @Autowired(required = false)
    private SPostPlusService postPlusService;

    @Test
    public void testCreatePost() {
        if (postPlusService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 创建测试动态
        SPostPlus post = new SPostPlus();
        post.setUserId(1001L);
        post.setTitle("测试动态标题");
        post.setContent("这是一个使用 MyBatis-Plus 创建的测试动态");
        post.setPostType(1);
        post.setAuditStatus(1);
        post.setStatus(1);
        post.setPraiseNum(0);
        post.setCommentNum(0);
        post.setShareNum(0);

        boolean result = postPlusService.createPost(post);
        System.out.println("创建动态结果: " + result);
        System.out.println("动态ID: " + post.getId());
    }

    @Test
    public void testGetPostById() {
        if (postPlusService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 假设存在ID为1的动态
        SPostPlus post = postPlusService.getPostById(1L);
        if (post != null) {
            System.out.println("查询到动态: " + post.toString());
        } else {
            System.out.println("未找到ID为1的动态");
        }
    }

    @Test
    public void testGetPostsByUserId() {
        if (postPlusService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 查询用户动态
        List<SPostPlus> posts = postPlusService.getPostsByUserId(1001L);
        System.out.println("用户1001的动态数量: " + posts.size());
        posts.forEach(post -> System.out.println("动态: " + post.getTitle()));
    }

    @Test
    public void testGetUserPostPage() {
        if (postPlusService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 分页查询用户动态
        Page<SPostPlus> page = postPlusService.getUserPostPage(1001L, 1, 5);
        System.out.println("总记录数: " + page.getTotal());
        System.out.println("总页数: " + page.getPages());
        System.out.println("当前页记录数: " + page.getRecords().size());
    }

    @Test
    public void testCountUserPosts() {
        if (postPlusService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 统计用户动态数量
        Long count = postPlusService.countUserPosts(1001L);
        System.out.println("用户1001的动态总数: " + count);
    }

    @Test
    public void testIncreasePraiseNum() {
        if (postPlusService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 增加点赞数
        boolean result = postPlusService.increasePraiseNum(1L);
        System.out.println("增加点赞数结果: " + result);
    }

    @Test
    public void testBatchUpdateStatus() {
        if (postPlusService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 批量更新状态
        List<Long> postIds = Arrays.asList(1L, 2L, 3L);
        boolean result = postPlusService.batchUpdateStatus(postIds, 1);
        System.out.println("批量更新状态结果: " + result);
    }

    @Test
    public void testGetUserHotPosts() {
        if (postPlusService == null) {
            System.out.println("MyBatis-Plus 未启用，跳过测试");
            return;
        }

        // 查询用户热门动态
        List<SPostPlus> hotPosts = postPlusService.getUserHotPosts(1001L, 5);
        System.out.println("用户1001的热门动态数量: " + hotPosts.size());
        hotPosts.forEach(post -> 
            System.out.println("热门动态: " + post.getTitle() + ", 点赞数: " + post.getPraiseNum())
        );
    }
}
