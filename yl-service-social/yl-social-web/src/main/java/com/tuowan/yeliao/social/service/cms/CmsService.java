package com.tuowan.yeliao.social.service.cms;

import com.easyooo.framework.common.util.DateUtils;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.comp.operate.UserOperateComponent;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.data.entity.config.TFemaleLevelNum;
import com.tuowan.yeliao.commons.data.entity.user.UChatMaster;
import com.tuowan.yeliao.commons.data.entity.user.UMessageSetting;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserLevel;
import com.tuowan.yeliao.commons.data.enums.config.NoticeSysType;
import com.tuowan.yeliao.commons.data.enums.general.ReviewResultType;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.commons.data.manager.user.ChatMasterManager;
import com.tuowan.yeliao.commons.data.manager.user.MessageSettingManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.support.config.impl.FemaleLevelNumConfig;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.web.common.form.UserIdForm;
import com.tuowan.yeliao.commons.web.common.vo.BoolTypeVO;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.social.comp.chatmaster.ChatMasterComponent;
import com.tuowan.yeliao.social.comp.friend.FateMatchComponent;
import com.tuowan.yeliao.social.comp.friend.NetCallComponent;
import com.tuowan.yeliao.social.comp.home.VideoChatComponent;
import com.tuowan.yeliao.social.data.entity.UUserExclusiveChatup;
import com.tuowan.yeliao.social.data.entity.UUserQuickReply;
import com.tuowan.yeliao.social.data.enums.cms.FemaleFlowType;
import com.tuowan.yeliao.social.data.manager.chat.ExclusiveChatupManager;
import com.tuowan.yeliao.social.data.manager.friend.ChatManager;
import com.tuowan.yeliao.social.data.persistence.UUserQuickReplyMapper;
import com.tuowan.yeliao.social.data.search.repository.UserFeatureRepository;
import com.tuowan.yeliao.social.web.form.cms.*;
import com.tuowan.yeliao.social.web.form.friend.netcall.NetCallIdForm;
import com.tuowan.yeliao.social.web.form.videoChat.VideoChatAuditForm;
import com.tuowan.yeliao.social.web.vo.cms.FemaleFlowInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * social cms服务实现
 */
@Service
public class CmsService {

    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private NetCallComponent netCallComponent;
    @Autowired
    private NoticeComponent noticeComponent;
    @Autowired
    private MessageSettingManager messageSettingManager;
    @Autowired
    private UserOperateComponent userOperateComponent;
    @Autowired
    private UserFeatureRepository userFeatureRepository;
    @Autowired
    private UUserQuickReplyMapper uUserQuickReplyMapper;
    @Autowired
    private ChatMasterManager chatMasterManager;
    @Autowired
    private VideoChatComponent videoChatComponent;
    @Autowired
    private FateMatchComponent fateMatchComponent;
    @Autowired
    private ExclusiveChatupManager exclusiveChatupManager;
    @Autowired
    private ChatMasterComponent chatMasterComponent;
    @Autowired
    private ChatManager chatManager;

    /**
     * 修改用户私信价格
     */
    @BusiCode
    public void updateUserMsgFee(UpdateUserMsgFeeForm form){
        userLockTemplate.acquireTransactionLock(GlobalUtils.uid());
        /*if (!MessageSettingConfig.isValidChatFee(form.getMsgFee())) {
            throw new BusiException(ErrCodeType.InvalidArguments);
        }*/
        UUserBasic userBasic = userInfoManager.getUserBasic(form.getUserId());
        if(Objects.isNull(userBasic)){
            throw new DataException("目标用户信息不存在！！");
        }
        UUserLevel userLevel = userInfoManager.getUserLevel(form.getUserId());
        Integer userCharmLevel = userLevel.getCharmLevel();
        // 检查魅力等级是否满足
        /*for (MsgFeeConfigDTO dto : MessageSettingConfig.getNewChatFeeList(userBasic.getSex())) {
            if (form.getMsgFee().equals(dto.getFee()) && dto.getCharmLevel() != null && userCharmLevel < dto.getCharmLevel()) {
                throw new BusiException(MsgUtils.format("魅力等级达到{}级才能选择哦", dto.getCharmLevel()));
            }
        }*/
        // 修改私信价格
        UMessageSetting update = new UMessageSetting(form.getUserId());
        update.setMsgFee(form.getMsgFee());
        messageSettingManager.updateSetting(update);
    }

    /**
     * 禁止推荐
     */
    @BusiCode(BusiCodeDefine.BanRecom)
    public void saveBanRecom(UserRecomBanForm form) {
        // 标记为禁止推荐
        userOperateComponent.banRecom(GlobalUtils.uid(), form.getTargetUserId(), form.getReason());
        // 附近移除、推荐列表移除
        userFeatureRepository.delete(form.getTargetUserId());
    }

    /**
     * 通话是否存在遮罩
     *
     * @param form
     * @return
     */
    @BusiCode
    public BoolTypeVO getExistCallMask(NetCallIdForm form) {
        boolean status = netCallComponent.existNetCallMaskCache(form.getCallId());
        return BoolTypeVO.build(BoolType.valueOf(status));
    }

    /**
     * 批量审核快捷回复
     */
    @BusiCode
    public void saveBatchQuickReply(BatchQuickReplyForm form){
        if(ReviewResultType.Wait == form.getAuditStatus()){
            return;
        }
        UUserQuickReply reply = uUserQuickReplyMapper.selectByPrimaryKey(new UUserQuickReply(form.getReplyId()));
        if (reply == null) {
            throw new BusiException("快捷回复不存在，请刷新后重试");
        }
        boolean reject = ReviewResultType.Reject == form.getAuditStatus();
        UUserQuickReply apply = new UUserQuickReply(form.getReplyId());
        apply.setUserId(reply.getUserId());
        apply.setAuditStatus(form.getAuditStatus());
        apply.setAuditReason(form.getAuditReason());
        apply.setAuditUserId(GlobalUtils.uid());
        apply.setAuditTime(new Date());
        if (reject) {
            apply.setStatus(Status.Disable);
        }
        uUserQuickReplyMapper.updateByPrimaryKeySelective(apply);
        if (reject) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("content", reply.getContent());
            paramMap.put("reason", form.getAuditReason());
            noticeComponent.sendSystemNotice(reply.getUserId(), NoticeSysType.DeleteQuickReply, paramMap);
        }
    }

    /**
     * 禁用快捷回复
     */
    @BusiCode
    public void saveBanQuickReply(BatchQuickReplyForm form){
        if(Objects.isNull(form.getReplyId())){
            throw new DataException("请选择禁用对象");
        }
        UUserQuickReply reply = uUserQuickReplyMapper.selectByPrimaryKey(new UUserQuickReply(form.getReplyId()));
        if (reply == null) {
            throw new BusiException("快捷回复不存在，请刷新后重试");
        }
        UUserQuickReply apply = new UUserQuickReply(form.getReplyId());
        apply.setStatus(Status.Disable);
        apply.setUserId(reply.getUserId());
        uUserQuickReplyMapper.updateByPrimaryKeySelective(apply);
    }

    @BusiCode(BusiCodeDefine.VideoChatCmsAudit)
    public void saveAuditVideoChat(VideoChatAuditForm form){
        videoChatComponent.dealVideoChatAudit(form.getUserId(), form.getSign(), form.getPass().boolValue(), form.getAuditReason(), 1);
    }

    @BusiCode
    public void saveValidAuditVideoChat(UserIdForm form){
        videoChatComponent.saveVideoChatDeleteData(form.getUserId());
    }

    /**
     * 修改女用户数字等级
     */
    @BusiCode(BusiCodeDefine.CmsFemaleNumLevel)
    public void updateFemaleNumLevel(UpdateFemaleNumLevelForm form){
        UChatMaster master = chatMasterManager.getChatMaster(form.getUserId());
        if(Objects.isNull(master)){
            throw new DataException("女用户信息不存在，请仔细检查ID");
        }
        // 更新嘉宾新数字等级
        chatMasterManager.updateMasterNumLevel(master, form.getLevel(), "CMS手动指定", GlobalUtils.busiCode(), GlobalUtils.uid());
        // 修改数字等级队列
        fateMatchComponent.dealFemaleNumLevelChange(String.valueOf(master.getUserId()), master.getFemaleLevel(), form.getLevel());
    }

    /**
     * 删除用户专属搭讪
     */
    @BusiCode(BusiCodeDefine.DeleteUserEcu)
    public void deleteUserEcu(DeleteUserEcuForm form){
        UUserExclusiveChatup ecu = exclusiveChatupManager.getExclusiveChatup(form.getCfgId());
        if(Objects.isNull(ecu)){
            return;
        }
        // 删除
        exclusiveChatupManager.deleteExclusiveChatUp(ecu);
        // 系统消息通知用户
        noticeComponent.sendSystemNotice(ecu.getUserId(), NoticeSysType.EcuCmsDelete);
    }

    /**
     * 获取女用户流量信息
     */
    @BusiCode
    public FemaleFlowInfoVO femaleFlowInfo(UserIdForm form){
        Long userId = form.getUserId();
        Date now = DateUtils.nowTime();
        TFemaleLevelNum config = FemaleLevelNumConfig.getConfig(chatMasterManager.getFemaleLevel(userId));
        // FemaleLevelExchangeConfig.Detail exchangeNumLevelCgf = chatMasterComponent.getFemaleExchangeNumLevelCgf(userId, now);
        // 总的
        int dayLimit = BusiUtils.getFemaleLimitTimes(config.getDayLimit());  // 今日女用户系统搭讪次数
        int activeLimit = BusiUtils.getFemaleLimitTimes(config.getActiveLimit());  // 今日女用户主动搭讪次数
        int actCallLimit = BusiUtils.getFemaleLimitTimes(config.getActCallLimit());  // 今日女用户主动视频次数
        // 今天已使用的
        long femaleDayFlowCount = fateMatchComponent.getFemaleDayFlowCount(now, userId);//系统搭讪
        long femaleChatUpTimesForDay = chatManager.getFemaleChatUpTimesForDay(now, userId);//主动搭讪
        int femaleActCallTimes = netCallComponent.getFemaleActCallTimes(now, userId);//主动视频

        // 返回值封装
        FemaleFlowInfoVO vo = new FemaleFlowInfoVO();

        vo.setSystemFlow(dayLimit);
        vo.setSystemFlowUse((int)femaleDayFlowCount);

        vo.setActChatUp(activeLimit);
        vo.setActChatUpUse((int)femaleChatUpTimesForDay);

        vo.setActCall(actCallLimit);
        vo.setActCallUse(femaleActCallTimes);

        return vo;
    }

    /**
     * 修改女用户流量信息
     */
    @BusiCode
    public void updateFemaleFlow(UpdateFemaleFlowForm form){
        if(form.getNum() == 0){
            return;
        }
        Long userId = form.getUserId();
        Date now = DateUtils.nowTime();
        if(form.getNum() > 0){ //增加
            if(FemaleFlowType.SystemCu == form.getType()){
                fateMatchComponent.updateFemaleDayFlowCount(now, userId, -form.getNum());
                // 只要是增加次数 我们就去从每日限制池中删除一次
                // 1>、将数据从冷静池中删除
                fateMatchComponent.removeFromDayLimitSoberPond(userId.toString());
                // 2>、将女用户判断加入对应的等级队列
                fateMatchComponent.addLevelQueueForFemale(userId);
            }
            if(FemaleFlowType.ActCu == form.getType()){
                chatManager.updateFemaleChatUpTimesForDay(now, userId, -form.getNum());
            }
            if(FemaleFlowType.ActCall == form.getType()){
                netCallComponent.updateFemaleActCallTimes(now, userId, -form.getNum());
            }
            // 发送系统通知告知女用户
            noticeComponent.sendSystemNotice(userId, NoticeSysType.FemaleFlowCmsAdd);
        }else{ // 减少
            if(FemaleFlowType.SystemCu == form.getType()){
                fateMatchComponent.updateFemaleDayFlowCount(now, userId, -form.getNum());
            }
            if(FemaleFlowType.ActCu == form.getType()){
                chatManager.updateFemaleChatUpTimesForDay(now, userId, -form.getNum());
            }
            if(FemaleFlowType.ActCall == form.getType()){
                netCallComponent.updateFemaleActCallTimes(now, userId, -form.getNum());
            }
        }
    }

    /**
     * 修改女用户最低价格设置
     */
    @BusiCode
    public void updateFemaleFee(UpdateFemaleFeeForm form){
        if(Objects.isNull(form.getMinMsgFee()) && Objects.isNull(form.getMinVoiceFee()) && Objects.isNull(form.getMinVideoFee())){
            return;
        }
    }

    /**
     * 取消女用户最低价格设置
     */
    @BusiCode
    public void updateCancelFemaleFee(UserIdForm form){
        UChatMaster master = chatMasterManager.getChatMaster(form.getUserId());
        if(Objects.isNull(master)){
            return;
        }
    }
}
