package com.tuowan.yeliao.social.service.cosmos;

import com.easyooo.framework.common.util.ArrayUtils;
import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.config.enums.ObjectLockType;
import com.tuowan.yeliao.commons.config.lock.ObjectLockTemplate;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ReviewStatus;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserLocation;
import com.tuowan.yeliao.commons.data.enums.config.NoticeSysType;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.enums.general.ChatFilterType;
import com.tuowan.yeliao.commons.data.manager.config.NewsManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.open.shumei.ShumeiSupport;
import com.tuowan.yeliao.commons.open.shumei.TextValidResult;
import com.tuowan.yeliao.commons.open.shumei.enums.ChannelType;
import com.tuowan.yeliao.commons.open.yidun.YidunSupport;
import com.tuowan.yeliao.commons.open.yidun.enums.YidunReviewType;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.commons.web.proxy.config.NotifyMode;
import com.tuowan.yeliao.social.data.entity.SCosmos;
import com.tuowan.yeliao.social.data.entity.SCosmosStat;
import com.tuowan.yeliao.social.data.manager.cosmos.CosmosManager;
import com.tuowan.yeliao.social.web.form.cosmos.CosmosAuditForm;
import com.tuowan.yeliao.social.web.form.cosmos.CosmosIdForm;
import com.tuowan.yeliao.social.web.form.cosmos.SendCosmosForm;
import com.tuowan.yeliao.social.web.vo.cosmos.CosmosDetailVO;
import com.tuowan.yeliao.social.web.vo.cosmos.CosmosListVO;
import com.tuowan.yeliao.social.web.vo.cosmos.CosmosVO;
import com.tuowan.yeliao.social.web.vo.cosmos.SendCosmosVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.resps.Tuple;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 宇宙逻辑实现
 */
@Service
public class CosmosService {
    /**
     * 用户每日发送小宇宙次数限制
     */
    private static final Integer USER_SEND_TIMES_LIMIT = 5;
    /**
     * 用户每日可查看次数限制
     */
    private static final Integer USER_WATCH_TIMES_LIMIT = 20;

    @Autowired
    private ShumeiSupport shumeiSupport;
    @Autowired
    private CosmosManager cosmosManager;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private ObjectLockTemplate objectLockTemplate;
    @Autowired
    private NoticeComponent noticeComponent;
    @Autowired
    private NewsManager newsManager;


    /**
     * 获取小宇宙数据
     */
    @BusiCode(BusiCodeDefine.CosmosList)
    public CosmosListVO queryCosmos() {
        Long userId = GlobalUtils.uid();
        // 获取7日内看过的数据
        Date now = new Date();
        List<Tuple> userWatchDataOf7 = cosmosManager.getUserWatchDataOf7(userId);
        List<Tuple> waitDelete = userWatchDataOf7.stream().filter(item -> DateUtils.plusDays(now, -7).getTime() > item.getScore())
                .collect(Collectors.toList());
        cosmosManager.removeUserWatchDataOf7(userId, waitDelete);
        userWatchDataOf7.removeAll(waitDelete);

        String listStr = cosmosManager.listCosmos(GlobalUtils.sexType(), userWatchDataOf7);
        List<String> list = StringUtils.isEmpty(listStr) ? Collections.emptyList() : ArrayUtils.toList(listStr.split(","));
        List<CosmosVO> resultList = new ArrayList<>();
        list.forEach(item -> {
            // 获取宇宙信息
            SCosmos cosmos = cosmosManager.getCosmos(Long.valueOf(item));
            // 获取用户信息
            UUserBasic userBasic = userInfoManager.getUserBasic(cosmos.getUserId());
            CosmosVO vo = new CosmosDetailVO();
            vo.setUserId(userBasic.getUserId());
            vo.setNickname(userBasic.getNickname());
            vo.setHeadPic(userBasic.getHeadPic());
            vo.setAge(BusiUtils.getAgeByDate(userBasic.getBirthDate()) + "岁");
            vo.setCosmosId(cosmos.getCosmosId());
            vo.setCity(cosmos.getCity());
            resultList.add(vo);
        });
        // 返回值封装
        CosmosListVO listVO = new CosmosListVO();
        listVO.setList(resultList);
        listVO.setSurplusTimes(Math.max(0, USER_SEND_TIMES_LIMIT - cosmosManager.getUserSendCount(userId)));
        return listVO;
    }

    /**
     * 获取小宇宙信息详情
     */
    @BusiCode(BusiCodeDefine.CosmosDetail)
    public CosmosDetailVO saveQueryCosmosDetail(CosmosIdForm form) {
        Long userId = GlobalUtils.uid();
        long userWatchCount = cosmosManager.getUserWatchCount(userId);
        if (USER_WATCH_TIMES_LIMIT <= userWatchCount) {
            throw new BusiException("当日查看次数已用完，明日再来吧~~");
        }
        SCosmos cosmos = cosmosManager.getCosmos(form.getCosmosId());
        if (Objects.isNull(cosmos)) {
            throw new DataException("数据不存在！");
        }
        UUserBasic userBasic = userInfoManager.getUserBasic(cosmos.getUserId());
        if (Objects.isNull(userBasic)) {
            throw new DataException("数据不存在！");
        }
        Date now = new Date();
        // 记录宇宙查看数DB
        SCosmosStat cosmosStat = new SCosmosStat();
        cosmosStat.setCosmosId(form.getCosmosId());
        cosmosStat.setUserId(userId);
        cosmosStat.setCreateTime(now);
        cosmosManager.saveCosmosStat(cosmosStat);
        // 递增宇宙查看数
        cosmosManager.incrCosmosWatchCount(form.getCosmosId());
        // 递增今日用户查看数
        cosmosManager.incrUserWatchCount(userId);
        // 记录用户7日内查看
        cosmosManager.saveUserWatchDataOf7(userId, now.getTime(), form.getCosmosId());

        CosmosDetailVO resultVO = new CosmosDetailVO();
        resultVO.setUserId(userBasic.getUserId());
        resultVO.setNickname(userBasic.getNickname());
        resultVO.setHeadPic(userBasic.getHeadPic());
        resultVO.setSex(userBasic.getSex());
        resultVO.setCosmosId(cosmos.getCosmosId());
        resultVO.setContent(cosmos.getContent());
        resultVO.setCity(BoolType.True == cosmos.getDisplayCity() ? cosmos.getCity() : null);
        resultVO.setSendTime(cosmos.getCreateTime().getTime());
        return resultVO;
    }

    /**
     * 发送小宇宙
     */
    @BusiCode(value = BusiCodeDefine.CosmosSend, forward = NotifyMode.DYNAMIC)
    public SendCosmosVO saveSendCosmos(SendCosmosForm form) {
        Long userId = GlobalUtils.uid();
        userLockTemplate.acquireTransactionLock(userId);
        GlobalUtils.extValue(BusinessDataKey.BreakNotify, true);
        long userSendCount = cosmosManager.getUserSendCount(userId);
        if (userSendCount >= USER_SEND_TIMES_LIMIT) {
            throw new BusiException(ErrCodeType.SendCosmosLimit);
        }
        // 发送内容合法校验
        TextValidResult result = shumeiSupport.verifyText(userId, form.getContent(), ChannelType.DYNAMIC_COMMENT, GlobalUtils.userType());
        if (ChatFilterType.Pass != result.getFilterType()) {
            throw new BusiException("宇宙内容不合法，发送失败！！");
        }
        // 位置信息
        UUserLocation userLocation = userInfoManager.getUserLocation(userId);
        String city = userLocation == null ? null : userLocation.getCity();
        Double lat = userLocation == null ? null : Double.valueOf(userLocation.getLat());
        Double lng = userLocation == null ? null : Double.valueOf(userLocation.getLng());

        Date now = new Date();
        // 数据库记录
        SCosmos cosmos = new SCosmos();
        cosmos.setUserId(userId);
        cosmos.setContent(form.getContent());
        cosmos.setCity(city);
        cosmos.setLat(lat);
        cosmos.setLng(lng);
        cosmos.setDisplayCity(form.getDisplayCity());
        cosmos.setStatus(ReviewStatus.Wait);
        cosmos.setCreateTime(now);
        cosmosManager.saveCosmos(cosmos);
        // 提交人工审核
        BoolType boolType = SettingsConfig.getBoolType(SettingsType.CosmosAuditFreeSwitch);
        if (BoolType.False == boolType) {
            if (!YidunSupport.aSyncSubmitText(YidunReviewType.Cosmos, GlobalUtils.uid(), cosmos.getCosmosId(), form.getContent())) {
                throw new BusiException("发送失败，请稍后重试");
            }
        } else {
            GlobalUtils.extValue(BusinessDataKey.CosmosId, cosmos.getCosmosId());
            GlobalUtils.extValue(BusinessDataKey.BreakNotify, false);
        }
        // 记录用户今日发送数量
        cosmosManager.incrUserSendCount(userId);
        // 返回值填充
        SendCosmosVO resultVO = new SendCosmosVO();
        resultVO.setSurplusTimes(Math.max(0, USER_SEND_TIMES_LIMIT - (userSendCount + 1)));
        return resultVO;
    }

    /**
     * 宇宙审核
     */
    @BusiCode(BusiCodeDefine.CosmosAudit)
    public void saveAudit(CosmosAuditForm form) {
        objectLockTemplate.acquireTransactionLock(ObjectLockType.CosmosAudit, form.getCosmosId());
        SCosmos cosmos = cosmosManager.getCosmos(form.getCosmosId());
        if (Objects.isNull(cosmos) || ReviewStatus.Wait != cosmos.getStatus()) {
            return;
        }
        // 修改审核状态
        SCosmos update = new SCosmos(form.getCosmosId());
        update.setStatus(form.getAuditStatus());
        cosmosManager.updateCosmos(update);

        if (ReviewStatus.Pass == form.getAuditStatus()) {
            // 1.放入宇宙集合（这里我们将分数设置为当前的时间戳）
            UUserBasic userBasic = userInfoManager.getUserBasic(cosmos.getUserId());
            cosmosManager.putCosmosCollect(cosmos.getCosmosId(), new Date().getTime(), userBasic.getSex());
        }
        if (ReviewStatus.Refuse == form.getAuditStatus()) {
            // 审核拒绝
            // 1.递减今日发送数量
            if (DateUtils.isSameDay(cosmos.getCreateTime())) {
                cosmosManager.decrUserSendCount(cosmos.getUserId());
            }
            // 2.发送系统通知
            Map<String, Object> params = new HashMap<>();
            params.put("reason", form.getAuditReason());
            noticeComponent.sendSystemNotice(cosmos.getUserId(), NoticeSysType.CosmosAuditRefuse, params);
        }
    }

}
