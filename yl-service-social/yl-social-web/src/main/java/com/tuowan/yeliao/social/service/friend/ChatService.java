package com.tuowan.yeliao.social.service.friend;

import com.alibaba.nacos.common.utils.Pair;
import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.extend.SimpleMap;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.comp.blind.BlindGiftComponent;
import com.tuowan.yeliao.commons.comp.chat.RemindComponent;
import com.tuowan.yeliao.commons.comp.check.ShuMeiPicComponent;
import com.tuowan.yeliao.commons.comp.dingtalk.DingtalkMessageComponent;
import com.tuowan.yeliao.commons.comp.headline.UserNoticeComponent;
import com.tuowan.yeliao.commons.comp.luckybox.LuckyGiftComponent;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.comp.operate.UserOperateComponent;
import com.tuowan.yeliao.commons.comp.operate.dto.OperateStatusDTO;
import com.tuowan.yeliao.commons.comp.user.*;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.YidunConfig;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.context.GlobalDataUtils;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.core.constant.FieldConstant;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.constant.VersionConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BackCodeDefine;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.business.SocialGiftDefine;
import com.tuowan.yeliao.commons.core.enums.business.SocialGoodsDefine;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.ComponentException;
import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.core.exception.GeneralException;
import com.tuowan.yeliao.commons.data.dto.common.ShumeiAudioResultDTO;
import com.tuowan.yeliao.commons.data.dto.common.TextStyleDTO;
import com.tuowan.yeliao.commons.data.dto.social.ChatTipsInfoDTO;
import com.tuowan.yeliao.commons.data.dto.user.GuardInfoDTO;
import com.tuowan.yeliao.commons.data.dto.user.UserBusiDTO;
import com.tuowan.yeliao.commons.data.entity.config.*;
import com.tuowan.yeliao.commons.data.entity.user.*;
import com.tuowan.yeliao.commons.data.enums.acct.WealthType;
import com.tuowan.yeliao.commons.data.enums.config.*;
import com.tuowan.yeliao.commons.data.enums.general.ChatFilterType;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.commons.data.enums.social.*;
import com.tuowan.yeliao.commons.data.enums.user.*;
import com.tuowan.yeliao.commons.data.enums.yd.LabelType;
import com.tuowan.yeliao.commons.data.manager.commons.SeqManager;
import com.tuowan.yeliao.commons.data.manager.config.CommonTextManager;
import com.tuowan.yeliao.commons.data.manager.config.NewsManager;
import com.tuowan.yeliao.commons.data.manager.config.ProdTagManager;
import com.tuowan.yeliao.commons.data.manager.config.SocialProdManager;
import com.tuowan.yeliao.commons.data.manager.user.*;
import com.tuowan.yeliao.commons.data.persistence.config.TAnimationMapper;
import com.tuowan.yeliao.commons.data.persistence.config.TProdSocialGiftMapper;
import com.tuowan.yeliao.commons.data.persistence.config.TProdSocialGoodsMapper;
import com.tuowan.yeliao.commons.data.persistence.user.UUserBasicMapper;
import com.tuowan.yeliao.commons.data.support.config.impl.FemaleLevelNumConfig;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.commons.data.utils.AppVersionUtils;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.data.utils.HtmlUrlUtils;
import com.tuowan.yeliao.commons.data.utils.RuleRunUtils;
import com.tuowan.yeliao.commons.open.rongim.RongImSupport;
import com.tuowan.yeliao.commons.open.shumei.ShumeiAudioSupport;
import com.tuowan.yeliao.commons.open.shumei.ShumeiSupport;
import com.tuowan.yeliao.commons.open.yidun.YidunSupport;
import com.tuowan.yeliao.commons.open.yidun.dto.YidunResultDTO;
import com.tuowan.yeliao.commons.open.yidun.enums.YidunResult;
import com.tuowan.yeliao.commons.open.yidun.enums.YidunReviewType;
import com.tuowan.yeliao.commons.web.common.form.FriendIdForm;
import com.tuowan.yeliao.commons.web.common.vo.ListVO;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.commons.web.proxy.config.NotifyMode;
import com.tuowan.yeliao.log.data.entity.CShumeiAudioLog;
import com.tuowan.yeliao.social.comp.chatmaster.ChatMasterComponent;
import com.tuowan.yeliao.social.comp.consume.dto.BlindBoxInfoDTO;
import com.tuowan.yeliao.social.comp.friend.*;
import com.tuowan.yeliao.social.comp.friend.dto.ChatUpUserInfoDTO;
import com.tuowan.yeliao.social.comp.log.LogComponent;
import com.tuowan.yeliao.social.data.config.ChatConfig;
import com.tuowan.yeliao.social.data.dto.friend.*;
import com.tuowan.yeliao.social.data.entity.FChatNetCall;
import com.tuowan.yeliao.social.data.entity.FRelationBasic;
import com.tuowan.yeliao.social.data.entity.UUserQuickReply;
import com.tuowan.yeliao.social.data.entity.family.FFamily;
import com.tuowan.yeliao.social.data.enums.friend.*;
import com.tuowan.yeliao.social.data.manager.chat.ChatRiskManager;
import com.tuowan.yeliao.social.data.manager.chat.ExclusiveChatupManager;
import com.tuowan.yeliao.social.data.manager.family.FamilyManager;
import com.tuowan.yeliao.social.data.manager.family.FreeGiftManager;
import com.tuowan.yeliao.social.data.manager.friend.ChatGuideManager;
import com.tuowan.yeliao.social.data.manager.friend.ChatManager;
import com.tuowan.yeliao.social.data.manager.friend.FriendRelationManager;
import com.tuowan.yeliao.social.data.manager.friend.GroupLuckyGiftManager;
import com.tuowan.yeliao.social.data.manager.post.PostManger;
import com.tuowan.yeliao.social.data.manager.room.RoomManager;
import com.tuowan.yeliao.social.data.persistence.FRelationBasicMapper;
import com.tuowan.yeliao.social.data.search.document.UserFeature;
import com.tuowan.yeliao.social.data.search.document.VideoChatFeature;
import com.tuowan.yeliao.social.data.search.repository.OnlineFemaleRepository;
import com.tuowan.yeliao.social.data.search.repository.UserFeatureRepository;
import com.tuowan.yeliao.social.data.search.repository.VideoChatFeatureRepository;
import com.tuowan.yeliao.social.web.form.friend.*;
import com.tuowan.yeliao.social.web.form.friend.chat.*;
import com.tuowan.yeliao.social.web.form.friend.RqGiftForm;
import com.tuowan.yeliao.social.web.form.user.UserChatInfoForm;
import com.tuowan.yeliao.social.web.vo.family.ExtraAttributeVO;
import com.tuowan.yeliao.social.web.vo.family.FamilyUserCardVO;
import com.tuowan.yeliao.social.web.vo.friend.chat.*;
import com.tuowan.yeliao.social.web.vo.user.UserChatInfoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 好友聊天相关业务
 */
@Service
public class ChatService {

    private Logger LOG = LoggerFactory.getLogger(getClass());

    @Autowired
    private SocialProdManager socialProdManager;
    @Autowired
    private ChatConsumeComponent chatConsumeComponent;
    @Autowired
    private TProdSocialGiftMapper tProdSocialGiftMapper;
    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private ShumeiSupport shumeiSupport;
    @Autowired
    private FriendRelationComponent relationComponent;
    @Autowired
    private UUserBasicMapper uUserBasicMapper;
    @Autowired
    private MessageSettingManager messageSettingManager;
    @Autowired
    private ShuMeiPicComponent shumeiPicComponent;
    @Autowired
    private FRelationBasicMapper fRelationBasicMapper;
    @Autowired
    private FriendRelationManager friendRelationManager;
    @Autowired
    private NewsManager newsManager;
    @Autowired
    private TAnimationMapper tAnimationMapper;
    @Autowired
    private UserSocialBagComponent userSocialBagComponent;
    @Autowired
    private TProdSocialGoodsMapper tProdSocialGoodsMapper;
    @Autowired
    private ProdTagManager prodTagManager;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private UserFirstChargeComponent userFirstChargeComponent;
    @Autowired
    private UserOperateComponent userOperateComponent;
    @Autowired
    private UserVisitManager userVisitManager;
    @Autowired
    private FateMatchComponent fateMatchComponent;
    @Autowired
    private FateMatchUserComponent fateMatchUserComponent;
    @Autowired
    private LuckyGiftComponent luckyGiftComponent;
    @Autowired
    private CommonTextManager commonTextManager;
    @Autowired
    private GroupLuckyGiftManager groupLuckyGiftManager;
    @Autowired
    private NetCallMatchComponent netCallMatchComponent;
    @Autowired
    private ChatManager chatManager;
    @Autowired
    private ChatGuideManager chatGuideManager;
    @Autowired
    private UserGuardComponent userGuardComponent;
    @Autowired
    private NetCallComponent netCallComponent;
    @Autowired
    private ChatRiskManager chatRiskManager;
    @Autowired
    private NoticeComponent noticeComponent;
    @Autowired
    private CpComponent cpComponent;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private SocialRedisTemplate socialRedisTemplate;
    @Autowired
    private FreeGiftManager freeGiftManager;
    @Autowired
    private IntimateComponent intimateComponent;
    @Autowired
    private PostManger postManger;
    @Autowired
    private ShumeiAudioSupport shumeiAudioSupport;
    @Autowired
    private QuickReplyComponent quickReplyComponent;
    @Autowired
    private DingtalkMessageComponent dingtalkMessageComponent;
    @Autowired
    private ChatMasterManager chatMasterManager;
    @Autowired
    private OnlineFemaleRepository onlineFemaleRepository;
    @Autowired
    private ChatupDispatchComponent chatupDispatchComponent;
    @Autowired
    private VipGuideComponent vipGuideComponent;
    @Autowired
    private LogComponent logComponent;
    @Autowired
    private UserVisitManager userVisitManager1;
    @Autowired
    private FriendRelationComponent friendRelationComponent;
    @Autowired
    private ExclusiveChatupManager exclusiveChatupManager;
    @Autowired
    private UserFeatureRepository userFeatureRepository;
    @Autowired
    private RemindComponent remindComponent;
    @Autowired
    private SeqManager seqManager;
    @Autowired
    private BothComponent bothComponent;
    @Autowired
    private ChatMasterComponent chatMasterComponent;
    @Autowired
    private VideoChatFeatureRepository videoChatFeatureRepository;
    @Autowired
    private BlindGiftComponent blindGiftComponent;
    @Autowired
    private RongImSupport rongImSupport;
    @Autowired
    private UserNoticeComponent userNoticeComponent;
    @Autowired
    private ActionStatManager actionStatManager;
    @Autowired
    private RoomManager roomManager;


    /**
     * 获取消息首页信息
     *
     * @return
     */
    @BusiCode
    public ChatHomeInfoVO saveGetHomeInfo() {
        ChatHomeInfoVO vo = new ChatHomeInfoVO();
        Long userId = GlobalUtils.uid();
        SexType sexType = GlobalUtils.sexType();
        // 通话记录红点
        vo.setCallMark(BoolType.valueOf(netCallComponent.getNetCallMark(userId)));
        UUserBasic basic = userInfoManager.getUserBasic(userId);
        // 密友在线标识
        vo.setIntimateOnline(BoolType.valueOf(intimateComponent.intimateOnline(userId)));
        boolean vip = BusiUtils.isVip(basic);
        vo.setVip(BoolType.valueOf(vip));
        return vo;
    }

    /**
     * 获取用户在线状态
     *
     * @param form
     * @return
     */
    @BusiCode
    public Map<String, OnlineStatus> getOnlineStatus(QueryStatusForm form) {
        Map<String, OnlineStatus> statusMap = new HashMap<>();
        Set<String> userIds = ListUtils.toSet(form.getUserIds());
        for (String userId : userIds) {
            UUserBasic userBasic = userInfoManager.getUserBasic(Long.parseLong(userId));
            OnlineStatus onlineStatus = userInfoManager.getOnlineStatus(userBasic.getUserId(), userBasic.getSex());
            if (onlineStatus != OnlineStatus.Offline) {
                statusMap.put(userId, onlineStatus);
            }
        }
        return statusMap;
    }

    /**
     * 私信聊天初始化
     */
    @BusiCode(value = BusiCodeDefine.ChatBasic, forward = NotifyMode.ALWAYS)
    public ChatRoomVO saveBasicInfo(ChatBasicForm form) {
        ChatRoomVO vo = new ChatRoomVO();
        // 双方基本资料
        Long friendId = form.getFriendId();
        Long userId = GlobalUtils.uid();
        SexType sexType = GlobalUtils.sexType();
        if (friendId <= 0) {
            throw new BusiException("当前用户不存在");
        }
        UUserBasic basic = userInfoManager.getUserBasic(userId);
        UUserExt ext = userInfoManager.getUserExt(userId);
        UUserBasic friendBasic = userInfoManager.getUserBasic(friendId);

        if(Objects.isNull(friendBasic)){
            throw new DataException("该用户已注销！");
        }
        // 检查私信封禁状态
        this.checkChatStatus(userId);
        RelationDTO relationDTO = friendRelationManager.getRelation(userId, friendId);
        IntimateDTO intimateDTO = intimateComponent.getChatIntimateInfo(relationDTO);
        vo.setUser(this.buildChatUserBasicVO(basic, intimateDTO.getIntimateLevel()));
        // 是否只能发语音, 1.12调整取消文字权限控制
        vo.getUser().setOnlySendVoiceMsg(BoolType.False);
        UUserMore friendMore = userInfoManager.getUserMore(friendId);
        UUserExt friendExt = userInfoManager.getUserExt(friendId);
        String voiceUrl = userInfoManager.getUserVoiceUrl(friendId);
        vo.setFriendUser(this.buildChatUserBasicVO(basic, friendBasic, friendExt, friendMore, intimateDTO.getIntimateLevel(), voiceUrl));
        vo.setBeans(GlobalUtils.beans());
        // 亲密度信息
        intimateDTO.setIntimatePrivilegeTipIcon(friendRelationManager.getIntimateLevelPrivilegeTip(userId, friendId, intimateDTO.getIntimateLevel()));
        vo.setIntimate(intimateDTO);
        // 对方消息设置
        UMessageSetting friendSetting = messageSettingManager.getSetting(friendId);
        // 是否情侣(V2.2.0以下永久为False 防止自动打卡)
        vo.setCp(BoolType.False);
        // 通话特权免费
        boolean voiceCallFree = netCallComponent.privilegeNetCallFreeByType(userId, friendId, NetCallType.Voice);
        boolean videoCallFree = netCallComponent.privilegeNetCallFreeByType(userId, friendId, NetCallType.Video);
        vo.setVoiceFee(voiceCallFree ? 0 : friendSetting.getVoiceFee());
        vo.setVideoFee(videoCallFree ? 0 : friendSetting.getVideoFee());
        // 快捷语音使用
        if (sexType == SexType.Female) {
            // 如果己方是女的，可以邀请对方打电话付费，需要返回自己的费用
            UMessageSetting mySetting = messageSettingManager.getSetting(userId);
            vo.setMyVoiceFee(voiceCallFree ? 0 : mySetting.getVoiceFee());
            vo.setMyVideoFee(videoCallFree ? 0 : mySetting.getVideoFee());
        }
        vo.setCanQuickVoice(BoolType.False);
        vo.setChatTicketCnt(0);
        vo.setRedPacketTtl(fateMatchComponent.getRedPacketInfo(GlobalUtils.uid(), friendId));
        vo.setBagChange(BoolType.valueOf(userSocialBagComponent.bagChanged(userId)));
        // 广告列表
        vo.setAdList(relationComponent.getPrivateChatAdList(sexType, friendBasic.getSex(), friendId));
        vo.setShowSystemReply(BoolType.valueOf(false));
        // 是否有限时免费券
        vo.setFreeTime(videoCallFree ? BoolType.False : BoolType.valueOf(netCallComponent.hasFreeTimeVideoTicketBagCnt(userId)));
        // 聊天背景
        vo.setChatBg(relationComponent.getChatBg(friendBasic, friendMore));
        // 是否拉黑对方
        vo.setBlacklistSign(BoolType.valueOf(relationDTO.getMyBlacklistTime() != null));
        // 是否关注对方
        vo.setFollow(BoolType.valueOf(relationDTO.getMyFollowTime() != null));
        // 私聊页面快捷回复数据合集
        vo.setQuickReplyTextList(findQuickReplyList(userId, sexType));
        // 微信查看地址
        if(SexType.Female == friendBasic.getSex() && StringUtils.isNotEmpty(friendMore.getWxQr()) && messageSettingManager.hasLookWx(friendId)){
            vo.setWxLookUrl(HtmlUrlUtils.getWxLookUrl(friendId));
        }
        if(SexType.Male == sexType){
            // 我方是男用户 备注：根据亲密度值来判断
            vo.setCallSwitch(BoolType.valueOf(relationDTO.getIntimateNum() >= GlobalConstant.MALE_ACT_CALL_NEED_INTIMATE));
        }else {
            // 我方是女用户 备注：根据主动拨打人数判断
            vo.setCallSwitch(BoolType.valueOf(netCallComponent.checkFemaleToMaleCall(userId, friendId)));
        }
        // 消息状态查看权限【需要有邀请人】
        vo.setLookMsgStatus(BoolType.valueOf(Objects.nonNull(ext.getInviteUserId())));

        return vo;
    }

    /**
     * 查询用户卡片
     *
     * @param form
     */
    @BusiCode
    public FamilyUserCardVO getUserCard(FriendIdForm form) {
        Long targetUserId = form.getFriendId();
        UUserBasic targetBasic = userInfoManager.getUserBasic(targetUserId);
        UserBusiDTO busiDTO = GlobalDataUtils.busi(targetUserId);
        boolean hideLevel = messageSettingManager.hasHideLevel(targetUserId);
        boolean hideLocation = messageSettingManager.hasHideLocation(targetUserId);
        FamilyUserCardVO vo = new FamilyUserCardVO(targetBasic, busiDTO, null, hideLevel);
        return vo;
    }

    /**
     * 获取快捷回复列表
     *
     * @param userId
     * @param sexType
     * @return
     */
    private List<QuickReplyDTO> findQuickReplyList(Long userId, SexType sexType) {
        List<QuickReplyDTO> list = new ArrayList<>();
        List<UUserQuickReply> replyList = quickReplyComponent.getCanUseReplyList(userId);
        replyList.forEach(t -> {
            list.add(new QuickReplyDTO(t));
        });
        // 自己设置的少于10条内容，那我们用系统的补足
        // 女用户不给默认文案
        if(SexType.Male == sexType && list.size() < 10){
            // 系统文案
            List<String> systemReply = commonTextManager.getRandomText(TextUseType.QuickReply, sexType, 10 - list.size());
            systemReply.forEach(t -> {
                list.add(new QuickReplyDTO(GlobalConstant.SYS_TID, t));
            });
        }
        return list;
    }

    /**
     * 刷新私信聊天道具列表
     *
     * @return
     */
    @BusiCode
    public ChatRoomVO findChatPropList() {
        ChatRoomVO vo = new ChatRoomVO();
        // 聊天券数量, 已经不需要聊天券，写死字段兼容客户端
        vo.setChatTicketCnt(0);
        return vo;
    }

    /**
     * 获取亲密度信息
     */
    @BusiCode
    public IntimateVO findIntimateInfo(FriendIdForm form) {
        Long userId = GlobalUtils.uid();
        Long friendId = form.getFriendId();
        // 亲密度信息
        IntimateDTO intimate = intimateComponent.getChatIntimateInfo(userId, friendId);
        // 情侣空间信息
        String tips = null;
        IntimateCpDTO cpInfo = new IntimateCpDTO(intimate.getIntimateLevel(), tips, GlobalConstant.OPEN_CP_INTIMATE_LEVEL);
        UUserBasic basic = userInfoManager.getUserBasic(userId);
        UUserBasic friendBasic = userInfoManager.getUserBasic(friendId);
        UUserMore friendMore = userInfoManager.getUserMore(friendId);
        // 亲密度等级列表
        List<IntimateLevelDTO> intimateList = intimateComponent.getIntimateLevelList(basic, friendBasic, friendMore, intimate.getIntimateLevel(), intimate.getNextIntimateLevel());
        return new IntimateVO(intimate, cpInfo, intimateList);
    }

    /**
     * @param form
     * @return
     */
    @BusiCode
    public ChatUserVO getUserInfo(FriendIdForm form) {
        UUserBasic friendUser = userInfoManager.getUserBasic(form.getFriendId());
        RelationDTO relation = friendRelationManager.getRelation(GlobalUtils.uid(), form.getFriendId());
        String friendNotes = friendRelationComponent.getFriendNotes(GlobalUtils.uid(), form.getFriendId(), relation.getRelation());
        OnlineStatus onlineStatus = userInfoManager.getOnlineStatus(friendUser.getUserId(), friendUser.getSex());
        return ChatUserVO.create(friendUser, onlineStatus, friendNotes, relation.getIntimateNum());
    }

    /**
     * @param form
     * @return
     */
    @BusiCode
    public ListVO getUserInfos(UserInfosForm form) {
        if(ListUtils.isEmpty(form.getFriendIds())){
            return ListVO.createEmpty();
        }
        List<ChatUserVO> resultList = new ArrayList<>();
        form.getFriendIds().forEach(friendId -> {
            UUserBasic friendUser = userInfoManager.getUserBasic(friendId);
            RelationDTO relation = friendRelationManager.getRelation(GlobalUtils.uid(), friendId);
            String friendNotes = friendRelationComponent.getFriendNotes(GlobalUtils.uid(), friendId, relation.getRelation());
            OnlineStatus onlineStatus = userInfoManager.getOnlineStatus(friendId, friendUser.getSex());
            resultList.add(ChatUserVO.create(friendUser, onlineStatus, friendNotes, relation.getIntimateNum()));
        });
        return ListVO.create(resultList);
    }

    /**
     * 查询聊天礼物列表
     */
    @BusiCode
    public ChatQueryGiftVO findGiftsInfo(GiftQueryForm form) {
        Long userId = GlobalUtils.uid();
        List<TProdSocialGift> list = socialProdManager.getGiftPanel(form.getGiftGroup());
        Map<String, List<TProdTag>> tagMap = prodTagManager.getTagMap();
        // 数量模板
        Map<String, List<TProdGiftCount>> countTplMap = socialProdManager.getGiftCountMap();
        list.sort(Comparator.comparing(TProdSocialGift::getGroupOrderNum));
        List<ChatGiftGroupVO> groupList = new ArrayList<>();
        Integer userLevel = GlobalUtils.userLevel();
        for (SocialGiftGroup group : SocialGiftGroup.values()) {
            List<ChatGiftVO> groupGiftList = new ArrayList<>();
            for (TProdSocialGift gift : list) {
                if (gift.getGroupCode() != group) {
                    continue;
                }
/*                if (form.getSourceType() == ChatSourceType.Post) {
                    Integer orderNum = ChatConfig.POST_REWARD_GIFT_MAP.get(gift.getGiftId());
                    if (orderNum == null) {
                        continue;
                    }
                    gift.setGroupOrderNum(orderNum);
                }*/
                Map<String, Object> specialParams = getGiftSpecialParams(gift.getSpecialParams());
                String tagContent = prodTagManager.findTagContentById(tagMap, gift.getGiftId(), TagUseType.Gift);

                // 检查Count模板
                String countTplCode = gift.getCountTplCode();
                if (!countTplMap.containsKey(countTplCode)) {
                    countTplCode = ChatConfig.DEFAULT_SOCIAL_GIFT_COUNT_TPL;
                }
                // 设置数量模板
                List<ChatGiftCountVO> countItemList = ChatGiftCountVO.create(countTplMap.get(countTplCode));
                ChatGiftVO chatGift = ChatGiftVO.create(gift, specialParams, tagContent, countItemList, userLevel);
                groupGiftList.add(chatGift);
            }
            if (ListUtils.isNotEmpty(groupGiftList)) {
                groupGiftList.sort(Comparator.comparing(ChatGiftVO::getOrderNum));
                groupList.add(new ChatGiftGroupVO(group.getId(), group.getDesc(), groupGiftList));
            }
        }

        ChatQueryGiftVO vo = new ChatQueryGiftVO();
        vo.setBeans(GlobalUtils.beans());
        vo.setRechargeBeans(GlobalUtils.rechargeBeans());
        vo.setPlatformBeans(GlobalUtils.platformBeans());
        vo.setSilver(GlobalUtils.silver());
        vo.setGiftGroupList(groupList);
        // 是否符合首充条件
        vo.setShowFirstRecharge(userFirstChargeComponent.checkFirstCharge(userId, GlobalUtils.clientType()));
        // 是否显示充值礼包入口(注册渠道为爱思并且使用苹果客户端则隐藏)

        vo.setShowRechargeBag(BoolType.False);
        /*UUserExt ext = userInfoManager.getUserExt(userId);
        if (!GlobalUtils.packageType().isMajia()) {
            vo.setShowRechargeBag(userInfoManager.isAisiRegChannel(GlobalUtils.clientType(), ext.getChannelCode()) ? BoolType.False : BoolType.True);
        }*/
        // 用户背包
        vo.setBagChange(BoolType.valueOf(userSocialBagComponent.bagChanged(userId)));
        vo.setCupidGoodFortuneRule(HtmlUrlUtils.getCupidGoodFortuneRule());
        return vo;
    }

    /**
     * 用户背包列表(包括礼物和消费券)
     *
     * @return
     */
    @BusiCode
    public List<ChatGiftVO> listUserBag(BagForm form) {
        Long userId = GlobalUtils.uid();
        // 礼物或物品标签
        Map<String, List<TProdTag>> tagMap = prodTagManager.getTagMap();
        // 数量模板
        Map<String, List<TProdGiftCount>> countTplMap = socialProdManager.getGiftCountMap();
        List<ChatGiftVO> resultList = new ArrayList<>();
        if(form.getType() == null || form.getType() == ProdType.Gift){
            // 背包礼物列表
            resultList.addAll(listUserBagGifts(userId, tagMap, countTplMap));
        }
        if(form.getType() == null || form.getType() == ProdType.Goods){
            // 背包物品列表
            resultList.addAll(listUserBagGoods(userId, tagMap, countTplMap));
        }
        // 移除背包异动标记
        // userSocialBagComponent.removeBagChangeMark(userId);
        return resultList;
    }

    /**
     * 发送聊天消息
     *
     * @param form
     * @return
     */
    @BusiCode(value = BusiCodeDefine.SendChatMsg, forward = NotifyMode.DYNAMIC)
    public ChatConsumeVO saveSendMsg(ChatSendMsgForm form) {
        Long sendUserId = GlobalUtils.uid();
        Long friendId = form.getFriendId();
        UUserBasic basic = userInfoManager.getUserBasic(sendUserId);
        UUserExt ext = userInfoManager.getUserExt(sendUserId);
        UUserLevel level = userInfoManager.getUserLevel(sendUserId);
        userLockTemplate.acquireTransactionLock(sendUserId);
        // 其中一方处于审核模式下，则发私信仅自己可见
        if (newsManager.isBothAndroidReviewVersion(sendUserId, friendId)) {
            return ChatConsumeVO.readonly(GlobalUtils.beans(), form.getContent());
        }
        // 检查用户关系，是否拉黑等
        RelationDTO relationDTO = this.checkFriendRelation(sendUserId, friendId);
        // 检查用户发言限制，是否真人认证，是否实名认证等
        this.checkPermission(basic, friendId, form.getContentType(), form.getSourceType(), form.getContent(), form.getVoiceId() != null, relationDTO.getIntimateNum().intValue());
        // 检查用户内容送检
        this.checkContentSubmission(relationDTO, form);
        // 检查女用户每日私聊人数限制
        this.checkFemaleMsgUserLimit(relationDTO, sendUserId, basic.getSex(), friendId);
        // 消息内容上下文
        GlobalUtils.extValue(BusinessDataKey.Content, form.getContent());
        GlobalUtils.extValue(BusinessDataKey.IntimateNum, relationDTO.getIntimateNum());
        // 易盾风控检测
        UrlParamsMap ydRiskSwitch = UrlParamsMap.build(SettingsConfig.getString(SettingsType.RegYdRiskControl));
        if(ydRiskSwitch.getBoolean("msg") && (ChatContentType.Text == form.getContentType() || ChatContentType.Pic == form.getContentType() || ChatContentType.Audio == form.getContentType())){
            Object sceneData = MapUtils.gmap("targetId", String.valueOf(friendId), "contentType", ChatContentType.Pic == form.getContentType() ? "image" : form.getContentType().name().toLowerCase());
            userOperateComponent.ydRiskControlCheck(YidunConfig.RISK_CONTROL_MSG_BUSINESS_ID, form.getYdRiskToken(), sendUserId, GlobalUtils.clientIp(), sceneData);
        }
        // 是否直接附带某种固定提示
        ChatRiskType noCheckRiskType = null;
        UUserBasic friendUser = userInfoManager.getUserBasic(friendId);
        GlobalUtils.extValue(BusinessDataKey.FriendSex, friendUser.getSex());
        String newContent = null;
        if (form.getContentType() == ChatContentType.Text && form.isVerifyText()) {
            // 文本校验
            this.verifyTextPlus(basic, ext, level, relationDTO.getIntimateNum(), relationDTO.getIntimateLevel(), friendId, form.getContent());
        } else if (form.getContentType() == ChatContentType.Pic && form.isVerifyText()) {
            // 图片校验
            this.verifyPic(basic, ext, level, relationDTO.getIntimateNum(), relationDTO.getIntimateLevel(), friendId, form.getContent());
        } else if (form.getContentType() == ChatContentType.Audio && form.isVerifyText()) {
            // 语音校验
            this.verifyAudio(basic, ext, level, relationDTO.getIntimateNum(), relationDTO.getIntimateLevel(), friendId, form.getContent());
        }
        String msgId = StringUtils.getUUID();
        ChatConsumeDTO consumeDTO = chatConsumeComponent.saveSendMsg(relationDTO, form.getSourceType(), form.getContentType());
        SocialGoodsDefine goodsDefine = SocialGoodsDefine.getByGoodId(consumeDTO.getProdId());
        ChatConsumeVO vo = new ChatConsumeVO(GlobalUtils.beans(), consumeDTO.getTotalBeans(), consumeDTO.getProdCnt(), goodsDefine, relationDTO);
        // 设置提成金额
        if (isShowPresentCash(consumeDTO.getTargetPresentCash())) {
            vo.setPresentCash(BusiUtils.cashToYuanSimplifyStr(consumeDTO.getTargetPresentCash(), 4));
        }
        vo.setTipsInfo(this.buildChatTipsInfoIfNeed(basic, GlobalUtils.sexType(), friendId, form.getContentType(), form.getContent(), newContent, noCheckRiskType, consumeDTO.getTotalBeans()));
        vo.setMsgId(msgId);
        GlobalUtils.extValue(BusinessDataKey.TotalBeans, consumeDTO.getTotalBeans());
        // 增加互动标记
        fateMatchUserComponent.addInteractMark(sendUserId, friendId);
        // 增加visit<访问>相关互动标记
        userVisitManager1.saveInteractSign(BusiUtils.generateRelationId(sendUserId, friendId));
        // 记录消息发送记录，等待融云回调验证，验证通过，才能下发给接收方
        chatConsumeComponent.markWaitCallbackNew(msgId);
        return vo;
    }

    /**
     * 检查用户发言权限，主要限制打广告
     *
     * @param friendId
     * @param contentType
     * @param content
     */
    private void checkPermission(UUserBasic basic, Long friendId, ChatContentType contentType, ChatSourceType sourceType,
                                 String content, boolean quickVoice, Integer intimateNum) {
        UUserBasic friendBasic = userInfoManager.getUserBasic(friendId);
        if(basic.getSex() == friendBasic.getSex()){
            throw new BusiException("暂不支持！");
        }
        // 检查内容是否合法，骰子只能是1-6点，石头剪刀布的结果只能是这三种
        String validContent = contentType.getContent();
        if (StringUtils.isNotBlank(validContent) && !StringUtils.containsTarget(validContent, content)) {
            throw new BusiException("发送内容有误");
        }
        UMessageSetting setting = messageSettingManager.getSetting(basic.getUserId());
        if(BoolType.False == setting.getMsgSend()){
            throw new BusiException("您已被禁言，禁止发送消息！");
        }
    }

    private void checkContentSubmission(RelationDTO relationDTO, ChatSendMsgForm msgForm){
        try {
            if(relationDTO.getIntimateNum() >= IntimateNumPrivilege.CKLT.getNum()){
                msgForm.setVerifyText(false);
            }
        }catch (Exception e){
            LOG.error("ChatService-checkContentSubmission-error e:", e);
            // do nothing
        }
    }

    private void checkFemaleMsgUserLimit(RelationDTO relationDTO, Long userId, SexType sexType, Long friendId){
        try {
            // 消息发送方不是女用户 直接返回
            if(SexType.Female != sexType){
                return;
            }
            // 双方存在亲密度 直接返回
            if(relationDTO.getIntimateNum() > 0){
                return;
            }
            // 双方有私聊会话建立时间 直接返回
            if(Objects.nonNull(relationDTO.getFccTime())){
                return;
            }
            // 判断今天是否达到私聊新用户人数限制
            String dateStr = DateUtils.toString(new Date(GlobalUtils.reqTime()), DatePattern.YMD2);
            if(actionStatManager.getFemaleSocialUsersNum(dateStr, userId, FieldConstant.FEMALE_ACT_MSG_NEW_MALE)  >= chatMasterManager.getActMsgLimit(userId)){
                throw new BusiException("今日主动私聊人数已用尽！");
            }
            // 事物后记录女用户今天主动私聊新用户人数
            CallbackAfterTransactionUtil.send(() -> {
                actionStatManager.addFemaleByTypeForUsers(userId, FieldConstant.FEMALE_ACT_MSG_NEW_MALE, dateStr, friendId);
            });
        }catch (GeneralException e){
            throw e;
        }catch (Exception e){
            LOG.error("ChatService-checkFemaleMsgUserLimit-error e:", e);
        }
    }

    /**
     * 开通/取代守护
     *
     * @param form
     * @return
     */
    @BusiCode(value = BusiCodeDefine.OpenGuard, forward = NotifyMode.ALWAYS)
    public OpenGuardResultVO saveOpenGuard(OpenGuardForm form) {
        Long userId = GlobalUtils.uid();
        if (userId.equals(form.getFriendId())) {
            throw new BusiException("不能给自己开通守护");
        }
        userLockTemplate.acquireTransactionLock(userId);
        // 检查用户关系
        RelationDTO relationDTO = checkFriendRelation(userId, form.getFriendId());

        // 检查礼物可用性
        Integer giftId = SocialGiftDefine.GuardAngle.getGiftId();
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(giftId));
        doCheckGift(gift);

        Long totalBeans = form.getCount() * gift.getBeans().longValue();
        // 保存消费记录
        UUserBasic targetUser = uUserBasicMapper.selectByPrimaryKey(new UUserBasic(form.getFriendId()));
        GlobalUtils.extValue(BusinessDataKey.FriendSex, targetUser.getSex());
        ChatConsumeDTO consumeDTO = null; //chatConsumeComponent.saveSendGift(relationDTO, targetUser, gift, form.getCount(), totalBeans, ChatSourceType.Guard, null, false, false);


        String presentCash = null;
        if (consumeDTO.getTargetPresentCash() != null && consumeDTO.getTargetPresentCash() > 0) {
            presentCash = BusiUtils.cashToYuanStr(consumeDTO.getTargetPresentCash());
        }

        // 开通守护
        GuardInfoDTO topGuardInfo = userGuardComponent.saveOpenGuard(userId, form.getFriendId(), giftId, form.getCount());
        ChatGiftSimpleVO giftInfo = ChatGiftSimpleVO.create(gift, form.getCount(), null);
        GlobalUtils.formValue("chatGiftId", giftId);
        return OpenGuardResultVO.create(topGuardInfo, relationDTO.getRelationId(), giftInfo, presentCash);
    }

    /**
     * 私信文本校验（新）
     * 备注：对于私信文本的校验，检验接口请求不成功我们也让发送
     */
    private void verifyTextPlus(UUserBasic userBasic, UUserExt userExt, UUserLevel userLevel, Long intimateNum, Integer intimateLevel, Long friendId, String content) {
        if(StringUtils.isBlank(content)){
            throw new BusiException("不允许发送空白消息！");
        }
        YidunResultDTO.CommonResult commonResult = YidunSupport.syncSubmitChatText(YidunReviewType.TextChat, userBasic, userExt, userLevel, intimateLevel, GlobalUtils.clientIp(), friendId, userBasic.getUserId(), content);
        // 如果亲密度大于 10000 允许发送联系方式
        if(Objects.nonNull(commonResult.getLabel()) && commonResult.getLabel() == LabelType.Ad){
            if(intimateNum < 10000){
                throw new BusiException(ErrCodeType.IntimateNeNotSendContact);
            }else{
                return;
            }
        }
        // 内容违规直接抛出【内容涉嫌违规】
        if(YidunResult.Invalid == commonResult.getResult()){
            throw new BusiException(ErrCodeType.ContentFailed);
        }
        // 内容嫌疑 且 标签是[广告][广告法]抛出【内容涉嫌违规】
        if(YidunResult.Suspect == commonResult.getResult() && (LabelType.Ad == commonResult.getLabel() || LabelType.AdLaw == commonResult.getLabel())){
            throw new BusiException(ErrCodeType.ContentFailed);
        }
    }

    /**
     * 私信图片校验
     * 女用户发图片：魅力等级需达到6级
     * 男用户发图片：财富等级需达到6级
     */
    private void verifyPic(UUserBasic sendUserBasic, UUserExt sendUserExt, UUserLevel sendUserLevel, Long intimateNum, Integer intimateLevel, Long friendId, String picUrl){
        YidunResultDTO.CommonResult commonResult = YidunSupport.syncSubmitChatPic(YidunReviewType.PicChat, sendUserBasic, sendUserExt, sendUserLevel, intimateLevel, GlobalUtils.clientIp(), friendId, sendUserBasic.getUserId(), picUrl);
        // 如果亲密度大于 10000 允许发送联系方式
        if(Objects.nonNull(commonResult.getLabel()) && commonResult.getLabel() == LabelType.Ad){
            if(intimateNum < 10000){
                throw new BusiException(ErrCodeType.IntimateNeNotSendContact);
            }else{
                return;
            }
        }
        // 内容违规直接抛出【内容涉嫌违规】
        if(YidunResult.Invalid == commonResult.getResult()){
            throw new BusiException(ErrCodeType.ImageFailed);
        }
        // 内容嫌疑 且 标签是[广告][广告法]抛出【内容涉嫌违规】
        if(YidunResult.Suspect == commonResult.getResult() && (LabelType.Ad == commonResult.getLabel() || LabelType.AdLaw == commonResult.getLabel())){
            throw new BusiException(ErrCodeType.ImageFailed);
        }
    }

    /**
     * 私信语音限制
     * 女用户发语音：魅力等级需达到6级
     * 男用户发语音：财富等级需达到6级
     */
    private void verifyAudio(UUserBasic sendUserBasic, UUserExt sendUserExt, UUserLevel sendUserLevel, Long intimateNum, Integer intimateLevel, Long friendId, String audioUrl){
        YidunResultDTO.CommonResult commonResult = YidunSupport.syncSubmitChatAudio(YidunReviewType.AudioChat, sendUserBasic, sendUserExt, sendUserLevel, intimateLevel, GlobalUtils.clientIp(), friendId, sendUserBasic.getUserId(), audioUrl);
        // 如果亲密度大于 10000 允许发送联系方式
        if(Objects.nonNull(commonResult.getLabel()) && commonResult.getLabel() == LabelType.Ad){
            if(intimateNum < 10000){
                throw new BusiException(ErrCodeType.IntimateNeNotSendContact);
            }else{
                return;
            }
        }
        // 内容违规直接抛出【内容涉嫌违规】
        if(YidunResult.Invalid == commonResult.getResult()){
            throw new BusiException(ErrCodeType.AudioFailed);
        }
        // 内容嫌疑 且 标签是[广告][广告法]抛出【内容涉嫌违规】
        if(YidunResult.Suspect == commonResult.getResult() && (LabelType.Ad == commonResult.getLabel() || LabelType.AdLaw == commonResult.getLabel())){
            throw new BusiException(ErrCodeType.AudioFailed);
        }
    }

    /**
     * 生成打招呼消息提醒
     */
    private ChatTipsInfoDTO buildChatUpTipsInfoIfNeed() {
        String textTpl = "回复消息可获得${award}，可在收益功能内${withDraw}哦~";
        Map<String, Object> textParam = new HashMap<>();
        textParam.put("${award}", "积分奖励");
        textParam.put("${withDraw}", "提现");
        List<TextStyleDTO> textStyleList = new ArrayList<>();
        textStyleList.add(TextStyleDTO.Builder.create().setStyleType("basic").setEl("${award}").setColor("#FDE504").build());
        textStyleList.add(TextStyleDTO.Builder.create().setStyleType("basic").setEl("${withDraw}").setColor("#16EBFF").build());
        return ChatTipsInfoDTO.create(textTpl, textParam, textStyleList);
    }

    /**
     * 生成私信消息提醒
     * * @param sendUserId
     *
     * @param friendId
     * @return
     */
    private ChatTipsInfoDTO buildChatTipsInfoIfNeed(UUserBasic userBasic, SexType sexType, Long friendId, ChatContentType contentType, String content, String newContent, ChatRiskType noCheckRiskType, Long totalBean) {
        // 如果发送方是男用户方 并且是第一次私聊、搭讪
        if(SexType.Male == sexType && !bothComponent.existMark(userBasic.getUserId(), friendId, UserBothKeyMark.FirstPrivateChat)){
            UMessageSetting targetMesSet = messageSettingManager.getSetting(friendId);
            return ChatTipsInfoDTO.createByTypeAndParams(ChatTipsType.MaleFirstSendChatMsg, MapUtils.gmap("bean", targetMesSet.getMsgFee()));
        }
        // 敏感词提示
        if (StringUtils.isNotEmpty(newContent) && newContent.contains("*")) {
            return ChatTipsInfoDTO.createByType(ChatTipsType.Sensitive);
        }
        Long sendUserId = userBasic.getUserId();
        if (userBasic.getRealPerson() == BoolType.False && userBasic.getSex() == SexType.Female
                && chatConsumeComponent.needRealHeadRemind(sendUserId, friendId)) {
            BoolType boolType = userInfoManager.checkUserFinishRealPerson(sendUserId);
            return ChatTipsInfoDTO.createByType(BoolType.True == boolType ? ChatTipsType.RealHead : ChatTipsType.RealPerson);
        }
        // 直接根据风险类型返回
        if (null != noCheckRiskType) {
            return ChatTipsInfoDTO.create(noCheckRiskType);
        }
        // 是否触发敏感词提醒
        // 根据用户的消费金额来决定是否需要触发提醒
        String relationId = BusiUtils.generateRelationId(sendUserId, friendId);
        Long consumeGiftBeans = socialRedisTemplate.getLong(RedisKey.create(SocialKeyDefine.PrivateGiftConsumeBeans, relationId));
        ChatRiskType riskType = chatRiskManager.check(contentType, content, sexType, consumeGiftBeans);
        if (riskType != null) {
            // 敏感词触发日志
            logComponent.saveChatRiskLog(GlobalUtils.tid(), GlobalUtils.reqTime(), sendUserId, friendId, content, riskType);
            return ChatTipsInfoDTO.create(riskType);
        }
        ChatTipsType type = chatGuideManager.getChatTipsType(sendUserId, friendId);
        if (type == null) {
            List<ChatTipsType> tips = remindComponent.getDayRemind(sendUserId, friendId);
            type = ListUtils.isNotEmpty(tips) ? tips.get(0) : null;
        }
        return ChatTipsInfoDTO.createByType(type);
    }

    /**
     * 替换转义字和移除非法字符 并判断用户发言是否改变
     */
    private Map.Entry<Boolean, String> repalceChatContentSpecialChars(String source) {
        String filterSpecialChars = BusiUtils.filterSpecialChars(source);
        String result = BusiUtils.escapeChars(filterSpecialChars);
        return new AbstractMap.SimpleEntry<>(!result.equals(source), result);
    }

    /**
     * 赠送盲盒礼物
     *
     * @param form
     * @return
     */
    @BusiCode(value = BusiCodeDefine.SendBlindBox, forward = NotifyMode.DYNAMIC)
    public ChatSendGiftVO saveSendBlindBox(ChatSendGiftForm form) {
        Long userId = GlobalUtils.uid();
        if (userId.equals(form.getFriendId())) {
            throw new BusiException("不能给自己送礼物");
        }
        userLockTemplate.acquireTransactionLock(userId);
        // 检查礼物可用性
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(form.getChatGiftId()));
        if (BoolType.True != gift.getIsBlind()) {
            GlobalUtils.extValue(BusinessDataKey.BreakNotify, true);
            return null;
        }
        if (form.getCount() != 1) {
            throw new BusiException("送礼数量必须为1");
        }
        doCheckGift(gift);

        // 检查用户关系
        RelationDTO relationDTO = checkFriendRelation(userId, form.getFriendId());
        UUserBasic targetUser = uUserBasicMapper.selectByPrimaryKey(new UUserBasic(form.getFriendId()));
        // 保存消费记录
        Long totalBeans = form.getCount() * gift.getBeans().longValue();
        BlindBoxInfoDTO blindBoxInfo = chatConsumeComponent.saveSendBlindBox(relationDTO, targetUser, gift, form.getCount(), totalBeans);
        blindBoxInfo.setSpecialParams(this.getGiftSpecialParams(blindBoxInfo.getGift().getSpecialParams()));

        ChatSendGiftVO vo = ChatSendGiftVO.create(totalBeans, relationDTO);
        vo.setBindTid(GlobalUtils.tid());
        vo.setBlindBoxInfo(blindBoxInfo);
        vo.setGiftInfo(ChatGiftSimpleVO.create(blindBoxInfo));
        vo.setTipsInfo(ChatTipsInfoDTO.create(MsgUtils.format(ChatConfig.BLIND_BOX_TEXT_TPL, GlobalUtils.nickname(), gift.getGiftName(), form.getCount(), blindBoxInfo.getProdName(), blindBoxInfo.getProdCnt())));
        vo.setBeans(GlobalUtils.beans());
        // 盲盒增加财富经验
        GlobalUtils.extValue(BusinessDataKey.ConsumerTotalBeans, totalBeans);
        Long callId = netCallComponent.getCallId(userId);
        if (callId != null && callId.equals(netCallComponent.getCallId(form.getFriendId()))) {
            GlobalUtils.extValue(BusinessDataKey.SendStreamMsg, BoolType.True);
        }
        // 盲盒拆出的礼物总价值
        GlobalUtils.extValue(BusinessDataKey.AwardValue, blindBoxInfo.getTotalBeans());
        GlobalUtils.extValue(BusinessDataKey.AwardGiftName, blindBoxInfo.getProdName());
        GlobalUtils.extValue(BusinessDataKey.AwardGiftPic, blindBoxInfo.getProdPic());
        GlobalUtils.extValue(BusinessDataKey.AwardGiftCount, blindBoxInfo.getProdCnt());
        GlobalUtils.extValue(BusinessDataKey.ConsumerGiftName, gift.getGiftName());
        GlobalUtils.extValue(BusinessDataKey.ConsumerGiftPic, gift.getPic());
        GlobalUtils.extValue(BusinessDataKey.UserIds, new Long[]{userId, form.getFriendId()});
        return vo;
    }

    /**
     * 聊天送礼
     *
     * @param form
     * @return
     */
    @BusiCode(value = BusiCodeDefine.SendChatGift, forward = NotifyMode.ALWAYS)
    public ChatSendGiftVO saveSendGift(ChatSendGiftForm form) {
        Long userId = GlobalUtils.uid();
        if (userId.equals(form.getFriendId())) {
            throw new BusiException("不能给自己送礼物");
        }
        if (form.getCount() < 1) {
            throw new BusiException("送礼数量必须大于1");
        }
        userLockTemplate.acquireTransactionLock(userId);
        // 检查用户关系
        RelationDTO relationDTO = checkFriendRelation(userId, form.getFriendId());
        // 检查礼物可用性
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(form.getChatGiftId()));
        // 检查礼物可用性
        this.doCheckGift(gift);
        Long totalBeans = form.getCount() * gift.getBeans().longValue();
        Long totalSilver = form.getCount() * gift.getSilver().longValue();
        GlobalUtils.extValue(BusinessDataKey.IntimateLevel, relationDTO.getIntimateLevel());
        GlobalUtils.extValue(BusinessDataKey.ConsumeGiftGroup, gift.getGroupCode());
        boolean fromBag = form.getFromBag().boolValue();
        // 保存消费记录
        UUserBasic targetUser = userInfoManager.getUserBasic(form.getFriendId());
        Long targetPresentCash = chatConsumeComponent.saveSendGift(form.getSourceType(), userId, targetUser, gift, form.getCount(), totalBeans, totalSilver, fromBag);
        Map<String, Object> giftSpecialParams = getGiftSpecialParams(gift.getSpecialParams(), form.getCount());
        GlobalUtils.extValue(BusinessDataKey.ConsumeGiftSpecialParams, giftSpecialParams);
        GlobalUtils.extValue(BusinessDataKey.ConsumeGiftSpecialType, gift.getSpecialType());
        String msgId = StringUtils.getUUID();
        ChatSendGiftVO vo = ChatSendGiftVO.create(totalBeans, relationDTO);
        vo.setMsgId(msgId);
        vo.setGiftInfo(ChatGiftSimpleVO.create(gift, form.getCount(), getGiftSpecialParams(gift.getSpecialParams())));
        if (fromBag) {
            // 从背包送出，直接更新背包数量即可
            vo.setBagCount(userSocialBagComponent.getGiftCnt(userId, gift.getGiftId()));
        }
        vo.setBeans(GlobalUtils.beans());
        vo.setRechargeBeans(GlobalUtils.rechargeBeans());
        vo.setPlatformBeans(GlobalUtils.platformBeans());
        vo.setSilver(GlobalUtils.silver());
        if (isShowPresentCash(targetPresentCash)) {
            vo.setPresentCash(BusiUtils.cashToYuanSimplifyStr(targetPresentCash, 4));
        }
        // 增加互动标记
        fateMatchUserComponent.addInteractMark(userId, form.getFriendId());
        // 增加访客相关互动标记
        userVisitManager1.saveInteractSign(BusiUtils.generateRelationId(userId, form.getFriendId()));
        // 待融云回调验证标记
        chatConsumeComponent.markWaitCallbackNew(msgId);
        return vo;
    }


    @BusiCode
    public RqGiftVO saveRqGift(RqGiftForm form){
        if(SexType.Female != GlobalUtils.sexType()){
            throw new BusiException("暂不支持！");
        }
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(form.getGiftId()));
        if(Objects.isNull(gift)){
            throw new DataException("礼物不存在！");
        }
        // 判断对方账户余额是否足够
        UserBusiDTO friendBusi = GlobalDataUtils.busi(form.getFriendId());
        if(WealthType.Bean == gift.getGroupCode().getType() && friendBusi.getRechargeBeans() < gift.getBeans()){
            throw new BusiException("对方账户余额不足！");
        }
        if(WealthType.Silver == gift.getGroupCode().getType() && friendBusi.getSilver() < gift.getSilver()){
            throw new BusiException("对方账户余额不足！");
        }
        // 如果是Vip礼物 则判断对方是否是Vip
        if(SocialGiftGroup.Vip == gift.getGroupCode() && !userInfoManager.isVip(form.getFriendId())){
            throw new BusiException("对方不是Vip，无法赠送Vip礼物！");
        }
        // 索要频率控制
        Pair<Boolean, Long> checkPair = chatConsumeComponent.checkRqGiftFrequency(GlobalUtils.uid(), form.getFriendId());
        if(!checkPair.getFirst()){
            throw new BusiException(MsgUtils.format("索要礼物过于频繁，请{}秒后再试~", checkPair.getSecond()));
        }
        // 礼物索要频率记录
        chatConsumeComponent.recordRqGiftFrequency(GlobalUtils.uid(), form.getFriendId());

        String msgId = null;
        if(Objects.nonNull(form.getCallId())){ // 表示是通话中送礼

            FChatNetCall call = netCallComponent.getChatNetCall(form.getCallId());
            if(Objects.isNull(call) || (!call.getUserId().equals(form.getFriendId()) && !call.getFriendId().equals(form.getFriendId()))){
                throw new DataException(ErrCodeType.InvalidArguments);
            }
            if(NetCallStatus.Accept != call.getStatus()){
                throw new BusiException("当前未在通话中，不支持索要礼物！");
            }
            // 发送通知
            messageComponent.sendRequestGiftNoticeInCall(GlobalUtils.uid(), form.getFriendId(), form.getCallId(), gift.getGiftId(), gift.getGroupCode(), gift.getGiftName(), gift.getPic(), 1, gift.getBeans());
        }else{ // 表示是非通话中送礼

            // 待融云回调验证标记
            msgId = StringUtils.getUUID();
            chatConsumeComponent.markWaitCallbackNew(msgId);
        }
        // 返回值构建
        RqGiftVO vo = new RqGiftVO();
        vo.setFriendId(GlobalUtils.uid());
        vo.setGiftId(gift.getGiftId());
        vo.setGiftGroup(gift.getGroupCode());
        vo.setGiftName(gift.getGiftName());
        vo.setGiftPic(gift.getPic());
        vo.setCount(1); // 我们默认只要索要1个
        vo.setBeans(gift.getBeans());
        vo.setMsgId(msgId);
        return vo;
    }

    /**
     * 测试经验值累加（送礼）
     */
    @BusiCode(value = BusiCodeDefine.SendChatGift, forward = NotifyMode.ALWAYS)
    public void saveSendGiftTest(ChatSendBackupGiftForm form) {
        if (UnifiedConfig.isProdEnv()) {
            throw new BusiException("测试环境接口！！");
        }
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(form.getGiftId()));
        GlobalUtils.setUserId(form.getUserId());
        GlobalUtils.extValue(BusinessDataKey.ConsumerTotalBeans, gift.getBeans().longValue());
        Map<String, Object> formMap = new HashMap<>();
        formMap.put("friendId", form.getFriendId());
        GlobalUtils.getGlobalContext().setFormMap(formMap);
    }

    /**
     * 私聊页面初始化视频聊数据
     */
    private List<PvVideoChatVO> vcFriendList(UUserBasic friendBasic, UUserMore friendMore, UMessageSetting friendSetting ){
        // 获取 通话物品券信息
        Pair<Integer, ProdGoodsType> ticketInfo = netCallComponent.getVideoCallUseTicketInfo(GlobalUtils.uid());
        Integer prodId = Objects.isNull(ticketInfo) ? null : ticketInfo.getFirst();
        // 获取好友视频单价
        List<PvVideoChatVO> resultList = new ArrayList<>();
        resultList.add(PvVideoChatVO.build(friendBasic, friendMore, prodId, MsgUtils.format("视频通话 {}金币/分钟", friendSetting.getVideoFee())));
        List<VideoChatFeature> videoChatFeatures = videoChatFeatureRepository.searchVideoChatForPv(GlobalUtils.sexType(), 10);
        if(ListUtils.isNotEmpty(videoChatFeatures)){
            videoChatFeatures.forEach(item -> {
                if(friendBasic.getUserId().equals(item.getId())){
                    return;
                }
                UUserBasic itemBasic = userInfoManager.getUserBasic(item.getId());
                UUserMore itemMore = userInfoManager.getUserMore(item.getId());
                Integer itemVideoFee = messageSettingManager.getSetting(item.getId()).getVideoFee();
                resultList.add(PvVideoChatVO.build(itemBasic, itemMore, prodId, MsgUtils.format("视频通话 {}金币/分钟", itemVideoFee)));
            });
        }
        return resultList;
    }

    /**
     * 检查是否覆盖关系盖章
     *
     * @param gift
     * @param userId
     */
    private void doCheckSeal(TProdSocialGift gift, Integer count, Long userId, Long friendId, String sealName) {
        List<UUserBag> bagList = userSocialBagComponent.findUserValidGoods(friendId, userId, ProdGoodsType.SocialSeal);
        if (ListUtils.isEmpty(bagList)) {
            return;
        }
        UUserBag bag = bagList.get(0);
        Integer sealGoodsId = UrlParamsMap.build(gift.getExtJsonCfg()).getInt("sealGoodsId");
        if (bag.getProdId().equals(sealGoodsId)) {
            // 同一个盖章，直接延长有效期，不用覆盖
            return;
        }
        String oldSealName = bag.getProdName();
        if (StringUtils.isEmpty(oldSealName)) {
            oldSealName = tProdSocialGoodsMapper.selectByPrimaryKey(new TProdSocialGoods(bag.getProdId())).getGoodsName();
        }
        sealName = StringUtils.isNotEmpty(sealName) ? sealName : gift.getGiftName();
        throw new BusiException(ErrCodeType.ReplaceSeal, MsgUtils.format("确定使用【{}】（{}天）覆盖【{}】（{}天）", sealName, count * 3, oldSealName, DateUtils.getDiffDays(DateUtils.nowTime(), bag.getExpTime()) + 1));
    }

    /**
     * 检查是否能够发消息
     */
    private RelationDTO checkFriendRelation(Long userId, Long friendId) {
        if (userId.equals(friendId)) {
            throw new BusiException("不能对自己操作~");
        }
        // 检查私信封禁状态
        checkChatStatus(userId);
        String relationId = BusiUtils.generateRelationId(userId, friendId);
        FRelationBasic relation = fRelationBasicMapper.selectByPrimaryKey(new FRelationBasic(relationId));
        RelationDTO dto = RelationDTO.create(userId, friendId, relation);
        // 判断拉黑关系
        if (dto.getMyBlacklistTime() != null) {
            if (BusiCodeDefine.SendChatMsg == GlobalUtils.busiCodeName()) {
                throw new BusiException("你把对方拉入黑名单了，无法发送消息哦~");
            }
            if (BusiCodeDefine.SendChatGift == GlobalUtils.busiCodeName()) {
                throw new BusiException("解除黑名单后才能相互送礼哦");
            }
            if (BusiCodeDefine.ChatInvite == GlobalUtils.busiCodeName()) {
                throw new BusiException("解除黑名单后才能邀请对方哦");
            }
        }
        if (dto.getTargetBlacklistTime() != null) {
            if (BusiCodeDefine.SendChatMsg == GlobalUtils.busiCodeName()) {
                throw new BusiException("你已被对方加入黑名单");
            }
            if (BusiCodeDefine.SendChatGift == GlobalUtils.busiCodeName()) {
                throw new BusiException("对方已将你拉黑，送礼失败");
            }
            if (BusiCodeDefine.ChatInvite == GlobalUtils.busiCodeName()) {
                throw new BusiException("对方已将你拉黑，邀请失败");
            }
        }
        return dto;
    }

    /**
     * 检查礼物的可用性
     */
    private void doCheckGift(TProdSocialGift gift) {
        if(Objects.isNull(gift)){
            throw new BusiException("礼物不存在，请刷新页面重试哦~");
        }
        // VIP礼物是否支持赠送
        if(SocialGiftGroup.Vip == gift.getGroupCode() && !userInfoManager.isVip(GlobalUtils.uid())){
            throw new BusiException("该礼物是VIP用户才能赠送哦~");
        }
        // 礼物是否解锁
        if (GlobalUtils.userLevel() < gift.getUnlockLevel()) {
            throw new BusiException("当前礼物未解锁，无法赠送");
        }
        // 当前时间礼物是否有效
        Date nowTime = DateUtils.nowTime();
        if (gift.getEffTime() != null && nowTime.before(gift.getEffTime())) {
            throw new BusiException(MsgUtils.format("该礼物{}才能送哦~", DateUtils.toString(gift.getEffTime(), DatePattern.MD_H)));
        }
        if (gift.getStatus() != Status.Enable || (gift.getExpTime() != null && nowTime.after(gift.getExpTime()))) {
            throw new BusiException("礼物已下架，请刷新礼物列表");
        }
    }

    /**
     * READONLY：客户端能看不能送
     * HIDE：客户端不显示
     * SHOW：正常显示也能送
     * <p>
     * MatchRule 如果以Whitelist开头，表示需要验证是否在指定的白名单中
     * 规则 ：Whitelist#白名单Code<白名单分组>（多个以逗号分隔）
     *
     * @return true意味着满足，允许显示或者送出
     */
    private boolean checkGiftRuleMatch(TProdSocialGift g, boolean needValidReadonly) {
        if (StringUtils.isNotBlank(g.getMatchRule())) {
            String matchValue = RuleRunUtils.eval(g.getMatchRule(), "giftId", g.getGiftId());
            if ("HIDE".equalsIgnoreCase(matchValue)) {
                return false;
            }
            return !needValidReadonly || !"READONLY".equalsIgnoreCase(matchValue);
        }
        return true;
    }

    /**
     * READONLY：客户端能看不能送
     * HIDE：客户端不显示
     * SHOW：正常显示也能送
     * <p>
     * MatchRule 如果以Whitelist开头，表示需要验证是否在指定的白名单中
     * 规则 ：Whitelist#白名单Code<白名单分组>（多个以逗号分隔）
     *
     * @return true意味着满足，允许显示或者送出
     */
    private boolean checkGoodsRuleMatch(TProdSocialGoods g, boolean needValidReadonly) {
        if (StringUtils.isNotBlank(g.getMatchRule())) {
            String matchValue = RuleRunUtils.eval(g.getMatchRule(), "goodsId", g.getGoodsId());
            if ("HIDE".equalsIgnoreCase(matchValue)) {
                return false;
            }
            return !needValidReadonly || !"READONLY".equalsIgnoreCase(matchValue);
        }
        return true;
    }

    private UserChatBasicVO buildChatUserBasicVO(UUserBasic basic, Integer intimateLevel) {
        UserChatBasicVO vo = new UserChatBasicVO();
        vo.setUserId(basic.getUserId());
        vo.setNickname(basic.getNickname());
        vo.setHeadPic(basic.getHeadPic());
        vo.setSex(basic.getSex());
        vo.setUserType(basic.getUserType());
        vo.setRealPerson(basic.getRealPerson());
        vo.setRealName(BoolType.valueOf(userInfoManager.hasRealName(basic.getUserId())));
        if (intimateLevel != null) {
            vo.setCpIcon(cpComponent.getCpIcon(intimateLevel.longValue()));
        }
        vo.setHeadFrame(GlobalUtils.headFrame());
        // 气泡样式
        vo.setExtraAttribute(new ExtraAttributeVO(userSocialBagComponent.getWearChatBubble(basic)));
        // 是否完成真人认证
        BoolType finishRealPerson = userInfoManager.checkUserFinishRealPerson(basic.getUserId());
        vo.setFinishRealPerson(finishRealPerson);
        // 特权icon
        vo.setPrivilegeIcon(IntimatePrivilege.getPrivilegeIconInChat(intimateLevel));
        vo.setVipType(BusiUtils.getVipType(basic));
        return vo;
    }

    private UserChatBasicVO buildChatUserBasicVO(UUserBasic currBasic, UUserBasic basic, UUserExt ext, UUserMore more, Integer intimateLevel, String voiceUrl) {
        UserChatBasicVO vo = new UserChatBasicVO();
        vo.setUserId(basic.getUserId());
        vo.setNickname(basic.getNickname());
        vo.setHeadPic(basic.getHeadPic());
        UserBusiDTO busiDTO = GlobalDataUtils.busi(basic.getUserId());
        vo.setHeadFrame(busiDTO.getHeadFrame());
        vo.setSex(basic.getSex());
        vo.setUserType(basic.getUserType());
        vo.setAge(BusiUtils.getAgeByDate(basic.getBirthDate()));
        vo.setMySign(StringUtils.isEmpty(basic.getMySign()) ? basic.getDefaultSign() : basic.getMySign());
        vo.setVoiceUrl(voiceUrl);
        vo.setRealName(BoolType.valueOf(userInfoManager.hasRealName(basic.getUserId())));
        vo.setRealPerson(basic.getRealPerson());
        OnlineStatus onlineStatus = userInfoManager.getOnlineStatus(basic.getUserId(), basic.getSex());
        vo.setOnlineStatus(onlineStatus);
        vo.setOnlineStatusText(onlineStatus.getDesc());
        if (intimateLevel != null) {
            vo.setCpIcon(cpComponent.getCpIcon(intimateLevel.longValue()));
        }
        // 属性列表
        vo.setAttrList(getAttrList(currBasic, basic, ext, more, vo.getAge()));
        // 封面列表
        vo.setPicList(userInfoManager.getUserPics(basic.getUserId()));
        // 好友卡片提示语
//        vo.setPoint(commonTextManager.getRandomText(TextUseType.ChatPoint));
        // 是否完成真人认证
        BoolType finishRealPerson = userInfoManager.checkUserFinishRealPerson(basic.getUserId());
        vo.setFinishRealPerson(finishRealPerson);
        // 特权icon
        vo.setPrivilegeIcon(IntimatePrivilege.getPrivilegeIconInChat(intimateLevel));
        vo.setVipType(BusiUtils.getVipType(basic));
        // 身高、体重、职业
        vo.setHeight(more.getHeight());
        vo.setWeight(more.getWeight());
        vo.setProfessionName(userInfoManager.getProfessionName(more));
        return vo;
    }

    /**
     * 获取属性列表
     *
     * @param currBasic
     * @param basic
     * @param more
     * @param age
     * @return
     */
    private List<AttrDTO> getAttrList(UUserBasic currBasic, UUserBasic basic, UUserExt ext, UUserMore more, Integer age) {
        List<AttrDTO> attrList = new ArrayList<>();
        attrList.add(AttrDTO.build(age + "岁"));
        if(SexType.Male == basic.getSex()){ // 年龄|家乡|现居
            TCity city = userInfoManager.getCityById(more.getHometownCityId());
            if(Objects.nonNull(city)){
                attrList.add(AttrDTO.build(MsgUtils.format("家乡:{}", city.getCity())));
            }else if(StringUtils.isNotEmpty(ext.getCity())){
                attrList.add(AttrDTO.build(MsgUtils.format("家乡:{}", ext.getCity())));
            }
            UUserLocation location = userInfoManager.getUserLocation(basic.getUserId());
            if(Objects.nonNull(location) && !messageSettingManager.hasHideLocation(basic.getUserId()) && StringUtils.isNotEmpty(location.getCity())){
                attrList.add(AttrDTO.build(MsgUtils.format("现居:{}", location.getCity())));
            }
        }
        if(SexType.Female == basic.getSex()){ // 年龄|职业|收入
            String professionName = userInfoManager.getProfessionName(more);
            if (StringUtils.isNotEmpty(professionName)) {
                attrList.add(AttrDTO.build(professionName));
            }
            String incomeText = userInfoManager.getIncomeText(more);
            if (StringUtils.isNotEmpty(incomeText)) {
                attrList.add(AttrDTO.build(incomeText));
            }
        }
        return attrList;
    }

    /**
     * 获取聊天详情
     */
    @BusiCode
    public UserChatInfoVO getDetail(UserChatInfoForm form) {
        Long userId = GlobalUtils.uid();
        UserChatInfoVO vo = new UserChatInfoVO();
        vo.setFriendId(form.getTargetUserId());
        // 获取对方用户信息
        UUserBasic userBasic = uUserBasicMapper.selectByPrimaryKey(new UUserBasic(form.getTargetUserId()));
        if (userBasic != null) {
            vo.setNickname(userBasic.getNickname());
            vo.setHeadPic(userBasic.getHeadPic());
            String mySign = userBasic.getMySign();
            if (StringUtils.isEmpty(mySign)) {
                mySign = userBasic.getDefaultSign();
            }
            vo.setMySign(mySign);
        }
        RelationDTO relationDTO = friendRelationManager.getRelation(userId, form.getTargetUserId());
        if (relationDTO.getRelation() != null) {
            vo.setIntimateLevel(relationDTO.getIntimateLevel());
            vo.setRemindSign(relationDTO.getMyRemindSign());
            vo.setBlacklistSign(BoolType.valueOf(relationDTO.getMyBlacklistTime() != null));
            vo.setFollow(BoolType.valueOf(Objects.nonNull(relationDTO.getMyFollowTime())));
        } else {
            vo.setRemindSign(BoolType.True);
            vo.setBlacklistSign(BoolType.False);
            vo.setFollow(BoolType.False);
        }
        return vo;
    }

    /**
     * 私信状态检查
     *
     * @param userId
     */
    private void checkChatStatus(Long userId) {
        OperateStatusDTO banChat = userOperateComponent.hasBanChat(userId);
        if (banChat.isIndefinite()) {
            throw new BusiException("由于账号存在疑似违规言论，已被全站禁言");
        }
        if (banChat.getRemainingSeconds() > 0) {
            throw new BusiException("由于账号存在疑似违规言论，已被全站禁言12小时");
        }
    }

    /**
     * 后台发送消息
     *
     * @param form
     */
    @BusiCode(BusiCodeDefine.BackupChatMsg)
    public void saveSendBackupMsg(ChatSendBackupMsgForm form) {
        form.setFriendId(GlobalUtils.uid());
        chatConsumeComponent.sendChatMsg(form.getUserId(), form.getFriendId(), form.getContent(), form.getImgUrl(), form.getContentType(), form.getFee(), form.getConsumeTicketCnt());
    }

    /**
     * 后台发送送礼消息
     *
     * @param form
     */
    @BusiCode(BusiCodeDefine.BackupChatMsg)
    public void saveSendBackupGiftMsg(ChatSendBackupGiftForm form) {
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(form.getGiftId()));
        chatConsumeComponent.sendBackupGiftMsg(form.getUserId(), form.getFriendId(), gift, form.getCount(), getGiftSpecialParams(gift.getSpecialParams()));
    }

    /**
     * 后台模拟发送一条私信消息
     */
    @BusiCode
    public void saveSendTestMsg(ChatSendMsgForm form) {
        Long userId = GlobalUtils.uid();
        FFamily family = familyManager.getFamily(1);
        chatConsumeComponent.sendInviteMsg(userId, form.getFriendId(), family);
    }

    /**
     * 获取用户背包礼物列表
     *
     * @param userId
     * @param tagMap
     * @param countTplMap
     * @return
     */
    private List<ChatGiftVO> listUserBagGifts(Long userId, Map<String, List<TProdTag>> tagMap, Map<String, List<TProdGiftCount>> countTplMap) {
        // 获取背包礼物列表
        List<UUserBag> bagList = userSocialBagComponent.findUserValidGifts(userId);
        if (ListUtils.isEmpty(bagList)) {
            return Collections.emptyList();
        }
        Integer userLevel = GlobalUtils.userLevel();
        // 根据礼物ID分组
        Map<Integer, List<UUserBag>> bagGroup = ListUtils.grouping(bagList, UUserBag::getProdId);
        List<ChatGiftVO> resultList = new ArrayList<>();
        for (Integer giftId : bagGroup.keySet()) {
            TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(giftId));
            if (gift == null) {
                LOG.error("用户{}背包内存在非法礼物，id为{}", userId, giftId);
                continue;
            }
            if (gift.getStatus() == Status.Disable) {
                continue;
            }
            // 根据匹配规则过滤礼物
            if (!checkGiftRuleMatch(gift, false)) {
                continue;
            }
            // 检查Count模板
            String countTplCode = gift.getCountTplCode();
            if (!countTplMap.containsKey(countTplCode)) {
                countTplCode = ChatConfig.DEFAULT_SOCIAL_GIFT_COUNT_TPL;
            }
            List<UUserBag> subBagList = bagGroup.get(giftId);
            Integer bagCount = subBagList.stream().mapToInt(UUserBag::getSurplusCount).sum();
            if (bagCount <= 0) {
                continue;
            }
            // 动画参数
            Map<String, Object> specialParams = getGiftSpecialParams(gift.getSpecialParams());
            // 角标内容
            String tagContent = prodTagManager.findTagContentById(tagMap, gift.getGiftId(), TagUseType.Gift);
            // 数量模板
            List<ChatGiftCountVO> countItemList = ChatGiftCountVO.create(countTplMap.get(countTplCode));
            ChatGiftVO giftVO = ChatGiftVO.create(gift, specialParams, tagContent, countItemList, userLevel);
            // 设置取该礼物背包数量
            giftVO.setBagCount(bagCount);
            // 过期时间
            giftVO.setExpTime(userSocialBagComponent.getLatelyExpTimeStr(subBagList, true));
            // 标记该礼物背包是否发送变更
            giftVO.setChangeMark(BoolType.valueOf(userSocialBagComponent.bagGiftChanged(userId, subBagList.get(0).getBagId())));
            resultList.add(giftVO);
        }
        resultList.sort(Comparator.comparing(ChatGiftVO::getOrderNum));
        return resultList;
    }

    /**
     * 获取用户背包物品列表
     *
     * @param userId
     * @param tagMap
     * @param countTplMap
     * @return
     */
    private List<ChatGiftVO> listUserBagGoods(Long userId, Map<String, List<TProdTag>> tagMap, Map<String, List<TProdGiftCount>> countTplMap) {
        // 获取背包物品列表
        List<UUserBag> bagList = userSocialBagComponent.findUserValidGoods(userId);
        if (ListUtils.isEmpty(bagList)) {
            return Collections.emptyList();
        }
        // 根据礼物ID分组
        Map<Integer, List<UUserBag>> bagGroup = ListUtils.grouping(bagList, UUserBag::getProdId);
        // 物品使用默认数量模板
        List<ChatGiftCountVO> countItemList = ChatGiftCountVO.create(countTplMap.get(ChatConfig.DEFAULT_SOCIAL_GIFT_COUNT_TPL));
        List<ChatGiftVO> resultList = new ArrayList<>();
        Map<Integer, ChatGiftVO> goodsMap = new HashMap<>();
        for (Integer goodsId : bagGroup.keySet()) {
            TProdSocialGoods goods = tProdSocialGoodsMapper.selectByPrimaryKey(new TProdSocialGoods(goodsId));
            if (goods == null) {
                LOG.error("用户{}背包内存在非法物品，id为{}", userId, goodsId);
                continue;
            }
            if (goods.getStatus() == Status.Disable || !goods.getGoodsType().isShowInBag()) {
                continue;
            }
            // 根据匹配规则过滤礼物
            if (!checkGoodsRuleMatch(goods, false)) {
                continue;
            }
            List<UUserBag> subBagList = bagGroup.get(goodsId);
            // 根据获取时间倒序，以至于能获取到相同物品最新的一个背包
            subBagList.sort(Comparator.comparing(UUserBag::getBagId).reversed());
            Integer bagCount = subBagList.stream().mapToInt(UUserBag::getSurplusCount).sum();
            // 背包清除有延时
            if (bagCount <= 0) {
                continue;
            }
            // 子级物品统一到父级物品展示
            Integer parentId = goods.getParentGoodsId();
            if (null != parentId) {
                ChatGiftVO vo = goodsMap.get(parentId);
                if (vo != null) {
                    // 已经存在，累加数量
                    Integer oldBagCount = null == vo.getBagCount() ? 0 : vo.getBagCount();
                    Integer newBagCount = subBagList.stream().mapToInt(UUserBag::getSurplusCount).sum();
                    vo.setBagCount(oldBagCount + newBagCount);
                    boolean change = userSocialBagComponent.bagGiftChanged(userId, subBagList.get(0).getBagId());
                    // 子级和父级综合的，有一个为true 则为true,否不做变化
                    if (change) {
                        vo.setChangeMark(BoolType.valueOf(true));
                    }
                    // 子级和父级综合的，哪个失效时间小显示哪个
                    Date goodExpTime = userSocialBagComponent.getLatelyExpTime(subBagList, false);
                    if (null != goodExpTime) {
                        // vo.getGoodExpTime()不存在或者goodExpTime 在 vo.getGoodExpTime()之前
                        if (null == vo.getGoodExpTime() || (DateUtils.getDiffSeconds(vo.getGoodExpTime(), goodExpTime) < 0)) {
                            // 物品过期时间
                            vo.setGoodExpTime(goodExpTime);
                            vo.setExpTime(userSocialBagComponent.formatExpTime(goodExpTime));
                        }
                    }
                    continue;
                }
                goodsId = parentId;
                goods = tProdSocialGoodsMapper.selectByPrimaryKey(new TProdSocialGoods(goodsId));
            }
            // 角标内容
            String tagContent = prodTagManager.findTagContentById(tagMap, goodsId, TagUseType.Goods);
            ChatGiftVO giftVO = ChatGiftVO.create(goods, tagContent, countItemList);
            // 设置取该物品背包数量
            giftVO.setBagCount(bagCount);
            // 标记该礼物品包是否发送变更
            giftVO.setChangeMark(BoolType.valueOf(userSocialBagComponent.bagGiftChanged(userId, subBagList.get(0).getBagId())));
            // 物品过期时间
            giftVO.setExpTime(userSocialBagComponent.getLatelyExpTimeStr(subBagList, false));
            goodsMap.put(goodsId, giftVO);
        }
        if (goodsMap.size() > 0) {
            resultList = new ArrayList<>(goodsMap.values());
            resultList.sort(Comparator.comparing(ChatGiftVO::getOrderNum));
        }
        return resultList;
    }

    /**
     * 获取礼物动画参数配置
     *
     * @param specialParams
     * @return
     */
    private Map<String, Object> getGiftSpecialParams(String specialParams, Integer sendCount) {
        if (StringUtils.isEmpty(specialParams)) {
            return null;
        }

        Map<String, Object> paramMap = JsonUtils.toJsonMap(specialParams);
        if (paramMap.get("animNeedCount") != null) {
            // 部分礼物需要达到指定的送礼数量才会触发
            if (sendCount == null || sendCount < Integer.parseInt(String.valueOf(paramMap.get("animNeedCount")))) {
                return null;
            }
        }
        String animateCode = paramMap.get("animCode") == null ? null : paramMap.get("animCode").toString();
        if (StringUtils.isNotEmpty(animateCode)) {
            TAnimation animation = tAnimationMapper.selectByPrimaryKey(new TAnimation(animateCode));
            if (animation != null) {
                paramMap.put("svgaUrl", animation.getSvgaUrl());
                paramMap.put("webpUrl", animation.getWebpUrl());
            }
        }
        return paramMap;
    }

    /**
     * 获取礼物动画参数配置
     *
     * @param specialParams
     * @return
     */
    private Map<String, Object> getGiftSpecialParams(String specialParams) {
        return getGiftSpecialParams(specialParams, null);
    }


    /**
     * 聊天邀请信息
     *
     * @param form
     * @return
     */
    @BusiCode(BusiCodeDefine.ChatInvite)
    public ChatInviteVO saveInviteChat(ChatInviteForm form) {
        Long userId = GlobalUtils.uid();
        Date now = new Date(GlobalUtils.reqTime());
        // 如果其中一方是审核模式，提示对方已被邀请过多
        if (newsManager.isBothAndroidReviewVersion(userId, form.getFriendId())) {
            throw new BusiException("他被太多人邀请了，请稍后再试");
        }
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        UUserBasic friendBasic = userInfoManager.getUserBasic(form.getFriendId());
        if (userBasic.getSex() == friendBasic.getSex()) {
            throw new BusiException("暂不支持！");
        }
        // 检查对方是否开启了视频邀请
        if(ChatInviteType.VideoCall == form.getInviteType() && !messageSettingManager.hasVideoInvite(form.getFriendId())){
            throw new BusiException("对方已关闭视频邀请服务！");
        }
        // 视频邀请-检查自己是否开启了视频接听服务
        if(ChatInviteType.VideoCall == form.getInviteType() && !messageSettingManager.hasVideoAnswer(userId)){
            throw new BusiException("您已关闭视频接听服务，无法邀请通话！");
        }
        // 语音邀请-检查自己是否开启了语音接听服务
        if(ChatInviteType.VoiceCall == form.getInviteType() && !messageSettingManager.hasVoiceCall(userId)){
            throw new BusiException("您已关闭语音接听服务，无法邀请通话！");
        }
        // 检查用户好友关系
        this.checkFriendRelation(userId, form.getFriendId());
        // 女用户邀请需满足真人
        if (SexType.Female == userBasic.getSex() && (BoolType.True != userBasic.getRealPerson() || BoolType.True != userBasic.getRealName())) {
            throw new BusiException("完成真人、实名认证后才可邀请哦～");
        }
        if (form.getInviteType() == ChatInviteType.VideoCall || form.getInviteType() == ChatInviteType.VoiceCall) {
            userOperateComponent.checkBanAudio(userId, friendBasic.getUserId());
        }
        // 邀请评率限制
        chatManager.checkInviteLimit(now, userBasic, form.getFriendId(), form.getInviteType());
        String msgId = StringUtils.getUUID();
        ChatInviteVO vo = new ChatInviteVO(form.getInviteType(), friendBasic.getSex());
        vo.setMsgId(msgId);
        TextUseType useType = null;
        if (form.getInviteType() == ChatInviteType.VideoCall) {
            useType = TextUseType.InviteVideoCall;
            vo.setPrice(messageSettingManager.getSetting(userId).getVideoFee());
            if(SexType.Female == GlobalUtils.sexType()){
                CallbackAfterTransactionUtil.send(() -> {
                    // 记录女用户【视频邀请】次数
                    actionStatManager.incrFemaleByType(userId, 1L, DateUtils.toString(now, DatePattern.YMD2), FieldConstant.FEMALE_VIDEO_INVITE_NUM);
                });
            }
            // 记录邀请记录
            netCallComponent.saveCallListCache(userId, form.getFriendId(), null, NetCallType.Video, NetCallSourceType.Invite, DateUtils.nowTime());
        } else if (form.getInviteType() == ChatInviteType.VoiceCall) {
            useType = TextUseType.InviteVoiceCall;
            vo.setPrice(messageSettingManager.getSetting(userId).getVoiceFee());
            if(SexType.Female == GlobalUtils.sexType()){
                CallbackAfterTransactionUtil.send(() -> {
                    // 记录女用户【语音邀请】次数
                    actionStatManager.incrFemaleByType(userId, 1L, DateUtils.toString(now, DatePattern.YMD2), FieldConstant.FEMALE_VOICE_INVITE_NUM);
                });
            }
            // 记录邀请记录
            netCallComponent.saveCallListCache(userId, form.getFriendId(), null, NetCallType.Voice, NetCallSourceType.Invite, DateUtils.nowTime());
        }
        if (useType != null) {
            vo.setContent(commonTextManager.getRandomText(useType, SexType.Male));
        }
        // 邀请成功之后邀请限制缓存处理
        chatManager.saveInviteInfo(now, userId, GlobalUtils.sexType(), form.getFriendId(), form.getInviteType());
        // 消息只读,目前没有业务控制
        vo.setReadOnly(BoolType.False);
        vo.setFriendId(friendBasic.getUserId());
        vo.setNickname(friendBasic.getNickname());
        vo.setHeadPic(friendBasic.getHeadPic());
        // 待融云回调验证
        chatConsumeComponent.markWaitCallbackNew(msgId);
        return vo;
    }

    @BusiCode
    public void saveTestFate(FateTestForm form) {
        // 创建红包派发订单
        Long targetUserId = form.getTargetUserId();
        Long userId = form.getUserId();
        fateMatchComponent.test(targetUserId, userId);
    }

    /**
     * 测试系统私信提醒
     *
     * @param form
     */
    @BusiCode
    public void saveSendTestTips(FateTestForm form) {
        Map<String, Object> paramMap = new HashMap<>();
        UUserBasic friend = userInfoManager.getUserBasic(form.getTargetUserId());
        if (friend == null) {
            throw new BusiException("用户不存在");
        }
        paramMap.put("friendId", friend.getUserId());
        paramMap.put("nickname", friend.getNickname());
        paramMap.put("headPic", friend.getHeadPic());
        ChatTipsInfoDTO tips = ChatTipsInfoDTO.createWithTouchText("恭喜你有收到礼物啦！${touchText}", "点击查看情侣空间", null, ClientTouchType.LoveSpace);
        paramMap.putAll(JsonUtils.toJsonMap(tips));
        Map<BusinessDataKey, Object> extMap = new HashMap<>();
        extMap.put(BusinessDataKey.CustomMap, paramMap);
        messageComponent.sendMsgToUserInContext(BackCodeDefine.ChatTips, extMap, form.getTargetUserId());
    }
    @BusiCode
    public void saveChatUpMatchTest(FateTestForm form) {
        //fateMatchUserComponent.saveSendChatUpMatchMessage(form.getUserId(), form.getTargetUserId());
    }

    @BusiCode
    public ListVO getCuUsers(){
        // 仅允许男用户调用
        if(SexType.Male != GlobalUtils.sexType()){
            throw new BusiException(ErrCodeType.InvalidArguments);
        }
        boolean androidReviewVersion = newsManager.isAndroidReviewVersion(GlobalUtils.clientType(), GlobalUtils.clientVersion(), GlobalUtils.channelId(), GlobalUtils.packageType(), GlobalUtils.uid());
        if(androidReviewVersion){
            return ListVO.createEmpty();
        }
        Date now = new Date(GlobalUtils.reqTime());
        List<String> list = fateMatchComponent.maleMatchFemale(now, GlobalUtils.uid(), 6, MatchType.FC);
        if(ListUtils.isEmpty(list) || list.size() < 3){
            return ListVO.createEmpty();
        }
        List<ChatUpUserInfoDTO> resultList = new ArrayList<>();
        list.forEach(item -> {
            UUserBasic userBasic = userInfoManager.getUserBasic(Long.valueOf(item));
            resultList.add(ChatUpUserInfoDTO.build01(userBasic.getUserId(), userBasic.getNickname(), userBasic.getHeadPic()));
        });
        return ListVO.create(resultList);
    }

    /**
     * 搭讪
     *
     * @param form
     * @return
     */
    @BusiCode(value = BusiCodeDefine.SendChatMsg, forward = NotifyMode.ALWAYS)
    public ChatConsumeVO saveChatUp(ChatSendMsgForm form) {
        Long userId = GlobalUtils.uid();
        if(SexType.Male != GlobalUtils.sexType()){
            throw new BusiException(ErrCodeType.InvalidArguments);
        }
        // 搭讪卡剩余
        if(ChatSourceType.TodayFate != form.getSourceType()){
            TProdSocialGoods goods = tProdSocialGoodsMapper.selectByPrimaryKey(new TProdSocialGoods(SocialGoodsDefine.ChatUp.getGoodsId()));
            int surplusChatUpCnt = userSocialBagComponent.getGoodsCnt(userId, ProdGoodsType.SocialTicket, goods.getGoodsId());
            if(surplusChatUpCnt <= 0 && GlobalUtils.beans() < goods.getBeans()){
                throw new ComponentException(ErrCodeType.BeanNotEnough);
            }
        }

        Date now = new Date(GlobalUtils.reqTime());
        String extGiftMsgId = StringUtils.getUUID();
        ChatConsumeVO vo = saveSendMsg(form);
        vo.setExtGiftMsgId(extGiftMsgId);
        vo.setContent(form.getContent());
        vo.setChatUpPrice(0);
        vo.setTipsInfo(this.buildChatUpTipsInfoIfNeed());
        vo.setGiftVO(ChatGiftSimpleVO.create("玫瑰花", "cfg/gift/rose.jpg", 1));
        // 记录今天搭讪用户（备注：男用户给女用户搭讪 一天只能搭讪一次；女用户给男用户搭讪 12小时可以搭讪一次）
        chatManager.saveChatUpUser(now, userId, GlobalUtils.sexType(), form.getFriendId());
        //记录搭讪数据
        logComponent.saveMaleChatUpData(userId, form.getFriendId(), form.getContent(), null, null,
                form.getSourceType().getId());
        // 一键搭讪完 后续处理
        if(form.getSourceType() == ChatSourceType.TodayFate){
            TFemaleLevelNum config = FemaleLevelNumConfig.getConfig(chatMasterManager.getFemaleLevel(form.getFriendId()));
            fateMatchComponent.recordMaleFastCuData(now, form.getFriendId(), config.getFcuHourLimit(), config.getFcuDayLimit());
        }
        // 打上融云回调标记 备注：客户端需要携带一个礼物消息
        chatConsumeComponent.markWaitCallbackNew(extGiftMsgId);
        return vo;
    }

    /**
     * 扣减金币
     */
    @BusiCode(BusiCodeDefine.FrontBusiDeductBean)
    public void saveDeductBean(DeductBeanForm form){
        Long userId = GlobalUtils.uid();
        userLockTemplate.acquireTransactionLock(userId);
        if(DeductBeanType.LookChargePic == form.getType()){
            // 校验金币是否足够
            if(GlobalUtils.beans() < form.getType().getBeans()){
                throw new BusiException(ErrCodeType.BeanNotEnough);
            }
            chatConsumeComponent.saveLookChargePic(userId, form.getTargetUserId(), -DeductBeanType.LookChargePic.getBeans());
        } else if(DeductBeanType.LookChargeVideo == form.getType()){
            // 校验金币是否足够
            if(GlobalUtils.beans() < form.getType().getBeans()){
                throw new BusiException(ErrCodeType.BeanNotEnough);
            }
            chatConsumeComponent.saveLookChargeVideo(userId, form.getTargetUserId(), -DeductBeanType.LookChargeVideo.getBeans());
        } else{
            throw new BusiException(ErrCodeType.InvalidArguments);
        }
    }

    /**
     * 保存数美音频文件结果
     *
     * @param form
     */
    @BusiCode
    public void saveAudioText(ShumeiAudioCallbackForm form) {
        String btId = form.getBtId();
        if (StringUtils.isEmpty(btId)) {
            return;
        }
        String[] msgInfo = btId.split("_");
        Long tid = null;
        Long reqTime = null;
        String channel = null;
        if (msgInfo.length == 3) {
            tid = Long.parseLong(msgInfo[1]);
            reqTime = Long.parseLong(msgInfo[2]);
            channel = "C";
        } else {
            tid = Long.parseLong(msgInfo[0]);
            reqTime = Long.parseLong(msgInfo[1]);
            channel = "S";
        }
        // 调用日志服务存储
        CShumeiAudioLog log = new CShumeiAudioLog(new Date(reqTime), tid);
        log.setAudioText(form.getAudioText());
        log.setCallbackTime(DateUtils.nowTime());
        log.setRiskLevel(form.getRiskLevel());
        log.setLabels(form.getLabels());
        log.setChannel(channel);
        logComponent.saveShumeiAudioLog(log);
        if (ListUtils.isEmpty(form.getDetail())) {
            return;
        }
        if (form.getCallbackParam() == null || form.getCallbackParam().get("userId") == null) {
            return;
        }
        Long userId = Long.parseLong(String.valueOf(form.getCallbackParam().get("userId")));
        for (ShumeiAudioResultDTO dto : form.getDetail()) {
            // 广告：广告：广告-音频 触发打广告预警
            if (dto.getRiskType() != null && dto.getRiskType().equals(300) && "MA000007006001001".equals(dto.getAudioModel())) {
                boolean success = logComponent.triggerAdRisk(userId, UserActionRiskTriggerType.InvalidAudio, ChatFilterType.Pass, form.getAudioText());
                if (success) {
                    // 全站禁言12小时
                    userOperateComponent.saveBanChat(userId, 12 * 3600);
                }
                return;
            }
        }
    }

    @BusiCode
    public void saveTestChatup(FateTestForm form) {
        Long userId = form.getUserId();
        Long friendId = form.getTargetUserId();
        FRelationBasic basic = fRelationBasicMapper.selectByPrimaryKey(new FRelationBasic(BusiUtils.generateRelationId(userId, friendId)));
        RelationDTO dto = RelationDTO.create(userId, friendId, basic);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("user", buildChatUserBasicVO(userInfoManager.getUserBasic(userId), dto.getIntimateLevel()));
        paramMap.put("friendUser", buildChatUserBasicVO(userInfoManager.getUserBasic(friendId), dto.getIntimateLevel()));
        paramMap.put("content", ListUtils.random(Arrays.asList("你好啊， 你是哪里人啊， 你喜欢什么样的女孩子")));
        paramMap.put("tips", ChatTipsInfoDTO.createChatupTips("[飞吻][飞吻][飞吻]小红娘觉得你俩很般配，已为你${touchText}, 别错过缘分", "主动搭讪", ChatTipsShowType.Sender));
        paramMap.put("receiverTips", ChatTipsInfoDTO.createChatupTips("[飞吻][飞吻][飞吻]小红娘觉得你俩很般配，向你发来${touchText}, 别错过缘分", "主动搭讪", ChatTipsShowType.Receiver));
        GlobalUtils.extValue(BusinessDataKey.CustomMap, paramMap);
        GlobalUtils.extValue(BusinessDataKey.OverwriteBusiCode, BusiCodeDefine.SystemChatUp);
        GlobalUtils.extValue(BusinessDataKey.UserId, userId);
        messageComponent.sendMatchMsgs();

    }

    /**
     * 处理聊天消息回调
     * 服务端发送的消息不会回调此接口
     *
     * @param form
     * @return
     */
    @BusiCode
    public ChatCallbackVO saveCallbackPlusNew(ChatCallbackForm form) {
        // 判断是否开启消息回调校验
        if (!SettingsConfig.getBoolean(SettingsType.RongImCallbackCheck)) {
            return ChatCallbackVO.pass();
        }
        try {
            if(!GlobalConstant.CLIENT_VALID_MSG_TYPE.contains(form.getMsgType())){
                return ChatCallbackVO.refuse();
            }
            // 解析消息内容，获取我们的 msgId
            SimpleMap contentMap = JsonUtils.toSimpleMap(form.getContent());
            SimpleMap extraMap = JsonUtils.toSimpleMap(contentMap.getString("extra"));
            String msgId = extraMap.getString("msgId");
            if (chatConsumeComponent.processCallbackNew(msgId)) {
                // 最近交互记录处理
                chatConsumeComponent.saveSocialLastlyInteractiveTime(form.getFromUserId(), form.getTargetId());
                // 消息回复情况处理
                chatConsumeComponent.recordReplyData(Long.valueOf(form.getFromUserId()), Long.valueOf(form.getTargetId()), true, true);
                return ChatCallbackVO.pass();
            }
        } catch (Exception e) {
            LOG.error("私信回调解析消息失败, 消息内容：" + form.getContent(), e);
        }
        return ChatCallbackVO.refuse();
    }

    @BusiCode
    public ChatCallbackVO saveCallbackPlusOld(ChatCallbackForm form) {
        // 判断是否开启消息回调校验
        if (!SettingsConfig.getBoolean(SettingsType.RongImCallbackCheck)) {
            return ChatCallbackVO.pass();
        }
        try {
            Map<String, Object> msgInfoMap = JsonUtils.toJsonMap(form.getContent());
            String md5Content = null; // 各类消息用于编码的内容
            ChatContentType contentType = null; // 消息类型
            // 我们只对我们设置的消息类型做处理 【RC:HQVCMsg<私聊-语音> | RC:ImgMsg<私聊-图片> | RC:TxtMsg<私聊-文本>】
            if(Objects.equals(form.getMsgType(), "RC:TxtMsg")){
                contentType = ChatContentType.Text;
                md5Content = (String) msgInfoMap.get("content");
            }else if(Objects.equals(form.getMsgType(), "RC:ImgMsg")){
                contentType = ChatContentType.Pic;
                md5Content = (String) msgInfoMap.get("imageUri");
            }else if(Objects.equals(form.getMsgType(), "RC:HQVCMsg")){
                contentType = ChatContentType.Audio;
                md5Content = ChatContentType.Audio.name();
            }else{
                return SettingsConfig.getString(SettingsType.InvalidRyMsgType).contains(form.getMsgType()) ? ChatCallbackVO.refuse() : ChatCallbackVO.pass();
            }

            //如果 md5Content 和 contentType 只要有一个没有值 我们就拒绝
            if(StringUtils.isEmpty(md5Content)){
                return ChatCallbackVO.refuse();
            }
            if (chatConsumeComponent.processCallbackOld(form.getFromUserId(), form.getTargetId(), contentType, md5Content)) {
                return ChatCallbackVO.pass();
            }
        } catch (Exception e) {
            LOG.error("私信回调解析消息失败, 消息内容：" + form.getContent(), e);
        }
        return ChatCallbackVO.refuse();
    }

    /**
     * 修改自动搭讪状态
     *
     * @param form
     */
    @BusiCode
    public void updateChatUpStatus(ChatUpStatusUpdateForm form) {
        chatupDispatchComponent.updateChatUpStatus(GlobalUtils.uid(), form.getChatUpStatus());
    }

    @BusiCode
    public void saveTestCloseChatUpStatus(FateTestForm form) {
        Long userId = form.getUserId();
        chatupDispatchComponent.updateChatUpStatus(userId, BoolType.False);
        GlobalUtils.extValue(BusinessDataKey.OverwriteBusiCode, BusiCodeDefine.ChatUpStatusClosed);
        GlobalUtils.extValue(BusinessDataKey.UserId, userId);
        messageComponent.sendMatchMsgs();

    }

    /**
     * 是否显示提成
     *
     * @param targetPresentCash
     * @return
     */
    private boolean isShowPresentCash(Long targetPresentCash) {
        if (targetPresentCash == null || targetPresentCash <= 0) {
            return false;
        }
        return true;
    }
}
