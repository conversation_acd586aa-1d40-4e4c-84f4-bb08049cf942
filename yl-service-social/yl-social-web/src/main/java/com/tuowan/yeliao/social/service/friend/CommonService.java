package com.tuowan.yeliao.social.service.friend;

import com.alibaba.nacos.common.utils.Pair;
import com.tuowan.yeliao.commons.comp.consume.ConsumePresentComponent;
import com.tuowan.yeliao.commons.comp.user.UserBusiComponent;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.data.dto.user.BeansConsumeDTO;
import com.tuowan.yeliao.commons.data.entity.user.UUserMedia;
import com.tuowan.yeliao.commons.data.entity.user.UUserUnlockMedia;
import com.tuowan.yeliao.commons.data.enums.acct.WealthType;
import com.tuowan.yeliao.commons.data.enums.social.ChatContentType;
import com.tuowan.yeliao.commons.data.enums.user.MediaType;
import com.tuowan.yeliao.commons.data.enums.user.UserCashSourceType;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.social.data.dto.friend.ChatConsumeDTO;
import com.tuowan.yeliao.social.data.enums.friend.ChatSourceType;
import com.tuowan.yeliao.social.data.manager.friend.ChatManager;
import com.tuowan.yeliao.social.web.form.common.UnlockChargeMediaForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

@Service
public class CommonService {

    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private UserBusiComponent userBusiComponent;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private ChatManager chatManager;
    @Autowired
    private ConsumePresentComponent consumePresentComponent;

    /**
     * 解锁付费媒资
     * @param form
     */
    @BusiCode(BusiCodeDefine.UnlockChargeMedia)
    public void saveUnlockChargeMedia(UnlockChargeMediaForm form){
        Long userId = GlobalUtils.uid();
        userLockTemplate.acquireTransactionLock(userId);
        if(MediaType.ChargePic == form.getType()){
            // 媒资是否存在
            UUserMedia userMedia = userInfoManager.getUserMedia(form.getRecordId());
            if(Objects.isNull(userMedia)){
                throw new DataException("照片不存在！");
            }
            if(GlobalUtils.beans() < GlobalConstant.UNLOCK_CHARGE_PIC){
                throw new BusiException(ErrCodeType.BeanNotEnough);
            }
            // 扣减金币
            BeansConsumeDTO consumeDTO = userBusiComponent.addBeans(userId, -GlobalConstant.UNLOCK_CHARGE_PIC.longValue());
            // 计算提成
            Pair<Long, Long> cashPair = BusiUtils.presentCommonCash(consumeDTO.getConsumeTotalBeans(), consumeDTO.getConsumePlatformBeans());
            // 消费记录
            ChatConsumeDTO dto = new ChatConsumeDTO();
            dto.setTid(GlobalUtils.tid());
            dto.setTotalBeans(consumeDTO.getConsumeTotalBeans());
            dto.setPlatformBeans(consumeDTO.getConsumePlatformBeans());
            dto.setFromUserId(userId);
            dto.setToUserId(userMedia.getUserId());
            dto.setSourceType(ChatSourceType.ChargeMedia);
            dto.setContentType(ChatContentType.UnlockMedia);
            dto.setFeeUserId(userId);
            dto.setConsumeTime(new Date());

            dto.setTargetPresentUserId(userMedia.getUserId());
            dto.setTargetPresentCash(cashPair.getFirst());
            dto.setTargetPlatformCash(cashPair.getSecond());
            chatManager.addChatConsumeLog(dto, BusiUtils.generateRelationId(userId, userMedia.getUserId()));
            // 给对方加积分
            consumePresentComponent.addPresent(UserCashSourceType.UnlockMedia, userId, userMedia.getUserId(), cashPair.getFirst(), cashPair.getSecond());
            // 解锁数据记录
            UUserUnlockMedia media = new UUserUnlockMedia();
            media.setUserId(userId);
            media.setRecordId(form.getRecordId());
            media.setType(WealthType.Bean);
            media.setCount(GlobalConstant.UNLOCK_CHARGE_PIC);
            media.setCreateTime(new Date(GlobalUtils.reqTime()));
            userInfoManager.saveUserUnlockMedia(media);
            // 缓存删除
            userInfoManager.deleteUserUnlockChargeMediaCache(userId);
        }
    }
}
