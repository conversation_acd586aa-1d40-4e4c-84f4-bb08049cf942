package com.tuowan.yeliao.social.service.friend;

import cn.hutool.core.collection.CollectionUtil;
import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.acct.api.remote.AcctWebRemote;
import com.tuowan.yeliao.acct.api.request.UserRequest;
import com.tuowan.yeliao.acct.api.response.UserDayIncomeResponse;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.constant.FieldConstant;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.entity.config.TFemaleLevelNum;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.user.FemaleLevel;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.enums.user.UserCashSourceType;
import com.tuowan.yeliao.commons.data.manager.user.ActionStatManager;
import com.tuowan.yeliao.commons.data.manager.user.ChatMasterManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.support.config.impl.FemaleLevelNumConfig;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.web.common.form.PageForm;
import com.tuowan.yeliao.commons.web.common.vo.ListVO;
import com.tuowan.yeliao.commons.web.common.vo.PageVO;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.social.comp.chatmaster.ChatMasterComponent;
import com.tuowan.yeliao.social.comp.friend.FateMatchComponent;
import com.tuowan.yeliao.social.comp.friend.NetCallComponent;
import com.tuowan.yeliao.social.data.dto.query.acct.IncomeStatDetailDTO;
import com.tuowan.yeliao.social.data.entity.UScoreChange;
import com.tuowan.yeliao.social.data.enums.friend.GoddessIncomeType;
import com.tuowan.yeliao.social.data.manager.friend.ChatManager;
import com.tuowan.yeliao.social.data.persistence.query.CommQueryMapper;
import com.tuowan.yeliao.social.web.form.friend.GoddessDateForm;
import com.tuowan.yeliao.social.web.form.friend.goddess.IncomeForm;
import com.tuowan.yeliao.social.web.vo.friend.goddess.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class GoddessService {

    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private AcctWebRemote acctWebRemote;
    @Autowired
    private ActionStatManager actionStatManager;
    @Autowired
    private ChatMasterComponent chatMasterComponent;
    @Autowired
    private CommQueryMapper commQueryMapper;
    @Autowired
    private ChatManager chatManager;
    @Autowired
    private ChatMasterManager chatMasterManager;
    @Autowired
    private FateMatchComponent fateMatchComponent;
    @Autowired
    private NetCallComponent netCallComponent;

    /**
     * 首页
     */
    @BusiCode
    public HomeVO home(){
        if(SexType.Female != GlobalUtils.sexType()){
            throw new BusiException("暂不支持！");
        }
        Long userId = GlobalUtils.uid();
        Date now = new Date(GlobalUtils.reqTime());
        String dateStr = DateUtils.toString(now, DatePattern.YMD2);
        // 返回值构建
        HomeVO vo = new HomeVO();
        List<HomeItemVO> itemVOList = new LinkedList<>();
        FemaleLevel femaleLevel = chatMasterManager.getFemaleLevel(userId);
        TFemaleLevelNum config = FemaleLevelNumConfig.getConfig(femaleLevel);
        // 1、主动搭讪
        long useActCuTimes = chatManager.getFemaleChatUpTimesForDay(now, userId);
        int totalActCuTimes = chatMasterManager.getActiveLimit(GlobalUtils.uid());
        itemVOList.add(HomeItemVO.build(MsgUtils.format("{}/{} 次", Math.min(useActCuTimes, totalActCuTimes), totalActCuTimes), "主动搭讪"));
        // 2、系统搭讪
        long useSysCuTimes = fateMatchComponent.getFemaleDayFlowCount(now, userId);
        int totalSysCuTimes =  chatMasterManager.getScuDayLimit(userId);
        itemVOList.add(HomeItemVO.build(MsgUtils.format("{}/{} 次", Math.min(useSysCuTimes, totalSysCuTimes), totalSysCuTimes), "系统推送"));
        // 3、私聊新用户
        long useActMsgTimes = actionStatManager.getFemaleSocialUsersNum(dateStr, userId, FieldConstant.FEMALE_ACT_MSG_NEW_MALE);
        int totalActMsgTimes = chatMasterManager.getActMsgLimit(userId);
        itemVOList.add(HomeItemVO.build(MsgUtils.format("{}/{} 人", Math.min(useActMsgTimes, totalActMsgTimes), totalActMsgTimes), "私聊新用户"));
        // 4、主动拨打
        Long useActCallNum = netCallComponent.getFemaleActCallUsersNum(dateStr, userId);
        int totalActCallTimes = chatMasterManager.getActCallLimit(userId);


      /*  // 4、系统搭讪限制
        itemVOList.add(HomeItemVO.build(BusiUtils.cashToYuanSimplifyStr(fateMatchComponent.getFemaleOutRestSoberPondNeedCash(GlobalUtils.uid(), new Date(GlobalUtils.reqTime())), 4) + "积分", "系统搭讪限制"));
        // 5、消息回复率
        String msgReplyRate = (int)Math.ceil(actionStatManager.getFemaleReplyRate(dateStr, GlobalUtils.uid())) + "%";
        itemVOList.add(HomeItemVO.build(msgReplyRate, "消息回复率"));
        // 6、男用户消息回复率  备注：系统加 0.01 然后再显示
        int msgMaleReplyRate = (int)Math.ceil(actionStatManager.getMaleReplyRate(dateStr, GlobalUtils.uid()));
        itemVOList.add(HomeItemVO.build(msgMaleReplyRate == 0 ? "0%" : msgMaleReplyRate + 1 + "%", "男用户回复率"));
        // 7、今日被拨打接通率
        String femaleDayBeCallSrRate = netCallComponent.getFemaleDayBeCallSrRate(GlobalUtils.uid());
        itemVOList.add(HomeItemVO.build(femaleDayBeCallSrRate, "被拨打接通率"));
        // 8、未回复人数
        itemVOList.add(HomeItemVO.build(String.valueOf(chatConsumeComponent.getNoReplyPeople(dateStr, GlobalUtils.uid(), 1D, System.currentTimeMillis())), "未回复人数"));
*/

        // 9、今日收益
        UserDayIncomeResponse response = acctWebRemote.userDayIncome(UserRequest.build1(GlobalUtils.uid())).getData();
        itemVOList.add(HomeItemVO.build(BusiUtils.cashToYuanSimplifyStr(response.getDayIncome(), 2) + "元", "今日收益"));
        // 10、今日在线时长
        String onlineDuration = actionStatManager.getFemaleActionStatPoint(userId, dateStr, FieldConstant.ONLINE_DURATION);
        String onlineMin = StringUtils.isEmpty(onlineDuration)  ? "0分钟" : (Integer.parseInt(onlineDuration) / 60) + "分钟";
        itemVOList.add(HomeItemVO.build(onlineMin, "今日在线时长"));

        vo.setHeadPic(GlobalUtils.headPic());
        vo.setFemaleLevelText(config.getLevel().getDesc());
        vo.setList(itemVOList);
        return vo;
    }

    /**
     * 音视频指标说明
     */
    @BusiCode
    public ListVO introduceCall(){
        Long totalCash = commQueryMapper.queryUserTodayIncome(GlobalUtils.uid(), DateUtils.getStartOfDay(new Date(GlobalUtils.reqTime())));
        Map<String, Long> incomeMap = chatMasterComponent.queryFemaleDaySocialIncome(new Date(GlobalUtils.reqTime()), GlobalUtils.uid());

        // 返回值构建
        List<IntroduceCallVO> list = new ArrayList<>();
        list.add(IntroduceCallVO.build("私聊收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.PayMsg.getId(), 0L), 4)));
        list.add(IntroduceCallVO.build("礼物收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.SendGift.getId(), 0L), 4)));
        list.add(IntroduceCallVO.build("语音通话收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.PayVoice.getId(), 0L), 4)));
        list.add(IntroduceCallVO.build("视频通话收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.PayVideo.getId(), 0L), 4)));
        list.add(IntroduceCallVO.build("红包收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.RedPacket.getId(), 0L), 4)));
        list.add(IntroduceCallVO.build("付费照片收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.ChargePic.getId(), 0L), 4)));
        list.add(IntroduceCallVO.build("付费视频收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.ChargeVideo.getId(), 0L), 4)));
        list.add(IntroduceCallVO.build("解锁颜照库收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.UnlockMedia.getId(), 0L), 4)));
        list.add(IntroduceCallVO.build("今日总收益", BusiUtils.cashToYuanSimplifyStr(totalCash, 4)));

        return ListVO.create(list);
    }

    /**
     * 福利权限说明
     */
    @BusiCode
    public ListVO introduceWelfare(){
        Map<FemaleLevel, TFemaleLevelNum> map = FemaleLevelNumConfig.getAllConfig().stream().collect(Collectors.toMap(TFemaleLevelNum::getLevel, v->v));
        List<IntroduceWelfareVO> list = new ArrayList<>();
        list.add(IntroduceWelfareVO.build(GlobalConstant.RONGYAO_GODDESS_PIC, "300%", BoolType.True, BoolType.True, MsgUtils.format("{}次/天", map.get(FemaleLevel.L1).getActiveLimit()), "300-800"));
        list.add(IntroduceWelfareVO.build(GlobalConstant.ZUANSHI_GODDESS_PIC, "200%", BoolType.True, BoolType.True, MsgUtils.format("{}次/天", map.get(FemaleLevel.L2).getActiveLimit()), "300-800"));
        list.add(IntroduceWelfareVO.build(GlobalConstant.HUANGJIN_GODDESS_PIC, "150%", BoolType.False, BoolType.False, MsgUtils.format("{}次/天", map.get(FemaleLevel.L3).getActiveLimit()), "300-600"));
        list.add(IntroduceWelfareVO.build(GlobalConstant.QINGTONG_GODDESS_PIC, "100%", BoolType.False, BoolType.False, MsgUtils.format("{}次/天", map.get(FemaleLevel.L4).getActiveLimit()), "300-500"));
        list.add(IntroduceWelfareVO.build(GlobalConstant.JIANXI_GODDESS_PIC, "50%", BoolType.False, BoolType.False, MsgUtils.format("{}次/天", map.get(FemaleLevel.L5).getActiveLimit()), "300-400"));

        return ListVO.create(list);
    }

    /**
     * 服务分说明
     */
    @BusiCode
    public ListVO introduceScore(){
        List<IntroduceScoreVO> list = new ArrayList<>();
        list.add(IntroduceScoreVO.build("【+】视频通话", "通话时长", "视频通话满1分钟奖励1服务分，每通视频通话最多奖励30服务分"));
        list.add(IntroduceScoreVO.build("【+】语音通话", "通话时长", "语音通话满1分钟奖励1服务分，每通语音通话最多奖励30服务分"));
        list.add(IntroduceScoreVO.build("【+】通话评价", "通话满意度", "音视频通话后男用户评价为满意奖励2服务分（通话满1分钟有效，且同个用户一天仅允许评价3次）"));
        list.add(IntroduceScoreVO.build("【+】私聊", "回复速度", "120秒内回复奖励0.1分（按每条消息计算）"));
        list.add(IntroduceScoreVO.build("【+】礼物", "礼物单价", "每收到价值1000金币的礼物，奖励1服务分"));

        list.add(IntroduceScoreVO.build("【-】音视频通话", "直接拒绝通话", "拒主动绝对方来电扣2分"));
        list.add(IntroduceScoreVO.build("【-】音视频通话", "主动挂断通话", "主动挂断与对方的通话扣2分"));
        list.add(IntroduceScoreVO.build("【-】音视频通话", "漏接音视频电话", "漏接对方打来的音视频电话扣1分（离线则无效）"));
        list.add(IntroduceScoreVO.build("【-】私聊", "回复速度", "超过120秒未回复扣0.5分（按每条消息计算）"));
        list.add(IntroduceScoreVO.build("【-】通话评价", "通话满意度", "音视频通话后男用户评价为不满意扣2服务分（通话满1分钟有效，且同个用户一天仅允许评价3次）"));
        list.add(IntroduceScoreVO.build("【-】人脸检测", "视频无人脸", "视频通话未识别到人脸扣3服务分（接通首分钟会持续进行人脸检测）"));

        return ListVO.create(list);
    }

    /**
     * 服务分明细列表数据
     */
    @BusiCode
    public PageVO scoreLog(PageForm form){
        List<ScoreItemLogVO> list = new ArrayList<>();
        List<UScoreChange> changes = chatMasterComponent.queryFemaleScoreChange(GlobalUtils.uid(), form.getOffset(), form.getLimit());
        changes.forEach(item -> {
            list.add(ScoreItemLogVO.build(item.getType().getDesc(), DateUtils.toString(item.getCreateTime()), BoolType.valueOf(item.getChange() > 0), MsgUtils.format("{}{}", item.getChange() > 0 ? "+" : "-", Math.abs(item.getChange())), ""));
        });
        return new PageVO(list, form.getOffset(), form.getLimit());
    }

    /**
     * 女神收益汇总明细
     */
    @Deprecated
    @BusiCode
    public ListVO income(IncomeForm form){
        Date now = new Date(GlobalUtils.reqTime());
        Map<String, Long> incomeMap = new HashMap<>();
        Long totalCash = 0L;
        if(GoddessIncomeType.Today == form.getType()){
            incomeMap = chatMasterComponent.queryFemaleDaySocialIncome(now, GlobalUtils.uid());
            totalCash = commQueryMapper.queryUserTodayIncome(GlobalUtils.uid(), DateUtils.getStartOfDay(now));
        }else if(GoddessIncomeType.LastWeek == form.getType()){
            Date weekStartTime = DateUtils.getNowWeekStartTime(now);
            incomeMap = chatMasterComponent.queryFemaleWeekSocialIncome(now, GlobalUtils.uid());
            totalCash = commQueryMapper.queryUserWeekIncome(GlobalUtils.uid(), weekStartTime, DateUtils.plusDays(weekStartTime, 6));
        }
        totalCash = Objects.isNull(totalCash) ? 0L : totalCash;
        Map<String, Object> extMap = new HashMap<>();
        extMap.put("cash", BusiUtils.cashToYuanSimplifyStr(totalCash, 4)); // 总收益（今日数据就是今日总收益、上周数据就是上周总收益）
        extMap.put("desc", form.getType().getContent());
        List<IncomeItemVO> list = new ArrayList<>();
        list.add(IncomeItemVO.build("私聊收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.PayMsg.getId(), 0L), 4)));
        list.add(IncomeItemVO.build("礼物收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.SendGift.getId(), 0L), 4)));
        list.add(IncomeItemVO.build("语音通话收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.PayVoice.getId(), 0L), 4)));
        list.add(IncomeItemVO.build("视频通话收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.PayVideo.getId(), 0L), 4)));
        list.add(IncomeItemVO.build("红包收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.RedPacket.getId(), 0L), 4)));
        list.add(IncomeItemVO.build("付费照片收益", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.ChargePic.getId(), 0L) + incomeMap.getOrDefault(UserCashSourceType.UnlockMedia.getId(), 0L), 4)));
        list.add(IncomeItemVO.build("其他收益", BusiUtils.cashToYuanSimplifyStr(totalCash - incomeMap.getOrDefault(UserCashSourceType.ChargePic.getId(), 0L) - incomeMap.getOrDefault(UserCashSourceType.UnlockMedia.getId(), 0L), 4)));

        return ListVO.create01(list, extMap);
    }

    /**
     * 女神规则
     */
    @BusiCode
    public RuleVO rule(){
        RuleVO vo = new RuleVO();
        vo.setDesc1("注意：本周的女神等级由上周的总服务分决定");
        vo.setDesc2("如有相关问题可添加官方客服微信：wywta520");
        // 板块1
        Map<String, Object> tableTitle = MapUtils.gmap("tableTitle", Arrays.asList("类别", "服务分要求", "音视频接通率", "音视频平均通话时长", "音视频通话单数"));
        List<List<String>> list1 = new ArrayList<>();
        list1.add(Arrays.asList(GlobalConstant.RONGYAO_GODDESS_PIC, "≥501", "≥65%", "≥100秒", "15"));
        list1.add(Arrays.asList(GlobalConstant.ZUANSHI_GODDESS_PIC, "301≤~≤500", "≥65%", "≥100秒", "8"));
        list1.add(Arrays.asList(GlobalConstant.HUANGJIN_GODDESS_PIC, "101≤~≤300", "≥50%", "≥60秒", "3"));
        list1.add(Arrays.asList(GlobalConstant.QINGTONG_GODDESS_PIC, "21≤~≤100", "-", "-", "-"));
        list1.add(Arrays.asList(GlobalConstant.JIANXI_GODDESS_PIC, "-9≤~≤20", "-", "-", "-"));
        vo.setPlate1(RuleTwoVO.build("音视频考核指标", ListVO.create01(list1, tableTitle)));
        // 板块2
        List<String> list2 = new ArrayList<>();
        list2.add("1、本周服务分<-10分（待定）");
        list2.add("2、历史最高为见习女神女用户，累计被淘汰>3次后将永久成为淘汰女用户");
        list2.add("3、其他类别女用户，累计被淘汰>5次后，将永久成为淘汰女用户");
        list2.add("4、采用作弊手段刷服务分");
        list2.add("5、经平台抽查人员发现用户存在重大违规违法行为");
        vo.setPlate2(RuleOneVO.build("以下情况直接取消女神等级", list2));
        // 板块3
        List<String> list3 = new ArrayList<>();
        list3.add("1、本周出现恶意违规(脚本/色情/欺骗/禁言等)直接降为见习女神");
        list3.add("2、本周通累计通话人数≥10人后");
        list3.add("2.1、见习女神通话被拨打≤40%降级为淘汰女用户");
        list3.add("2.2、青铜女神通话被拨打≤50%降级一级");
        list3.add("3、本周通累计通话人数≥20人数后");
        list3.add("3.1、黄金女神通话被拨打≤50%降级一级");
        list3.add("3.2、钻石女神通话被拨打≤50%降级一级");
        list3.add("3.3、荣耀女神通话被拨打≤60%降级一级");
        list3.add("4、因拒绝/黑屏/无人接通被关闭权限直接下降一级");
        list3.add("(本周被降级一次后，数据从新计算)");
        vo.setPlate3(RuleOneVO.build("以下情况下降女神等级", list3));
        // 板块4
        List<String> list4 = new ArrayList<>();
        list4.add("1、女神等级分为：荣耀女神、钻石女神、黄金女神、青铜女神、见习女神");
        list4.add("2、根据用户在平台的表现情况进行定级，其中以通话、直播、聊天等社交行为为主要测算依据，以便于用户更好的体验平台。");
        list4.add("3、荣耀女神、钻石女神可以获得壕友列表和女神推荐权。");
        list4.add("4、壕友列表，拥有该列表的女神可进行主动拨打给列表中的男用户，列表中的男用户都是平台内优质用户。");
        list4.add("5、女神推荐权，拥有该权利的女用户将被平台推荐给优质男用户，拥有更多的曝光量，获得更多收益。");
        list4.add("6、其他等级均获得平台常规权益");
        vo.setPlate4(RuleOneVO.build("不同等级权益说明", list4));
        return vo;
    }

    /**
     * 数据中心
     */
    @BusiCode
    public DataCenterVO dataCenter(GoddessDateForm form){
        Long userId = GlobalUtils.uid();
        Date date = DateUtils.parse(form.getDate(), DatePattern.YMD);
        Date minDate = DateUtils.plusDays(date, -30);
        // 返回值构建
        DataCenterVO result = new DataCenterVO();
        if(DateUtils.isSameDay(date)){ // 今天数据
            Map<String, String> statAll = actionStatManager.getFemaleActionStatAll(userId, DateUtils.toString(date, DatePattern.YMD2));

            result.setDayOnline(NumberUtils.formatWithRoundingDown(NumberUtils.toDouble(statAll.get(FieldConstant.ONLINE_DURATION)) / 3600, 2));
            // 数据统计
            List<DataCenterDataStatItemVO> dataStats = new ArrayList<>();
            dataStats.add(DataCenterDataStatItemVO.build1("cfg/goddess/data_msg.png", TcItemVO.build1("收到信息", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_RECEIVE_MSG_NUM))), "条"), TcItemVO.build1("有效回复", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VALID_REPLY_NUM))), "条")));
            dataStats.add(DataCenterDataStatItemVO.build1("cfg/goddess/data_voice.png", TcItemVO.build1("收到语音", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_OC_TIMES)) + NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_OI_TIMES))), "通"), TcItemVO.build1("成功接通", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_OC_SUC_TIMES)) + NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_OI_SUC_TIMES))), "通")));
            dataStats.add(DataCenterDataStatItemVO.build1("cfg/goddess/data_video.png", TcItemVO.build1("收到视频", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VC_TIMES)) + NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VM_TIMES)) + NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VI_TIMES)) + NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VF_TIMES))), "通"), TcItemVO.build1("成功接通", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VC_SUC_TIMES)) + NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VM_SUC_TIMES)) + NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VI_SUC_TIMES)) + NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VF_SUC_TIMES))), "通")));
            dataStats.add(DataCenterDataStatItemVO.build1("cfg/goddess/data_gift.png", TcItemVO.build1("收到金币礼物", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_BEAN_GIFT_NUM))), "个"), TcItemVO.build1("收到银币礼物", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_SILVER_GIFT_NUM))), "个")));
            result.setDataStats(dataStats);

        }else{ // 往日数据
            result.setDayOnline("--");
            // 数据统计
            List<DataCenterDataStatItemVO> dataStats = new ArrayList<>();
            dataStats.add(DataCenterDataStatItemVO.build1("cfg/goddess/data_msg.png", TcItemVO.build1("收到信息", "--", "条"), TcItemVO.build1("有效回复", "--", "条")));
            dataStats.add(DataCenterDataStatItemVO.build1("cfg/goddess/data_voice.png", TcItemVO.build1("收到语音", "--", "通"), TcItemVO.build1("成功接通", "--", "通")));
            dataStats.add(DataCenterDataStatItemVO.build1("cfg/goddess/data_video.png", TcItemVO.build1("收到视频", "--", "通"), TcItemVO.build1("成功接通", "--", "通")));
            dataStats.add(DataCenterDataStatItemVO.build1("cfg/goddess/data_gift.png", TcItemVO.build1("收到金币礼物", "--", "个"), TcItemVO.build1("收到银币礼物", "--", "个")));
            result.setDataStats(dataStats);
        }
        List<IncomeStatDetailDTO> incomes = commQueryMapper.queryUserMonthIncomeDetail(userId, minDate, date);
        Map<Date, Long> incomesMap = incomes.stream().collect(Collectors.toMap(IncomeStatDetailDTO::getStatDate, IncomeStatDetailDTO::getTotalCash));
        // 当日积分
        result.setDayIncome(BusiUtils.cashToYuanSimplifyStr(incomesMap.get(date), 2));
        // 积分统计
        List<DataCenterCashStatItemVO> cashStats = new ArrayList<>();
        cashStats.add(DataCenterCashStatItemVO.build1("cfg/goddess/cash_today.png", TcItemVO.build1("当日积分", BusiUtils.cashToYuanSimplifyStr(incomesMap.get(date), 2), "积分")));
        cashStats.add(DataCenterCashStatItemVO.build1("cfg/goddess/cash_yesterday.png", TcItemVO.build1("昨日积分", BusiUtils.cashToYuanSimplifyStr(incomesMap.get(DateUtils.plusDays(date, -1)), 2), "积分")));
        cashStats.add(DataCenterCashStatItemVO.build1("cfg/goddess/cash_seven.png", TcItemVO.build1("7日积分", BusiUtils.cashToYuanSimplifyStr(incomes.stream().filter(f -> f.getStatDate().after(DateUtils.plusDays(date, -7))).mapToLong(IncomeStatDetailDTO::getTotalCash).sum(), 2), "积分")));
        cashStats.add(DataCenterCashStatItemVO.build1("cfg/goddess/cash_fifteen.png", TcItemVO.build1("15日积分", BusiUtils.cashToYuanSimplifyStr(incomes.stream().filter(f -> f.getStatDate().after(DateUtils.plusDays(date, -15))).mapToLong(IncomeStatDetailDTO::getTotalCash).sum(), 2), "积分")));
        cashStats.add(DataCenterCashStatItemVO.build1("cfg/goddess/cash_fifteen.png", TcItemVO.build1("30日积分", BusiUtils.cashToYuanSimplifyStr(incomes.stream().filter(f -> f.getStatDate().after(DateUtils.plusDays(date, -30))).mapToLong(IncomeStatDetailDTO::getTotalCash).sum(), 2), "积分")));
        cashStats.add(DataCenterCashStatItemVO.build1("cfg/goddess/cash_total.png", TcItemVO.build1("总积分", BusiUtils.cashToYuanSimplifyStr(commQueryMapper.queryUserTotalIncome(userId), 2), "积分")));
        result.setCashStats(cashStats);

        return result;
    }

    /**
     * 数据中心详情
     */
    @BusiCode
    public ListVO dataCenterDetail(GoddessDateForm form){
        Long userId = GlobalUtils.uid();
        Date date = DateUtils.parse(form.getDate(), DatePattern.YMD);
        // 返回值构建
        List<DataCenterDetailVO> resultList = new ArrayList<>();
        if(DateUtils.isSameDay(date)){ // 今日数据
            String dateStr = DateUtils.toString(date, DatePattern.YMD2);
            Map<String, String> statAll = actionStatManager.getFemaleActionStatAll(userId, DateUtils.toString(date, DatePattern.YMD2));
            Map<String, Long> incomeMap = chatMasterComponent.queryFemaleDaySocialIncome(date, userId);
            // 消息统计
            List<TcItemVO> msgList = new ArrayList<>();
            msgList.add(TcItemVO.build2("收到消息数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_RECEIVE_MSG_NUM)))));
            msgList.add(TcItemVO.build2("有效回复数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VALID_REPLY_NUM)))));
            msgList.add(TcItemVO.build2("有效回复率", String.valueOf(statAll.containsKey(FieldConstant.FEMALE_RECEIVE_MSG_NUM) ? NumberUtils.formatWithRoundingDown(NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_VALID_REPLY_NUM)) / NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_RECEIVE_MSG_NUM)), 2) : 0.0)));
            msgList.add(TcItemVO.build2("主动搭讪数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_ACT_CU_NUM)))));
            msgList.add(TcItemVO.build2("系统搭讪数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_SYS_CU_NUM)))));
            msgList.add(TcItemVO.build1("消息积分", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.PayMsg.getId(), 0L), 4), "积分"));
            resultList.add(DataCenterDetailVO.build1("消息统计", "cfg/goddess/detail_msg.png", msgList));
            // 常规视频
            List<TcItemVO> videoList = new ArrayList<>();
            videoList.add(TcItemVO.build2("响铃次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VC_TIMES)))));
            videoList.add(TcItemVO.build2("接通次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VC_SUC_TIMES)))));
            videoList.add(TcItemVO.build2("接通率", String.valueOf(statAll.containsKey(FieldConstant.FEMALE_VC_TIMES) ? NumberUtils.formatWithRoundingDown(NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_VC_SUC_TIMES)) / NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_VC_TIMES)), 2) : 0.0)));
            videoList.add(TcItemVO.build1("通话时长", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VC_DURATION))), "秒"));
            videoList.add(TcItemVO.build2("接通人数", String.valueOf(NumberUtils.toInt(actionStatManager.getFemaleSocialUsersNum(dateStr, userId, FieldConstant.FEMALE_VC_USERS)))));
            videoList.add(TcItemVO.build1("视频积分", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.PayVideo.getId(), 0L), 4), "积分"));
            resultList.add(DataCenterDetailVO.build1("常规视频统计", "cfg/goddess/detail_video.png", videoList));
            // 视频速配
            List<TcItemVO> videoMatchList = new ArrayList<>();
            videoMatchList.add(TcItemVO.build2("曝光次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VM_BG_NUM)))));
            videoMatchList.add(TcItemVO.build2("响铃次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VM_TIMES)))));
            videoMatchList.add(TcItemVO.build2("接通次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VM_SUC_TIMES)))));
            videoMatchList.add(TcItemVO.build2("接通率", String.valueOf(statAll.containsKey(FieldConstant.FEMALE_VM_TIMES) ? NumberUtils.formatWithRoundingDown(NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_VM_SUC_TIMES)) / NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_VM_TIMES)), 2) : 0.0)));
            videoMatchList.add(TcItemVO.build1("通话时长", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VM_DURATION))), "秒"));
            videoMatchList.add(TcItemVO.build2("接通人数", String.valueOf(NumberUtils.toInt(actionStatManager.getFemaleSocialUsersNum(dateStr, userId, FieldConstant.FEMALE_VM_USERS)))));
            videoMatchList.add(TcItemVO.build1("视频积分", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.PayVideo.getId(), 0L), 4), "积分"));
            resultList.add(DataCenterDetailVO.build1("视频速配统计", "cfg/goddess/detail_video.png", videoMatchList));
            // 视频邀请
            List<TcItemVO> videoInviteList = new ArrayList<>();
            videoInviteList.add(TcItemVO.build2("邀请次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VIDEO_INVITE_NUM)))));
            videoInviteList.add(TcItemVO.build2("响铃次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VI_TIMES)))));
            videoInviteList.add(TcItemVO.build2("接通次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VI_SUC_TIMES)))));
            videoInviteList.add(TcItemVO.build2("接通率", String.valueOf(statAll.containsKey(FieldConstant.FEMALE_VI_TIMES) ? NumberUtils.formatWithRoundingDown(NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_VI_SUC_TIMES)) / NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_VI_TIMES)), 2) : 0.0)));
            videoInviteList.add(TcItemVO.build1("通话时长", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VI_DURATION))), "秒"));
            videoInviteList.add(TcItemVO.build2("接通人数", String.valueOf(NumberUtils.toInt(actionStatManager.getFemaleSocialUsersNum(dateStr, userId, FieldConstant.FEMALE_VI_USERS)))));
            videoInviteList.add(TcItemVO.build1("视频积分", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.PayVideo.getId(), 0L), 4), "积分"));
            resultList.add(DataCenterDetailVO.build1("视频邀请统计", "cfg/goddess/detail_video.png", videoInviteList));
            // 视频缘分
            List<TcItemVO> videoFateList = new ArrayList<>();
            videoFateList.add(TcItemVO.build2("曝光次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VF_BG_NUM)))));
            videoFateList.add(TcItemVO.build2("响铃次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VF_TIMES)))));
            videoFateList.add(TcItemVO.build2("接通次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VF_SUC_TIMES)))));
            videoFateList.add(TcItemVO.build2("接通率", String.valueOf(statAll.containsKey(FieldConstant.FEMALE_VF_TIMES) ? NumberUtils.formatWithRoundingDown(NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_VF_SUC_TIMES)) / NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_VF_TIMES)), 2) : 0.0)));
            videoFateList.add(TcItemVO.build1("通话时长", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VF_DURATION))), "秒"));
            videoFateList.add(TcItemVO.build2("接通人数", String.valueOf(NumberUtils.toInt(actionStatManager.getFemaleSocialUsersNum(dateStr, userId, FieldConstant.FEMALE_VF_USERS)))));
            videoFateList.add(TcItemVO.build1("视频积分", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.PayVideo.getId(), 0L), 4), "积分"));
            resultList.add(DataCenterDetailVO.build1("视频缘分统计", "cfg/goddess/detail_video.png", videoFateList));
            // 常规语音
            List<TcItemVO> voiceList = new ArrayList<>();
            voiceList.add(TcItemVO.build2("响铃次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_OC_TIMES)))));
            voiceList.add(TcItemVO.build2("接通次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_OC_SUC_TIMES)))));
            voiceList.add(TcItemVO.build2("接通率", String.valueOf(statAll.containsKey(FieldConstant.FEMALE_OC_TIMES) ? NumberUtils.formatWithRoundingDown(NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_OC_SUC_TIMES)) / NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_OC_TIMES)), 2) : 0.0)));
            voiceList.add(TcItemVO.build1("通话时长", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_OC_DURATION))), "秒"));
            voiceList.add(TcItemVO.build2("接通人数", String.valueOf(NumberUtils.toInt(actionStatManager.getFemaleSocialUsersNum(dateStr, userId, FieldConstant.FEMALE_OC_USERS)))));
            voiceList.add(TcItemVO.build1("语音积分", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.PayVoice.getId(), 0L), 4), "积分"));
            resultList.add(DataCenterDetailVO.build1("常规语音统计", "cfg/goddess/detail_voice.png", voiceList));
            // 语音邀请
            List<TcItemVO> voiceInviteList = new ArrayList<>();
            voiceInviteList.add(TcItemVO.build2("邀请次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_VOICE_INVITE_NUM)))));
            voiceInviteList.add(TcItemVO.build2("响铃次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_OI_TIMES)))));
            voiceInviteList.add(TcItemVO.build2("接通次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_OI_SUC_TIMES)))));
            voiceInviteList.add(TcItemVO.build2("接通率", String.valueOf(statAll.containsKey(FieldConstant.FEMALE_OI_TIMES) ? NumberUtils.formatWithRoundingDown(NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_OI_SUC_TIMES)) / NumberUtils.toDouble(statAll.get(FieldConstant.FEMALE_OI_TIMES)), 2) : 0.0)));
            voiceInviteList.add(TcItemVO.build1("通话时长", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_OI_DURATION))), "秒"));
            voiceInviteList.add(TcItemVO.build2("接通人数", String.valueOf(NumberUtils.toInt(actionStatManager.getFemaleSocialUsersNum(dateStr, userId, FieldConstant.FEMALE_OI_USERS)))));
            voiceInviteList.add(TcItemVO.build1("语音积分", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.PayVoice.getId(), 0L), 4), "积分"));
            resultList.add(DataCenterDetailVO.build1("常规语音统计", "cfg/goddess/detail_voice.png", voiceInviteList));
            // 礼物统计
            List<TcItemVO> giftList = new ArrayList<>();
            giftList.add(TcItemVO.build1("金币礼物", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_BEAN_GIFT_NUM))), "个"));
            giftList.add(TcItemVO.build1("银币礼物", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_SILVER_GIFT_NUM))), "个"));
            giftList.add(TcItemVO.build1("礼物积分", BusiUtils.cashToYuanSimplifyStr(incomeMap.getOrDefault(UserCashSourceType.SendGift.getId(), 0L), 4), "积分"));
            resultList.add(DataCenterDetailVO.build1("礼物统计", "cfg/goddess/detail_gift.png", giftList));
            // 其他统计
            List<TcItemVO> otherList = new ArrayList<>();
            otherList.add(TcItemVO.build2("主动挂断次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_ACT_HU_TIMES)))));
            otherList.add(TcItemVO.build2("拒绝通话次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_RF_CALL_TIMES)))));
            otherList.add(TcItemVO.build2("10秒内通话次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_LOW_10S_CALL_TIMES)))));
            otherList.add(TcItemVO.build2("60秒内通话次数", String.valueOf(NumberUtils.toInt(statAll.get(FieldConstant.FEMALE_LOW_60S_CALL_TIMES)))));
            resultList.add(DataCenterDetailVO.build1("其他统计", null, otherList));
        }else { // 往日数据

        }

        return ListVO.create(resultList);
    }
}
