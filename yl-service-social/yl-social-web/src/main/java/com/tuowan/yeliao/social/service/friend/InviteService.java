package com.tuowan.yeliao.social.service.friend;

import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.config.configuration.impl.AppConfig;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserExt;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.data.utils.HtmlUrlUtils;
import com.tuowan.yeliao.commons.open.email.EasyPoiExcelDefine;
import com.tuowan.yeliao.commons.open.email.EmailSupport;
import com.tuowan.yeliao.commons.web.common.form.PageForm;
import com.tuowan.yeliao.commons.web.common.vo.PageVO;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.social.comp.friend.FriendRelationComponent;
import com.tuowan.yeliao.social.comp.friend.UserInviteComponent;
import com.tuowan.yeliao.social.data.config.InvitorPresentConfig;
import com.tuowan.yeliao.social.data.dto.query.acct.InviteFriendIncomeDTO;
import com.tuowan.yeliao.social.data.dto.query.acct.UserDayIncomeDTO;
import com.tuowan.yeliao.social.data.entity.UInviteFriends;
import com.tuowan.yeliao.social.data.entity.UInviteStat;
import com.tuowan.yeliao.social.data.enums.friend.InviteIncomeType;
import com.tuowan.yeliao.social.data.persistence.query.CommQueryMapper;
import com.tuowan.yeliao.social.web.form.friend.invite.FriendsForm;
import com.tuowan.yeliao.social.web.form.friend.invite.IncomeForm;
import com.tuowan.yeliao.social.web.form.friend.invite.ShareIncomeDataForm;
import com.tuowan.yeliao.social.web.vo.friend.invite.CashRankVO;
import com.tuowan.yeliao.social.web.vo.friend.invite.HomeVO;
import com.tuowan.yeliao.social.web.vo.friend.invite.InviteItemVO;
import com.tuowan.yeliao.social.web.vo.friend.invite.InviteParamVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class InviteService {

    @Autowired
    private UserInviteComponent userInviteComponent;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private FriendRelationComponent friendRelationComponent;
    @Autowired
    private CommQueryMapper commQueryMapper;
    @Autowired
    private EmailSupport emailSupport;

    /**
     * 邀请页首页
     */
    @BusiCode
    public HomeVO home(){
        Long userId = GlobalUtils.uid();
        UUserExt userExt = userInfoManager.getUserExt(userId);
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        HomeVO vo = new HomeVO();
        String inviteCode = userBasic.getInviteCode();
        UrlParamsMap paramsMap = UrlParamsMap.build(SettingsConfig.getString(SettingsType.InviteCodeUsers));
        if(StringUtils.isNotEmpty(paramsMap.getString(String.valueOf(userId)))){
            inviteCode = paramsMap.getString(String.valueOf(userId));
        }
        vo.setInviteCode(inviteCode);
        vo.setRuleUrl(HtmlUrlUtils.getInviteRule());
        vo.setRcPresentRate("20%"); // 默认 20%
        // 获取当前用户的提成比例
        InvitorPresentConfig.Detail detail = InvitorPresentConfig.getDetail(userExt.getInviteFixScale(), userExt.getTotalRpInvite());
        vo.setIcPresentRate(MsgUtils.format("{}%", (int)(detail.presentScale * 100)));
        if(!detail.isTop){
            Map<Integer, InvitorPresentConfig.Detail> detailMap = InvitorPresentConfig.INVITOR_PRESENT_SCALE.stream().collect(Collectors.toMap(k -> k.order, v -> v));
            // 获取下一等级
            InvitorPresentConfig.Detail nextDetail = detailMap.get(detail.order + 1);
            vo.setDesc1(MsgUtils.format("再邀请{}人女用户认证 可获得{}%收益奖励", nextDetail.minNum - userExt.getTotalRpInvite(), (int)(nextDetail.presentScale * 100)));
        }
        // 立即邀请参数封装
        String inviteTitle = MsgUtils.format(GlobalConstant.SOON_INVITE_TITLE, GlobalUtils.packageType().getDesc());
        vo.setInviteParamVO(InviteParamVO.build1(HtmlUrlUtils.getInviteDownloadUrl(inviteCode), inviteTitle, GlobalConstant.SOON_INVITE_CONTENT, AppConfig.FILE_URL + GlobalConstant.SOON_INVITE_MIN_PIC));
        // 分享收益明细开关
        vo.setShareIncomeDetail(BoolType.valueOf(SettingsConfig.getString(SettingsType.InviteShareIncomeDetail).contains(userId.toString())));
        return vo;
    }

    /**
     * 请求好友收益数据
     */
    @BusiCode
    public PageVO queryFriendIncome(PageForm form){
        Long userId = GlobalUtils.uid();
        List<InviteFriendIncomeDTO> list = userInviteComponent.queryInviteFriendIncome(userId, form.getOffset(), form.getLimit());
        list.forEach(item -> {
            item.setStatDateText(DateUtils.toString(item.getStatDate(), DatePattern.YMD5));
            item.setTotalCashText(BusiUtils.cashToYuanSimplifyStr(item.getTotalCash(), 2) + "积分");
        });
        return new PageVO(list, form.getOffset(), form.getLimit());
    }

    /**
     * 请求收益明细数据
     */
    @BusiCode
    public PageVO queryIncome(IncomeForm form){
        Long userId = GlobalUtils.uid();
        Date today = DateUtils.nowTime();
        // 如果请求的是第一页数据 则返回扩展信息
        Map<String, Object> extMap = new HashMap<>();
        if(form.getOffset() == 0){
            Long todayCash = userInviteComponent.queryInvitorDayCash(today, userId);
            UInviteStat invitorStat = userInviteComponent.getInvitorStat(userId);
            extMap.put("totalCash", BusiUtils.cashToYuanStr(invitorStat.getTotalCash(), 2));
            extMap.put("todayCash", BusiUtils.cashToYuanStr(todayCash, 2));
        }
        List<UInviteFriends> list = userInviteComponent.queryInviteFriendOrderCash(userId, form.getFriendId(), form.getType(), form.getOffset(), form.getLimit());
        List<InviteItemVO> resultList = new ArrayList<>();
        list.forEach(item -> {
            UUserBasic friendUser = userInfoManager.getUserBasic(item.getFriendId());
            UUserExt friendExt = userInfoManager.getUserExt(item.getFriendId());
            String friendNotes = friendRelationComponent.getFriendNotes(userId, item.getFriendId());
            InviteItemVO vo = new InviteItemVO();
            vo.setFriendUserId(friendUser.getUserId());
            vo.setFriendHeadPic(friendUser.getHeadPic());
            vo.setFriendNickname(StringUtils.isEmpty(friendNotes) ? friendUser.getNickname() : friendNotes);
            vo.setFriendRealPerson(friendUser.getRealPerson());

            vo.setTotalIncomeText(MsgUtils.format("累计 +{}积分", BusiUtils.cashToYuanStr(item.getTotalCash(), 2)));
            vo.setTodayIncomeText(MsgUtils.format("今日 +{}", BusiUtils.cashToYuanStr(userInviteComponent.queryFriendDayCashRank(today, userId, item.getFriendId()), 2)));
            // 获取好友的最近在线描述
            vo.setDesc1(buildUserOnlineDesc(friendExt.getLastOpenTime(), today));
            vo.setNeedTouch(BoolType.valueOf(GlobalUtils.sexType() != friendUser.getSex()));
            resultList.add(vo);
        });
        return new PageVO(resultList, form.getOffset(), form.getLimit(), extMap);
    }

    /**
     * 获取邀请人数
     */
    @BusiCode
    public PageVO queryFriends(FriendsForm form){
        Long userId = GlobalUtils.uid();
        Date today = DateUtils.nowTime();
        // 如果请求的是第一页数据 则返回扩展信息
        Map<String, Object> extMap = new HashMap<>();
        if(form.getOffset() == 0){
            Long todayFriends = userInviteComponent.queryInvitorDayFriend(today, userId);
            UInviteStat invitorStat = userInviteComponent.getInvitorStat(userId);
            extMap.put("totalFriends", String.valueOf(invitorStat.getTotalInvite()));
            extMap.put("todayFriends", String.valueOf(todayFriends));
        }
        List<UInviteFriends> list = userInviteComponent.queryInviteFriendOrderTime(userId, form.getFriendId(), form.getType(), form.getOffset(), form.getLimit());
        List<InviteItemVO> resultList = new ArrayList<>();
        list.forEach(item -> {
            UUserBasic friendUser = userInfoManager.getUserBasic(item.getFriendId());
            String friendNotes = friendRelationComponent.getFriendNotes(userId, item.getFriendId());
            InviteItemVO vo = new InviteItemVO();
            vo.setFriendUserId(item.getFriendId());
            vo.setFriendHeadPic(friendUser.getHeadPic());
            vo.setFriendNickname(StringUtils.isEmpty(friendNotes) ? friendUser.getNickname() : friendNotes);
            vo.setFriendRealPerson(friendUser.getRealPerson());

            vo.setTotalIncomeText(MsgUtils.format("累计 +{}积分", BusiUtils.cashToYuanStr(item.getTotalCash(), 2)));
            vo.setTodayIncomeText(MsgUtils.format("今日 +{}", BusiUtils.cashToYuanStr(userInviteComponent.queryFriendDayCashRank(today, userId, item.getFriendId()), 2)));
            // 获取好友的注册时间
            vo.setDesc1(DateUtils.toString(friendUser.getCreateTime(), DatePattern.YMD));
            vo.setNeedTouch(BoolType.valueOf(GlobalUtils.sexType() != friendUser.getSex()));
            resultList.add(vo);
        });
        return new PageVO(resultList, form.getOffset(), form.getLimit(), extMap);
    }

    /**
     * 获取推情人总收益排名
     */
    @BusiCode
    public PageVO queryTotalCashRank(PageForm form){
        if(form.getOffset() > 80){
            return PageVO.empty();
        }
        List<UInviteStat> list = userInviteComponent.queryInvitorTotalCashRank(form.getOffset(), form.getLimit());
        List<CashRankVO> resultList = new ArrayList<>();
        list.forEach(item -> {
            UUserBasic userBasic = userInfoManager.getUserBasic(item.getUserId());
            CashRankVO vo = new CashRankVO();
            vo.setUserId(item.getUserId());
            vo.setHeadPic(userBasic.getHeadPic());
            vo.setNickname(userBasic.getNickname());
            vo.setCashMsg(MsgUtils.format("已赚取{}积分", BusiUtils.cashToYuanStr(item.getTotalCash(), 2)));
            resultList.add(vo);
        });
        return new PageVO(resultList, form.getOffset(), form.getLimit());
    }

    /**
     * 分享收益数据
     */
    @BusiCode
    public void shareIncomeData(ShareIncomeDataForm form){
        Long invitorUserId = GlobalUtils.uid();
        UUserExt userExt = userInfoManager.getUserExt(invitorUserId);
        // 获取邀请人底下的所有用户
        List<UInviteFriends> inviteFriends = commQueryMapper.queryInviteFriends(invitorUserId);
        if(ListUtils.isEmpty(inviteFriends)){
            throw new BusiException("您还没邀请过人哦~");
        }
        List<Long> friendIds = inviteFriends.stream().map(UInviteFriends::getFriendId).collect(Collectors.toList());
        List<UserDayIncomeDTO> userDayIncomeDTOS = commQueryMapper.queryUserDayIncome(friendIds, DateUtils.parse(form.getDate(), DatePattern.YMD));
        // 根据收益降序排列
        userDayIncomeDTOS = userDayIncomeDTOS.stream().sorted(Comparator.comparing(UserDayIncomeDTO::getTotalCash).reversed()).collect(Collectors.toList());
        List<Map<String, Object>> resultList = new ArrayList<>();
        userDayIncomeDTOS.forEach(item -> {
            UUserBasic friendBasic = userInfoManager.getUserBasic(item.getUserId());
            resultList.add(MapUtils.gmap("statDateStr", form.getDate(), "userId", item.getUserId(), "nickname", friendBasic.getNickname(),
                    "totalCashStr", BusiUtils.cashToYuanStr(item.getTotalCash(), 2), "socialCashStr",  BusiUtils.cashToYuanStr(item.getFriendTotalRcCash(), 2),
                    "itChargeCashStr",  BusiUtils.cashToYuanStr(item.getItChargeTotalCash(), 2), "otherCashStr", BusiUtils.cashToYuanStr(item.getTotalCash() - item.getFriendTotalRcCash() - item.getItChargeTotalCash(), 2)));
        });
        emailSupport.sendAttachmentEmail(userExt.getPackageType(), form.getEmailAddress().trim(), EasyPoiExcelDefine.FEMALE_INCOME, resultList);
    }

    /**------------------- 额外 ----------------------- */

    /**
     * 获取用户现在信息描述
     * 备注：
     * 1、今天在线
     * 2、昨天在线
     * 3、3天前在线
     */
    private String buildUserOnlineDesc(Date lastOpenTime, Date now){
        if(lastOpenTime.after(now)){
            return "今天在线";
        }
        if(DateUtils.isSameDay(lastOpenTime, now)){
            return "今天在线";
        }
        if(DateUtils.isSameDay(DateUtils.plusDays(lastOpenTime, 1), now)){
            return "昨天在线";
        }
        return "3天前在线";
    }
}
