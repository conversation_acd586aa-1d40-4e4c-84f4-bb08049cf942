package com.tuowan.yeliao.social.service.friend;

import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.ListUtils;
import com.easyooo.framework.common.util.MapUtils;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.entity.user.UChatMasterVoice;
import com.tuowan.yeliao.commons.data.enums.config.NoticeSysType;
import com.tuowan.yeliao.commons.data.enums.general.ChatFilterType;
import com.tuowan.yeliao.commons.data.enums.general.ReviewResultType;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.commons.data.enums.social.QuickVoicePeriodType;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.persistence.user.UChatMasterVoiceMapper;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.open.shumei.ShumeiSupport;
import com.tuowan.yeliao.commons.open.shumei.TextValidResult;
import com.tuowan.yeliao.commons.open.shumei.enums.ChannelType;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.commons.web.proxy.config.NotifyMode;
import com.tuowan.yeliao.social.data.dto.friend.QuickVoiceDTO;
import com.tuowan.yeliao.social.web.form.friend.quickvoice.*;
import com.tuowan.yeliao.social.web.vo.friend.chat.QuickVoiceInfoVO;
import com.tuowan.yeliao.social.web.vo.friend.chat.QuickVoicePeriodVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 快捷语音相关业务
 *
 * <AUTHOR>
 * @date 2021/5/18 20:19
 */
@Service
public class QuickVoiceService {

    @Autowired
    private UChatMasterVoiceMapper uChatMasterVoiceMapper;
    @Autowired
    private ShumeiSupport shumeiSupport;
    @Autowired
    private NoticeComponent noticeComponent;


    /**
     * 查询快捷语音列表
     */
    @BusiCode
    public QuickVoiceInfoVO queryQuickVoiceInfo() {
        List<UChatMasterVoice> list = uChatMasterVoiceMapper.selectByUserId(new UChatMasterVoice(GlobalUtils.uid()));
        Map<QuickVoicePeriodType, List<UChatMasterVoice>> voiceMap = ListUtils.grouping(list, UChatMasterVoice::getTimePeriod);
        List<QuickVoicePeriodVO> periodList = new ArrayList<>();
        for (QuickVoicePeriodType periodType : QuickVoicePeriodType.values()) {
            QuickVoicePeriodVO periodDTO = new QuickVoicePeriodVO();
            periodDTO.setPeriod(periodType);
            periodDTO.setPeriodName(periodType.getDesc());
            periodDTO.setTimeRange(periodType.getStartTime() + "~" + periodType.getEndTime());
            periodDTO.setDesc(periodType.getRemark());
            periodDTO.setVoiceList(QuickVoiceDTO.create(voiceMap.get(periodType)));
            periodList.add(periodDTO);
        }
        QuickVoiceInfoVO vo = new QuickVoiceInfoVO();
        vo.setPeriodList(periodList);
        vo.setCurrPeriod(QuickVoicePeriodType.getCurrType(DateUtils.nowTime()));
        vo.setRemark("温馨提示：\n" +
                "1.可针对每个固定时段录制1个快捷语音，在该时段收到红包消息，可直接发送录制好的快捷语音。\n" +
                "2.建议录制能引起对方兴趣的语音，不要录制无意义内容。\n" +
                "3.录制语音（时长3~15秒），请保证语音内容清晰。\n" +
                "4.每条快捷语音只限发送100次，达到次数后需重新录制。");
        return vo;
    }

    /**
     * 新增快捷语音
     *
     * @param form
     */
    @BusiCode(value = BusiCodeDefine.EditQuickVoice, forward = NotifyMode.ALWAYS)
    public QuickVoiceDTO add(QuickVoiceAddForm form) {
        if (GlobalUtils.sexType() != SexType.Female) {
            throw new BusiException("您还没有该权限");
        }
        UChatMasterVoice voice = new UChatMasterVoice();
        voice.setUserId(GlobalUtils.uid());
        voice.setVoiceUrl(form.getVoiceUrl());
        voice.setVoiceLength(form.getVoiceLength());
        voice.setTimePeriod(form.getPeriod());
        voice.setCreateTime(DateUtils.nowTime());
        voice.setStatus(Status.Enable);
        voice.setUsedTimes(0);
        voice.setAuditStatus(ReviewResultType.Wait);
        uChatMasterVoiceMapper.insert(voice);

        Map<String, Object> map = new HashMap<>();
        map.put("nickname", BusiUtils.concatNameAndId(GlobalUtils.nickname(), GlobalUtils.uid()));
        GlobalUtils.extValue(BusinessDataKey.CustomMap, map);
        return QuickVoiceDTO.create(voice);
    }

    /**
     * 修改快捷语音->重新录制
     *
     * @param form
     */
    @BusiCode(value = BusiCodeDefine.EditQuickVoice, forward = NotifyMode.ALWAYS)
    public void update(QuickVoiceUpdateForm form) {
        UChatMasterVoice voice = uChatMasterVoiceMapper.selectByPrimaryKey(new UChatMasterVoice(form.getVoiceId()));
        if (voice == null || !voice.getUserId().equals(GlobalUtils.uid())) {
            throw new BusiException("语音不存在，请刷新后重试");
        }
        voice.setUserId(GlobalUtils.uid());
        voice.setVoiceUrl(form.getVoiceUrl());
        voice.setVoiceLength(form.getVoiceLength());
        voice.setAuditStatus(ReviewResultType.Wait);
        voice.setUsedTimes(0);
        voice.setAuditUserId(null);
        voice.setAuditTime(null);
        voice.setAuditReason(null);
        voice.setAuditRemark(null);
        uChatMasterVoiceMapper.updateByPrimaryKey(voice);

        Map<String, Object> map = new HashMap<>();
        map.put("nickname", BusiUtils.concatNameAndId(GlobalUtils.nickname(), GlobalUtils.uid()));
        GlobalUtils.extValue(BusinessDataKey.CustomMap, map);
    }

    /**
     * 新增快捷语音备注
     *
     * @param form
     */
    @BusiCode
    public void addRemark(QuickVoiceRemarkForm form) {
        UChatMasterVoice voice = uChatMasterVoiceMapper.selectByPrimaryKey(new UChatMasterVoice(form.getVoiceId()));
        if (voice == null || !voice.getUserId().equals(GlobalUtils.uid())) {
            throw new BusiException("语音不存在，请刷新后重试");
        }

        voice.setRemark(form.getRemark());
        voice.setUserId(GlobalUtils.uid());
        TextValidResult result = shumeiSupport.verifyText(GlobalUtils.uid(), form.getRemark(), ChannelType.MESSAGE, GlobalUtils.userType());
        if (ChatFilterType.Pass != result.getFilterType()) {
            throw new BusiException("内容违规，请重新输入");
        }
        voice.setRemark(form.getRemark());
        uChatMasterVoiceMapper.updateByPrimaryKeySelective(voice);
    }

    /**
     * 删除快捷语音备注
     *
     * @param form
     */
    @BusiCode
    public void delete(QuickVoiceDeleteForm form) {
        UChatMasterVoice voice = uChatMasterVoiceMapper.selectByPrimaryKey(new UChatMasterVoice(form.getVoiceId()));
        if (voice == null || !voice.getUserId().equals(GlobalUtils.uid())) {
            throw new BusiException("语音不存在，请刷新后重试");
        }
        voice.setStatus(Status.Disable);
        uChatMasterVoiceMapper.updateByPrimaryKeySelective(voice);
    }

    /**
     * 查询当前可用的
     *
     * @return
     */
    @BusiCode
    public QuickVoicePeriodVO listCurrVoice() {
        QuickVoicePeriodType period = QuickVoicePeriodType.getCurrType(DateUtils.nowTime());
        QuickVoicePeriodVO vo = new QuickVoicePeriodVO();
        vo.setTimeRange(period.getStartTime() + "~" + period.getEndTime());
        vo.setPeriod(period);
        vo.setPeriodName(period.getDesc());
        UChatMasterVoice voice = new UChatMasterVoice(GlobalUtils.uid());
        voice.setTimePeriod(period);
        voice.setAuditStatus(ReviewResultType.Pass);
        List<UChatMasterVoice> list = uChatMasterVoiceMapper.selectByUserId(voice);
        vo.setVoiceList(QuickVoiceDTO.create(list));
        return vo;
    }

    /**
     * 审核快捷语音
     *
     * @param form
     */
    @BusiCode
    public void saveAudit(QuickVoiceAuditForm form) {
        UChatMasterVoice voice = uChatMasterVoiceMapper.selectByPrimaryKey(new UChatMasterVoice(form.getVoiceId()));
        if (voice == null || voice.getAuditStatus() != ReviewResultType.Wait || voice.getStatus() == Status.Disable) {
            throw new BusiException("语音【不存在】或不是【待审核】状态");
        }
        voice.setAuditReason(form.getAuditReason());
        voice.setAuditRemark(form.getAuditRemark());
        voice.setAuditUserId(GlobalUtils.uid());
        voice.setAuditTime(DateUtils.nowTime());
        voice.setAuditStatus(form.getAuditStatus());
        uChatMasterVoiceMapper.updateByPrimaryKeySelective(voice);

        // 发送审核结果通知
        NoticeSysType noticeType = form.getAuditStatus() == ReviewResultType.Pass ? NoticeSysType.QuickVoicePass : NoticeSysType.QuickVoiceReject;
        noticeComponent.sendSystemNotice(voice.getUserId(), noticeType, MapUtils.gmap("reason", form.getAuditReason()));
    }
}
