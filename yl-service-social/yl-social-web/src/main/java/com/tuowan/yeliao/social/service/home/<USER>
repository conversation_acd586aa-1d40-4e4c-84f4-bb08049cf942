package com.tuowan.yeliao.social.service.home;

import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.comp.headline.UserNoticeComponent;
import com.tuowan.yeliao.commons.comp.signin.UserSignInComponent;
import com.tuowan.yeliao.commons.comp.task.NewTaskComponent;
import com.tuowan.yeliao.commons.comp.task.dto.NewTaskDTO;
import com.tuowan.yeliao.commons.comp.user.UserPopUpComponent;
import com.tuowan.yeliao.commons.comp.user.UserSocialBagComponent;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.redis.template.BusiRedisTemplate;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.general.user.ChatMasterRefreshType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.dto.social.MarketingDTO;
import com.tuowan.yeliao.commons.data.dto.social.VideoProdDTO;
import com.tuowan.yeliao.commons.data.dto.social.VmProdDTO;
import com.tuowan.yeliao.commons.data.dto.user.OnlineDTO;
import com.tuowan.yeliao.commons.data.dto.user.UserBagDTO;
import com.tuowan.yeliao.commons.data.dto.user.UserDTO;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGift;
import com.tuowan.yeliao.commons.data.entity.user.*;
import com.tuowan.yeliao.commons.data.enums.config.ProdGoodsType;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.enums.social.ChatRoomType;
import com.tuowan.yeliao.commons.data.enums.task.CpTaskStatus;
import com.tuowan.yeliao.commons.data.enums.user.*;
import com.tuowan.yeliao.commons.data.manager.commons.BusiManager;
import com.tuowan.yeliao.commons.data.manager.config.AdManager;
import com.tuowan.yeliao.commons.data.manager.config.NewsManager;
import com.tuowan.yeliao.commons.data.manager.user.ActionStatManager;
import com.tuowan.yeliao.commons.data.manager.user.ChatMasterManager;
import com.tuowan.yeliao.commons.data.manager.user.MessageSettingManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.persistence.config.TProdSocialGiftMapper;
import com.tuowan.yeliao.commons.data.persistence.user.UUserBasicMapper;
import com.tuowan.yeliao.commons.data.persistence.user.UUserExtMapper;
import com.tuowan.yeliao.commons.data.persistence.user.UUserLocationMapper;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.data.utils.HtmlUrlUtils;
import com.tuowan.yeliao.commons.data.utils.dto.AdDefineDTO;
import com.tuowan.yeliao.commons.mq.producer.MessageQueueProducer;
import com.tuowan.yeliao.commons.web.common.form.PageForm;
import com.tuowan.yeliao.commons.web.common.form.TotpCodeForm;
import com.tuowan.yeliao.commons.web.common.form.UserIdForm;
import com.tuowan.yeliao.commons.web.common.vo.ListVO;
import com.tuowan.yeliao.commons.web.common.vo.PageVO;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.commons.web.proxy.config.NotifyMode;
import com.tuowan.yeliao.social.comp.chatmaster.ChatMasterComponent;
import com.tuowan.yeliao.social.comp.consume.dto.FriendsTopDTO;
import com.tuowan.yeliao.social.comp.consume.dto.GiftTvWallDTO;
import com.tuowan.yeliao.social.comp.friend.*;
import com.tuowan.yeliao.social.comp.home.HomeRecomComponent;
import com.tuowan.yeliao.social.comp.home.HomeVideoChatComponent;
import com.tuowan.yeliao.social.data.dto.friend.MgRankUserDTO;
import com.tuowan.yeliao.social.data.dto.friend.RelationDTO;
import com.tuowan.yeliao.social.data.entity.USocialWishConfig;
import com.tuowan.yeliao.social.data.enums.friend.HomeTabType;
import com.tuowan.yeliao.social.data.enums.friend.PhoneStatusType;
import com.tuowan.yeliao.social.data.enums.game.GameType;
import com.tuowan.yeliao.social.data.manager.friend.ChatManager;
import com.tuowan.yeliao.social.data.manager.friend.FriendRelationManager;
import com.tuowan.yeliao.social.data.manager.friend.UserSocialWishManager;
import com.tuowan.yeliao.social.data.persistence.USocialWishConfigMapper;
import com.tuowan.yeliao.social.data.search.document.UserFeature;
import com.tuowan.yeliao.social.data.search.repository.UserFeatureRepository;
import com.tuowan.yeliao.social.web.form.friend.nearby.HeartTouchForm;
import com.tuowan.yeliao.social.web.form.friend.nearby.NearbyQueryForm;
import com.tuowan.yeliao.social.web.form.home.*;
import com.tuowan.yeliao.social.web.vo.home.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.resps.Tuple;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 交友首页相关业务
 *
 * <AUTHOR>
 * @date 2020/7/1 15:15
 */
@Service
public class HomeService {
    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private UserFeatureRepository userFeatureRepository;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private BusiRedisTemplate busiRedisTemplate;
    @Autowired
    private AdManager adManager;
    @Autowired
    private HomeRecomComponent homeRecomComponent;
    @Autowired
    private FriendRelationManager friendRelationManager;
    @Autowired
    private UserNoticeComponent homeHeadLineComponent;
    @Autowired
    private NetCallMatchComponent netCallMatchComponent;
    @Autowired
    private NewsManager newsManager;
    @Autowired
    private FateMatchBakComponent fateMatchBakComponent;
    @Autowired
    private HomeVideoChatComponent videoChatComponent;
    @Autowired
    private NewTaskComponent newTaskComponent;
    @Autowired
    private ChatManager chatManager;
    @Autowired
    private NetCallComponent netCallComponent;
    @Autowired
    private MessageSettingManager settingManager;
    @Autowired
    private UserStayComponent userStayComponent;
    @Autowired
    private ActionStatManager actionStatManager;
    @Autowired
    private USocialWishConfigMapper uSocialWishConfigMapper;
    @Autowired
    private UserSignInComponent userSignInComponent;
    @Autowired
    private UserSocialWishManager userSocialWishManager;
    @Autowired
    private BusiManager busiManager;
    @Autowired
    private UUserBasicMapper uUserBasicMapper;
    @Autowired
    private UUserExtMapper uUserExtMapper;
    @Autowired
    private UUserLocationMapper uUserLocationMapper;
    @Autowired
    private ChatMasterComponent chatMasterComponent;
    @Autowired
    private MessageQueueProducer messageQueueProducer;
    @Autowired
    private UserPopUpComponent userPopUpComponent;
    @Autowired
    private FateMatchComponent fateMatchComponent;
    @Autowired
    private UserSocialBagComponent userSocialBagComponent;
    @Autowired
    private ChatMasterManager chatMasterManager;
    @Autowired
    private FriendRelationComponent friendRelationComponent;
    @Autowired
    private ChatConsumeComponent chatConsumeComponent;
    @Autowired
    private TProdSocialGiftMapper tProdSocialGiftMapper;
    @Autowired
    private MessageSettingManager messageSettingManager;
    @Autowired
    private MindGiftComponent mindGiftComponent;

    /**
     * 获取首页信息
     *
     * @return
     */
    @BusiCode
    public HomeInfoVO getInfo() {
        HomeInfoVO vo = new HomeInfoVO();
        Long userId = GlobalUtils.uid();
        SexType sexType = GlobalUtils.sexType();
        vo.setShowAdvertAward(HtmlUrlUtils.getAdvertAwardUrl(GlobalUtils.packageType()));
        vo.setVideoMatch(netCallMatchComponent.getVideoMatchStatus(userId, GlobalUtils.sexType()));
        vo.setRedPacketCount(fateMatchBakComponent.getRedPacketCount(userId));
        vo.setRedPacketTtl(fateMatchBakComponent.getRedPacketTtl(userId));
        vo.setShowSignPopup(BoolType.valueOf(userSignInComponent.canShowSignPopup(userId, GlobalUtils.sexType())));
        vo.setShowInvite(BoolType.True);
        // 默认定位
        vo.setDefaultTab(getDefaultTab(userId));
        return vo;
    }

    /**
     * 右滑喜欢用户
     *
     * @param form
     */
    @BusiCode(value = BusiCodeDefine.HeartTouch, forward = NotifyMode.DYNAMIC)
    public void saveHeartTouch(HeartTouchForm form) {
        Long userId = GlobalUtils.uid();
        Long friendId = form.getFriendId();
        if (friendId <= 0) {
            // 客户端有bug， 可能会传0
            return;
        }
        RelationDTO relation = friendRelationManager.getRelation(userId, friendId);
        if (relation != null) {
            if (relation.getTargetBlacklistTime() != null) {
                throw new BusiException("对方已将你拉黑，不能互发消息和心动");
            }
        }
    }


    /**
     * 查询置顶头条消息列表
     *
     * @return
     */
    @BusiCode
    public List<String> headlineMsgList() {
        return homeHeadLineComponent.getRecentTopMsg();
    }

    /**
     * 推荐列表
     *
     * @return
     */
    @BusiCode
    public RecommendVO recommendList(HomeRecomListForm form) {
        List<VideoChatUserVO> resultList = new ArrayList<>();
        Long currUserId = GlobalUtils.uid();
        // 用户是否含有限时免费券
        boolean freeTime = netCallComponent.hasFreeTimeVideoTicketBagCnt(currUserId);
        // 视频聊列表
        List<Tuple> tuples = videoChatComponent.videoChatListByScore(form.getOffset(), form.getLimit());
        // 同分值随机排序
        List<Tuple> newTuples = new ArrayList<>();
        Map<Double, List<Tuple>> tupleMap = tuples.stream().collect(Collectors.groupingBy(dto -> dto.getScore()));
        if (null != tupleMap && tupleMap.size() > 0) {
            Map<Double, List<Tuple>> resultMap = new LinkedHashMap<>();
            // map 分值key倒序
            tupleMap.entrySet().stream().sorted(Map.Entry.<Double, List<Tuple>>comparingByKey().reversed()).forEachOrdered(e -> resultMap.put(e.getKey(), e.getValue()));
            resultMap.forEach((k, v) -> {
                List<Tuple> scoreList = tupleMap.get(k);
                Collections.shuffle(scoreList);
                newTuples.addAll(scoreList);
            });
        }
        for (Tuple tuple : newTuples) {
            Long userId = Long.valueOf(tuple.getElement());
            // 用户基本信息
            UUserBasic basic = userInfoManager.getUserBasic(userId);
            // 推荐视频
            UUserMore more = userInfoManager.getUserMore(userId);
            VideoChatUserVO chatUser = new VideoChatUserVO(basic, more);
            // 限时免费
            chatUser.setFreeTime(BoolType.False);
            if (freeTime) {
                int num = RandomUtils.getInt(1, 6);
                chatUser.setFreeTime(num <= 2 ? BoolType.True : BoolType.False);
            }
            // 用户在线信息
            OnlineDTO onlineInfo = userInfoManager.buildOnlineInfo(basic.getUserId(), basic.getSex());
            chatUser.setOnlineStatus(onlineInfo.getOnlineStatus());
            chatUser.setOnlineStatusText(onlineInfo.getOnlineStatusText());
            boolean vip = BusiUtils.isVip(basic);
            chatUser.setVip(BoolType.valueOf(vip));
            if (vip) {
                // 会员用户昵称颜色
                chatUser.setNickColor(GlobalConstant.VIP_NICK_COLOR);
            }
            resultList.add(chatUser);
        }
        RecommendVO vo = new RecommendVO(resultList, form.getLimit());
        if (form.getOffset() == 0) {
            vo.setAdList(adManager.listBannerAds(GlobalUtils.clientType(), GlobalUtils.packageType(), GlobalUtils.clientVersion()));
            // 是否显示上推荐
            if (SexType.Female == GlobalUtils.sexType()) {
                vo.setOnRecommend(BoolType.valueOf(settingManager.hasVideoChatAuth(currUserId)));
            }
        }
        // 头像认证状态
        BoolType realPerson = GlobalUtils.realPerson();
        // 实名状态
        BoolType realName = userInfoManager.isRealName(GlobalUtils.uid());
        vo.setRealPerson(realPerson);
        vo.setRealName(realName);
        return vo;
    }


    /**
     * 获取新手任务
     *
     * @param userId
     * @param sexType
     * @return
     */
    private NewTaskVO getNewTask(Long userId, SexType sexType) {
        if (newTaskComponent.hideOpenNewTask(userId)) {
            return null;
        }
        List<NewTaskDTO> taskList = newTaskComponent.getNewTask(userId, sexType);
        if (ListUtils.isEmpty(taskList)) {
            return null;
        }
        // 时间控制校验
        String totalCash = NewTaskComponent.FEMALE_TOTAL_AWARD_CASH;
        for (NewTaskDTO dto : taskList) {
            if (UserAuthType.RealName == dto.getAuthType() && CpTaskStatus.Finish == dto.getStatus()) {
                totalCash = NewTaskComponent.FEMALE_REAL_HEAD_AWARD_CASH_DESC;
            }
            if (UserAuthType.RealHead == dto.getAuthType() && CpTaskStatus.Finish == dto.getStatus()) {
                totalCash = NewTaskComponent.FEMALE_REAL_NAME_AWARD_CASH_DESC;
            }
        }
        newTaskComponent.saveOpenNewTaskDayCache(userId);
        return new NewTaskVO(totalCash, taskList, ClientTouchType.EditMineHomePage, null);
    }

    /**
     * 定位渠道改变
     *
     * @param userId
     * @return
     */
    private HomeTabType getDefaultTab(Long userId) {
        UUserExt ext = userInfoManager.getUserExt(userId);
        if (StringUtils.isNotEmpty(ext.getChannelCode())) {
            return HomeTabType.NearBy;
        }
        // 华为渠道
        if (StringUtils.containsTarget(ext.getChannelCode(), GlobalConstant.REG_HUAWEI_CHANNEL)) {
            return HomeTabType.Recommend;
        }
        return HomeTabType.NearBy;
    }

    /**
     * 男用户在线列表
     *
     * @param form
     * @return
     */
    @BusiCode
    public PageVO getMaleRecomList(MaleListForm form) {
        Long currUserId = GlobalUtils.uid();
        SexType currSexType = GlobalUtils.sexType();
        if(SexType.Female != currSexType){
            throw new BusiException(ErrCodeType.InvalidArguments);
        }
        Date now = new Date(GlobalUtils.reqTime());
        boolean isWhitelistChannel = newsManager.isAndroidReviewVersion(GlobalUtils.clientType(), GlobalUtils.clientVersion(), GlobalUtils.channelId(), GlobalUtils.packageType(), currUserId);
        List<UserFeature> userList;
        if (isWhitelistChannel) {
            userList = maleWhiteList();
        } else {
            USocialWishConfig config = userSocialWishManager.getUserSearConfig(currUserId);
            userList = maleRecomList(form, config);
        }
        // 封装返回数据
        List<HomeMaleUserVO> resultList = new ArrayList<>();
        for (UserFeature feature : userList) {
            resultList.add(getMaleHomeUserInfo(now, feature.getId(), feature.getSex(), currUserId, currSexType));
        }
        // 加白数据 随机在线数据
        if (isWhitelistChannel) {
            setWhitelistMaleOnlineStatus(resultList);
        }
        return new PageVO(shuffleMaleList(resultList), form.getOffset(), form.getLimit());
    }

    /**
     * 男用户新人列表
     */
    @BusiCode
    public PageVO getMaleNewList(MaleListForm form) {
        Long currUserId = GlobalUtils.uid();
        SexType currSexType = GlobalUtils.sexType();
        if(SexType.Female != currSexType){
            throw new BusiException(ErrCodeType.InvalidArguments);
        }
        Date now = new Date(GlobalUtils.reqTime());
        boolean isWhitelistChannel = newsManager.isAndroidReviewVersion(GlobalUtils.clientType(), GlobalUtils.clientVersion(), GlobalUtils.channelId(), GlobalUtils.packageType(), currUserId);
        List<UserFeature> userList;
        if (isWhitelistChannel) {
            userList = maleWhiteList();
        } else {
            USocialWishConfig config = userSocialWishManager.getUserSearConfig(currUserId);
            userList = maleNewList(form, config);
        }
        // 封装返回数据
        List<HomeMaleUserVO> resultList = new ArrayList<>();
        for (UserFeature feature : userList) {
            resultList.add(getMaleHomeUserInfo(now, feature.getId(), feature.getSex(), currUserId, currSexType));
        }
        // 加白数据 随机在线数据
        if (isWhitelistChannel) {
            setWhitelistMaleOnlineStatus(resultList);
        }
        return new PageVO(shuffleMaleList(resultList), form.getOffset(), form.getLimit());
    }

    /**
     * 男用户附近列表
     */
    @BusiCode
    public PageVO getMaleNearList(MaleListForm form) {
        Long currUserId = GlobalUtils.uid();
        SexType currSexType = GlobalUtils.sexType();
        if(SexType.Female != currSexType){
            throw new BusiException(ErrCodeType.InvalidArguments);
        }
        Date now = new Date(GlobalUtils.reqTime());
        boolean isWhitelistChannel = newsManager.isAndroidReviewVersion(GlobalUtils.clientType(), GlobalUtils.clientVersion(), GlobalUtils.channelId(), GlobalUtils.packageType(), currUserId);
        List<UserFeature> userList;
        if (isWhitelistChannel) {
            userList = maleWhiteList();
        } else {
            USocialWishConfig config = userSocialWishManager.getUserSearConfig(currUserId);
            userList = maleNearList(form, config);
        }
        // 封装返回数据
        List<HomeMaleUserVO> resultList = new ArrayList<>();
        for (UserFeature feature : userList) {
            resultList.add(getMaleHomeUserInfo01(now, feature.getId(), feature.getSex(), currUserId, currSexType));
        }
        // 加白数据 随机在线数据
        if (isWhitelistChannel) {
            setWhitelistMaleOnlineStatus(resultList);
        }
        return new PageVO(shuffleMaleList(resultList), form.getOffset(), form.getLimit());
    }

    /**
     * 女用户热门列表
     */
    @BusiCode
    public PageVO getFemaleRecomList(FemaleListForm form){
        Long currUserId = GlobalUtils.uid();
        SexType currSexType = GlobalUtils.sexType();
        Date now = new Date(GlobalUtils.reqTime());
        if(SexType.Male != currSexType){
            throw new BusiException(ErrCodeType.InvalidArguments);
        }
        boolean isWhitelistChannel = newsManager.isAndroidReviewVersion(GlobalUtils.clientType(), GlobalUtils.clientVersion(), GlobalUtils.channelId(), GlobalUtils.packageType(), currUserId);
        List<UserFeature> userList;
        if (isWhitelistChannel) {
            userList = femaleWhiteList();
        } else {
            USocialWishConfig config = userSocialWishManager.getUserSearConfig(currUserId);
            userList = femaleRecomList(form, config);
        }
        // 封装返回数据
        List<HomeFemaleUserVO> resultList = new ArrayList<>();
        for (UserFeature feature : userList) {
            HomeFemaleUserVO femaleUserVO = getFemaleHomeUserInfo01(now, feature.getId(), feature.getSex(), currUserId, currSexType);
            femaleUserVO.setExtScore(feature.getData1()); // 对于热门列表来说 我们这边加上女用户的额外分数去随机打乱顺序
            femaleUserVO.setOnlineScore(femaleUserVO.getOnlineStatus().getScore());
            resultList.add(femaleUserVO);
        }
        // 加白数据 随机在线数据
        if (isWhitelistChannel) {
            setWhitelistFemaleOnlineStatus(resultList);
        }
        return new PageVO(this.shuffleFemaleList01(resultList), form.getOffset(), form.getLimit());
    }

    /**
     * 女用户新人列表
     */
    @BusiCode
    public PageVO getFemaleNewList(FemaleListForm form){
        Long currUserId = GlobalUtils.uid();
        SexType currSexType = GlobalUtils.sexType();
        Date now = new Date(GlobalUtils.reqTime());
        if(SexType.Male != currSexType){
            throw new BusiException(ErrCodeType.InvalidArguments);
        }
        boolean isWhitelistChannel = newsManager.isAndroidReviewVersion(GlobalUtils.clientType(), GlobalUtils.clientVersion(), GlobalUtils.channelId(), GlobalUtils.packageType(), currUserId);
        List<UserFeature> userList;
        if (isWhitelistChannel) {
            userList = femaleWhiteList();
        } else {
            USocialWishConfig config = userSocialWishManager.getUserSearConfig(currUserId);
            userList = femaleNewList(form, config);
        }
        // 封装返回数据
        List<HomeFemaleUserVO> resultList = new ArrayList<>();
        for (UserFeature feature : userList) {
            resultList.add(getFemaleHomeUserInfo01(now, feature.getId(), feature.getSex(), currUserId, currSexType));
        }
        // 加白数据 随机在线数据
        if (isWhitelistChannel) {
            setWhitelistFemaleOnlineStatus(resultList);
        }
        return new PageVO(this.shuffleFemaleList(resultList), form.getOffset(), form.getLimit());
    }

    /**
     * 女用户活跃列表
     */
    @BusiCode
    public PageVO getFemaleActiveList(FemaleListForm form){
        Long currUserId = GlobalUtils.uid();
        SexType currSexType = GlobalUtils.sexType();
        Date now = new Date(GlobalUtils.reqTime());
        if(SexType.Male != currSexType){
            throw new BusiException(ErrCodeType.InvalidArguments);
        }
        boolean isWhitelistChannel = newsManager.isAndroidReviewVersion(GlobalUtils.clientType(), GlobalUtils.clientVersion(), GlobalUtils.channelId(), GlobalUtils.packageType(), currUserId);
        List<UserFeature> userList;
        if (isWhitelistChannel) {
            userList = femaleWhiteList();
        } else {
            USocialWishConfig config = userSocialWishManager.getUserSearConfig(currUserId);
            userList = femaleActiveList(form, config);
        }
        // 封装返回数据
        List<HomeFemaleUserVO> resultList = new ArrayList<>();
        for (UserFeature feature : userList) {
            resultList.add(getFemaleHomeUserInfo01(now, feature.getId(), feature.getSex(), currUserId, currSexType));
        }
        // 加白数据 随机在线数据
        if (isWhitelistChannel) {
            setWhitelistFemaleOnlineStatus(resultList);
        }
        return new PageVO(this.shuffleFemaleList(resultList), form.getOffset(), form.getLimit());
    }

    /**
     * 女用户附近列表
     */
    @BusiCode
    public PageVO getFemaleNearByList(FemaleListForm form){
        Long currUserId = GlobalUtils.uid();
        SexType currSexType = GlobalUtils.sexType();
        Date now = new Date(GlobalUtils.reqTime());
        if(SexType.Male != currSexType){
            throw new BusiException(ErrCodeType.InvalidArguments);
        }
        boolean isWhitelistChannel = newsManager.isAndroidReviewVersion(GlobalUtils.clientType(), GlobalUtils.clientVersion(), GlobalUtils.channelId(), GlobalUtils.packageType(), currUserId);
        List<UserFeature> userList;
        if (isWhitelistChannel) {
            userList = Collections.emptyList();
        } else {
            USocialWishConfig config = userSocialWishManager.getUserSearConfig(currUserId);
            userList = femaleNearByList(form, config);
        }
        // 封装返回数据
        List<HomeFemaleUserVO> resultList = new ArrayList<>();
        for (UserFeature feature : userList) {
            resultList.add(getFemaleHomeUserInfo02(now, feature.getId(), feature.getSex(), currUserId, currSexType));
        }
        return new PageVO(resultList, form.getOffset(), form.getLimit());
    }

    /**
     * 女用户女神列表
     */
    @BusiCode
    public PageVO getFemaleBeautyList(FemaleListForm form){
        Long currUserId = GlobalUtils.uid();
        SexType currSexType = GlobalUtils.sexType();
        Date now = new Date(GlobalUtils.reqTime());
        if(SexType.Male != currSexType){
            throw new BusiException(ErrCodeType.InvalidArguments);
        }
        // USocialWishConfig config = userSocialWishManager.getUserSearConfig(currUserId);
        boolean isWhitelistChannel = newsManager.isAndroidReviewVersion(GlobalUtils.clientType(), GlobalUtils.clientVersion(), GlobalUtils.channelId(), GlobalUtils.packageType(), currUserId);
        List<UserFeature> userList;
        if (isWhitelistChannel) {
            userList = femaleWhiteList();
        } else {
            userList = femaleBeautyList(form, null);
        }
        // 封装返回数据
        List<HomeFemaleUserVO> resultList = new ArrayList<>();
        for (UserFeature feature : userList) {
            resultList.add(getFemaleHomeUserInfo01(now, feature.getId(), feature.getSex(), currUserId, currSexType));
        }
        // 加白数据 随机在线数据
        if (isWhitelistChannel) {
            setWhitelistFemaleOnlineStatus(resultList);
        }
        return new PageVO(this.shuffleFemaleList(resultList), form.getOffset(), form.getLimit());
    }

    /**
     * 女用户列表打乱顺序
     */
    private List<HomeFemaleUserVO> shuffleFemaleList(List<HomeFemaleUserVO> list){
        if(ListUtils.isEmpty(list)){
            return list;
        }
        Collections.shuffle(list);
        return list.stream().sorted((s1,s2) -> (int)(s2.getOnlineStatus().getScore() - s1.getOnlineStatus().getScore())).collect(Collectors.toList());
    }
    private List<HomeFemaleUserVO> shuffleFemaleList01(List<HomeFemaleUserVO> list){
        if(ListUtils.isEmpty(list)){
            return list;
        }
        Collections.shuffle(list);
        return list.stream().sorted(Comparator.comparing(HomeFemaleUserVO::getOnlineScore).thenComparing(HomeFemaleUserVO::getExtScore).reversed()).collect(Collectors.toList());
    }

    /**
     * 男用户列表打乱顺序
     */
    private List<HomeMaleUserVO> shuffleMaleList(List<HomeMaleUserVO> list){
        if(ListUtils.isEmpty(list)){
            return list;
        }
        Collections.shuffle(list);
        return list.stream().sorted((s1,s2) -> (int)(s2.getOnlineStatus().getScore() - s1.getOnlineStatus().getScore())).collect(Collectors.toList());
    }

    /**
     * 设置加白用户在线状态
     *
     * @param resultList
     */
    private void setWhitelistMaleOnlineStatus(List<HomeMaleUserVO> resultList) {
        // 无在线用户随机取几个用户设置在线状态
        int count = RandomUtils.getInt(3, 10);
        for (HomeMaleUserVO user : resultList) {
            user.setOnlineStatus(OnlineStatus.Online);
            count--;
            if (count <= 0) {
                break;
            }
        }
    }

    /**
     * 设置加白用户在线状态
     *
     * @param resultList
     */
    private void setWhitelistFemaleOnlineStatus(List<HomeFemaleUserVO> resultList) {
        // 无在线用户随机取几个用户设置在线状态
        int count = RandomUtils.getInt(3, 10);
        for (HomeFemaleUserVO user : resultList) {
            user.setOnlineStatus(OnlineStatus.Online);
            count--;
            if (count <= 0) {
                break;
            }
        }
    }

    /**
     * 刷新嘉宾信息
     *
     * @param form
     */
    @BusiCode
    public void saveRefreshChatMaster(UserIdForm form) {
        Map<BusinessDataKey, Object> params = new HashMap<>();
        params.put(BusinessDataKey.UserId, form.getUserId());
        params.put(BusinessDataKey.RefreshType, ChatMasterRefreshType.BindCode);
        //messageQueueProducer.sendAsync(BusiCodeDefine.RefreshChatMaster, MessageTag.SocialBusi, params);
    }

    /**
     * 获取首页扩展信息
     */
    @BusiCode
    public ExtDataVO getExtData(){
        Long userId = GlobalUtils.uid();
        SexType sexType = GlobalUtils.sexType();
        UUserExt userExt = userInfoManager.getUserExt(userId);
        // 构建返回值
        ExtDataVO vo = new ExtDataVO();
        vo.setCommonAdList(adManager.listCommonBannerAds(GlobalUtils.clientType(), GlobalUtils.packageType(), GlobalUtils.clientVersion()));
        if(SexType.Male == sexType){  // 男用户
            vo.setVideoMatchHpIcon(fateMatchComponent.getMaleHomeVideoMatchHpIcon());
            List<FriendsTopDTO> friendsTopDTOS = new ArrayList<>();
            friendsTopDTOS.add(FriendsTopDTO.build1("视频速配", "cfg/common/male_home_vm.png", ClientTouchType.VideoMatch, null));
            if(GlobalUtils.clientType() == ClientType.Android){
                friendsTopDTOS.add(FriendsTopDTO.build1("聊天室", "cfg/common/male_home_cr.png", ClientTouchType.ChatRoom, null));
            }
            vo.setMakeFriendsTopList(friendsTopDTOS);
            vo.setmDto(MarketingDTO.build1(GlobalConstant.NEW_USER_RECHARGE_IDG, ClientTouchType.FastRecharge, null));
        }else{ // 女用户
            List<FriendsTopDTO> friendsTopDTOS = new ArrayList<>();
            friendsTopDTOS.add(FriendsTopDTO.build1("任务中心", "cfg/common/female_home_ts.png", ClientTouchType.TaskCenter, null));
            if(GlobalUtils.clientType() == ClientType.Android){
                friendsTopDTOS.add(FriendsTopDTO.build1("聊天室", "cfg/common/female_home_cr.png", ClientTouchType.ChatRoom, null));
            }
            vo.setMakeFriendsTopList(friendsTopDTOS);
        }
        // 判断用户当日是否弹窗青少年弹窗《还有全局参数控制》
        vo.setPopTeenagers(BoolType.valueOf(userInfoManager.checkUserPopTeenagers(userId)));
        if(BoolType.True == vo.getPopTeenagers()){
            userInfoManager.recordUserPopTeenagers(userId);
        }
        // 视频券信息
        List<UserBagDTO> videoFreeInfo = userSocialBagComponent.findGoodsInfoList(userId, ProdGoodsType.SocialVideoFreeTicket);
        List<UserBagDTO> videoDiscountInfo = userSocialBagComponent.findGoodsInfoList(userId, ProdGoodsType.SocialVideoDiscountTicket);
        vo.setVideoProdDTO(VideoProdDTO.build(videoFreeInfo.size(), videoDiscountInfo.size()));
        // 视频速配券信息
        List<UserBagDTO> vmDiscountInfo = userSocialBagComponent.findGoodsInfoList(userId, ProdGoodsType.SocialVideoMatchDiscountTicket);
        if(ListUtils.isNotEmpty(vmDiscountInfo)){
            int num = vmDiscountInfo.stream().map(UserBagDTO::getSurplusCount).filter(Objects::nonNull).reduce(0, Integer::sum);
            Date expireTime = vmDiscountInfo.stream().map(UserBagDTO::getExpTime).filter(Objects::nonNull).max(Comparator.naturalOrder()).orElse(null);
            vo.setVmProdDTO(VmProdDTO.build(num, Objects.nonNull(expireTime) ? expireTime.getTime() : null));
        }
        // 心意榜信息
        Long mgFirstUser = mindGiftComponent.queryYesterdayMgRankFirst(new Date(GlobalUtils.reqTime()));
        if(Objects.nonNull(mgFirstUser)){
            Long eldestBrother = mindGiftComponent.queryYesterdayMgRankFirstEldest(new Date(GlobalUtils.reqTime()), mgFirstUser);
            if(Objects.nonNull(eldestBrother)){
                UUserBasic sender = userInfoManager.getUserBasic(eldestBrother);
                UUserBasic receiver = userInfoManager.getUserBasic(mgFirstUser);
                vo.setMgRankUserDTO(MgRankUserDTO.build1(BoolType.True, BoolType.True, sender.getNickname(), sender.getHeadPic(), receiver.getNickname(), receiver.getHeadPic()));
            }
        }
        // 易盾只能风控开关控制
        UrlParamsMap ydRiskSwitch = UrlParamsMap.build(SettingsConfig.getString(SettingsType.RegYdRiskControl));
        vo.setMsgYdRiskSwitch(ydRiskSwitch.getBool("msg"));
        vo.setCallYdRiskSwitch(ydRiskSwitch.getBool("call"));
        vo.setDpCrBottomTable(BoolType.valueOf(StringUtils.isNotEmpty(userExt.getLookChatRoomType()) && EnumUtils.byId(userExt.getLookChatRoomType(), ChatRoomType.class).getSpe()));
        return vo;
    }

    /**
     * 查询头条消息
     *
     * @return
     */
    @BusiCode
    public HeadLineMsgVO headlineMsg() {
        // 置顶头条
        List<String> topList = homeHeadLineComponent.getRecentTopMsg();
        // 头条列表
        List<String> msgList = homeHeadLineComponent.getRecentMsg();
        // 头条规则页
        String ruleUrl = HtmlUrlUtils.getHeadLineMsgUrl();
        return new HeadLineMsgVO(topList, msgList, ruleUrl);
    }

    /**
     * 获取附近的人筛选配置
     *
     * @return
     */
    @BusiCode
    public SearchConfigVO saveFindSearchConfig() {
        Long userId = GlobalUtils.uid();
        USocialWishConfig config = uSocialWishConfigMapper.selectByPrimaryKey(new USocialWishConfig(userId));
        if (config == null) {
            config = USocialWishConfig.createConfigDefault(userId);
        }
        SearchConfigVO vo = new SearchConfigVO();
        vo.setMinAge(config.getMinAge());
        vo.setMaxAge(config.getMaxAge());
        return vo;
    }

    /**
     * 保存社交标签配置
     *
     * @param form
     */
    @BusiCode
    public void saveSearchConfig(SearchConfigEditForm form) {
        USocialWishConfig config = new USocialWishConfig();
        config.setUserId(GlobalUtils.uid());
        config.setMinAge(form.getMinAge());
        config.setMaxAge(form.getMaxAge());
        config.setCreateTime(new Date());
        if (uSocialWishConfigMapper.updateByPrimaryKeySelective(config) <= 0) {
            uSocialWishConfigMapper.insert(config);
        }
    }

    /**
     * 刷新附近/推荐列表
     *
     * @param form
     */
    @BusiCode
    public void saveRefreshList(TotpCodeForm form) {
        busiManager.verifyAdminTotpCode(form.getCode());
        // 删除索引
        if (Objects.equals(form.getType(), "0")) {
            userFeatureRepository.deleteIndex(new UserFeature());
            return;
        }
        // 创建索引
        if (Objects.equals(form.getType(), "1")) {
            userFeatureRepository.createIndex(new UserFeature());
            return;
        }
        // 初始化数据
        if (Objects.equals(form.getType(), "2")) {
            List<UUserBasic> userList = uUserBasicMapper.selectTestUserList();
            for (UUserBasic user : userList) {
                homeRecomComponent.refreshUserScore(user.getUserId());
            }
            return;
        }
    }

    /**
     * 初始化数据
     */
    @BusiCode
    public void saveInit(){
        if(UnifiedConfig.isProdEnv()){
            throw new BusiException(ErrCodeType.InvalidInviteCode);
        }
        List<Long> allUserIds = userInfoManager.getAllUserIds();
        for(Long userId : allUserIds){
            // 加入新人推荐队列
            homeRecomComponent.refreshUserScore(userId);
        }
    }

    @BusiCode
    public AdListVO adList() {
        List<AdDefineDTO> list = adManager.listBannerAds(GlobalUtils.clientType(), GlobalUtils.packageType(), GlobalUtils.clientVersion());
        return new AdListVO(list);
    }

    /**
     * 请求视频匹配数据
     * @return
     */
    @BusiCode
    public ListVO queryVideoMatch(){
        return null;
        /*
        // 如果是一个女用户来请求 直接抛出异常
        if(SexType.Female == GlobalUtils.sexType()){
            throw new DataException(ErrCodeType.InvalidArguments);
        }
        Date now = new Date();
        List<VideoMatchVO> list = new LinkedList<>();
        // 匹配三条数据
        List<String> userIdStr = fateMatchComponent.maleMatchFemale(now, GlobalUtils.uid(), 3, MatchType.VM);
        if(ListUtils.isEmpty(userIdStr)){
            return null;
        }
        // 判断是否有视频免费券
        Pair<Integer, ProdGoodsType> ticketInfo = netCallComponent.getVideoCallUseTicketInfo(GlobalUtils.uid());
        Integer prodId = Objects.isNull(ticketInfo) ? null : ticketInfo.getFirst();
        userIdStr.forEach(item -> {
            Long userId = Long.valueOf(item);
            UUserBasic userBasic = userInfoManager.getUserBasic(userId);
            String userPic = userBasic.getHeadPic();
            list.add(VideoMatchVO.build(userBasic.getUserId(), userPic, prodId));
            // 缓存记录女用户一天 消息信息
            actionStatManager.addSendChatMsg(userId, SexType.Female, GlobalUtils.uid(), FemaleMsg.SysCmi);
        });
        // 记录曝光数据
        fateMatchComponent.recordFemaleBgForVideoFlow(now, GlobalUtils.uid(), userIdStr);
        return ListVO.create(list);
        */
    }

    /**
     * 请求视频邀请数据
     * @return
     */
    @BusiCode
    public VideoInviteVO queryVideoInvite(){
        return null;
        /*
        // 如果是一个女用户来请求 直接抛出异常
        if(SexType.Female == GlobalUtils.sexType()){
            throw new DataException(ErrCodeType.InvalidArguments);
        }
        Date now = new Date();
        // 匹配一条数据
        List<String> userIdStr = fateMatchComponent.maleMatchFemale(now, GlobalUtils.uid(), 1, MatchType.VI);
        if(ListUtils.isEmpty(userIdStr)){
            return null;
        }
        Long userId = Long.valueOf(userIdStr.get(0));
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        String friendNotes = friendRelationManager.getFriendNotes(GlobalUtils.uid(), userId);
        // 获取显示照片
        String userPic = userBasic.getHeadPic();
        String videoUrl = null;
        // 判断是否有视频免费券
        Pair<Integer, ProdGoodsType> ticketInfo = netCallComponent.getVideoCallUseTicketInfo(GlobalUtils.uid());
        Integer prodId = Objects.isNull(ticketInfo) ? null : ticketInfo.getFirst();
        String iconDesc = Objects.isNull(ticketInfo) ? null : ProdGoodsType.SocialVideoFreeTicket == ticketInfo.getSecond() ? "免费一分钟" : "享受折扣通话";
        // 缓存记录女用户一天 消息信息
        actionStatManager.addSendChatMsg(userId, SexType.Female, GlobalUtils.uid(), FemaleMsg.SysCmi);
        // 记录曝光数据
        fateMatchComponent.recordFemaleBgForVideoFlow(now, GlobalUtils.uid(), userIdStr);
        String nickname = StringUtils.isEmpty(friendNotes) ? userBasic.getNickname() : friendNotes;
        return VideoInviteVO.build(userBasic.getUserId(), nickname, userPic, videoUrl, BusiUtils.getAgeByDate(userBasic.getBirthDate()), "位置保密", iconDesc, prodId);
        */
    }

    @BusiCode
    public GiftWallVO queryGiftWall(){
        Long userId = GlobalUtils.uid();
        UMessageSetting setting = settingManager.getSetting(userId);
        List<GiftTvWallDTO> giftTvWallDTOS = giftWallInfo(chatConsumeComponent.getCommonGiftWall());
        // 返回值 封装
        GiftWallVO vo = new GiftWallVO();
        vo.setOnGiftWallVO(buildOnGiftWallVO(userId, OnGiftWallType.Normal));
        vo.setList(giftTvWallDTOS);
        vo.setDesc("请勿轻信送礼物兑现承诺的行为，请理智消费。如遇问题可查看《平台防诈骗公告》");
        vo.setGwRuleUrl(HtmlUrlUtils.getGiftWallRule());
        return vo;
    }

    @BusiCode
    public GiftFsVO queryGiftFs(){
        GiftFsVO vo = new GiftFsVO();
        List<String> giftWall = chatConsumeComponent.getCommonGiftWall();
        List<Tuple> baGiftWall = chatConsumeComponent.getBaGiftWall(GlobalUtils.uid(), new Date(GlobalUtils.reqTime()), 5);
        if(ListUtils.isEmpty(giftWall) && ListUtils.isEmpty(baGiftWall)){
            return vo;
        }
        vo.setList(giftWallInfo(giftWall));
        vo.setFsList(giftWallInfo(baGiftWall.stream().map(Tuple::getElement).collect(Collectors.toList())));
        if(ListUtils.isNotEmpty(baGiftWall)){
            chatConsumeComponent.saveUserLookBaGiftLastTime(GlobalUtils.uid(), baGiftWall.get(0).getScore());
        }
        return vo;
    }

    @BusiCode
    public ListVO queryGameHome(){
        List<GameItemVO> resultList = new ArrayList<>();
        for (GameType value : GameType.values()) {
            resultList.add(GameItemVO.build(value));
        }
        return ListVO.create(resultList);
    }

    @BusiCode
    public Object saveTest(){
        if(UnifiedConfig.isProdEnv()){
            throw new BusiException(ErrCodeType.InvalidInviteCode);
        }
        Date now = new Date();
        // List<String> strings = fateMatchComponent.maleMatchFemale(now, GlobalUtils.uid(), 1);
        // fateMatchComponent.maleMatchFinishDeal(now, GlobalUtils.uid(), strings);
        return "strings";
    }

    @BusiCode
    public ListVO searchUser(SearchUsersForm form){
        List<UUserBasic> basics = new ArrayList<>();
        if(BusiUtils.isAllNumber(form.getSearchStr())){
            // 用户ID搜索
            UUserBasic userBasic = userInfoManager.getUserBasic(Long.valueOf(form.getSearchStr()));
            if(Objects.nonNull(userBasic) && GlobalUtils.sexType() != userBasic.getSex()){
                basics.add(userBasic);
            }
        }else{
            // 用户昵称搜索（模糊）
            List<UserFeature> features = userFeatureRepository.searchMatchNickname(SexType.getTaSexType(GlobalUtils.sexType()), form.getSearchStr());
            features.forEach(item -> {
                basics.add(userInfoManager.getUserBasic(item.getId()));
            });
        }
        List<UserDTO> resultList = new ArrayList<>();
        basics.forEach(item -> {
            UserDTO dto = new UserDTO();
            dto.setUserId(item.getUserId());
            dto.setNickname(item.getNickname());
            dto.setHeadPic(item.getHeadPic());
            dto.setSex(item.getSex());
            dto.setAge(BusiUtils.getAgeByDate(item.getBirthDate()));
            dto.setVipType(BusiUtils.getVipType(item));
            dto.setRealPerson(item.getRealPerson());
            dto.setOnlineStatus(userInfoManager.getOnlineStatus(item.getUserId(), item.getSex()));
            resultList.add(dto);
        });
        return ListVO.create(resultList);
    }

    /**
     * 附近列表获取用户数据
     *
     * @param currUserId
     * @param currSexType
     * @param form
     * @return
     */
    private List<UserFeature> getNearByUserList(Long currUserId, SexType currSexType, NearbyQueryForm form) {
        if (BoolType.False == form.getHasPosition()) {
            return new ArrayList<>();
        }
        // 固定位置的用户，按照固定位置来搜索
        if (busiRedisTemplate.sismember(userInfoManager.buildFixLocationUserKey(), currUserId.toString())) {
            UUserLocation location = userInfoManager.getUserLocation(currUserId);
            // 修改请求参数
            form.setLat(String.valueOf(Double.parseDouble(location.getLat())));
            form.setLon(String.valueOf(Double.parseDouble(location.getLng())));
        }
        USocialWishConfig config = userSocialWishManager.getUserSearConfig(currUserId);
        return userFeatureRepository.searchNearby(form.getLat(), form.getLon(), form.getOffset(), form.getFetchLimit(), currUserId, currSexType, config);
    }

    /**
     * 加白男用户数据
     */
    private List<UserFeature> maleWhiteList() {
        return userFeatureRepository.searchMaleWhiteList();
    }

    /**
     * 在线列表男用户数据
     */
    private List<UserFeature> maleRecomList(PageForm form, USocialWishConfig config) {
        return userFeatureRepository.searchMaleRecom(form.getOffset(), form.getLimit(), config, getUserHomePageExcludeUsers(GlobalUtils.uid()));
    }

    /**
     * 新人列表男用户数据
     */
    private List<UserFeature> maleNewList(PageForm form, USocialWishConfig config) {
        return userFeatureRepository.searchMaleNew(form.getOffset(), form.getLimit(), config, getUserHomePageExcludeUsers(GlobalUtils.uid()));
    }

    /**
     * 附近列表男用户数据
     */
    private List<UserFeature> maleNearList(PageForm form, USocialWishConfig config) {
        // 如果隐藏了位置权限 就返回空集合
        if(messageSettingManager.hasHideLocation(GlobalUtils.uid())){
            return Collections.emptyList();
        }
        // 获取定位信息 备注：如果没有获取到定位信息 或者 经度维度有一个没有 就返回空合集
        UUserLocation location = userInfoManager.getUserLocation(GlobalUtils.uid());
        if(Objects.isNull(location) || StringUtils.isEmpty(location.getLat()) || StringUtils.isEmpty(location.getLng())){
            return Collections.emptyList();
        }
        return userFeatureRepository.searchMaleNear(form.getOffset(), form.getLimit(), config, getUserHomePageExcludeUsers(GlobalUtils.uid()), location.getLat(), location.getLng());
    }

    /**
     * 加白女用户数据
     */
    private List<UserFeature> femaleWhiteList() {
        return userFeatureRepository.searchFemaleWhiteList();
    }

    /**
     * 热门列表女用户数据
     */
    private List<UserFeature> femaleRecomList(PageForm form, USocialWishConfig config) {
        Set<Long> excludeUsers = getUserHomePageExcludeUsers(GlobalUtils.uid());
        //excludeUsers.addAll(messageSettingManager.queryVideoAnswerCloseFemales().stream().map(Long::valueOf).collect(Collectors.toList())); // 需要额外过滤掉 关闭视频接听权限的女用户
        return userFeatureRepository.searchFemaleRecom(form.getOffset(), form.getLimit(), config, excludeUsers);
    }

    /**
     * 新人列表女用户数据
     */
    private List<UserFeature> femaleNewList(PageForm form, USocialWishConfig config) {
        Set<Long> excludeUsers = getUserHomePageExcludeUsers(GlobalUtils.uid());
        //excludeUsers.addAll(messageSettingManager.queryVideoAnswerCloseFemales().stream().map(Long::valueOf).collect(Collectors.toList())); // 需要额外过滤掉 关闭视频接听权限的女用户
        return userFeatureRepository.searchFemaleNew(form.getOffset(), form.getLimit(), config, excludeUsers);
    }

    /**
     * 活跃列表女用户数据
     */
    private List<UserFeature> femaleActiveList(PageForm form, USocialWishConfig config) {
        return userFeatureRepository.searchFemaleActive(form.getOffset(), form.getLimit(), config, getUserHomePageExcludeUsers(GlobalUtils.uid()));
    }

    /**
     * 附近列表女用户数据
     */
    private List<UserFeature> femaleNearByList(PageForm form, USocialWishConfig config) {
        // 如果隐藏了位置权限 就返回空集合
        if(messageSettingManager.hasHideLocation(GlobalUtils.uid())){
            return Collections.emptyList();
        }
        // 获取定位信息 备注：如果没有获取到定位信息 或者 经度维度有一个没有 就返回空合集
        UUserLocation location = userInfoManager.getUserLocation(GlobalUtils.uid());
        if(Objects.isNull(location) || StringUtils.isEmpty(location.getLat()) || StringUtils.isEmpty(location.getLng())){
            return Collections.emptyList();
        }
        return userFeatureRepository.searchFemaleNearBy(form.getOffset(), form.getLimit(), config, getUserHomePageExcludeUsers(GlobalUtils.uid()), location.getLat(), location.getLng());
    }

    /**
     * 女神列表女用户数据
     */
    private List<UserFeature> femaleBeautyList(PageForm form, USocialWishConfig config) {
        Set<Long> excludeUsers = getUserHomePageExcludeUsers(GlobalUtils.uid());
        //excludeUsers.addAll(messageSettingManager.queryVideoAnswerCloseFemales().stream().map(Long::valueOf).collect(Collectors.toList())); // 需要额外过滤掉 关闭视频接听权限的女用户
        return userFeatureRepository.searchFemaleBeauty(form.getOffset(), form.getLimit(), config, excludeUsers);
    }

    /**
     * 获取用户首页数据排除用户
     */
    private Set<Long> getUserHomePageExcludeUsers(Long userId){
        return friendRelationManager.getUserLatelyBlackUsers(userId);
    }

    /**
     * 封装首页男用户信息
     *
     * @param userId
     * @param currUserId
     * @param currSexType
     * @return
     */
    private HomeMaleUserVO getMaleHomeUserInfo(Date now, Long userId, SexType sex, Long currUserId, SexType currSexType) {
        UUserBasic user = userInfoManager.getUserBasic(userId);
        Integer userLevel = settingManager.hasHideLevel(userId) ? null : userInfoManager.getUserLevel(userId).getUserLevel();
        UUserMore userMore = userInfoManager.getUserMore(userId);
        String friendNotes = friendRelationComponent.getFriendNotes(currUserId, userId);
        List<String> attrList = homeRecomComponent.getUserAttrList(user, userMore);
        BoolType chatUp = BoolType.valueOf(chatManager.chatUpDay(now, currUserId, userId));
        OnlineStatus onlineStatus = userInfoManager.getOnlineStatus(userId, sex);
        return HomeMaleUserVO.build1(user, userLevel, onlineStatus, chatUp, attrList, null, friendNotes);
    }
    private HomeMaleUserVO getMaleHomeUserInfo01(Date now, Long userId, SexType sex, Long currUserId, SexType currSexType) {
        UUserBasic user = userInfoManager.getUserBasic(userId);
        Integer userLevel = settingManager.hasHideLevel(userId) ? null : userInfoManager.getUserLevel(userId).getUserLevel();
        UUserMore userMore = userInfoManager.getUserMore(userId);
        UUserLocation location = userInfoManager.getUserLocation(userId);
        String friendNotes = friendRelationComponent.getFriendNotes(currUserId, userId);
        List<String> attrList = homeRecomComponent.getNearUserAttrList(user, userMore, location);
        BoolType chatUp = BoolType.valueOf(chatManager.chatUpDay(now, currUserId, userId));
        OnlineStatus onlineStatus = userInfoManager.getOnlineStatus(userId, sex);
        return HomeMaleUserVO.build1(user, userLevel, onlineStatus, chatUp, attrList, null, friendNotes);
    }

    /**
     * 封装首页女用户信息
     */
    private HomeFemaleUserVO getFemaleHomeUserInfo01(Date now, Long userId, SexType sex, Long currUserId, SexType currSexType) {
        UUserBasic user = userInfoManager.getUserBasic(userId);
        UUserMore more = userInfoManager.getUserMore(userId);
        UMessageSetting setting = settingManager.getSetting(userId);
        String friendNotes = friendRelationComponent.getFriendNotes(currUserId, userId);
        OnlineStatus onlineStatus = userInfoManager.getOnlineStatus(userId, sex);
        BoolType chatUp = BoolType.valueOf(chatManager.chatUpDay(now, currUserId, userId));
        List<String> attrList = homeRecomComponent.getUserAttrList(user, more);
        return HomeFemaleUserVO.build1(user, null, onlineStatus, friendNotes, setting.getVideoFee(), null, chatUp, attrList);
    }
    private HomeFemaleUserVO getFemaleHomeUserInfo02(Date now, Long userId, SexType sex, Long currUserId, SexType currSexType) {
        UUserBasic user = userInfoManager.getUserBasic(userId);
        UUserMore more = userInfoManager.getUserMore(userId);
        UUserLocation location = userInfoManager.getUserLocation(userId);
        UMessageSetting setting = settingManager.getSetting(userId);
        String friendNotes = friendRelationComponent.getFriendNotes(currUserId, userId);
        OnlineStatus onlineStatus = userInfoManager.getOnlineStatus(userId, sex);
        BoolType chatUp = BoolType.valueOf(chatManager.chatUpDay(now, currUserId, userId));
        List<String> attrList = homeRecomComponent.getNearUserAttrList(user, more, location);
        return HomeFemaleUserVO.build1(user, null, onlineStatus, friendNotes, setting.getVideoFee(), null, chatUp, attrList);
    }
    private HomeFemaleUserVO getFemaleHomeUserInfo03(Date now, Long userId, SexType sex, Long currUserId, SexType currSexType) {
        UUserBasic user = userInfoManager.getUserBasic(userId);
        UUserMore more = userInfoManager.getUserMore(userId);
        String cover = userInfoManager.getUserCover(userId);
        UMessageSetting setting = settingManager.getSetting(userId);
        String friendNotes = friendRelationComponent.getFriendNotes(currUserId, userId);
        OnlineStatus onlineStatus = userInfoManager.getOnlineStatus(userId, sex);
        BoolType chatUp = BoolType.valueOf(chatManager.chatUpDay(now, currUserId, userId));
        List<UUserMedia> picMedia = userInfoManager.getUserMedias(userId, MediaType.CommonPic);
        List<String> picStr = ListUtils.isEmpty(picMedia) ? null : picMedia.stream().map(UUserMedia::getMediaValue).collect(Collectors.toList());
        String mySign = StringUtils.isNotEmpty(user.getMySign()) ? user.getMySign() : user.getDefaultSign();
        List<String> attrList = new ArrayList<>();
        if(messageSettingManager.hasHideLocation(userId)){
            attrList = homeRecomComponent.getUserAttrList(user, more);
        }else{
            UUserLocation meLocation = userInfoManager.getUserLocation(currUserId);
            UUserLocation herLocation = userInfoManager.getUserLocation(userId);
            if(Objects.nonNull(meLocation) && Objects.nonNull(herLocation)){
                Double km = BusiUtils.getDistanceKm(meLocation.getLat(), meLocation.getLng(), herLocation.getLat(), herLocation.getLng());
                if(Objects.nonNull(km)){
                    attrList.add(MsgUtils.format("相距{}km", km));
                }
            }
            if(Objects.nonNull(herLocation)){
                attrList.add(MsgUtils.format("{}·{}", herLocation.getProvince(), herLocation.getCity()));
            }
        }
        return HomeFemaleUserVO.build2(user, cover, onlineStatus, friendNotes, setting.getVideoFee(), null, chatUp, picStr, mySign, attrList);
    }

    private List<GiftTvWallDTO> giftWallInfo(List<String> giftWallInfo) {
        if(ListUtils.isEmpty(giftWallInfo)){
            return Collections.emptyList();
        }
        List<GiftTvWallDTO> resultList = new ArrayList<>();
        giftWallInfo.forEach(item -> {
            try {
                GiftTvWallDTO dto = JsonUtils.deserializeAsObject(item, GiftTvWallDTO.class);
                UMessageSetting userSetting = settingManager.getSetting(dto.getUserId()); // 送礼方
                UMessageSetting friendSetting = settingManager.getSetting(dto.getFriendId()); // 收礼方
                boolean userIsAnonymous = false;
                boolean friendIsAnonymous = false;

                String userNickname = "匿名用户";
                String userHeadPic = GlobalConstant.GIFT_WALL_SEND_HEAD_PIC;
                if(!userIsAnonymous){
                    UUserBasic userBasic = userInfoManager.getUserBasic(dto.getUserId());
                    userNickname = userBasic.getNickname();
                    userHeadPic = userBasic.getHeadPic();
                }

                String friendNickname = "匿名用户";
                String friendHeadPic = GlobalConstant.GIFT_WALL_RECEIVE_HEAD_PIC;
                if(!friendIsAnonymous){
                    UUserBasic friendBasic = userInfoManager.getUserBasic(dto.getFriendId());
                    friendNickname = friendBasic.getNickname();
                    friendHeadPic = friendBasic.getHeadPic();
                }

                TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(dto.getGiftId()));
                dto.setGiftName(gift.getGiftName());
                dto.setGiftPic(gift.getPic());
                dto.setUserNickname(userNickname);
                dto.setUserHeadPic(userHeadPic);
                dto.setFriendNickname(friendNickname);
                dto.setFriendHeadPic(friendHeadPic);

                resultList.add(dto);
            }catch (Exception e){
                LOG.error("HomeService-giftWallInfo2-error e:", e);
                // do nothing
            }
        });
        return resultList;
    }

    private OnGiftWallVO buildOnGiftWallVO(Long userId, OnGiftWallType onGiftWallType){
        OnGiftWallVO vo = new OnGiftWallVO();
        vo.setOnGiftWallType(onGiftWallType);
        vo.setTips(onGiftWallType.getTips());

        vo.setUserNickname("匿名");
        vo.setUserHeadPic(GlobalConstant.GIFT_WALL_SEND_HEAD_PIC);

        vo.setFriendNickname("---");
        vo.setFriendHeadPic(GlobalConstant.GIFT_WALL_RECEIVE_HEAD_PIC);

        if(OnGiftWallType.Normal == onGiftWallType){
            UUserBasic userBasic = userInfoManager.getUserBasic(userId);
            vo.setUserNickname(userBasic.getNickname());
            vo.setUserHeadPic(userBasic.getHeadPic());
        }
        return vo;
    }
}
