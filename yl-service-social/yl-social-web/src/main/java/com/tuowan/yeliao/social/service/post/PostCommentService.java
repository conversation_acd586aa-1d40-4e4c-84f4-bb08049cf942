package com.tuowan.yeliao.social.service.post;

import com.easyooo.framework.common.util.ArrayUtils;
import com.easyooo.framework.common.util.ListUtils;
import com.easyooo.framework.common.util.MsgUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.config.configuration.impl.RongImConfig;
import com.tuowan.yeliao.commons.config.enums.ObjectLockType;
import com.tuowan.yeliao.commons.config.lock.ObjectLockTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.context.GlobalDataUtils;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.eventbus.EventBusPublisher;
import com.tuowan.yeliao.commons.eventbus.enums.EventBusType;
import com.tuowan.yeliao.commons.open.yidun.YidunSupport;
import com.tuowan.yeliao.commons.open.yidun.dto.YidunResultDTO;
import com.tuowan.yeliao.commons.open.yidun.enums.YidunResult;
import com.tuowan.yeliao.commons.open.yidun.enums.YidunReviewType;
import com.tuowan.yeliao.commons.web.common.vo.PageVO;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.commons.web.proxy.config.NotifyMode;
import com.tuowan.yeliao.social.comp.post.PostHeatComponent;
import com.tuowan.yeliao.social.data.entity.post.SPost;
import com.tuowan.yeliao.social.data.entity.post.SPostComment;
import com.tuowan.yeliao.social.data.entity.post.SPostCommentPraise;
import com.tuowan.yeliao.social.data.enums.post.FriendPostOptType;
import com.tuowan.yeliao.social.data.manager.post.PostCommentManager;
import com.tuowan.yeliao.social.data.manager.post.PostManger;
import com.tuowan.yeliao.social.web.form.post.CommentForm;
import com.tuowan.yeliao.social.web.form.post.CommentIdForm;
import com.tuowan.yeliao.social.web.form.post.CommentQueryForm;
import com.tuowan.yeliao.social.web.vo.post.CommentVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 动态评论业务封装
 *
 * <AUTHOR>
 * @date 2022/7/11 16:37
 */
@Service
public class PostCommentService {

    @Autowired
    private PostManger postManger;
    @Autowired
    private PostCommentManager postCommentManager;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private PostHeatComponent postHeatComponent;
    @Autowired
    private ObjectLockTemplate objectLockTemplate;
    @Autowired
    private EventBusPublisher eventBusPublisher;

    /**
     * 每天评论默认显示回复数据
     */
    private final Integer MAX_REPLY_NUM = 3;

    /**
     * 评论动态
     *
     * @param form
     * @return
     */
    @BusiCode(value = BusiCodeDefine.PostComment, forward = NotifyMode.DYNAMIC)
    public CommentVO saveComment(CommentForm form) {
        Long userId = GlobalUtils.uid();
        SPost post = postManger.getPostById(form.getPostId());
        if (post == null) {
            throw new BusiException("动态已被作者删除，无法评论！");
        }
        // 校验评论内容
        checkCommentContent(form.getPostId(), form.getCommentId(), userId, form.getContent());
        UUserBasic replyUser = null;
        Long parentCommentId = null;
        if (form.getCommentId() != null) {
            SPostComment comment = postCommentManager.getComment(form.getCommentId());
            if (comment == null) {
                throw new BusiException("内容已被作者删除，无法回复");
            }
            replyUser = userInfoManager.getUserBasic(comment.getUserId());
            parentCommentId = GlobalConstant.ROOT_ID.equals(comment.getParentId()) ? form.getCommentId() : comment.getParentId();
        }
        UUserBasic user = userInfoManager.getUserBasic(userId);
        UUserBasic postUser = userInfoManager.getUserBasic(post.getUserId());
        // 新增评论
        SPostComment comment = postCommentManager.addComment(userId, parentCommentId, replyUser, form.getPostId(), form.getContent());
        // 增加评论数
        postManger.updateCommentNum(form.getPostId(), 1);
        // 如果是1级评论还需要发送通知消息
        if(Objects.isNull(form.getCommentId())){
            // 消息发送校验
            if(!postManger.checkPostNoticeMsg(FriendPostOptType.Comment, userId, post.getUserId(), post.getPostId())){
                GlobalUtils.extValue(BusinessDataKey.BreakNotify, true);
            }else {
                setPostNoticeParams(userId, post, FriendPostOptType.Comment, form.getContent());
                // 记录此次操作已发送通知消息
                postManger.recordPostOptNotice(FriendPostOptType.Comment, userId, post.getPostId());
            }
        }
        // 事物之后发送事件总线 备注：性别不一样我们才发
        if(user.getSex() != postUser.getSex()){
            CallbackAfterTransactionUtil.send(() -> {
                eventBusPublisher.sendAsync(EventBusType.UserPostOpt, post.getUserId());
            });
        }

        return CommentVO.build(user, replyUser, comment, post.getUserId(), null, post.getRealPerson());
    }

    /**
     * 动态评论点赞
     */
    @BusiCode(BusiCodeDefine.CommentPraise)
    public void savePraiseComment(CommentIdForm form){
        Long userId = GlobalUtils.uid();
        objectLockTemplate.acquireTransactionLock(ObjectLockType.Common, MsgUtils.format("commentPraiseOpt:{}", userId));
        SPostComment postComment = postCommentManager.getPostComment(form.getCommentId());
        if(Objects.isNull(postComment)){
            throw new BusiException("当前评论被删除，无法点赞");
        }
        SPostCommentPraise commentPraise = postCommentManager.getCommentPraise(new SPostCommentPraise(form.getCommentId(), userId));
        if(Objects.nonNull(commentPraise)){
            return;
        }
        // 保存评论点赞消息
        SPostCommentPraise praise = new SPostCommentPraise(form.getCommentId(), userId);
        praise.setCreateTime(new Date(GlobalUtils.reqTime()));
        postCommentManager.saveCommentPraise(praise);
        // 增加动态评论点赞数
        SPostComment update = new SPostComment(form.getCommentId());
        update.setPraiseNum(1);
        postCommentManager.updatePostComment(update);
        postCommentManager.removeUserCommentPraiseCache(userId);
    }

    /**
     * 取消动态评论点赞
     */
    @BusiCode(BusiCodeDefine.CancelCommentPraise)
    public void saveCancelPraiseComment(CommentIdForm form){
        Long userId = GlobalUtils.uid();
        objectLockTemplate.acquireTransactionLock(ObjectLockType.Common, MsgUtils.format("commentPraiseOpt:{}", userId));
        // 删除动态评论点赞信息
        SPostCommentPraise praise = new SPostCommentPraise(form.getCommentId(), GlobalUtils.uid());
        postCommentManager.deleteCommentPraise(praise);
        // 修改动态评论点赞数
        SPostComment postComment = postCommentManager.getPostComment(form.getCommentId());
        if(Objects.nonNull(postComment)){
            SPostComment update = new SPostComment(form.getCommentId());
            update.setPraiseNum(-1);
            postCommentManager.updatePostComment(update);
        }
        postCommentManager.removeUserCommentPraiseCache(GlobalUtils.uid());
    }

    /**
     * 评论列表
     *
     * @param form
     * @return
     */
    @BusiCode
    public PageVO getCommentList(CommentQueryForm form) {
        SPost post = postManger.getPostById(form.getPostId());
        if (Objects.isNull(post)) {
           throw new BusiException("动态不存在或已被删除！");
        }
        List<SPostComment> commentList = postCommentManager.getCommentList(form.getPostId(), form.getParentCommentId(), form.getOffset(), form.getLimit());
        if (ListUtils.isEmpty(commentList)) {
            return new PageVO(Collections.EMPTY_LIST, form.getOffset(), form.getLimit());
        }
        // 获取用户点赞评论数据
        Set<String> commentPraiseData = postCommentManager.getUserCommentPraiseData(GlobalUtils.uid());
        List<CommentVO> resultList = new ArrayList<>();
        for (SPostComment comment : commentList) {
            UUserBasic user = GlobalDataUtils.userBasic(comment.getUserId());
            UUserBasic replyUser = null;
            List<CommentVO> replyList = null;
            if (Objects.equals(GlobalConstant.ROOT_ID, form.getParentCommentId())) {
                // 一级评论，加载3条回复
                replyList = getLatelyReplyList(comment, post, commentPraiseData);
            } else {
                // 二级评论
                replyUser = GlobalDataUtils.userBasic(comment.getReplyUserId());
            }
            CommentVO vo = CommentVO.build(user, replyUser, comment, post.getUserId(), BoolType.valueOf(commentPraiseData.contains(comment.getCommentId().toString())), post.getRealPerson());
            if (replyList != null) {
                vo.setReplyList(replyList);
            }
            resultList.add(vo);
        }
        return new PageVO(resultList, form.getOffset(), form.getLimit());
    }

    /**
     * 点赞评论发送系统消息
     *
     * @param userId
     * @param post
     * @param type
     */
    private void setPostNoticeParams(Long userId, SPost post, FriendPostOptType type, String comment) {
        UUserBasic basic = userInfoManager.getUserBasic(userId);
        Map<String, Object> params = new HashMap<>();
        params.put("nickname", basic.getNickname());
        params.put("headPic", basic.getHeadPic());
        // 动态信息
        params.put("postId", post.getPostId());
        // 塞一张图片
        if (StringUtils.isNotEmpty(post.getPics())) {
            List<String> picList = ArrayUtils.toList(post.getPics().split(","));
            params.put("pic", picList.get(0));
        } else if (StringUtils.isNotEmpty(post.getVideoCover())) {
            params.put("pic", post.getVideoCover());
        }
        params.put("content", post.getContent());
        params.put("createTime", System.currentTimeMillis());
        params.put("title", MsgUtils.format(type.getContent()));
        params.put("comment", comment);
        GlobalUtils.extValue(BusinessDataKey.CustomMap, params);
        GlobalUtils.extValue(BusinessDataKey.SenderId, RongImConfig.POST_NOTICE_SENDER);
        GlobalUtils.extValue(BusinessDataKey.ObjectName, RongImConfig.POST_NOTICE_OBJECT_NAME);
        GlobalUtils.extValue(BusinessDataKey.PushContent, MsgUtils.format("你有一条新的{}消息", type.getDesc()));
        GlobalUtils.extValue(BusinessDataKey.UserId, post.getUserId());
        GlobalUtils.extValue(BusinessDataKey.TargetUserId, userId); // 点击跳转的人
    }

    /**
     * 获取最近3条回复列表
     *
     * @param parent
     * @param post
     * @return
     */
    private List<CommentVO> getLatelyReplyList(SPostComment parent, SPost post, Set<String> commentPraiseData) {
        if (parent.getReplyNum() == 0) {
            return null;
        }
        List<SPostComment> commentList = postCommentManager.getCommentList(parent.getPostId(), parent.getCommentId(), 0, MAX_REPLY_NUM);
        if (ListUtils.isEmpty(commentList)) {
            return null;
        }
        List<CommentVO> replyList = new ArrayList<>();
        for (SPostComment comment : commentList) {
            UUserBasic user = GlobalDataUtils.userBasic(comment.getUserId());
            UUserBasic replyUser = GlobalDataUtils.userBasic(comment.getReplyUserId());
            replyList.add(CommentVO.build(user, replyUser, comment, post.getUserId(), BoolType.valueOf(commentPraiseData.contains(comment.getCommentId().toString())), post.getRealPerson()));
        }
        return replyList;
    }

    /**
     * 校验评论内容
     *
     * @param userId
     * @param content
     */
    private void checkCommentContent(Long postId, Long commentId, Long userId, String content) {
        // 字数校验
        if (content.length() > 200) {
            throw new BusiException("内容不能超过200个字符");
        }
        // 特殊字符过滤
        if (BusiUtils.isIllegalText(content)) {
            throw new BusiException(ErrCodeType.IllegalText);
        }
        // 易盾审核校验 备注：评论属于公开内容 嫌疑、拒绝 都不允许发送
        UUserBasic basic = userInfoManager.getUserBasic(userId);
        YidunResultDTO.CommonResult result = YidunSupport.syncSubmitText(YidunReviewType.CommentWords, basic, commentId, postId, content);
        if(result.getResult() == YidunResult.SubmitFail){
            // 易盾提交失败 直接抛出异常
            throw new BusiException("评论提交审核失败，请稍后重试！");
        }
        if(result.getResult() != YidunResult.Pass){
            throw new BusiException("评论内容涉嫌违规，请修改后重试！");
        }
    }
}
