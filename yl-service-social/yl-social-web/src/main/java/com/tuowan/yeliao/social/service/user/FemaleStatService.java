package com.tuowan.yeliao.social.service.user;

import com.easyooo.framework.common.util.DateUtils;
import com.tuowan.yeliao.acct.api.remote.AcctWebRemote;
import com.tuowan.yeliao.acct.api.request.UserRequest;
import com.tuowan.yeliao.acct.api.response.TotalCashResponse;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.http.Root;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.log.api.remote.LogWebRemote;
import com.tuowan.yeliao.log.api.request.FemaleIncomeRequest;
import com.tuowan.yeliao.log.api.response.FemaleIncomeVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class FemaleStatService {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private LogWebRemote logWebRemote;
    @Autowired
    private AcctWebRemote acctWebRemote;

    @BusiCode
    public FemaleIncomeVO queryData(){
        Long userId = GlobalUtils.uid();
        FemaleIncomeRequest request = new FemaleIncomeRequest();
        request.setStartTime(DateUtils.plusDays(DateUtils.trunc(new Date()), -7));
        request.setUserId(userId);
        Root<FemaleIncomeVO> femaleIncome = logWebRemote.getFemaleIncome(request);
        if(femaleIncome.isFailure()){
            throw new BusiException("当前网络较差，请重试！");
        }
        FemaleIncomeVO data = femaleIncome.getData();
        Root<TotalCashResponse> totalCash = acctWebRemote.getTotalCash(UserRequest.build1(userId));
        if (totalCash.isFailure()) {
            data.setTotalCash("暂无");
        } else if (null != totalCash.getData()){
            long totalCashNum = null == totalCash.getData().getTotalCash() ? 0L : totalCash.getData().getTotalCash();
            data.setTotalCash(BusiUtils.cashToYuanStr(totalCashNum));
        }
        return data;
    }


}
