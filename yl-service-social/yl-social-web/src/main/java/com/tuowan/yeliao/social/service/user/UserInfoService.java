package com.tuowan.yeliao.social.service.user;

import com.easyooo.framework.common.util.ListUtils;
import com.easyooo.framework.common.util.NumberUtils;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.comp.user.UserAuthComponent;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.context.request.UserIdRequest;
import com.tuowan.yeliao.commons.context.response.UserIdsResponse;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.dto.social.GiftDTO;
import com.tuowan.yeliao.commons.data.entity.user.UChatMaster;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.social.MatchType;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.manager.commons.SeqManager;
import com.tuowan.yeliao.commons.data.manager.config.NewsManager;
import com.tuowan.yeliao.commons.data.manager.user.ChatMasterManager;
import com.tuowan.yeliao.commons.data.manager.user.MessageSettingManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.mq.enums.MessageTag;
import com.tuowan.yeliao.commons.mq.producer.MessageQueueProducer;
import com.tuowan.yeliao.commons.web.common.form.TargetUserIdForm;
import com.tuowan.yeliao.commons.web.common.vo.ListVO;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.commons.web.proxy.config.RemoteInvoke;
import com.tuowan.yeliao.social.api.request.*;
import com.tuowan.yeliao.social.api.response.IntimateResponse;
import com.tuowan.yeliao.social.api.response.MatchFemaleResponse;
import com.tuowan.yeliao.social.api.response.MyInfoResponse;
import com.tuowan.yeliao.social.comp.chatmaster.ChatMasterComponent;
import com.tuowan.yeliao.social.comp.friend.*;
import com.tuowan.yeliao.social.comp.home.HomeRecomComponent;
import com.tuowan.yeliao.social.comp.home.VideoChatComponent;
import com.tuowan.yeliao.social.comp.log.LogComponent;
import com.tuowan.yeliao.social.data.config.FemaleLevelExchangeConfig;
import com.tuowan.yeliao.social.data.dto.friend.RelationDTO;
import com.tuowan.yeliao.social.data.entity.FChatNetCall;
import com.tuowan.yeliao.social.data.entity.FRelationTotal;
import com.tuowan.yeliao.social.data.enums.friend.NetCallFinishType;
import com.tuowan.yeliao.social.data.manager.chat.ChatRiskManager;
import com.tuowan.yeliao.social.data.manager.friend.ChatManager;
import com.tuowan.yeliao.social.data.manager.friend.FriendRelationManager;
import com.tuowan.yeliao.social.data.manager.friend.GiftWallManager;
import com.tuowan.yeliao.social.data.search.document.UserFeature;
import com.tuowan.yeliao.social.data.search.document.VideoChatFeature;
import com.tuowan.yeliao.social.data.search.repository.UserFeatureRepository;
import com.tuowan.yeliao.social.web.form.user.info.UpdateMasterExtScoreForm;
import com.tuowan.yeliao.social.web.form.user.info.UpdateMasterLevelForm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户在线状态封装
 *
 * <AUTHOR>
 * @date 2022/4/21 15:56
 */
@Service
public class UserInfoService {

    @Autowired
    private NetCallComponent netCallComponent;
    @Autowired
    private HomeRecomComponent homeRecomComponent;
    @Autowired
    private NetCallMatchComponent netCallMatchComponent;
    @Autowired
    private IntimateComponent intimateComponent;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private ChatMasterManager chatMasterManager;
    @Autowired
    private ChatManager chatManager;
    @Autowired
    private ChatRiskManager chatRiskManager;
    @Autowired
    private MessageSettingManager messageSettingManager;
    @Autowired
    private UserAuthComponent userAuthComponent;
    @Autowired
    private MessageQueueProducer messageQueueProducer;
    @Autowired
    private UserFeatureRepository userFeatureRepository;
    @Autowired
    private NewsManager newsManager;
    @Autowired
    private FateMatchUserComponent fateMatchUserComponent;
    @Autowired
    private FateMatchComponent fateMatchComponent;
    @Autowired
    private VisitComponent visitComponent;
    @Autowired
    private ChatMasterComponent chatMasterComponent;
    @Autowired
    private VideoChatComponent videoChatComponent;
    @Autowired
    private UserInviteComponent userInviteComponent;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private SeqManager seqManager;
    @Autowired
    private LogComponent logComponent;
    @Autowired
    private FriendRelationManager friendRelationManager;
    @Autowired
    private GiftWallManager giftWallManager;

    private final Logger LOG = LoggerFactory.getLogger(getClass());

    /**
     * 解除用户商店审核推荐封禁
     */
    @RemoteInvoke
    public void saveReleaseStoreAuditShield(UserIdRequest request){
        // 男用户加入匹配队列、女用户加入等级队列
        fateMatchComponent.addSysMsgMatchQueue(request.getUserId());
        // 刷新用户推荐列表分值
        homeRecomComponent.refreshUserScore(request.getUserId());
    }

    /**
     * 好友亲密度信息
     */
    @RemoteInvoke
    public IntimateResponse queryIntimate(Long userId, Long friendId){
        RelationDTO relation = friendRelationManager.getRelation(userId, friendId);
        return IntimateResponse.build(NumberUtils.toLong(relation.getGiftIntimateNum()));
    }

    /**
     * 获取《我的页面》的关注数、粉丝数、好友数
     */
    @RemoteInvoke
    public MyInfoResponse getMyInfoData(Long userId){
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        FRelationTotal relationTotal = friendRelationManager.getRelationTotal(userId);
        Integer exchangeLevel = null;
        if(SexType.Female == userBasic.getSex()){
            FemaleLevelExchangeConfig.Detail exchangeNumLevelCgf = chatMasterComponent.getFemaleExchangeNumLevelCgf(userId, new Date());
            exchangeLevel = exchangeNumLevelCgf.level;
        }
        if(Objects.isNull(relationTotal)){
            return MyInfoResponse.build(0, 0, 0, exchangeLevel);
        }
        return MyInfoResponse.build(relationTotal.getFollowCnt(), relationTotal.getFansCnt(), relationTotal.getFriendCnt(), exchangeLevel);
    }

    @RemoteInvoke
    public void saveUserOnline(boolean isFirstAlive) {
        Long userId = GlobalUtils.uid();
        SexType sex = GlobalUtils.sexType();
        boolean isAndroidReviewVersion = newsManager.isAndroidReviewVersion(GlobalUtils.clientType(), GlobalUtils.clientVersion(), GlobalUtils.channelId(), GlobalUtils.packageType(), userId);
        // 如果是审核模式 直接返回不做任何处理
        if (isAndroidReviewVersion) {
            return;
        }
        // 维持音视频通话用户心跳
        netCallComponent.processUserAlive(userId);
        if (isFirstAlive) {
            // 男用户加入匹配队列、女用户加入等级队列
            fateMatchComponent.addSysMsgMatchQueue(userId);
            // 刷新用户推荐列表分值
            homeRecomComponent.refreshUserScore(userId);
            // 男用户加入视频缘分队列
            // fateMatchComponent.addVideoFateQueue(userId, sex);
        }
    }

    @RemoteInvoke
    public void saveUserOffline(Long userId) {
        UUserBasic user = userInfoManager.getUserBasic(userId);
        // 男用户从匹配队列中移除、女用户从等级队列中移除
        fateMatchComponent.removeMatchQueue(user);
        // 刷新用户列表推荐分
        homeRecomComponent.refreshUserScore(userId, true);
    }

    @RemoteInvoke
    public void saveUserLocationChange(UserLocationChangeRequest request){
        UserFeature userFeature = new UserFeature(request.getUserId());
        userFeature.setLoc(BusiUtils.buildUserLocForEs(request.getLng(), request.getLat()));
        userFeatureRepository.updateSelective(userFeature);
    }

    @RemoteInvoke
    public void saveUserBirthDayChange(UserBirthDayChangeRequest request){
        UserFeature userFeature = new UserFeature(request.getUserId());
        userFeature.setBirth(request.getBirthDay().getTime());
        userFeatureRepository.updateSelective(userFeature);
    }

    @RemoteInvoke
    public void saveUserNicknameChange(UserNicknameChangeRequest request){
        UserFeature userFeature = new UserFeature(request.getUserId());
        userFeature.setData4(request.getNickname());
        userFeatureRepository.updateSelective(userFeature);
    }

    /**
     * 礼物墙数据
     */
    @BusiCode
    public ListVO queryGiftWall(TargetUserIdForm form){
        List<GiftDTO> giftDTOS = giftWallManager.listUserGiftWall(form.getTargetUserId());
        return ListVO.create(giftDTOS);
    }

    /**
     * 修改聊主等级
     *
     * @param form
     */
    @RemoteInvoke
    @BusiCode(BusiCodeDefine.UpdateMasterLevel)
    public void updateMasterLevel(UpdateMasterLevelForm form) {
        Long targetUserId = form.getUserId();
        // 聊主校验
        UChatMaster chatMaster = chatMasterManager.getChatMaster(targetUserId);
        if (chatMaster == null) {
            throw new BusiException("聊主信息不存在！");
        }
        // 新旧等级没有变化 直接返回
        if (chatMaster.getChatMasterLevel() == form.getNewChatMasterLevel()) {
            return;
        }
        chatMasterManager.updateMasterLevel(chatMaster, form.getNewChatMasterLevel(), form.getRemark(), GlobalUtils.busiCode(), GlobalUtils.uid());
        // 聊主等级变更，刷新首页推荐用户
        CallbackAfterTransactionUtil.send(() -> {
            messageQueueProducer.sendAsync(BusiCodeDefine.RefreshHomeUser, MessageTag.SocialBusi, new Long[]{targetUserId});
        });
    }

    /**
     * 修改聊主额外分数
     *
     * @param form
     */
    @RemoteInvoke
    @BusiCode
    public void updateMasterExtScore(UpdateMasterExtScoreForm form) {
        Long targetUserId = form.getUserId();
        // 聊主校验
        UChatMaster chatMaster = chatMasterManager.getChatMaster(targetUserId);
        if (chatMaster == null) {
            throw new BusiException("聊主信息不存在！");
        }
        UChatMaster update = new UChatMaster(targetUserId);
        update.setExtScore(form.getExtScore());
        chatMasterManager.updateChatMaster(update);
        // 聊主额外分数变更，刷新首页推荐用户
        CallbackAfterTransactionUtil.send(() -> {
            messageQueueProducer.sendAsync(BusiCodeDefine.RefreshHomeUser, MessageTag.SocialBusi, new Long[]{targetUserId});
        });
    }

    /**
     * 封禁通话
     */
    @RemoteInvoke
    public void saveBanCall(BanCallRequest request){
        // 从视频匹配列表中移除
        netCallMatchComponent.removeMatchQueue(userInfoManager.getUserBasic(request.getTargetUserId()));
        if (null == request.getCallId()) {
            return;
        }
        FChatNetCall netCall = netCallComponent.getChatNetCall(request.getCallId());
        if (null == netCall || netCall.getFinishTime() != null) {
            return;
        }
        // 如果通话涉黄，加入强制结束队列
        netCallComponent.putToDisconnectQueue(request.getCallId(), NetCallFinishType.Artificial);
    }

    /**
     * 封禁用户
     */
    @RemoteInvoke
    public void saveBanUser(BanUserRequest request){
        removeUserInfo(request.getUserId(), "封禁用户账号");
    }

    /**
     * 分解设备
     *
     * @param request
     */
    @RemoteInvoke
    public void saveBanDevice(BanDeviceRequest request) {
        for (Long userId : request.getUserIds()) {
            removeUserInfo(userId, "封禁用户设备");
        }
    }

    @RemoteInvoke
    public void saveInviteOpt(InviteBindRequest request){
        userInviteComponent.inviteOpt(request.getInviterId(), request.getUserId(), request.getInviteBindType());
    }

    @RemoteInvoke
    public void saveReleaseInviteOpt(InviteBindRequest request){
        userInviteComponent.releaseInviteOpt(request.getInviterId(), request.getUserId());
    }

    @RemoteInvoke
    public void deleteVideoChatData(VideoUpdateRequest request){
        VideoChatFeature feature = new VideoChatFeature();
        feature.setId(request.getUserId());
        videoChatComponent.deleteVideoChatData(feature);
    }

    @RemoteInvoke
    public UserIdsResponse queryHqNoticeFemales(Long userId){
        List<UserFeature> userFeatures = userFeatureRepository.searchHqNoticeFemale();
        return UserIdsResponse.build(userFeatures.stream().map(UserFeature::getId).collect(Collectors.toList()));
    }


    private void removeUserInfo(Long userId, String remark) {
        // 删除嘉宾等级统计数据，并将等级改成D
        // chatMasterComponent.clearStatInfo(userId, remark);
        // 禁止被搜索到，包括附近的人和喜欢列表
        userFeatureRepository.delete(userId);
        // 从视频匹配列表中移除
        // netCallMatchComponent.removeMatchQueue(userInfoManager.getUserBasic(userId));
        // 拒绝W状态的提现
        // CashServiceInvoke.revokeWithdraw(targetUserId);
        // 推荐视频队列移除
        // videoChatComponent.removeFromRecomQueu(userId);
        // 如果用户在语音房的麦上，直接下麦
        /*FFamilyAudioLiveMic mic = familyLiveManager.saveOffMic(targetUserId, AudioLiveMicEndType.Ban, GlobalUtils.uid());
        if (Objects.nonNull(mic)) {
            Map<BusinessDataKey, Object> paramMap = new HashMap<>();
            paramMap.put(BusinessDataKey.UserId, targetUserId);
            paramMap.put(BusinessDataKey.RoomIds, new Long[]{mic.getFamilyId().longValue()});
            messageComponent.sendMsgToRoomInContext(BusiCodeDefine.AudioLiveOffMic, paramMap, mic.getFamilyId().longValue());
        }*/
    }
}
