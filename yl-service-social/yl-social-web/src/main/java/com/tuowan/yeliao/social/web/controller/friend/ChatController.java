package com.tuowan.yeliao.social.web.controller.friend;

import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.ListUtils;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.context.config.HeaderType;
import com.tuowan.yeliao.commons.context.config.Request;
import com.tuowan.yeliao.commons.context.config.SessionType;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.WebException;
import com.tuowan.yeliao.commons.core.http.ReturnUtils;
import com.tuowan.yeliao.commons.core.http.Root;
import com.tuowan.yeliao.commons.data.enums.config.ProdType;
import com.tuowan.yeliao.commons.data.enums.social.ChatContentType;
import com.tuowan.yeliao.commons.data.enums.user.OnlineStatus;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.web.common.form.FriendIdForm;
import com.tuowan.yeliao.commons.web.common.util.WebUtils;
import com.tuowan.yeliao.commons.web.common.vo.ListVO;
import com.tuowan.yeliao.social.data.enums.friend.ChatSourceType;
import com.tuowan.yeliao.social.service.friend.ChatService;
import com.tuowan.yeliao.social.web.controller.user.ChatUpForm;
import com.tuowan.yeliao.social.web.form.friend.*;
import com.tuowan.yeliao.social.web.form.friend.chat.*;
import com.tuowan.yeliao.social.web.form.user.UserChatInfoForm;
import com.tuowan.yeliao.social.web.vo.family.FamilyUserCardVO;
import com.tuowan.yeliao.social.web.vo.friend.chat.*;
import com.tuowan.yeliao.social.web.vo.user.UserChatInfoVO;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @title 好友聊天管理
 * @date 2020/7/2 13:40
 */
@RestController
@RequestMapping("/soc/friend/chat")
public class ChatController {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ChatService chatService;

    /**
     * @title 消息首页信息
     */
    @Request
    @RequestMapping("/home")
    public Root<ChatHomeInfoVO> home() {
        return ReturnUtils.root(chatService.saveGetHomeInfo());
    }

    /**
     * @title 获取用户状态
     */
    @Request
    @RequestMapping("/status")
    public Root<Map<String, OnlineStatus>> status(@RequestBody QueryStatusForm form) {
        return ReturnUtils.root(chatService.getOnlineStatus(form));
    }

    /**
     * @title 修改搭讪状态
     */
    @Request
    @RequestMapping("/chatUpStatus")
    public Root<Void> chatUpStatus(@RequestBody ChatUpStatusUpdateForm form) {
        chatService.updateChatUpStatus(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 初始化私信页面信息
     */
    @Request
    @RequestMapping("/basic")
    public Root<ChatRoomVO> basic(@RequestBody ChatBasicForm form) {
        return ReturnUtils.root(chatService.saveBasicInfo(form));
    }

    /**
     * @title 获取用户卡片
     */
    @Request
    @RequestMapping("/userCard")
    public Root<FamilyUserCardVO> userCard(@RequestBody FriendIdForm form) {
        return ReturnUtils.root(chatService.getUserCard(form));
    }

    /**
     * @title 获取亲密度信息
     */
    @Request
    @RequestMapping("/intimate")
    public Root<IntimateVO> intimate(@RequestBody FriendIdForm form) {
        return ReturnUtils.root(chatService.findIntimateInfo(form));
    }

    /**
     * 用户会话信息
     */
    @Request
    @RequestMapping("/userInfo")
    public Root<ChatUserVO> userInfo(@RequestBody FriendIdForm form) {
        return ReturnUtils.root(chatService.getUserInfo(form));
    }

    /**
     * 【批量】
     * 用户会话信息
     */
    @Request
    @RequestMapping("/userInfos")
    public Root<ListVO> userInfos(@RequestBody UserInfosForm form) {
        return ReturnUtils.root(chatService.getUserInfos(form));
    }

    /**
     * @title 好友聊天礼物列表
     */
    @Request
    @RequestMapping("/giftsInfo")
    public Root<ChatQueryGiftVO> giftsInfo(@RequestBody GiftQueryForm form) {
        return ReturnUtils.root(chatService.findGiftsInfo(form));
    }

    /**
     * 接口调用：<br/>
     * 1、用户首次点击背包，<br/>
     * 2、背包新增物品时（从无到有新增，不包含数量变化，会有消息推送小红点提示）<br/>
     *
     * @title 用户背包列表
     */
    @Request
    @RequestMapping("/bag")
    public Root<List<ChatGiftVO>> bag(@RequestBody BagForm form) {
        return ReturnUtils.root(chatService.listUserBag(form));
    }

    /**
     * 语音通话结束之后需要刷新
     *
     * @title 刷新私信道具列表
     */
    @Deprecated
    @Request
    @RequestMapping("/propList")
    public Root<ChatRoomVO> propList() {
        return ReturnUtils.root(chatService.findChatPropList());
    }

    /**
     * @title 送出私聊礼物
     */
    @Request
    @RequestMapping("/sendGift")
    public Root<ChatSendGiftVO> sendGift(@RequestBody ChatSendGiftForm form) {
        if (ProdType.Goods == form.getProdType()) {
            throw new WebException("物品不支持赠送！");
        }
        return ReturnUtils.root(chatService.saveSendGift(form));
    }

    /**
     * @title 索要礼物
     */
    @Request
    @RequestMapping("/rqGift")
    public Root<RqGiftVO> rqGift(@RequestBody RqGiftForm form) {
        return ReturnUtils.root(chatService.saveRqGift(form));
    }

    /**
     * @title 发送私聊消息
     */
    @Request
    @RequestMapping("/sendMsg")
    public Root<ChatConsumeVO> sendMsg(@RequestBody ChatSendMsgForm form) {
        if (BusiUtils.isIllegalText(form.getContent())) {
            throw new BusiException(ErrCodeType.IllegalText);
        }
        form.setContentType(ChatContentType.Text);
        return ReturnUtils.root(chatService.saveSendMsg(form));
    }

    /**
     * @title 发送私聊图片
     */
    @Request(containExternalInvoke = true)
    @RequestMapping("/sendPic")
    public Root<ChatConsumeVO> sendPic(@RequestBody ChatSendMsgForm form) {
        form.setContentType(ChatContentType.Pic);
        return ReturnUtils.root(chatService.saveSendMsg(form));
    }

    /**
     * @title 发送付费图片
     */
    @Request
    @RequestMapping("/sendChargePic")
    public Root<ChatConsumeVO> sendChargePic(@RequestBody ChatSendMsgForm form) {
        form.setContentType(ChatContentType.ChargePic);
        return ReturnUtils.root(chatService.saveSendMsg(form));
    }

    /**
     * @title 发送付费视频
     */
    @Request
    @RequestMapping("/sendChargeVideo")
    public Root<ChatConsumeVO> sendChargeVideo(@RequestBody ChatSendMsgForm form) {
        form.setContentType(ChatContentType.ChargeVideo);
        return ReturnUtils.root(chatService.saveSendMsg(form));
    }

    /**
     * @title 发送位置
     */
    @Request(containExternalInvoke = true)
    @RequestMapping("/sendLocation")
    public Root<ChatConsumeVO> sendLocation(@RequestBody ChatSendMsgForm form) {
        form.setContentType(ChatContentType.Location);
        return ReturnUtils.root(chatService.saveSendMsg(form));
    }

    /**
     * @title 发送私聊语音消息
     */
    @Request
    @RequestMapping("/sendVoice")
    public Root<ChatConsumeVO> sendVoice(@RequestBody ChatSendVoiceForm form) {
        return ReturnUtils.root(chatService.saveSendMsg(ChatSendMsgForm.createVoiceMsg(form)));
    }

    /**
     * 发送内容content 只能是 1,2,3,4,5,6 这几个结果
     *
     * @title 发送摇塞子表情
     */
    @Request
    @RequestMapping("/sendDice")
    public Root<ChatConsumeVO> sendDice(@RequestBody ChatSendMsgForm form) {
        form.setContentType(ChatContentType.DiceGame);
        return ReturnUtils.root(chatService.saveSendMsg(form));
    }

    /**
     * 发送内容只能是 rock（石头）,paper（布）,scissors（剪刀） 这几个选项
     *
     * @title 发送石头剪刀布表情
     */
    @Request
    @RequestMapping("/sendFinger")
    public Root<ChatConsumeVO> sendFinger(@RequestBody ChatSendMsgForm form) {
        form.setContentType(ChatContentType.FingerGame);
        return ReturnUtils.root(chatService.saveSendMsg(form));
    }

    /**
     * @title 开通/取代/继续守护
     */
    @Request
    @RequestMapping("/openGuard")
    public Root<OpenGuardResultVO> openGuard(@RequestBody OpenGuardForm form) {
        if (form.getReplace() == null) {
            form.setReplace(BoolType.False);
        }
        return ReturnUtils.root(chatService.saveOpenGuard(form));
    }

    /**
     * 包括邀请音视频通话，实名认证等
     *
     * @title 发送邀请
     */
    @Request
    @RequestMapping("/invite")
    public Root<ChatInviteVO> invite(@RequestBody ChatInviteForm form) {
        return ReturnUtils.root(chatService.saveInviteChat(form));
    }

    /**
     * 后台给用户发消息
     *
     * @param form
     * @return
     */
    @Request(clients = ClientType.Robot, header = HeaderType.Whitelist)
    @RequestMapping("/sendBackupMsg")
    public Root<Void> sendBackupMsg(@RequestBody ChatSendBackupMsgForm form) {
        ChatConsumeVO vo = chatService.saveSendMsg(ChatSendMsgForm.create(form));
        form.setFee(vo.getConsumeBeans());
        form.setConsumeTicketCnt(vo.getConsumeChatTicket());
        chatService.saveSendBackupMsg(form);
        return ReturnUtils.empty();
    }

    /**
     * CMS后台模拟用户给自己发消息
     *
     * @param form
     * @return
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/cmsSendMsg")
    public Root<Void> cmsSendMsg(@RequestBody ChatSendBackupMsgForm form) {
        form.setUserId(form.getUserId());
        form.setConsumeTicketCnt(0);
        form.setContentType(ChatContentType.Text);
        form.setFee(0L);
        form.setContent(ListUtils.random(Arrays.asList("听说你找我，现在我来了", "又咋拉？", "大哥，找我啥事儿？", "[微笑]")));
        chatService.saveSendBackupMsg(form);
        return ReturnUtils.empty();
    }

    /**
     * 后台给用户送礼
     */
    @Request(clients = ClientType.Robot, header = HeaderType.Whitelist)
    @RequestMapping("/sendBackupGift")
    public Root<Void> sendBackupGift(@RequestBody ChatSendBackupGiftForm form) {
        chatService.saveSendGift(ChatSendGiftForm.create(form));
        chatService.saveSendBackupGiftMsg(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 获取好友聊天详情信息
     */
    @Request
    @RequestMapping("/detail")
    public Root<UserChatInfoVO> detail(@RequestBody UserChatInfoForm form) {
        return ReturnUtils.root(chatService.getDetail(form));
    }

    @Request(value = SessionType.None, header = HeaderType.None)
    @RequestMapping("/testSendGift")
    public Root<Void> testSendGift(@Param("userId") Long userId, @Param("friendId") Long friendId, @Param("giftId") Integer giftId) {
        ChatSendBackupGiftForm form = new ChatSendBackupGiftForm();
        form.setCount(1);
        form.setUserId(userId);
        form.setFriendId(friendId);
        form.setGiftId(giftId);
        form.setSourceType(ChatSourceType.Chat);
        chatService.saveSendGiftTest(form);
        return ReturnUtils.empty();
    }

    @Request(value = SessionType.None, header = HeaderType.None)
    @RequestMapping("/testFate")
    public Root<Void> testFate(@Param("userId") Long userId, @Param("friendId") Long friendId) {
        if (UnifiedConfig.isProdEnv()) {
            throw new WebException("非法请求");
        }
        FateTestForm form = new FateTestForm();
        form.setUserId(userId);
        form.setTargetUserId(friendId);
        form.setFamiliar("true".equalsIgnoreCase(WebUtils.getCurrentRequest().getParameter("start")));
        chatService.saveTestFate(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 发送一条官方后台私信消息(测试)
     */
    @Request
    @RequestMapping("/testChatMsg")
    public Root<Void> testChatMsg(@RequestBody ChatSendMsgForm form) {
        if (UnifiedConfig.isProdEnv()) {
            throw new WebException("非法请求");
        }
        chatService.saveSendTestMsg(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 模拟发送一条私信提醒
     */
    @Request(session = SessionType.None, header = HeaderType.None)
    @RequestMapping("/testChatTips")
    public Root<Void> testChatTips(@Param("userId") Long userId, @Param("friendId") Long friendId) {
        if (UnifiedConfig.isProdEnv()) {
            throw new WebException("非法请求");
        }
        FateTestForm form = new FateTestForm();
        form.setUserId(userId);
        form.setTargetUserId(friendId);
        chatService.saveSendTestTips(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 模拟发送一条缘分匹配
     */
    @Request(session = SessionType.None, header = HeaderType.None)
    @RequestMapping("/testChatMatch")
    public Root<Void> testChatTipsMatch(@Param("userId") Long userId, @Param("friendId") Long friendId) {
        if (UnifiedConfig.isProdEnv()) {
            throw new WebException("非法请求");
        }
        FateTestForm form = new FateTestForm();
        form.setUserId(userId);
        form.setTargetUserId(friendId);
        chatService.saveChatUpMatchTest(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 获取一键搭讪用户列表
     */
    @Request
    @RequestMapping("/cuUsers")
    public Root<ListVO> cuUsers() {
        return ReturnUtils.root(chatService.getCuUsers());
    }

    /**
     * @title 搭讪
     */
    @Request
    @RequestMapping("/chatUp")
    public Root<ChatConsumeVO> chatUp(@RequestBody ChatUpForm form) {
        ChatSendMsgForm msgForm = new ChatSendMsgForm();
        msgForm.setContentType(ChatContentType.Text);
        msgForm.setFriendId(form.getFriendId());
        msgForm.setContent("我对你心动了，一起聊聊呗");
        msgForm.setSourceType(ChatSourceType.ChatUp);
        msgForm.setVerifyText(false);
        if (BoolType.True == form.getTodayFateNow()) {
            msgForm.setSourceType(ChatSourceType.TodayFate);
        }
        return ReturnUtils.root(chatService.saveChatUp(msgForm));
    }

    /**
     * 扣除金币
     */
    @Request
    @RequestMapping("/deductBean")
    public Root<Void> deductBean(@RequestBody DeductBeanForm form) {
        chatService.saveDeductBean(form);
        return ReturnUtils.empty();
    }

    /**
     * 音频文件识别回调
     *
     * @return
     */
    @Request(value = SessionType.None, header = HeaderType.None)
    @RequestMapping("/callback")
    public Root<Void> callback(HttpServletRequest request) {
        try {
            String jsonString = WebUtils.convertStreamToString(request.getInputStream());
            LOG.info("数美音频回调结果：" + jsonString);
            ShumeiAudioCallbackForm form = JsonUtils.deserializeAsObject(jsonString, ShumeiAudioCallbackForm.class);
            chatService.saveAudioText(form);
        } catch (IOException e) {
            LOG.error("数美音频文件识别回调失败，原因：", e);
        }
        return ReturnUtils.empty();
    }

    /**
     * 空间口测试
     *
     * @return
     */
    @Request(value = SessionType.None, header = HeaderType.None)
    @RequestMapping("/emptyTest")
    public Root<Void> emptyTest(HttpServletRequest request) {
        return ReturnUtils.empty();
    }

    @Request(value = SessionType.None, header = HeaderType.None)
    @RequestMapping("/testChatup")
    public Root<Void> testChatup(@Param("userId") Long userId, @Param("friendId") Long friendId) {
        if (UnifiedConfig.isProdEnv()) {
            throw new WebException("非法请求");
        }
        FateTestForm form = new FateTestForm();
        form.setUserId(userId);
        form.setTargetUserId(friendId);
        chatService.saveTestChatup(form);
        return ReturnUtils.empty();
    }

    @Request(value = SessionType.None, header = HeaderType.None)
    @RequestMapping("/testChatUpStatus")
    public Root<Void> testChatupStatus(@Param("userId") Long userId) {
        if (UnifiedConfig.isProdEnv()) {
            throw new WebException("非法请求");
        }
        FateTestForm form = new FateTestForm();
        form.setUserId(userId);
        form.setTargetUserId(userId);
        chatService.saveTestCloseChatUpStatus(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 聊天消息回调
     */
    @Request(header = HeaderType.None, session = SessionType.None)
    @RequestMapping("/rongCallback")
    public ChatCallbackVO rongCallback(ChatCallbackForm form) {
        ChatCallbackVO vo = chatService.saveCallbackPlusNew(form);
        if (vo.getPass() == 0) {
            LOG.warn("私信回调验证失败, 回调内容：{}", JsonUtils.seriazileAsString(form));
        }
        return vo;
    }
}
