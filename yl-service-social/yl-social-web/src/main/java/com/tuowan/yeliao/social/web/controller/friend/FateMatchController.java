package com.tuowan.yeliao.social.web.controller.friend;

import com.tuowan.yeliao.commons.context.config.Request;
import com.tuowan.yeliao.commons.core.http.ReturnUtils;
import com.tuowan.yeliao.commons.core.http.Root;
import com.tuowan.yeliao.social.service.friend.FateMatchService;
import com.tuowan.yeliao.social.web.vo.friend.fate.FateMatchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 缘分速配控制器
 */
@RestController
@RequestMapping("/soc/friend/fate")
public class FateMatchController {

    @Autowired
    private FateMatchService fateMatchService;

    /**
     * @title 匹配用户
     */
    @Request
    @RequestMapping("/match")
    public Root<FateMatchVO> match() {
        return ReturnUtils.root(fateMatchService.match());
    }

}
