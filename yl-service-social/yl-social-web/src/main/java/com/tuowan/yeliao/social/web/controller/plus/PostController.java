package com.tuowan.yeliao.social.web.controller.plus;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tuowan.yeliao.social.entity.plus.Post;
import com.tuowan.yeliao.social.service.plus.PostService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 动态控制器（MyBatis-Plus 版本）
 * 提供动态相关的 RESTful API
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Api(tags = "动态管理")
@RestController
@RequestMapping("/api/v2/post")
@ConditionalOnProperty(name = "mybatis-plus.enabled", havingValue = "true", matchIfMissing = false)
public class PostController {

    @Autowired
    private PostService postService;

    // ==================== 基础 CRUD 操作 ====================

    /**
     * 创建动态
     */
    @ApiOperation("创建动态")
    @PostMapping("/create")
    public boolean createPost(@ApiParam("动态信息") @RequestBody Post post) {
        return postService.createPost(post);
    }

    /**
     * 根据ID获取动态
     */
    @ApiOperation("根据ID获取动态")
    @GetMapping("/{id}")
    public Post getPost(@ApiParam("动态ID") @PathVariable Long id) {
        return postService.getPostById(id);
    }

    /**
     * 根据业务ID获取动态
     */
    @ApiOperation("根据业务ID获取动态")
    @GetMapping("/postId/{postId}")
    public Post getPostByPostId(@ApiParam("业务ID") @PathVariable Long postId) {
        return postService.getPostByPostId(postId);
    }

    /**
     * 更新动态
     */
    @ApiOperation("更新动态")
    @PutMapping("/update")
    public boolean updatePost(@ApiParam("动态信息") @RequestBody Post post) {
        return postService.updatePost(post);
    }

    /**
     * 删除动态
     */
    @ApiOperation("删除动态")
    @DeleteMapping("/{id}")
    public boolean deletePost(@ApiParam("动态ID") @PathVariable Long id) {
        return postService.deletePost(id);
    }

    /**
     * 批量删除动态
     */
    @ApiOperation("批量删除动态")
    @DeleteMapping("/batch")
    public boolean batchDeletePosts(@ApiParam("动态ID列表") @RequestParam List<Long> ids) {
        return postService.batchDeletePosts(ids);
    }

    // ==================== 查询操作 ====================

    /**
     * 根据用户ID查询动态列表
     */
    @ApiOperation("根据用户ID查询动态列表")
    @GetMapping("/user/{userId}")
    public List<Post> getUserPosts(@ApiParam("用户ID") @PathVariable Long userId) {
        return postService.getPostsByUserId(userId);
    }

    /**
     * 根据用户ID和动态类型查询
     */
    @ApiOperation("根据用户ID和动态类型查询")
    @GetMapping("/user/{userId}/type/{postType}")
    public List<Post> getUserPostsByType(
            @ApiParam("用户ID") @PathVariable Long userId,
            @ApiParam("动态类型") @PathVariable Integer postType) {
        return postService.getPostsByUserIdAndType(userId, postType);
    }

    /**
     * 分页查询用户动态
     */
    @ApiOperation("分页查询用户动态")
    @GetMapping("/user/{userId}/page")
    public Page<Post> getUserPostPage(
            @ApiParam("用户ID") @PathVariable Long userId,
            @ApiParam("当前页") @RequestParam(defaultValue = "1") int current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int size) {
        return postService.getUserPostPage(userId, current, size);
    }

    /**
     * 分页查询用户指定类型动态
     */
    @ApiOperation("分页查询用户指定类型动态")
    @GetMapping("/user/{userId}/type/{postType}/page")
    public Page<Post> getUserPostPageByType(
            @ApiParam("用户ID") @PathVariable Long userId,
            @ApiParam("动态类型") @PathVariable Integer postType,
            @ApiParam("当前页") @RequestParam(defaultValue = "1") int current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int size) {
        return postService.getUserPostPageByType(userId, postType, current, size);
    }

    /**
     * 根据动态类型查询
     */
    @ApiOperation("根据动态类型查询")
    @GetMapping("/type/{postType}")
    public List<Post> getPostsByType(@ApiParam("动态类型") @PathVariable Integer postType) {
        return postService.getPostsByType(postType);
    }

    /**
     * 查询热门动态
     */
    @ApiOperation("查询热门动态")
    @GetMapping("/hot")
    public List<Post> getHotPosts(@ApiParam("数量限制") @RequestParam(defaultValue = "10") Integer limit) {
        return postService.getHotPosts(limit);
    }

    /**
     * 分页查询热门动态
     */
    @ApiOperation("分页查询热门动态")
    @GetMapping("/hot/page")
    public Page<Post> getHotPostPage(
            @ApiParam("当前页") @RequestParam(defaultValue = "1") int current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int size) {
        return postService.getHotPostPage(current, size);
    }

    /**
     * 查询最新动态
     */
    @ApiOperation("查询最新动态")
    @GetMapping("/latest")
    public List<Post> getLatestPosts(@ApiParam("数量限制") @RequestParam(defaultValue = "10") Integer limit) {
        return postService.getLatestPosts(limit);
    }

    /**
     * 搜索动态
     */
    @ApiOperation("搜索动态")
    @GetMapping("/search")
    public List<Post> searchPosts(@ApiParam("关键词") @RequestParam String keyword) {
        return postService.searchPosts(keyword);
    }

    /**
     * 分页搜索动态
     */
    @ApiOperation("分页搜索动态")
    @GetMapping("/search/page")
    public Page<Post> searchPostPage(
            @ApiParam("关键词") @RequestParam String keyword,
            @ApiParam("当前页") @RequestParam(defaultValue = "1") int current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int size) {
        return postService.searchPostPage(keyword, current, size);
    }

    /**
     * 查询待审核动态
     */
    @ApiOperation("查询待审核动态")
    @GetMapping("/pending")
    public List<Post> getPendingAuditPosts() {
        return postService.getPendingAuditPosts();
    }

    /**
     * 查询用户最近的动态
     */
    @ApiOperation("查询用户最近的动态")
    @GetMapping("/user/{userId}/recent")
    public List<Post> getUserRecentPosts(
            @ApiParam("用户ID") @PathVariable Long userId,
            @ApiParam("数量限制") @RequestParam(defaultValue = "5") Integer limit) {
        return postService.getUserRecentPosts(userId, limit);
    }

    // ==================== 统计操作 ====================

    /**
     * 统计用户动态数量
     */
    @ApiOperation("统计用户动态数量")
    @GetMapping("/user/{userId}/count")
    public Long countUserPosts(@ApiParam("用户ID") @PathVariable Long userId) {
        return postService.countUserPosts(userId);
    }

    /**
     * 统计用户各类型动态数量
     */
    @ApiOperation("统计用户各类型动态数量")
    @GetMapping("/user/{userId}/count/type")
    public List<Map<String, Object>> countUserPostsByType(@ApiParam("用户ID") @PathVariable Long userId) {
        return postService.countUserPostsByType(userId);
    }

    /**
     * 统计总动态数量
     */
    @ApiOperation("统计总动态数量")
    @GetMapping("/count/total")
    public Long countTotalPosts() {
        return postService.countTotalPosts();
    }

    // ==================== 互动操作 ====================

    /**
     * 点赞动态
     */
    @ApiOperation("点赞动态")
    @PostMapping("/{id}/praise")
    public boolean praisePost(@ApiParam("动态ID") @PathVariable Long id) {
        return postService.praisePost(id);
    }

    /**
     * 取消点赞
     */
    @ApiOperation("取消点赞")
    @DeleteMapping("/{id}/praise")
    public boolean unpraisePost(@ApiParam("动态ID") @PathVariable Long id) {
        return postService.unpraisePost(id);
    }

    /**
     * 增加评论数
     */
    @ApiOperation("增加评论数")
    @PostMapping("/{id}/comment")
    public boolean increaseCommentNum(@ApiParam("动态ID") @PathVariable Long id) {
        return postService.increaseCommentNum(id);
    }

    /**
     * 减少评论数
     */
    @ApiOperation("减少评论数")
    @DeleteMapping("/{id}/comment")
    public boolean decreaseCommentNum(@ApiParam("动态ID") @PathVariable Long id) {
        return postService.decreaseCommentNum(id);
    }

    /**
     * 分享动态
     */
    @ApiOperation("分享动态")
    @PostMapping("/{id}/share")
    public boolean sharePost(@ApiParam("动态ID") @PathVariable Long id) {
        return postService.sharePost(id);
    }

    // ==================== 状态管理 ====================

    /**
     * 审核通过
     */
    @ApiOperation("审核通过")
    @PutMapping("/{id}/approve")
    public boolean approvePost(@ApiParam("动态ID") @PathVariable Long id) {
        return postService.approvePost(id);
    }

    /**
     * 审核拒绝
     */
    @ApiOperation("审核拒绝")
    @PutMapping("/{id}/reject")
    public boolean rejectPost(@ApiParam("动态ID") @PathVariable Long id) {
        return postService.rejectPost(id);
    }

    /**
     * 启用动态
     */
    @ApiOperation("启用动态")
    @PutMapping("/{id}/enable")
    public boolean enablePost(@ApiParam("动态ID") @PathVariable Long id) {
        return postService.enablePost(id);
    }

    /**
     * 禁用动态
     */
    @ApiOperation("禁用动态")
    @PutMapping("/{id}/disable")
    public boolean disablePost(@ApiParam("动态ID") @PathVariable Long id) {
        return postService.disablePost(id);
    }

    /**
     * 批量更新状态
     */
    @ApiOperation("批量更新状态")
    @PutMapping("/batch/status")
    public boolean batchUpdateStatus(
            @ApiParam("动态ID列表") @RequestParam List<Long> postIds,
            @ApiParam("状态") @RequestParam Integer status) {
        return postService.batchUpdateStatus(postIds, status);
    }

    /**
     * 批量审核
     */
    @ApiOperation("批量审核")
    @PutMapping("/batch/audit")
    public boolean batchAudit(
            @ApiParam("动态ID列表") @RequestParam List<Long> postIds,
            @ApiParam("审核状态") @RequestParam Integer auditStatus) {
        return postService.batchAudit(postIds, auditStatus);
    }

    // ==================== 复杂查询 ====================

    /**
     * 查询用户热门动态
     */
    @ApiOperation("查询用户热门动态")
    @GetMapping("/user/{userId}/hot")
    public List<Post> getUserHotPosts(
            @ApiParam("用户ID") @PathVariable Long userId,
            @ApiParam("最小点赞数") @RequestParam(defaultValue = "10") Integer minPraise) {
        return postService.getUserHotPosts(userId, minPraise);
    }

    /**
     * 查询用户指定时间范围内的动态
     */
    @ApiOperation("查询用户指定时间范围内的动态")
    @GetMapping("/user/{userId}/dateRange")
    public List<Post> getUserPostsInDateRange(
            @ApiParam("用户ID") @PathVariable Long userId,
            @ApiParam("开始日期") @RequestParam String startDate,
            @ApiParam("结束日期") @RequestParam String endDate) {
        return postService.getUserPostsInDateRange(userId, startDate, endDate);
    }
}
