package com.tuowan.yeliao.social.web.controller.plus;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tuowan.yeliao.social.entity.plus.SPostPlus;
import com.tuowan.yeliao.social.service.plus.SPostPlusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 动态控制器（MyBatis-Plus 版本）
 * 演示新的 MyBatis-Plus 方案的使用
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@RequestMapping("/api/v2/post")
@ConditionalOnProperty(name = "mybatis-plus.enabled", havingValue = "true", matchIfMissing = false)
public class SPostPlusController {

    @Autowired
    private SPostPlusService postPlusService;

    /**
     * 创建动态
     */
    @PostMapping("/create")
    public boolean createPost(@RequestBody SPostPlus post) {
        return postPlusService.createPost(post);
    }

    /**
     * 根据ID获取动态
     */
    @GetMapping("/{id}")
    public SPostPlus getPost(@PathVariable Long id) {
        return postPlusService.getPostById(id);
    }

    /**
     * 更新动态
     */
    @PutMapping("/update")
    public boolean updatePost(@RequestBody SPostPlus post) {
        return postPlusService.updatePost(post);
    }

    /**
     * 删除动态
     */
    @DeleteMapping("/{id}")
    public boolean deletePost(@PathVariable Long id) {
        return postPlusService.deletePost(id);
    }

    /**
     * 根据用户ID查询动态列表
     */
    @GetMapping("/user/{userId}")
    public List<SPostPlus> getUserPosts(@PathVariable Long userId) {
        return postPlusService.getPostsByUserId(userId);
    }

    /**
     * 分页查询用户动态
     */
    @GetMapping("/user/{userId}/page")
    public Page<SPostPlus> getUserPostPage(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size) {
        return postPlusService.getUserPostPage(userId, current, size);
    }

    /**
     * 根据动态类型查询
     */
    @GetMapping("/type/{postType}")
    public List<SPostPlus> getPostsByType(@PathVariable Integer postType) {
        return postPlusService.getPostsByType(postType);
    }

    /**
     * 查询热门动态
     */
    @GetMapping("/hot")
    public List<SPostPlus> getHotPosts(@RequestParam(defaultValue = "10") Integer limit) {
        return postPlusService.getHotPosts(limit);
    }

    /**
     * 统计用户动态数量
     */
    @GetMapping("/user/{userId}/count")
    public Long countUserPosts(@PathVariable Long userId) {
        return postPlusService.countUserPosts(userId);
    }

    /**
     * 点赞动态
     */
    @PostMapping("/{id}/praise")
    public boolean praisePost(@PathVariable Long id) {
        return postPlusService.increasePraiseNum(id);
    }

    /**
     * 评论动态
     */
    @PostMapping("/{id}/comment")
    public boolean commentPost(@PathVariable Long id) {
        return postPlusService.increaseCommentNum(id);
    }

    /**
     * 批量更新状态
     */
    @PutMapping("/batch/status")
    public boolean batchUpdateStatus(
            @RequestParam List<Long> postIds,
            @RequestParam Integer status) {
        return postPlusService.batchUpdateStatus(postIds, status);
    }

    /**
     * 查询用户热门动态
     */
    @GetMapping("/user/{userId}/hot")
    public List<SPostPlus> getUserHotPosts(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "10") Integer minPraise) {
        return postPlusService.getUserHotPosts(userId, minPraise);
    }
}
