package com.tuowan.yeliao.social.web.controller.review;

import com.alibaba.fastjson.JSONObject;
import com.easyooo.framework.common.util.EnumUtils;
import com.easyooo.framework.common.util.JsonUtils;
import com.tuowan.yeliao.commons.context.config.HeaderType;
import com.tuowan.yeliao.commons.context.config.Request;
import com.tuowan.yeliao.commons.context.config.SessionType;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.open.yidun.YidunSupport;
import com.tuowan.yeliao.commons.open.yidun.enums.YidunReviewType;
import com.tuowan.yeliao.commons.web.common.util.WebUtils;
import com.tuowan.yeliao.social.service.cosmos.CosmosService;
import com.tuowan.yeliao.social.service.friend.QuickReplyService;
import com.tuowan.yeliao.social.service.post.PostService;
import com.tuowan.yeliao.social.service.user.ExclusiveChatUpService;
import com.tuowan.yeliao.social.service.videoChat.VideoChatService;
import com.tuowan.yeliao.social.web.form.review.YidunReviewForm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 社交内容审核管理
 *
 * <AUTHOR>
 * @date 2022/7/1 15:49
 */
@RestController
@RequestMapping("/soc/review")
public class SocialReviewController {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private QuickReplyService quickReplyService;
    @Autowired
    private PostService postService;
    @Autowired
    private CosmosService cosmosService;
    @Autowired
    private ExclusiveChatUpService exclusiveChatUpService;
    @Autowired
    private VideoChatService videoChatService;

    /**
     * 易盾文本审核结果回调
     *
     * @param request
     * @param response
     * @return
     */
    @Request(value = SessionType.None, header = HeaderType.None)
    @RequestMapping("/yidunText")
    public String yidunText(HttpServletRequest request, HttpServletResponse response) {
        JSONObject result = new JSONObject();
        result.put("code", 500);
        Map<String, String> params = WebUtils.getParameters(request);
        String callbackData = params.get("callbackData");
        LOG.info("callbackData -> {}", callbackData);
        JSONObject data = JSONObject.parseObject(callbackData);
        JSONObject antispam = data.getJSONObject("antispam");
        if (antispam == null) {
            LOG.error("易盾审核返回值错误，antispam为空");
            return WebUtils.writeJson(result, response);
        }
        // 结果类型，1：机器结果，2：人审结果，只处理人审结果
        int resultType = antispam.getIntValue("resultType");
        if (resultType != 2) {
            result.put("code", 200);
            return WebUtils.writeJson(result, response);
        }
        UrlParamsMap callback = UrlParamsMap.build(antispam.getString("callback"));
        YidunReviewType reviewType = EnumUtils.byName(callback.getString("t"), YidunReviewType.class);
        if (!YidunSupport.verifySignature(reviewType, params)) {
            LOG.warn("易盾审核签名验证失败，所有参数：{}", JsonUtils.seriazileAsString(params));
            return WebUtils.writeJson(result, response);
        }
        if (reviewType != null) {
            processTextReview(YidunReviewForm.buildText(reviewType, antispam));
            result.put("code", 200);
        } else {
            LOG.error("易盾审核类型未定义，原始数据：{}", callbackData);
        }
        return WebUtils.writeJson(result, response);
    }

    /**
     * 易盾融媒体审核结果回调
     *
     * @param request
     * @param response
     * @return
     */
//    @Request(value = SessionType.None, header = HeaderType.None)
//    @RequestMapping("/yidunMedia")
//    public String yidunMedia(HttpServletRequest request, HttpServletResponse response) {
//        JSONObject result = new JSONObject();
//        result.put("code", 500);
//        Map<String, String> params = WebUtils.getParameters(request);
//        String callbackData = params.get("callbackData");
//        LOG.info("callbackData -> {}", callbackData);
//        JSONObject data = JSONObject.parseObject(callbackData);
//        if (data.containsKey("antispam")) {
//            // 机审直接忽略
//            result.put("code", 200);
//            return WebUtils.writeJson(result, response);
//        }
//        JSONObject censor = data.getJSONObject("censor");
//        if (censor == null) {
//            LOG.error("易盾审核返回值错误，censor为空");
//            return WebUtils.writeJson(result, response);
//        }
//        // 结果类型，1：机器结果，2：人审结果，只处理人审结果
//        int resultType = censor.getIntValue("resultType");
//        if (resultType != 2) {
//            result.put("code", 200);
//            return WebUtils.writeJson(result, response);
//        }
//        UrlParamsMap callback = UrlParamsMap.build(censor.getString("callback"));
//        YidunReviewType reviewType = EnumUtils.byName(callback.getString("t"), YidunReviewType.class);
//        if (!YidunSupport.verifySignature(reviewType, params)) {
//            LOG.warn("易盾审核签名验证失败，所有参数：{}", JsonUtils.seriazileAsString(params));
//            return WebUtils.writeJson(result, response);
//        }
//        if (reviewType != null) {
//            processMediaReview(YidunReviewForm.buildMedia(reviewType, censor));
//            result.put("code", 200);
//        } else {
//            LOG.error("易盾审核类型未定义，原始数据：{}", callbackData);
//        }
//        return WebUtils.writeJson(result, response);
//    }



    /**
     * 易盾融媒体审核结果回调
     *
     * @param request
     * @param response
     * @return
     */
    @Request(value = SessionType.None, header = HeaderType.None)
    @RequestMapping("/yidunMedia")
    public String yidunMedia(HttpServletRequest request, HttpServletResponse response) {
        JSONObject result = new JSONObject();
        result.put("code", 500);
        Map<String, String> params = WebUtils.getParameters(request);
        String callbackData = params.get("callbackData");
        LOG.info("callbackData -> {}", callbackData);
        JSONObject data = JSONObject.parseObject(callbackData);
        if (data.containsKey("antispam")) {
            JSONObject antispam = data.getJSONObject("antispam");
            if (antispam == null) {
                LOG.error("易盾审核返回值错误，antispam为空");
                return WebUtils.writeJson(result, response);
            }
            UrlParamsMap callback = UrlParamsMap.build(antispam.getString("callback"));
            YidunReviewType reviewType = EnumUtils.byName(callback.getString("t"), YidunReviewType.class);
            if (!YidunSupport.verifySignature(reviewType, params)) {
                LOG.warn("易盾审核签名验证失败，所有参数：{}", JsonUtils.seriazileAsString(params));
                return WebUtils.writeJson(result, response);
            }
            if (reviewType != null) {
                processMediaReview(YidunReviewForm.buildMedia(reviewType, antispam));
                result.put("code", 200);
            } else {
                LOG.error("易盾审核类型未定义，原始数据：{}", callbackData);
            }
            return WebUtils.writeJson(result, response);
        }
        return WebUtils.writeJson(result, response);
    }

    /**
     * 处理文本审核结果
     *
     * @param form
     */
    private void processTextReview(YidunReviewForm form) {
        // 审核快捷回复
        if (YidunReviewType.QuickReply == form.getReviewType()) {
            quickReplyService.saveAudit(form.toQuickReply());
            return;
        }
        // 审核宇宙内容
        if (YidunReviewType.Cosmos == form.getReviewType()) {
            cosmosService.saveAudit(form.toCosmos());
            return;
        }
    }

    /**
     * 处理融媒体审核结果
     *
     * @param form
     */
    private void processMediaReview(YidunReviewForm form) {
        if (YidunReviewType.Post == form.getReviewType()) {
            postService.saveAuditPost(form.toPost());
            return;
        }
    }
}
