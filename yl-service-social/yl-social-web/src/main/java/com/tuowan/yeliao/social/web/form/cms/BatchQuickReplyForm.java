package com.tuowan.yeliao.social.web.form.cms;

import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.data.enums.general.ReviewResultType;
import com.tuowan.yeliao.commons.web.common.form.Form;

import java.util.Objects;

public class BatchQuickReplyForm implements Form {
    /**
     * 回复id
     */
    private Long replyId;
    /**
     * 回复ID合集
     */
    private String[] replyIds;
    /**
     * 是否通过
     */
    private ReviewResultType auditStatus;
    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 参数检查
     *
     * @return
     */
    public void paramsCheck() {
        if (Objects.isNull(this.replyIds) || this.replyIds.length == 0) {
            throw new DataException("请选择处理对象！");
        }
        if (Objects.isNull(this.auditStatus)) {
            throw new DataException("请选择审核结果！");
        }
    }

    public String[] getReplyIds() {
        return replyIds;
    }

    public void setReplyIds(String[] replyIds) {
        this.replyIds = replyIds;
    }

    public ReviewResultType getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(ReviewResultType auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getAuditReason() {
        return auditReason;
    }

    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason;
    }

    public Long getReplyId() {
        return replyId;
    }

    public void setReplyId(Long replyId) {
        this.replyId = replyId;
    }
}
