package com.tuowan.yeliao.social.web.form.cms;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.data.enums.user.FemaleLevel;
import com.tuowan.yeliao.commons.web.common.form.Form;

public class UpdateFemaleNumLevelForm implements Form {
    // 女用户id
    @LMNotNull
    private Long userId;
    @LMNotNull
    private FemaleLevel level;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public FemaleLevel getLevel() {
        return level;
    }

    public void setLevel(FemaleLevel level) {
        this.level = level;
    }
}
