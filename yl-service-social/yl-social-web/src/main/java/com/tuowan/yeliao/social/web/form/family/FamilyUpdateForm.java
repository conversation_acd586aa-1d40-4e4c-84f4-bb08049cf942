package com.tuowan.yeliao.social.web.form.family;


import com.tuowan.yeliao.commons.data.enums.general.Status;

/**
 * 家族修改表单
 *
 * <AUTHOR>
 * @date 2021/7/7 15:21
 */
public class FamilyUpdateForm extends FamilyIdForm {

    /** 禁言状态 */
    private Status allMuteStatus;

    /** 家族名称 */
    private String familyName;

    /** 家族介绍 */
    private String familyDesc;

    /** 家族封面 */
    private String coverPic;

    /** 标签设置 */
    private String tagIds;

    /** 城市ID */
    private Integer cityId;

    public Status getAllMuteStatus() {
        return allMuteStatus;
    }

    public void setAllMuteStatus(Status allMuteStatus) {
        this.allMuteStatus = allMuteStatus;
    }

    public String getFamilyName() {
        return familyName;
    }

    public void setFamilyName(String familyName) {
        this.familyName = familyName;
    }

    public String getFamilyDesc() {
        return familyDesc;
    }

    public void setFamilyDesc(String familyDesc) {
        this.familyDesc = familyDesc;
    }

    public String getCoverPic() {
        return coverPic;
    }

    public void setCoverPic(String coverPic) {
        this.coverPic = coverPic;
    }

    public String getTagIds() {
        return tagIds;
    }

    public void setTagIds(String tagIds) {
        this.tagIds = tagIds;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
}
