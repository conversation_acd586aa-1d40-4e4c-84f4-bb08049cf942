package com.tuowan.yeliao.social.web.form.friend;

import com.easyooo.framework.validate.config.LMLength;
import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.enums.config.ProdType;
import com.tuowan.yeliao.commons.web.common.form.Form;
import com.tuowan.yeliao.social.data.enums.friend.ChatQuoteType;
import com.tuowan.yeliao.social.data.enums.friend.ChatSourceType;
import com.tuowan.yeliao.social.web.form.friend.chat.ChatSendBackupGiftForm;

/**
 * 聊天送礼表单
 *
 * <AUTHOR>
 * @date 2020/7/3 21:29
 */
public class ChatSendGiftForm implements Form {

    /** 赠送的好友ID */
    @LMNotNull
    private Long friendId;
    /** 聊天礼物ID */
    @LMNotNull
    private Integer chatGiftId;
    /** 送礼数量 */
    @LMNotNull
    private Integer count = 1;
    /** 是否从背包送出 */
    @LMNotNull
    private BoolType fromBag;
    /** 商品类型 */
    @LMNotNull
    private ProdType prodType;
    /** 数据来源 */
    @LMNotNull
    private ChatSourceType sourceType;
    /**
     * 是否来自索要
     */
    private BoolType fromRequest;
    /**
     * 动态送礼对应的动态ID
     */
    private Long postId;
    /**
     * 私信引用类型
     */
    private ChatQuoteType quoteType;
    /**
     * 聊天室ID
     */
    private Long roomId;
    /**
     * 心意礼物 寄语
     */
    @LMLength(max = 30)
    private String mgMessage;

    public static ChatSendGiftForm create(ChatSendBackupGiftForm form) {
        ChatSendGiftForm giftForm = new ChatSendGiftForm();
        giftForm.setFriendId(form.getFriendId());
        giftForm.setChatGiftId(form.getGiftId());
        giftForm.setCount(form.getCount());
        giftForm.setFromBag(BoolType.True);
        giftForm.setProdType(ProdType.Gift);
        giftForm.setSourceType(form.getSourceType());
        return giftForm;
    }

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }

    public Integer getChatGiftId() {
        return chatGiftId;
    }

    public void setChatGiftId(Integer chatGiftId) {
        this.chatGiftId = chatGiftId;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public BoolType getFromBag() {
        return fromBag;
    }

    public void setFromBag(BoolType fromBag) {
        this.fromBag = fromBag;
    }

    public ProdType getProdType() {
        return prodType;
    }

    public void setProdType(ProdType prodType) {
        this.prodType = prodType;
    }

    public ChatSourceType getSourceType() {
        return sourceType;
    }

    public void setSourceType(ChatSourceType sourceType) {
        this.sourceType = sourceType;
    }

    public BoolType getFromRequest() {
        return fromRequest;
    }

    public void setFromRequest(BoolType fromRequest) {
        this.fromRequest = fromRequest;
    }

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public ChatQuoteType getQuoteType() {
        return quoteType;
    }

    public void setQuoteType(ChatQuoteType quoteType) {
        this.quoteType = quoteType;
    }

    public Long getRoomId() {
        return roomId;
    }

    public void setRoomId(Long roomId) {
        this.roomId = roomId;
    }

    public String getMgMessage() {
        return mgMessage;
    }

    public void setMgMessage(String mgMessage) {
        this.mgMessage = mgMessage;
    }
}
