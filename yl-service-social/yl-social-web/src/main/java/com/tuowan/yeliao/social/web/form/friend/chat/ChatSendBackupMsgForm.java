package com.tuowan.yeliao.social.web.form.friend.chat;

import com.easyooo.framework.validate.config.LMNotEmpty;
import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.data.enums.social.ChatContentType;
import com.tuowan.yeliao.commons.web.common.form.Form;
import com.tuowan.yeliao.social.data.enums.friend.ChatSourceType;

/**
 * 后台发送消息表单
 *
 * <AUTHOR>
 * @date 2020/11/4 11:49
 */
public class ChatSendBackupMsgForm implements Form {

    @LMNotNull
    private Long userId;
    private Long friendId;
    /**
     * 聊天内容，当消息类型为图片消息时，content是缩略图的 base64 编码
     */
    @LMNotEmpty
    private String content;
    /**
     * 图片地址
     */
    private String imgUrl;
    /**
     * 聊天内容类型，客户端不用传此字段，后台根据不同的接口控制
     */
    @LMNotEmpty
    private ChatContentType contentType;
    /**
     * 私信数据来源
     */
    private ChatSourceType sourceType;

    /**
     * 发言消费的费用
     */
    private Long fee;

    /**
     * 消耗的聊天券的数量
     */
    private Integer consumeTicketCnt;


    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public ChatContentType getContentType() {
        return contentType;
    }

    public void setContentType(ChatContentType contentType) {
        this.contentType = contentType;
    }

    public Long getFee() {
        return fee;
    }

    public void setFee(Long fee) {
        this.fee = fee;
    }

    public Integer getConsumeTicketCnt() {
        return consumeTicketCnt;
    }

    public void setConsumeTicketCnt(Integer consumeTicketCnt) {
        this.consumeTicketCnt = consumeTicketCnt;
    }

    public ChatSourceType getSourceType() {
        return sourceType;
    }

    public void setSourceType(ChatSourceType sourceType) {
        this.sourceType = sourceType;
    }
}
