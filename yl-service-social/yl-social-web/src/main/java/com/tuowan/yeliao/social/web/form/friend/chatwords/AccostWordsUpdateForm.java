package com.tuowan.yeliao.social.web.form.friend.chatwords;

import com.easyooo.framework.validate.config.LMNotEmpty;
import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.web.common.form.Form;
import org.apache.commons.lang3.StringUtils;

/**
 * 编辑搭讪常用语表单
 *
 * <AUTHOR>
 * @date 2020/10/10 16:03
 */
public class AccostWordsUpdateForm implements Form {

    /**
     * 常用语ID
     */
    @LMNotNull
    private Long wordsId;
    /**
     * 常用语内容
     */
    @LMNotEmpty
    private String words;

    public Long getWordsId() {
        return wordsId;
    }

    public void setWordsId(Long wordsId) {
        this.wordsId = wordsId;
    }

    public String getWords() {
        return StringUtils.trimToNull(words);
    }

    public void setWords(String words) {
        this.words = words;
    }
}
