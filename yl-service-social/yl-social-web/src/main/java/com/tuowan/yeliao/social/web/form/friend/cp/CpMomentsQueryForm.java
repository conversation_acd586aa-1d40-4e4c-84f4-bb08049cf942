package com.tuowan.yeliao.social.web.form.friend.cp;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.web.common.form.Form;

/**
 * 情侣动态查询表单
 * <AUTHOR>
 * @date 2021/6/4 11:56
 */
public class CpMomentsQueryForm implements Form {

    /** 上次查看的最后一条动态 */
    private Long lastMomentId;
    /** 好友ID */
    @LMNotNull
    private Long friendId;

    public Long getLastMomentId() {
        return lastMomentId;
    }

    public void setLastMomentId(Long lastMomentId) {
        this.lastMomentId = lastMomentId;
    }

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }
}
