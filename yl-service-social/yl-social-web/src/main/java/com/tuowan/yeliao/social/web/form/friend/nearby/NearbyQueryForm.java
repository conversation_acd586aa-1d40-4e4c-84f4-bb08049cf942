package com.tuowan.yeliao.social.web.form.friend.nearby;

import com.easyooo.framework.validate.config.LMRange;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.web.common.form.PageForm;

/**
 * 附近的人查询表单
 *
 * <AUTHOR>
 * @date 2020/12/15 10:37
 */
public class NearbyQueryForm extends PageForm {

    @LMRange(min = 0)
    private Integer offset;
    /**
     * 纬度值
     */
    private String lat;
    /**
     * 经度值
     */
    private String lon;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区县
     */
    private String district;
    /**
     * 用户手机是否给遇见爱开启位置权限
     */
    private BoolType hasPosition;

    public BoolType getHasPosition() {
        return hasPosition;
    }

    public void setHasPosition(BoolType hasPosition) {
        this.hasPosition = hasPosition;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    @Override
    public Integer getOffset() {
        return offset;
    }

    @Override
    public void setOffset(Integer offset) {
        this.offset = offset;
    }
}
