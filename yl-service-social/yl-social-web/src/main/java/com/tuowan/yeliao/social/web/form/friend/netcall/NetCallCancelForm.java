package com.tuowan.yeliao.social.web.form.friend.netcall;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.web.common.form.Form;
import com.tuowan.yeliao.social.data.enums.friend.NetCallCancelType;

/**
 * 音视频通话取消表单
 *
 * <AUTHOR>
 * @date 2020/7/7 17:53
 */
public class NetCallCancelForm implements Form {

    /** 通话ID */
    @LMNotNull
    private Long callId;
    /** 取消类型 */
    @LMNotNull
    private NetCallCancelType cancelType;

    public Long getCallId() {
        return callId;
    }

    public void setCallId(Long callId) {
        this.callId = callId;
    }

    public NetCallCancelType getCancelType() {
        return cancelType;
    }

    public void setCancelType(NetCallCancelType cancelType) {
        this.cancelType = cancelType;
    }
}
