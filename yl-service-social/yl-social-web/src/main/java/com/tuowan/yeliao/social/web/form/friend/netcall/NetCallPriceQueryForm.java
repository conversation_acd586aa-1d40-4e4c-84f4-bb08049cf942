package com.tuowan.yeliao.social.web.form.friend.netcall;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.data.enums.social.NetCallType;
import com.tuowan.yeliao.commons.web.common.form.Form;

/**
 * 音频价格查询
 *
 * <AUTHOR>
 * @date 2021/4/29 09:33
 */
public class NetCallPriceQueryForm  implements Form {

    /** 音频类型 */
    @LMNotNull
    private NetCallType callType;
    /** 用户ID */
    @LMNotNull
    private Long friendId;

    public NetCallType getCallType() {
        return callType;
    }

    public void setCallType(NetCallType callType) {
        this.callType = callType;
    }

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }
}
