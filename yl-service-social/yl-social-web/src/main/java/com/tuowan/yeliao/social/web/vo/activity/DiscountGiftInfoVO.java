package com.tuowan.yeliao.social.web.vo.activity;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/9/28 16:01
 */
public class DiscountGiftInfoVO {

    private Integer giftId;
    private String giftPic;
    private String giftName;

    /**
     * 原价
     */
    private Integer originBeans;
    /**
     * 现价
     */
    private Integer realBeans;
    /**
     * 折扣
     */
    private String discount;
    /**
     * 抢购倒计时，单位：秒
     */
    private Long ttl;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    public DiscountGiftInfoVO() {
    }

    public DiscountGiftInfoVO(Integer giftId, Integer originBeans, String discount, Date startTime, Date endTime) {
        this.giftId = giftId;
        this.originBeans = originBeans;
        this.discount = discount;
        this.startTime = startTime;
        this.endTime = endTime;
    }

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public String getGiftPic() {
        return giftPic;
    }

    public void setGiftPic(String giftPic) {
        this.giftPic = giftPic;
    }

    public String getGiftName() {
        return giftName;
    }

    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    public Integer getOriginBeans() {
        return originBeans;
    }

    public void setOriginBeans(Integer originBeans) {
        this.originBeans = originBeans;
    }

    public Integer getRealBeans() {
        return realBeans;
    }

    public void setRealBeans(Integer realBeans) {
        this.realBeans = realBeans;
    }

    public String getDiscount() {
        return discount;
    }

    public void setDiscount(String discount) {
        this.discount = discount;
    }

    public Long getTtl() {
        return ttl;
    }

    public void setTtl(Long ttl) {
        this.ttl = ttl;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
}
