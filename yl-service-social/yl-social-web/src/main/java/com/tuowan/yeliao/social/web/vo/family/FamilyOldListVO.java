package com.tuowan.yeliao.social.web.vo.family;


import com.tuowan.yeliao.commons.data.utils.dto.AdDefineDTO;
import com.tuowan.yeliao.commons.web.common.vo.PageVO;

import java.util.List;

/**
 * 家族老列表数据Vo
 *
 * <AUTHOR>
 * @date 2021/7/6 20:41
 */
public class FamilyOldListVO {

    /**
     * 广告列表
     */
    private List<AdDefineDTO> adList;
    /**
     * tab对应分页数据
     */
    private PageVO page;

    public FamilyOldListVO(List<AdDefineDTO> adList, PageVO page) {
        this.adList = adList;
        this.page = page;
    }

    public List<AdDefineDTO> getAdList() {
        return adList;
    }

    public void setAdList(List<AdDefineDTO> adList) {
        this.adList = adList;
    }

    public PageVO getPage() {
        return page;
    }

    public void setPage(PageVO page) {
        this.page = page;
    }


}
