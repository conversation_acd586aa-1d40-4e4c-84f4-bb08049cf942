package com.tuowan.yeliao.social.web.vo.family;


import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.social.data.entity.family.FFamily;
import com.tuowan.yeliao.social.data.enums.family.FamilyOutActionType;

/**
 * 家族设置
 *
 * <AUTHOR>
 * @date 2021/7/7 14:57
 */
public class FamilySettingsVO {

    /** 家族ID */
    private Integer familyId;

    /** 用户昵称 */
    private String nickname;

    /** 禁言状态(族长才有返回值) */
    private Status allMuteStatus;

    /** 离开家族动作 */
    private FamilyOutActionType outActionType;

    /** 解散家族提示信息 */
    private String disbandToast;

    public FamilySettingsVO(FFamily family, String nickname, Status allMuteStatus, FamilyOutActionType outActionType) {
        this.familyId = family.getFamilyId();
        this.nickname = nickname;
        this.allMuteStatus = allMuteStatus;
        this.outActionType = outActionType;
        this.disbandToast = GlobalConstant.DISBAND_TOAST;
    }

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public Status getAllMuteStatus() {
        return allMuteStatus;
    }

    public void setAllMuteStatus(Status allMuteStatus) {
        this.allMuteStatus = allMuteStatus;
    }

    public FamilyOutActionType getOutActionType() {
        return outActionType;
    }

    public void setOutActionType(FamilyOutActionType outActionType) {
        this.outActionType = outActionType;
    }

    public String getDisbandToast() {
        return disbandToast;
    }

    public void setDisbandToast(String disbandToast) {
        this.disbandToast = disbandToast;
    }
}
