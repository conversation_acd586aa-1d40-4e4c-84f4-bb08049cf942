package com.tuowan.yeliao.social.web.vo.friend.fate;

import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.enums.user.SexType;

public class FateMatchVO {
    /**
     * 对象用户标识id
     */
    private Long userId;
    /**
     * 对象用户昵称
     */
    private String nickname;
    /**
     * 对象用户头像
     */
    private String headPic;
    /**
     * 对象用户性别
     */
    private SexType sexType;
    /**
     * 对象用户年龄
     */
    private String age;
    /**
     * 对象用户是否真人认证
     */
    private BoolType realPerson;
    /**
     * 对象用户城市信息
     */
    private String city;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public SexType getSexType() {
        return sexType;
    }

    public void setSexType(SexType sexType) {
        this.sexType = sexType;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public BoolType getRealPerson() {
        return realPerson;
    }

    public void setRealPerson(BoolType realPerson) {
        this.realPerson = realPerson;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }
}
