package com.tuowan.yeliao.social.web.vo.friend.relation;


import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;

/**
 * 引导信息文案封装
 *
 * <AUTHOR>
 * @date 2021/1/13 13:19
 */
public class GuideInfoVO {

    /** 剩余心动解锁次数， -1 表示不限制 */
    private Integer remainUnlockTimes;
    /** 引导提示内容 */
    private String guideText;
    /** 任务点击跳转类型 */
    private ClientTouchType touchType;
    /** 任务点击按钮文案 */
    private String touchText;
    /** 能查询记录的最大值 -1 看所有 */
    private Long viewNum;

    public Integer getRemainUnlockTimes() {
        return remainUnlockTimes;
    }

    public void setRemainUnlockTimes(Integer remainUnlockTimes) {
        this.remainUnlockTimes = remainUnlockTimes;
    }

    public String getGuideText() {
        return guideText;
    }

    public void setGuideText(String guideText) {
        this.guideText = guideText;
    }

    public ClientTouchType getTouchType() {
        return touchType;
    }

    public void setTouchType(ClientTouchType touchType) {
        this.touchType = touchType;
    }

    public String getTouchText() {
        return touchText;
    }

    public void setTouchText(String touchText) {
        this.touchText = touchText;
    }

    public Long getViewNum() {
        return viewNum;
    }

    public void setViewNum(Long viewNum) {
        this.viewNum = viewNum;
    }
}
