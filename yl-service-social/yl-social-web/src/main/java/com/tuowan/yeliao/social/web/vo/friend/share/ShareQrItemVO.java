package com.tuowan.yeliao.social.web.vo.friend.share;

public class ShareQrItemVO {
    // 邀请页二维码（落地页链接信息）
    private String qrCodeBase64;
    // 背景图片
    private String posterPic;

    public static ShareQrItemVO build1(String qrCodeBase64, String posterPic){
        ShareQrItemVO vo = new ShareQrItemVO();
        vo.setQrCodeBase64(qrCodeBase64);
        vo.setPosterPic(posterPic);
        return vo;
    }

    public String getQrCodeBase64() {
        return qrCodeBase64;
    }

    public void setQrCodeBase64(String qrCodeBase64) {
        this.qrCodeBase64 = qrCodeBase64;
    }

    public String getPosterPic() {
        return posterPic;
    }

    public void setPosterPic(String posterPic) {
        this.posterPic = posterPic;
    }
}
