package com.tuowan.yeliao.social.web.vo.videoChat;

import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.enums.user.VipType;
import com.tuowan.yeliao.social.data.enums.user.CallStatus;

public class VideoChatHomeVO {
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 图片地址
     */
    private String picUrl;
    /**
     * 视频地址
     */
    private String videoUrl;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * vip
     */
    private VipType vipType;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 距离
     */
    private String area;

    /**
     * 真人认证
     */
    private BoolType realPerson;


    /**
     * 实名认证
     */
    private BoolType realName;


    /**
     * 拨打空闲状态
     */
    private CallStatus callStatus;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public VipType getVipType() {
        return vipType;
    }

    public void setVipType(VipType vipType) {
        this.vipType = vipType;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public BoolType getRealPerson() {
        return realPerson;
    }

    public void setRealPerson(BoolType realPerson) {
        this.realPerson = realPerson;
    }

    public BoolType getRealName() {
        return realName;
    }

    public void setRealName(BoolType realName) {
        this.realName = realName;
    }

    public CallStatus getCallStatus() {
        return callStatus;
    }

    public void setCallStatus(CallStatus callStatus) {
        this.callStatus = callStatus;
    }
}
