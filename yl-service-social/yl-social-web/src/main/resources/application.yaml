config:
  entity:
    basePackages: com.tuowan.yeliao.commons.data.entity,com.tuowan.yeliao.social.data.entity
  data:
    basePackages: com.tuowan.yeliao.commons.data,com.tuowan.yeliao.social.data
  service:
    transExpression: execution(public * com.tuowan.yeliao.social.service..*.*(..))

apimt:
  scan:
    base:
      package: com.tuowan.yeliao.social.web.controller
  api:
    module:
      list: component=xy-component&web.support=xy-commons-web
  request:
    header: x-inf||s=81dc9bdb52d04dc20036dbd8313ed055&t=I&v=*******&d=XX iPhone&m=Mi 6&n=wifi&r=cmcc&o=ios10.1&i=fe123ab212cd1321bcam1&h=13798121516&c=WDGG&e=123&w=xxxx&x=2323232323