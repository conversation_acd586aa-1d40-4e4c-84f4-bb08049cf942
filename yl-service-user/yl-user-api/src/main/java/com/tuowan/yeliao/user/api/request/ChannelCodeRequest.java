package com.tuowan.yeliao.user.api.request;

import com.tuowan.yeliao.commons.data.enums.user.SexType;

public class ChannelCodeRequest {
    /**
     * 用户标识
     */
    private Long userId;
    /**
     * 用户性别
     */
    private SexType sexType;
    /**
     * 渠道id
     */
    private String channelId;
    /**
     * 渠道code
     */
    private String channelCode;

    public static ChannelCodeRequest build(Long userId, SexType sexType, String channelId, String channelCode){
        ChannelCodeRequest request = new ChannelCodeRequest();
        request.setUserId(userId);
        request.setSexType(sexType);
        request.setChannelId(channelId);
        request.setChannelCode(channelCode);
        return request;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public SexType getSexType() {
        return sexType;
    }

    public void setSexType(SexType sexType) {
        this.sexType = sexType;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }
}
