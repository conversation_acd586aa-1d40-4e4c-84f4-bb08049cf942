package com.tuowan.yeliao.user.service;

import com.alibaba.nacos.common.utils.Pair;
import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.comp.grant.Grant;
import com.tuowan.yeliao.commons.comp.grant.GrantComponent;
import com.tuowan.yeliao.commons.comp.grant.dto.VariableAwardDTO;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.comp.message.dto.AwardMsgDTO;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.comp.operate.UserOperateComponent;
import com.tuowan.yeliao.commons.comp.signin.UserSignInComponent;
import com.tuowan.yeliao.commons.comp.task.TaskComponent;
import com.tuowan.yeliao.commons.comp.user.UserBlackListComponent;
import com.tuowan.yeliao.commons.comp.user.UserBusiComponent;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.enums.MiniLockType;
import com.tuowan.yeliao.commons.config.lock.MiniLockTemplate;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.BusiRedisTemplate;
import com.tuowan.yeliao.commons.config.redis.template.UserRedisTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.context.proxy.ProxyExecutors;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.constant.IconConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.business.SocialGoodsDefine;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.user.UserType;
import com.tuowan.yeliao.commons.core.enums.redis.BusiKeyDefine;
import com.tuowan.yeliao.commons.core.enums.redis.UserKeyDefine;
import com.tuowan.yeliao.commons.data.dto.common.RedisShardDTO;
import com.tuowan.yeliao.commons.data.dto.user.AddressDTO;
import com.tuowan.yeliao.commons.data.entity.config.TAward;
import com.tuowan.yeliao.commons.data.entity.config.TAwardDetail;
import com.tuowan.yeliao.commons.data.entity.user.*;
import com.tuowan.yeliao.commons.data.enums.config.AwardGrantType;
import com.tuowan.yeliao.commons.data.enums.config.AwardType;
import com.tuowan.yeliao.commons.data.enums.config.NoticeSysType;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.enums.general.*;
import com.tuowan.yeliao.commons.data.enums.user.*;
import com.tuowan.yeliao.commons.data.manager.commons.BusiManager;
import com.tuowan.yeliao.commons.data.manager.commons.SeqManager;
import com.tuowan.yeliao.commons.data.manager.config.PicManager;
import com.tuowan.yeliao.commons.data.manager.config.NewsManager;
import com.tuowan.yeliao.commons.data.manager.user.*;
import com.tuowan.yeliao.commons.data.persistence.config.TAwardDetailMapper;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.data.utils.HtmlUrlUtils;
import com.tuowan.yeliao.commons.data.utils.RedisShardUtils;
import com.tuowan.yeliao.commons.open.common.AliyunIpSupport;
import com.tuowan.yeliao.commons.open.dto.LocationDTO;
import com.tuowan.yeliao.commons.open.shumei.TextValidResult;
import com.tuowan.yeliao.commons.open.yidun.YidunSupport;
import com.tuowan.yeliao.commons.open.yidun.enums.YidunReviewType;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.commons.web.proxy.config.MQInvoke;
import com.tuowan.yeliao.commons.web.session.RedisSessionManager;
import com.tuowan.yeliao.social.api.remote.SocialWebRemote;
import com.tuowan.yeliao.user.comp.UserCheckComponent;
import com.tuowan.yeliao.user.comp.UserOptComponent;
import com.tuowan.yeliao.user.data.entity.UUserBlacklistStatistic;
import com.tuowan.yeliao.user.data.enums.UserGroupBlackListResult;
import com.tuowan.yeliao.user.data.manager.acct.UserAccountManager;
import com.tuowan.yeliao.user.data.manager.user.UserCommonManager;
import com.tuowan.yeliao.user.data.persistence.UUserBlacklistStatisticMapper;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 公用服务
 */
@Service
public class CommonService implements ApplicationContextAware {

    @Autowired
    private NewsManager newsManager;
    @Autowired
    private BusiManager busiManager;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private NoticeComponent noticeComponent;
    @Autowired
    private GrantComponent grantComponent;
    @Autowired
    private BusiRedisTemplate busiRedisTemplate;
    @Autowired
    private UserSignInComponent userSignInComponent;
    @Autowired
    private MessageSettingManager messageSettingManager;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private MiniLockTemplate miniLockTemplate;
    @Autowired
    private UserCheckComponent userCheckComponent;
    @Autowired
    private UserBusiComponent userBusiComponent;
    @Autowired
    private PicManager picManager;
    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private UserBlackListComponent userBlackListComponent;
    @Autowired
    private UUserBlacklistStatisticMapper userBlacklistStatisticMapper;
    @Autowired
    private UserOperateComponent userOperateComponent;
    @Autowired
    private UserKeyMarkManager userKeyMarkManager;
    @Autowired
    private RedisSessionManager redisSessionManager;
    @Autowired
    private ChatMasterManager chatMasterManager;
    @Autowired
    private SocialWebRemote socialWebRemote;
    @Autowired
    private TAwardDetailMapper tAwardDetailMapper;
    @Autowired
    private TaskComponent taskComponent;
    @Autowired
    private UserRedisTemplate userRedisTemplate;
    @Autowired
    private UserCommonManager userCommonManager;
    @Autowired
    private SeqManager seqManager;
    @Autowired
    private UserAccountManager userAccountManager;
    @Autowired
    private AliyunIpSupport aliyunIpSupport;
    @Autowired
    private UserOptComponent userOptComponent;

    /** 发奖的各种实现类 */
    private Map<AwardType, Grant> grantSetImpl = new HashMap<>();

    /**
     * 全站消息过期消息
     */
    public List<String> queryExpireBroadcastNotice(){
        return noticeComponent.queryExpireBroadcastNotice();
    }

    /**
     * 获取待奖励视频速配折扣券奖励用户
     */
    public List<String> queryVideoMatchTicketWaitSendQueueUsers(){
        return userOperateComponent.queryVideoMatchTicketWaitSendQueueUsers();
    }


    /**
     * 获取权限待恢复数据
     */
    public List<String> queryWaitRightReleaseDatas(){
        return userOptComponent.queryUserRightReleaseDatas();
    }

    /**
     * 处理用户权限恢复
     */
    public void saveDealUserRightRelease(String member){
        // 从队列中移除
        userOptComponent.removeUserRightReleaseQueue(member);
        // 权限恢复
        String[] values = member.split("#");
        Long targetUserId = Long.valueOf(values[0]);
        String rightSign = values[1];

        UMessageSetting update = new UMessageSetting();
        update.setUserId(targetUserId);
        if("msgSend".equals(rightSign)){ // 私聊权限
            update.setMsgSend(BoolType.True);
            messageSettingManager.updateSetting(update);
            // 系统消息通知
            noticeComponent.sendSystemNotice(targetUserId, NoticeSysType.CommonNotice, MapUtils.gmap("content", MsgUtils.format("消息发送权限已打开，快去和喜欢的Ta聊天吧！")));
        }else if("voiceCall".equals(rightSign)){ // 语音拨打
            update.setVoiceCall(BoolType.True);
            messageSettingManager.updateSetting(update);
            // 系统消息通知
            noticeComponent.sendSystemNotice(targetUserId, NoticeSysType.CommonNotice, MapUtils.gmap("content", MsgUtils.format("语音拨打权限已打开，快去和喜欢的Ta聊天吧！")));
        }else if("voiceAnswer".equals(rightSign)){ // 语音接听
            update.setVoiceAnswer(BoolType.True);
            messageSettingManager.updateSetting(update);
            // 系统消息通知
            noticeComponent.sendSystemNotice(targetUserId, NoticeSysType.CommonNotice, MapUtils.gmap("content", MsgUtils.format("语音接听权限已打开，快去和喜欢的Ta聊天吧！")));
        }else if("videoCall".equals(rightSign)){ // 视频拨打
            update.setVideoCall(BoolType.True);
            messageSettingManager.updateSetting(update);
            // 系统消息通知
            noticeComponent.sendSystemNotice(targetUserId, NoticeSysType.CommonNotice, MapUtils.gmap("content", MsgUtils.format("视频拨打权限已打开，快去和喜欢的Ta聊天吧！")));
        }else if("videoAnswer".equals(rightSign)){ // 视频接听
            update.setVideoAnswer(BoolType.True);
            messageSettingManager.updateSetting(update);
            // 系统消息通知
            noticeComponent.sendSystemNotice(targetUserId, NoticeSysType.CommonNotice, MapUtils.gmap("content", MsgUtils.format("视频接听权限已打开，快去和喜欢的Ta聊天吧！")));
        }
    }

    /**
     * 奖励视频速配折扣券
     */
    public void saveAwardVideoMatchTicket(String userIdStr){
        Long userId = Long.valueOf(userIdStr);
        // 从队列中移除 能移除成功才执行
        if(!userOperateComponent.removeFromVideoMatchTicketWaitSendQueue(userId)){
            return;
        }
        // 判断是否达到发放时间
        if(!userOperateComponent.checkSendVideoMatchTicketTime(userId)){
            return;
        }
        // 发放折扣券
        Date expireTime = DateUtils.plusDays(new Date(), 1);
        grantComponent.saveCombineAutoGrant(userId, VariableAwardDTO.create("MaleOnlineVmtAward", "男上线速配折扣券奖励", 5, AwardType.SocialGoods, SocialGoodsDefine.VideoMatch5DiscountTicket.getGoodsId().longValue(), 0, null, null, expireTime));
        // 记录此次发放时间
        userOperateComponent.recordSendVideoMatchTicketTime(userId);
        // 弹窗提示
        messageComponent.sendVideoMatchTicketAwardPop(userId, expireTime);
    }

    /**
     * 清理过期全站消息
     */
    public void clearBroadcastNotice(String tid){
        // 第一步 先从过期队列中移除
        if(!noticeComponent.queryExpireBroadcastNotice(tid)){
            return;
        }
        noticeComponent.learExpireBroadcastNotice(tid);
    }

    /**
     * 关注好友 操作
     */
    public void saveUserFollowDeal(){
        Long userId = GlobalUtils.uid();
        Long friendId = GlobalUtils.formLong("friendId");
        // 清除 关注好友动态储存缓存
        userOperateComponent.delPostFollowUsers(userId, friendId);
    }

    /**
     * 取消关注 操作
     */
    public void saveUserUnFollowDral(){
        Long userId = GlobalUtils.uid();
        Long friendId = GlobalUtils.formLong("friendId");
        // 清除 关注好友动态储存缓存
        userOperateComponent.delPostFollowUsers(userId, friendId);
    }

    /**
     * 用户修改颜照库
     */
    public void saveUserMediaApply(){
        Long userId = GlobalUtils.uid();
        if(UnifiedConfig.isProdEnv()){
            return;
        }
        // 测试环境处理
        List<UUserMediaApply> applies = userCommonManager.queryUserMediaApply(userId);
        // 删除老的媒资信息
        List<UUserMedia> oldMedias = userInfoManager.getUserMedias(userId);
        oldMedias.forEach(item1 -> {
            userInfoManager.deleteUserMedia(item1);
        });
        // 修改 u_user_media_apply 记录的状态全部为审核通过
        userCommonManager.batchUpdateMediaApplyStatusByUserId(userId, MediaApplyStatus.PassCms);
        // 将 u_user_media_apply 复制到 u_user_media 中
        applies.forEach(item -> {
            UUserMedia media = new UUserMedia();
            media.setRecordId(Objects.isNull(item.getRecordId()) ? seqManager.getMediaRecordId() : item.getRecordId());
            media.setUserId(item.getUserId());
            media.setType(item.getType());
            media.setMediaValue(item.getMediaValue());
            media.setMediaExt(item.getMediaExt());
            media.setMediaIdx(item.getMediaIdx());
            media.setCreateTime(item.getCreateTime());
            userInfoManager.saveUserMedia(media);
        });
        // 修改 u_user_more 媒资审核状态
        UUserMore more = new UUserMore(userId);
        more.setMediaStatus(AuditStatus.Pass);
        userInfoManager.updateUserMore(more);
    }

    /**
     * 用户上线-计算男用户等级
     */
    @BusiCode
    public void updateMaleLevel(){
        // 仅处理男用户
        if(SexType.Male != GlobalUtils.sexType()){
            return;
        }
        Long userId = GlobalUtils.uid();
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        // 计算男用户等级
        ChatMasterLevel maleLevel = userInfoManager.calculateMaleLevel(userBasic);
        if(maleLevel != userBasic.getChatMasterLevel()){
            // 修改 u_user_basic 等级
            UUserBasic updateBasic = new UUserBasic(userId);
            updateBasic.setChatMasterLevel(maleLevel);
            userInfoManager.updateUserBasic(updateBasic);
        }
    }

    /**
     * 男用户上线发放视频速配折扣券
     */
    @MQInvoke
    public void saveMaleOnlineSendVideoMatchTicket(){
        // 仅给男用户赠送
        if(SexType.Male != GlobalUtils.sexType()){
            return;
        }
        Long userId = GlobalUtils.uid();
        // 判断是否达到发放时间
        if(!userOperateComponent.checkSendVideoMatchTicketTime(userId)){
            return;
        }
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        // 如果注册时间跟当前小于1小时 不发放
        if(DateUtils.getDiffHours(userBasic.getCreateTime()) < 1){
            return;
        }
        // 发放折扣券
        Date expireTime = DateUtils.plusDays(new Date(), 1);
        grantComponent.saveCombineAutoGrant(userId, VariableAwardDTO.create("MaleOnlineVmtAward", "男上线速配折扣券奖励", 5, AwardType.SocialGoods, SocialGoodsDefine.VideoMatch5DiscountTicket.getGoodsId().longValue(), 0, null, null, expireTime));
        // 记录此次发放时间
        userOperateComponent.recordSendVideoMatchTicketTime(userId);
        // 弹窗提示
        messageComponent.sendVideoMatchTicketAwardPop(userId, expireTime);
    }

    /**
     * 处理 商店审核禁止推荐用户
     */
    public void saveReleaseStoreAuditShieldUserDeal(Long userId){
        // 修改 u_user_ext 字段值
        UMessageSetting update = new UMessageSetting(userId);
        update.setBanRecom(BoolType.False);
        messageSettingManager.updateSetting(update);
        // 事物后删除redis的值
        CallbackAfterTransactionUtil.send(() -> {
            userRedisTemplate.zrem(buildStoreAuditShieldUser(), userId.toString());
        });
    }

    /**
     * 获取 商店审核禁止推荐用户
     */
    public List<String> queryStoreAuditShieldUser(long startTime){
        return userRedisTemplate.zrangeByScore(buildStoreAuditShieldUser(), 0D, startTime);
    }

    /**
     * 用户真人认证成功 任务处理
     */
    public void saveRealPersonSuccessTaskDeal(){
        // 如果不是男用户不做处理
        if(SexType.Male != GlobalUtils.sexType()){
            return;
        }
        taskComponent.saveProcessTask(BusiCodeDefine.RealHeadCertification, GlobalUtils.uid(), null);
    }

    /**
     * 用户在签到抽奖活动期间 实名认证、真人认证成功的处理
     */
    public void saveUserAuthSucDealInSignInAwardAct(){
        userSignInComponent.dealAuthSuc(GlobalUtils.uid());
    }

    /**
     * 获取待发送的延时系统通知
     */
    public Set<RedisShardDTO> getWaitSendSystemNotice(int jobShardIndex, int jobShardTotal) {
        return messageComponent.findWaitSendNotice(jobShardIndex, jobShardTotal);
    }

    /**
     * 发送延时系统通知
     */
    public void saveSendDelaySystemNotice(int shardIndex, String value){
        String uuid = GlobalUtils.extValue(BusinessDataKey.DelaySystemNoticeSign);
        if(StringUtils.isEmpty(uuid)){
            return;
        }
        miniLockTemplate.execute(MiniLockType.SendDelaySystemNotice, uuid, () -> {
            // 为避免重复发送，先将通知从队列中移除 移除成功之后再发送消息
            Long zremResule = busiRedisTemplate.zrem(messageComponent.buildSystemNoticeDelayQueue(shardIndex), value);
            if(Objects.isNull(zremResule) || zremResule < 1){
                return null;
            }
            messageComponent.sendMatchMsgs();
            return null;
        });
    }

    /**
     * 处理用户登录
     */
    public void saveDealUserLogin(){
        Long userId = GlobalUtils.extLong(BusinessDataKey.UserId);
        if(Objects.isNull(userId)){
            return;
        }
        UUserExt userExt = userInfoManager.getUserExt(userId);
        if(Objects.isNull(userExt)){
            return;
        }
        Long diffDays = DateUtils.getDiffDays(userExt.getLastOpenTime(), new Date());
        if(diffDays < 15){
            return;
        }
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        NoticeSysType noticeSysType = SexType.Female == userBasic.getSex() ? NoticeSysType.FeMaleUserLogin15 : NoticeSysType.MaleUserLogin15;
        noticeComponent.sendSystemNotice(userId, noticeSysType);
    }

    /**
     * 用户登录 延时注销处理
     */
    public void saveDestroyDealForLogin(){
        Long userId = GlobalUtils.extLong(BusinessDataKey.UserId);
        if(Objects.isNull(userId)){
            return;
        }
        Pair<Boolean, Long> destroyInfo = userAccountManager.getUserAccountDestroyInfo(userId);
        if(destroyInfo.getFirst()){
            String content = MsgUtils.format("账号将于{}完成注销，现在登录将视为放弃注销!", DateUtils.toString(new Date(destroyInfo.getSecond())));
            messageComponent.sendDestroyRemind(userId, content);
        }
    }

    /**
     * 处理用户注册昵称、头像校验
     */
    public void saveRegisterNicknameHeadPicCheck() {
        Long userId = GlobalUtils.extLong(BusinessDataKey.UserId);
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        if (Objects.isNull(userBasic)) {
            return;
        }
        userLockTemplate.acquireTransactionLock(userId);
        boolean needUpdate = false;
        boolean noticeSysType1 = false;
        boolean noticeSysType2 = false;
        UUserBasic update = new UUserBasic(userId);
        // 用户昵称合法性检查
        Pair<TextValidResult, Boolean> pair = userCheckComponent.checkUserNicknameValid(userBasic.getNickname(), userBasic.getUserId(), UserType.Viewer, userBasic.getSex());
        if (!pair.getSecond()) { // 如果是默认昵称则不需要检查
            if (pair.getFirst().getFilterType() != ChatFilterType.Pass) {
                needUpdate = true;
                update.setNickname(userInfoManager.getRandomNickName(userBasic.getUserId(), userBasic.getSex()));
                noticeSysType1 = true;
            } else if (userInfoManager.nicknameIsExists(userId, userBasic.getNickname())) {
                needUpdate = true;
                update.setNickname(userInfoManager.getRandomNickName(userBasic.getUserId(), userBasic.getSex()));
                noticeSysType2 = true;
            }
        }
        // 用户头像合法性检查
        if (BoolType.True == GlobalUtils.extEnum(BusinessDataKey.NeedCheckHeadPic, BoolType.class)) {
            needUpdate = true;
            boolean checkResult = userCheckComponent.checkUserHeadPicValid(userBasic.getUserId(), userBasic.getSex(), userBasic.getHeadPic());
            if (!checkResult) {
                update.setHeadPic(picManager.getRandomPics(userBasic.getSex()));
                update.setHeadStatus(HeadStatus.DeFault);
                noticeSysType1 = true;
            } else {
                update.setHeadStatus(HeadStatus.Pass);
            }
        }
        // 修改信息
        if (needUpdate) {
            userBusiComponent.updateUserBasic(update);
        }
        // 发送系统通知
        if (noticeSysType1) {
            noticeComponent.sendSystemNotice(userId, NoticeSysType.UserRegisterInfoViolation);
        }
        if (noticeSysType2) {
            noticeComponent.sendSystemNotice(userId, NoticeSysType.UserRegisterNicknameRepeat);
        }
    }

    /**
     * 处理用户注册 给邀请人发送注册成功通知
     */
    public void saveSendNotifyToInvitor(){
        Long userId = GlobalUtils.extLong(BusinessDataKey.UserId);
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        if (Objects.isNull(userBasic)) {
            return;
        }
        Long invitorUserId = GlobalUtils.extLong(BusinessDataKey.InviteUserId);
        if(Objects.isNull(invitorUserId)){
            return;
        }
        // 发送通知
        // 发送通知
        noticeComponent.sendSystemNotice(invitorUserId, NoticeSysType.InviteSuccess, MapUtils.gmap("nickname", userBasic.getNickname(),
                "overrideTouchType", ClientTouchType.Url.getId(), "overrideTouchValue", HtmlUrlUtils.getShareUrl(), "userId", userId));
    }

    /**
     * 处理用户注册 根据IP获取初始位置信息
     */
    public void saveUserRegIpAddress(){
        Long userId = GlobalUtils.extLong(BusinessDataKey.UserId);
        LocationDTO locationDto = aliyunIpSupport.getAddressByIp(GlobalUtils.clientIp());
        if(Objects.isNull(locationDto)){
            return;
        }
        StringBuilder addressStr = new StringBuilder();
        if(StringUtils.isNotEmpty(locationDto.getProvince())){
            addressStr.append(locationDto.getProvince());
            addressStr.append("·");
        }
        if(StringUtils.isNotEmpty(locationDto.getCity())){
            addressStr.append(locationDto.getCity());
            addressStr.append("·");
        }
        if(StringUtils.isNotEmpty(locationDto.getDistrict())){
            addressStr.append(locationDto.getDistrict());
            addressStr.append("·");
        }
        String finalAddressStr = addressStr.toString();
        if(StringUtils.isEmpty(finalAddressStr)){
            return;
        }
        UUserExt update = new UUserExt(userId);
        update.setIpCity(finalAddressStr.substring(0, finalAddressStr.length() - 1));
        update.setCity(locationDto.getCity());
        userInfoManager.updateUserExt(update);
        // 保存最开始的location数据
        UUserLocation location = new UUserLocation(userId);
        location.setLat(locationDto.getLat());
        location.setLng(locationDto.getLng());
        location.setProvince(locationDto.getProvince());
        location.setCity(locationDto.getCity());
        location.setDistrict(locationDto.getDistrict());
        location.setUpdateTime(new Date());
        userInfoManager.saveOrUpdateUserLocation(location);
    }

    /**
     * 男用户放入视频速配折扣券待发放队列
     */
    @MQInvoke
    public void saveMaleJoinVideoMatchTicketQueue(){
        Long userId = GlobalUtils.extLong(BusinessDataKey.UserId);
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        if (Objects.isNull(userBasic) || SexType.Male != userBasic.getSex()) {
            return;
        }
        // 放入队列 15 秒后发放
        userOperateComponent.addVideoMatchTicketWaitSendQueue(userId, DateUtils.plusSeconds(new Date(), 15));
    }

    /**
     * 处理用户注册奖励
     */
    public void saveRegisterAward() {
        Long userId = GlobalUtils.extLong(BusinessDataKey.UserId);
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        if (Objects.isNull(userBasic)) {
            return;
        }
        // 审核版本不发送奖励
        if(newsManager.isAndroidReviewVersion(GlobalUtils.clientType(), GlobalUtils.clientVersion(), GlobalUtils.channelId(), GlobalUtils.packageType(), userId)){
            return;
        }

        // 判断用户IP是否受发注册奖励限制
        if(SettingsConfig.getString(SettingsType.MaliceRegisterIp).contains(GlobalUtils.clientIp())){
            return;
        }
        // 设备检查，同时也起到幂等检查 避免MQ重复推送 发放多次的情况
        String uniSign = MsgUtils.format(GlobalUtils.packageType().getMaleMdSign(), GlobalUtils.deviceUuid());
        if (!busiManager.saveIdempotentMapper(uniSign, new Date())) {
            return;
        }

        // 男
        if (SexType.Male == userBasic.getSex()) {
            // 发放奖励1
            grantComponent.saveGrantPlatformBeans(FixedAward.MaleUserRegister1.getAwardCode(), userId, Integer.valueOf(FixedAward.MaleUserRegister1.getAwardValue()), FixedAward.MaleUserRegister1.getDesc());
            // 发送奖励2
            grantComponent.saveCombineAutoGrant(userId, VariableAwardDTO.create(FixedAward.MaleUserRegister2.getAwardCode(), "聊天券奖励", 11, AwardType.SocialGoods, Long.valueOf(FixedAward.MaleUserRegister2.getAwardValue()), 0, null, null, null));
            // 发送奖励3
            grantComponent.saveCombineAutoGrant(userId, VariableAwardDTO.create(FixedAward.MaleUserRegister3.getAwardCode(), "视频免费券奖励", 1, AwardType.SocialGoods, Long.valueOf(FixedAward.MaleUserRegister3.getAwardValue()), 0, null, null, null));

            // 发送系统通知  文案：欢迎来到【${app}】，送您${awardInfo},祝您早日淘到心仪的Ta。
            Map<String, Object> params = new HashMap<>();
            params.put("app", GlobalUtils.packageType().getDesc());
            params.put("awardInfo", "220金币、11张聊天卡、1张免费视频券");
            noticeComponent.sendSystemNotice(userId, NoticeSysType.MaleUserRegister, params);

            List<AwardMsgDTO> awardMsgDTOS = new ArrayList<>();
            awardMsgDTOS.add(new AwardMsgDTO("金币x220", IconConstant.REG_AWARD_BEAN, null));
            awardMsgDTOS.add(new AwardMsgDTO("聊天卡x11", SocialGoodsDefine.ChatTicket.getGoodsPic(), null));
            awardMsgDTOS.add(new AwardMsgDTO("视频券x1", SocialGoodsDefine.VideoTicket60.getGoodsPic(), null));
            messageComponent.sendRegisterAwardPop(awardMsgDTOS, userId);
        }
    }

    /**
     * 获取签到提醒用户
     */
    public List<String> getSignInRemindUsers(Integer shardIndex, Integer shardTotal){
        List<Integer> shardIndexes = RedisShardUtils.getSharedIndexes(shardIndex, shardTotal, RedisShardType.UserSignInRemind);
        if (ListUtils.isEmpty(shardIndexes)) {
            return null;
        }
        Date now = DateUtils.nowTime();
        String minStr = DateUtils.toString(now, DatePattern.HM);
        String maxStr = DateUtils.toString(DateUtils.plusMinutes(now, 30), DatePattern.HM);
        Double min = Double.valueOf(minStr.replace(":", "."));
        Double max = Double.valueOf(maxStr.replace(":", "."));
        List<String> userIds = new ArrayList<>();
        for (Integer item : shardIndexes) {
            RedisKey redisKey = RedisKey.create(BusiKeyDefine.UserSignInRemindQueue, item);
            List<String> member = busiRedisTemplate.zrangeByScore(redisKey, min, max);
            if (ListUtils.isNotEmpty(member)) {
                userIds.addAll(member);
            }
        }
        return userIds;
    }

    /**
     * 处理用户签到提醒
     */
    public void saveDealUserSignInRemind(String member, Date signInDate){
        Long userId = Long.valueOf(member);
        // 判断用户当日是否已经签到
        if(userSignInComponent.checkUserSignInToday(userId, signInDate)){
            return;
        }
        // 判断用户是否开启签到提醒
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        if(Objects.isNull(userBasic) || Status.Enable != userBasic.getStatus()){
            // 移除提醒队列
            userSignInComponent.removeSignInRemindQueue(userId);
            return;
        }
        UMessageSetting setting = messageSettingManager.getSetting(userId);
        if (Objects.isNull(setting) || BoolType.True != setting.getSignRemind()) {
            userSignInComponent.removeSignInRemindQueue(userId);
            return;
        }
        // 发送提醒签到系统通知
        noticeComponent.sendSystemNotice(userId, NoticeSysType.UserSignInRemind);
    }

    /**
     * 提交头像至人工审核
     *
     * @param user
     */
    public void reviewUserInfo(UUserBasic user) {
        // 审核头像
        if (HeadStatus.WaitArtificial == user.getHeadStatus()) {
            YidunSupport.submitUser(YidunReviewType.HeadPic, user.getUserId(), user.getUserId(), user.getHeadPic());
        }
    }

    /**
     * 获取待统计数据
     */
    public List<UIdentityAuth> getWaitStatisticUsers(int offset, int limit){
        return userInfoManager.getGroupBlackListStatisticUsers(offset, limit);
    }

    /**
     * 统计用户手机号身份证号在集团黑名单的信息
     */
    public void saveUserGroupBlackListInfo(UIdentityAuth identityAuth){
        UserGroupBlackListResult mobileIn = UserGroupBlackListResult.UnKnow;
        UserGroupBlackListResult idNumIn = UserGroupBlackListResult.UnKnow;
        // 先判断手机号
        if(StringUtils.isNotEmpty(identityAuth.getMobile())){
            boolean mobile = userBlackListComponent.isGroupBlack(identityAuth.getMobile(), null);
            mobileIn = mobile ? UserGroupBlackListResult.In : UserGroupBlackListResult.NotIn;
        }
        // 在判断身份证号
        if(StringUtils.isNotEmpty(identityAuth.getIdentityNo())){
            String decryptCertNum = BusiUtils.decrypt(identityAuth.getIdentityNo());
            boolean idNum = userBlackListComponent.isGroupBlack(null, decryptCertNum);
            idNumIn = idNum ? UserGroupBlackListResult.In : UserGroupBlackListResult.NotIn;
        }
        UUserBlacklistStatistic info = new UUserBlacklistStatistic(identityAuth.getUserId());
        info.setMobileIn(mobileIn);
        info.setIdnumIn(idNumIn);
        userBlacklistStatisticMapper.insert(info);
    }

    /**
     * 获取因聊天触发违禁词待封禁用户
     */
    public List<String> queryFemaleHitKwBans(){
        RedisKey redisKey = buildKeywordBanUserKey();
        return busiRedisTemplate.zrangeByScore(redisKey, 0, System.currentTimeMillis());
    }

    /**
     * 读取队列数据进行封号
     */
    public void saveUserIllegalKeyword(String value){
        Long userId = Long.valueOf(value);
        // 移除待封禁队列
        CallbackAfterTransactionUtil.send(() -> {
            busiRedisTemplate.zrem(buildKeywordBanUserKey(), value);
        });
        String detail = userOperateComponent.getUserHotKwDetail(userId);
        if(StringUtils.isEmpty(detail)){
            return;
        }
        //先判断封号开关是否开启
        boolean checkOpen = SettingsConfig.getBoolean(SettingsType.OpenBanUserIllegalWord);
        if (!checkOpen) {
            // 插入记录数据
            busiRedisTemplate.sadd(buildIllegalKeyWordDataRecord(), detail);
            return;
        }
        // 拿到配置中需要封禁的用户等级
        ChatMasterLevel chatMasterLevel = chatMasterManager.getChatMasterLevel(userId);
        boolean contain = StringUtils.containsTarget(SettingsConfig.getString(SettingsType.IllegalWordBanUserLevel), chatMasterLevel.getId());
        if (!contain) {
            busiRedisTemplate.sadd(buildIllegalKeyWordDataRecord(), detail);
            return;
        }
        UrlParamsMap build = UrlParamsMap.build(detail);
        ProxyExecutors.doProxy(BusiCodeDefine.BanUser, (context) -> {
            userOperateComponent.banUser(GlobalConstant.SYS_USER_ID, userId,"keyWordBlock",null, build.getInt("s"),"存在不良社交行为，恶意扰乱平台秩序，影响平台环境",null,"PrivateChat","Unhealthy","05","账号封禁"+ (build.getInt("s") == 86400 ? "1天": "永久"),"触发关键词脚本封禁处理:"+build.getString("w"));
            return null;
        });
        // 封禁次数加一
        int markValue = 1;
        UUserKeyMark keyMark = userKeyMarkManager.getKeyMark(userId, UserBusiKeyMark.FemaleChatHitKwBan);
        if(Objects.nonNull(keyMark)){
            markValue = markValue + Integer.parseInt(keyMark.getBusiValue());
        }
        userKeyMarkManager.saveOrUpdateUserMark(userId, UserBusiKeyMark.FemaleChatHitKwBan, String.valueOf(markValue));
        CallbackAfterTransactionUtil.send(() -> {
            // 强制用户下线
            redisSessionManager.sessionUnbind(userId);
        });
    }

    /**
     * 发放注册奖励--社交物品类
     */
    private void senRegisterAwardGoods(Map<String, Object> params, Long userId, String awardCode) {
        List<TAwardDetail> details = tAwardDetailMapper.selectByCode(new TAwardDetail(awardCode));
        for (TAwardDetail detail : details) {
            TAward a = new TAward();
            a.setAboutBeans(0);
            a.setCode(AwardGrantType.AutoGrant.name());
            a.setName(AwardGrantType.AutoGrant.getDesc());
            userLockTemplate.acquireTransactionLock(userId);
            Grant impl = grantSetImpl.get(detail.getAwardType());
            impl.doGrant(AwardGrantType.AutoGrant, userId, a, detail, 1, false, "新人注册礼包");
            SocialGoodsDefine good = SocialGoodsDefine.getByGoodId(Integer.valueOf(detail.getAwardTypeValue()));
            if (null != good) {
                params.put(good.name(), detail.getCount());
            }
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ? extends Grant> grantImpl = applicationContext.getBeansOfType(Grant.class);
        for (Grant impl : grantImpl.values()) {
            grantSetImpl.put(impl.getSupportType(), impl);
        }
    }

    private RedisKey buildKeywordBanUserKey(){
        return RedisKey.create(BusiKeyDefine.IllegalKeyWordBanQueue);
    }

    private RedisKey buildIllegalKeyWordDataRecord(){
        return RedisKey.create(BusiKeyDefine.IllegalKeyWordDataRecord);
    }

    private RedisKey buildStoreAuditShieldUser() {
        return RedisKey.create(UserKeyDefine.StoreAuditShieldUser);
    }
}
