package com.tuowan.yeliao.user.remote;

import com.alibaba.nacos.common.utils.Pair;
import com.tuowan.yeliao.commons.context.config.HeaderType;
import com.tuowan.yeliao.commons.context.config.Request;
import com.tuowan.yeliao.commons.context.request.UserIdRequest;
import com.tuowan.yeliao.commons.core.http.ReturnUtils;
import com.tuowan.yeliao.commons.core.http.Root;
import com.tuowan.yeliao.social.api.remote.SocialWebRemote;
import com.tuowan.yeliao.user.api.remote.UserWebRemote;
import com.tuowan.yeliao.user.api.request.ChannelCodeRequest;
import com.tuowan.yeliao.user.api.request.QueryIpCityRequest;
import com.tuowan.yeliao.user.api.response.QueryIpCityResponse;
import com.tuowan.yeliao.user.comp.UserOptComponent;
import com.tuowan.yeliao.user.service.acct.AuthService;
import com.tuowan.yeliao.user.service.user.UserInfoService;
import com.tuowan.yeliao.user.service.user.UserUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户WEB服务远程接口实现
 *
 * <AUTHOR>
 * @date 2022/4/12 21:34
 */
@RestController
public class UserWebRemoteImpl implements UserWebRemote {

    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private UserOptComponent userOptComponent;
    @Autowired
    private UserUpdateService userUpdateService;
    @Autowired
    private SocialWebRemote socialWebRemote;
    @Autowired
    private AuthService authService;

    @Override
    @Request(header = HeaderType.Whitelist)
    public Root<QueryIpCityResponse> queryIpCity(QueryIpCityRequest request) {
        Pair<String, String> ipAreaInfo = userOptComponent.getIpAreaInfo(request.getIp());
        QueryIpCityResponse response = new QueryIpCityResponse();
        response.setProvince(ipAreaInfo.getFirst());
        response.setCity(ipAreaInfo.getSecond());
        return ReturnUtils.root(response);
    }

    @Override
    @Request(header = HeaderType.Whitelist)
    public Root<Void> updateChannelCode(ChannelCodeRequest request) {
        boolean needOpt = userUpdateService.updateUserChannelInfo(request.getUserId(), request.getSexType(), request.getChannelId(), request.getChannelCode());
        // social 模块操作
        if(needOpt){
            socialWebRemote.releaseStoreAuditShield(UserIdRequest.build(request.getUserId()));
        }
        return ReturnUtils.empty();
    }
}
