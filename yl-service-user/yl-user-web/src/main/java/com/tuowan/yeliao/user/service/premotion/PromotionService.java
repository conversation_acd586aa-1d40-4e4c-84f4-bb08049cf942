package com.tuowan.yeliao.user.service.premotion;

import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.log.api.remote.LogWebRemote;
import com.easyooo.framework.common.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 推广业务封装
 *
 * <AUTHOR>
 * @date 2022/2/8 17:58
 */
@Service
public class PromotionService {
    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private LogWebRemote logWebRemote;

    /**
     * 处理应用注册
     * <p>
     * 保存设备注册的用户
     */
    public void saveProcessAppRegister() {
        String deviceUuid = GlobalUtils.deviceUuid();
        if (StringUtils.isEmpty(deviceUuid)) {
            return;
        }
        /*objectLockTemplate.acquireTransactionLock(ObjectLockType.ClientActive, deviceUuid);
        String channelCode = GlobalUtils.extValue(BusinessDataKey.ChannelCode);
        CClientActive active = cClientActiveMapper.selectByDeviceUuid(deviceUuid);
        if (active == null) {
            active = buildClientActive(deviceUuid);
            if (StringUtils.isNotEmpty(channelCode)) {
                active.setChannelCode(channelCode);
            }
            active.setRegisterUserId(GlobalUtils.uid());
            active.setRegisterTime(new Date());
            cClientActiveMapper.insert(active);
            return;
        }
        // 如果当前设备已有归因渠道且已关联注册用户，则忽略
        if (StringUtils.isNotEmpty(active.getChannelCode()) && active.getRegisterUserId() != null) {
            return;
        }
        CClientActive update = new CClientActive(active.getLogId(), active.getCreateTime());
        boolean needUpdate = false;
        if (StringUtils.isEmpty(active.getChannelCode()) && StringUtils.isNotEmpty(channelCode)) {
            update.setChannelCode(channelCode);
            needUpdate = true;
        }
        if (active.getRegisterUserId() == null) {
            update.setRegisterUserId(GlobalUtils.uid());
            update.setRegisterTime(new Date());
            needUpdate = true;
        }
        if (needUpdate) {
            cClientActiveMapper.updateByPrimaryKeySelective(update);
        }*/
    }

}
