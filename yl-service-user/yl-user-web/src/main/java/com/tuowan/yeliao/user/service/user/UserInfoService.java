package com.tuowan.yeliao.user.service.user;

import com.alibaba.nacos.common.utils.Pair;
import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.extend.SimpleMap;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.acct.api.remote.AcctWebRemote;
import com.tuowan.yeliao.acct.api.request.FirstRechargeStatusRequest;
import com.tuowan.yeliao.commons.comp.signin.UserSignInComponent;
import com.tuowan.yeliao.commons.comp.signin.dto.NowRoundSignInfoDTO;
import com.tuowan.yeliao.commons.comp.signin.dto.SignInDayInfoDTO;
import com.tuowan.yeliao.commons.comp.user.ExpComponent;
import com.tuowan.yeliao.commons.comp.user.UserCommonComponent;
import com.tuowan.yeliao.commons.comp.user.UserSocialBagComponent;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.context.GlobalDataUtils;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.context.request.UserIdRequest;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.SocialGoodsDefine;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.general.ReviewStatus;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.InternalException;
import com.tuowan.yeliao.commons.core.http.Root;
import com.tuowan.yeliao.commons.data.dto.Session;
import com.tuowan.yeliao.commons.data.dto.user.UserBusiDTO;
import com.tuowan.yeliao.commons.data.entity.config.TLevel;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGoods;
import com.tuowan.yeliao.commons.data.entity.user.*;
import com.tuowan.yeliao.commons.data.enums.config.IncomeType;
import com.tuowan.yeliao.commons.data.enums.config.LevelType;
import com.tuowan.yeliao.commons.data.enums.user.*;
import com.tuowan.yeliao.commons.data.manager.config.AdManager;
import com.tuowan.yeliao.commons.data.manager.config.NewsManager;
import com.tuowan.yeliao.commons.data.manager.config.SocialProdManager;
import com.tuowan.yeliao.commons.data.manager.user.MessageSettingManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.manager.user.UserKeyMarkManager;
import com.tuowan.yeliao.commons.data.manager.user.UserVisitManager;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.data.utils.HtmlUrlUtils;
import com.tuowan.yeliao.commons.web.common.form.FriendIdForm;
import com.tuowan.yeliao.commons.web.common.vo.BoolTypeVO;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.commons.web.session.RedisSessionManager;
import com.tuowan.yeliao.social.api.remote.SocialWebRemote;
import com.tuowan.yeliao.social.api.request.HomePageRequest;
import com.tuowan.yeliao.social.api.request.IntimateRequest;
import com.tuowan.yeliao.social.api.response.HomePageResponse;
import com.tuowan.yeliao.social.api.response.IntimateResponse;
import com.tuowan.yeliao.social.api.response.MyInfoResponse;
import com.tuowan.yeliao.user.comp.UserTaskComponent;
import com.tuowan.yeliao.user.data.dto.MediaPicDTO;
import com.tuowan.yeliao.user.data.dto.MediaVideoDTO;
import com.tuowan.yeliao.user.data.dto.MediaVoiceDTO;
import com.tuowan.yeliao.user.data.entity.UUserWxApply;
import com.tuowan.yeliao.user.data.manager.query.QueryManager;
import com.tuowan.yeliao.user.data.manager.user.CustomerManager;
import com.tuowan.yeliao.user.data.manager.user.UserCommonManager;
import com.tuowan.yeliao.user.data.manager.user.UserTagManager;
import com.tuowan.yeliao.user.data.manager.user.UserWxManager;
import com.tuowan.yeliao.user.service.async.AsyncService;
import com.tuowan.yeliao.user.web.form.user.info.*;
import com.tuowan.yeliao.user.web.vo.signIn.SignInInfoVO;
import com.tuowan.yeliao.user.web.vo.user.*;
import com.tuowan.yeliao.user.web.vo.user.info.UserSimpleVO;
import com.tuowan.yeliao.user.web.vo.user.info.WxAuthCenterVO;
import com.tuowan.yeliao.user.web.vo.user.info.WxInfoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserInfoService {
    private static Logger LOG = LoggerFactory.getLogger(UserInfoService.class);

    @Autowired
    private AdManager adManager;
    @Autowired
    private AcctWebRemote acctWebRemote;
    @Autowired
    private SocialWebRemote socialWebRemote;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private ExpComponent expComponent;
    @Autowired
    private UserTagManager userTagManager;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private UserVisitManager userVisitManager;
    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private UserKeyMarkManager userKeyMarkManager;
    @Autowired
    private NewsManager newsManager;
    @Autowired
    private UserSignInComponent userSignInComponent;
    @Autowired
    private UserTaskComponent userTaskComponent;
    @Autowired
    private RedisSessionManager redisSessionManager;
    @Autowired
    private CustomerManager customerManager;
    @Autowired
    private MessageSettingManager messageSettingManager;
    @Autowired
    private QueryManager queryManager;
    @Autowired
    private UserWxManager userWxManager;
    @Autowired
    private UserSocialBagComponent userSocialBagComponent;
    @Autowired
    private SocialProdManager socialProdManager;
    @Autowired
    private UserCommonManager userCommonManager;
    @Autowired
    private UserCommonComponent userCommonComponent;

    /**
     * 我的信息
     *
     * @return
     */
    @BusiCode
    public UserMyInfoVO getUserMyInfo() {
        //  获取用户基本信息
        UUserBasic basic = userInfoManager.getUserBasic(GlobalUtils.uid());
        UUserLevel level = userInfoManager.getUserLevel(GlobalUtils.uid());
        UMessageSetting setting = messageSettingManager.getSetting(GlobalUtils.uid());
        // 是否是审核版本
        boolean androidReviewVersion = newsManager.isAndroidReviewVersion(GlobalUtils.clientType(), GlobalUtils.clientVersion(), GlobalUtils.channelId(), GlobalUtils.packageType(), GlobalUtils.uid());
        // 返回值构建
        UserMyInfoVO userMyInfoVO = new UserMyInfoVO();
        // 用户基本信息
        UserBaseInfoVO baseInfoVO = new UserBaseInfoVO();
        baseInfoVO.setUserId(basic.getUserId());
        baseInfoVO.setNickname(basic.getNickname());
        baseInfoVO.setHeadPic(basic.getHeadPic());
        baseInfoVO.setSex(basic.getSex());
        baseInfoVO.setAge(BusiUtils.getAgeByDate(basic.getBirthDate()));
        baseInfoVO.setRealPerson(basic.getRealPerson());
        baseInfoVO.setRealName(basic.getRealName());
        baseInfoVO.setCharmLevel(level.getCharmLevel());
        baseInfoVO.setUserLevel(level.getUserLevel());
        baseInfoVO.setVisitCnt(userVisitManager.getVisitAddCnt(GlobalUtils.uid()));
        baseInfoVO.setVipType(BusiUtils.getVipType(basic));
        baseInfoVO.setVipExpireTimeText(BusiUtils.getVipExpireTimeText(basic, DatePattern.YMD_HM));
        // 获取关注数、粉丝数、好友数
        Root<MyInfoResponse> myInfoResponse = socialWebRemote.myInfo(UserIdRequest.build(GlobalUtils.uid()));
        if(!myInfoResponse.isFailure()){
            baseInfoVO.setFollowCnt(myInfoResponse.getData().getFollowCnt());
            baseInfoVO.setFansCnt(myInfoResponse.getData().getFansCnt());
        }
        // 用户财富信息
        UserWealthInfoVO userWealthInfoVO = new UserWealthInfoVO();
        userWealthInfoVO.setBean(String.valueOf(GlobalUtils.beans()));
        userWealthInfoVO.setCash(BusiUtils.cashToYuanSimplifyStr(GlobalUtils.cash(), 2));
        userWealthInfoVO.setSilver(String.valueOf(GlobalUtils.silver()));
        userWealthInfoVO.setVoiceFee(String.valueOf(setting.getVoiceFee()));
        userWealthInfoVO.setVideoFee(String.valueOf(setting.getVideoFee()));
        // 获取用户物品信息
        List<UUserBag> bagList = userSocialBagComponent.findUserValidGoods(GlobalUtils.uid());
        if(ListUtils.isEmpty(bagList)){
            // 插入默认的 默认显示视频免费券
            userWealthInfoVO.setGoodsPic(SocialGoodsDefine.VideoTicket60.getGoodsPic());
            userWealthInfoVO.setGoodsNum("0");
        }else{
            Map.Entry<Integer, List<UUserBag>> next = ListUtils.grouping(bagList, UUserBag::getProdId).entrySet().iterator().next();
            TProdSocialGoods goods = socialProdManager.getSocialGood(next.getValue().get(0).getProdId());
            userWealthInfoVO.setGoodsPic(goods.getPic());
            userWealthInfoVO.setGoodsPic(String.valueOf(next.getValue().size()));
        }
        // 邀请地址
        if(!androidReviewVersion){
            userMyInfoVO.setInviteUrl(HtmlUrlUtils.getShareUrl());
        }
        // 签到信息
        userMyInfoVO.setSignInInfoVO(SexType.Male == GlobalUtils.sexType() ? getSignInInfo(GlobalUtils.uid(), GlobalUtils.sexType()) : null);
        userMyInfoVO.setBaseInfoVO(baseInfoVO);
        userMyInfoVO.setUserWealthInfoVO(userWealthInfoVO);
        userMyInfoVO.setCaptivatingGoddessUrl(SexType.Female == GlobalUtils.sexType() ? HtmlUrlUtils.getCaptivatingGoddessUrl(GlobalUtils.packageType()) : null);

        return userMyInfoVO;
    }

    /**
     * 主页信息
     */
    @BusiCode(value = BusiCodeDefine.UserHomePageVisit)
    public UserHomePageVO getHomePageInfo(UserHomeForm form) {
        Long userId = GlobalUtils.uid();
        Long targetUserId = form.getUserId() == null ? userId : form.getUserId();
        // 查询用户基本信息
        UUserBasic userBasic = userInfoManager.getUserBasic(targetUserId);
        // 查询用户等级信息
        UUserLevel userLevel = userInfoManager.getUserLevel(targetUserId);
        // 查询用户更多信息
        UUserMore userMore = userInfoManager.getUserMore(targetUserId);
        // 查询用户定位信息
        UUserLocation location = userInfoManager.getUserLocation(targetUserId);
        // 查询用户视频信息
        Pair<String, String> videoInfo = userInfoManager.getUserVideoInfo(targetUserId);
        // 返回值封装
        UserHomePageVO homePageVO = new UserHomePageVO();
        // 基本信息
        UserBaseInfoVO baseInfoVO = new UserBaseInfoVO();
        baseInfoVO.setUserId(userBasic.getUserId());
        baseInfoVO.setHeadPic(userBasic.getHeadPic());
        baseInfoVO.setNickname(userBasic.getNickname());
        baseInfoVO.setAge(BusiUtils.getAgeByDate(userBasic.getBirthDate()));
        baseInfoVO.setSex(userBasic.getSex());

        baseInfoVO.setConstellationText(Objects.isNull(userMore.getConstellationId()) ? "未知" : userMore.getConstellationId().getDesc());
        baseInfoVO.setVoiceUrl(userInfoManager.getUserVoiceUrl(targetUserId));
        baseInfoVO.setRealPerson(userBasic.getRealPerson());
        baseInfoVO.setVipType(BusiUtils.getVipType(userBasic));
        baseInfoVO.setCharmLevel(messageSettingManager.hasHideLevel(targetUserId) ? null : userLevel.getCharmLevel());
        baseInfoVO.setUserLevel(messageSettingManager.hasHideLevel(targetUserId) ? null : userLevel.getUserLevel());
        baseInfoVO.setMySign(StringUtils.isEmpty(userBasic.getMySign()) ? userBasic.getDefaultSign() : userBasic.getMySign());
        baseInfoVO.setVideoCover(videoInfo.getFirst());
        baseInfoVO.setVideoUrl(videoInfo.getSecond());
        baseInfoVO.setCoverInfos(userInfoManager.getHomePageCovers(targetUserId));
        baseInfoVO.setMyTags(userTagManager.getUserTagList(targetUserId, UserTagType.Character));
        // baseInfoVO.setTaTags(userTagManager.getUserTagList(targetUserId, UserTagType.LoveTa));
        baseInfoVO.setTogetherTags(userTagManager.getUserTagList(targetUserId, UserTagType.Together));
        baseInfoVO.setFootprints(BusiUtils.strToList(userMore.getFootprints(), ","));

        baseInfoVO.setHeight(userMore.getHeight());
        baseInfoVO.setWeight(userMore.getWeight());
        baseInfoVO.setStationCityAddr(Objects.nonNull(location) && !messageSettingManager.hasHideLocation(targetUserId) ? location.getCity() : null); // 现居地址
        //baseInfoVO.setCity(Objects.isNull(location) ? null : location.get); // 当前所在地
        baseInfoVO.setHomeTownAddr(BusiUtils.buildAddr(userInfoManager.getCityById(userMore.getHometownCityId()))); // 家乡
        baseInfoVO.setMarriageStatus(BusiUtils.buildCommonInfoByIDEnum(userMore.getMarriageStatus())); // 婚姻状态
        baseInfoVO.setEducation(BusiUtils.buildCommonInfoByIDEnum(userMore.getEducation())); // 教育
        baseInfoVO.setProfession(userInfoManager.getProfessionName(userMore)); // 职业
        baseInfoVO.setIncome(BusiUtils.buildCommonInfoByIDEnum(userMore.getIncome())); // 年收入
        baseInfoVO.setLiveTogether(BusiUtils.buildCommonInfoByIDEnum(userMore.getLiveTogether())); // 是否接受婚前同居
        baseInfoVO.setAppointment(BusiUtils.buildCommonInfoByIDEnum(userMore.getAppointment())); // 是否接受约会
        baseInfoVO.setLiveStatus(BusiUtils.buildCommonInfoByIDEnum(userMore.getLiveStatus())); // 居住状态
        baseInfoVO.setHouseStatus(BusiUtils.buildCommonInfoByIDEnum(userMore.getHouseStatus()));// 购房状态
        baseInfoVO.setCarStatus(BusiUtils.buildCommonInfoByIDEnum(userMore.getCarStatus()));// 购车状态

        baseInfoVO.setOnlineStatus(userInfoManager.getOnlineStatus(targetUserId, userBasic.getSex()));
        baseInfoVO.setStatus(userBasic.getStatus());
        // 获取关注数、粉丝数
        Root<HomePageResponse> homePageResponse = socialWebRemote.homePage(HomePageRequest.build(userId, targetUserId));
        if(!homePageResponse.isFailure()){
            baseInfoVO.setFollowCnt(homePageResponse.getData().getFollowCnt());
            baseInfoVO.setFansCnt(homePageResponse.getData().getFansCnt());
            homePageVO.setPosts(homePageResponse.getData().getPosts());
            homePageVO.setGifts(homePageResponse.getData().getGifts());
            homePageVO.setFollow(homePageResponse.getData().getFollow());
            homePageVO.setBlack(homePageResponse.getData().getBlack());
            homePageVO.setChatUp(homePageResponse.getData().getChatUpInToday());
            homePageVO.setChatRoomDTO(homePageResponse.getData().getChatRoomDTO());
            homePageVO.setMgwRankUsersHeadPic(homePageResponse.getData().getMgwRankUsersHeadPic());
            if(StringUtils.isNotEmpty(homePageResponse.getData().getFriendNotes())){
                baseInfoVO.setNickname(homePageResponse.getData().getFriendNotes());
            }
        }
        homePageVO.setBaseInfoVO(baseInfoVO);
        if(SexType.Female == userBasic.getSex() && !userId.equals(targetUserId) && StringUtils.isNotEmpty(userMore.getWxQr()) && messageSettingManager.hasLookWx(targetUserId)){
            homePageVO.setWxLookUrl(HtmlUrlUtils.getWxLookUrl(targetUserId));
        }

        // 异步处理主页访问（自己访问自己不错处理）
        if(!userId.equals(targetUserId)){
            asyncService.saveHomePageVisit(GlobalUtils.uid(), targetUserId);
        }

        return homePageVO;
    }

    /**
     * 颜照库信息
     */
    @BusiCode
    public UserMediaVO getUserMedia(FriendIdForm form){
        Long friendId = form.getFriendId();
        boolean vip = BusiUtils.isVip(userInfoManager.getUserBasic(GlobalUtils.uid()));
        // 返回值构建
        UserMediaVO vo = new UserMediaVO();
        // 语音
        List<UUserMedia> voices = userInfoManager.getUserMedias(friendId, MediaType.CommonVoice);
        if(ListUtils.isNotEmpty(voices)){
            UUserMedia media = voices.get(0);
            vo.setVoice(MediaVoiceDTO.build1(media.getRecordId(), media.getMediaValue()));
        }
        // 封面
        List<UUserMedia> covers = userInfoManager.getUserMedias(friendId, MediaType.CommonCover);
        if(ListUtils.isNotEmpty(covers)){
            UUserMedia media = covers.get(0);
            vo.setCover(MediaPicDTO.build2(media.getRecordId(), media.getMediaValue()));
        }
        // 视频
        List<UUserMedia> videos = userInfoManager.getUserMedias(friendId, MediaType.CommonVideo);
        if(ListUtils.isNotEmpty(videos)){
            UUserMedia media = videos.get(0);
            SimpleMap extMap = JsonUtils.toSimpleMap(media.getMediaExt());
            vo.setVideo(MediaVideoDTO.build1(media.getRecordId(), extMap.getString("videoCover"), media.getMediaValue()));
        }
        // 普通照片
        List<UUserMedia> pics = userInfoManager.getUserMedias(friendId, MediaType.CommonPic);
        if(ListUtils.isNotEmpty(pics)){
            vo.setPics(pics.stream().map(m -> MediaPicDTO.build2(m.getRecordId(), m.getMediaValue())).collect(Collectors.toList()));
        }
        // 收费照片
        List<UUserMedia> chargePics = userInfoManager.getUserMedias(friendId, MediaType.ChargePic);
        if(ListUtils.isNotEmpty(chargePics)){
            // 搜索我解锁的付费照片合集
            Set<String> unLockMediaIds = userCommonManager.getUserUnlockChargeMedia(GlobalUtils.uid());
            vo.setChargePics(chargePics.stream().map(m -> MediaPicDTO.build1(m.getRecordId(), m.getMediaValue(), BoolType.valueOf(vip || unLockMediaIds.contains(m.getRecordId().toString())))).collect(Collectors.toList()));
        }
        // 动态照片 备注：动态照片我们是从缓存中获取
        List<String> postPics = userCommonComponent.queryUserLatelyPostPics(friendId);
        if(ListUtils.isNotEmpty(postPics)){
            vo.setPostPics(postPics.stream().map(m -> MediaPicDTO.build2(GlobalConstant.ROOT_ID, m)).collect(Collectors.toList()));
        }

        return vo;
    }

    /**
     * 我的资料
     * 编辑资料 数据回显使用
     */
    @BusiCode
    public UserBaseInfoVO getDataInfo() {
        Long userId = GlobalUtils.uid();
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        UUserMore userMore = userInfoManager.getUserMore(userId);
        UserBaseInfoVO baseInfoVO = new UserBaseInfoVO();

        baseInfoVO.setUserId(userBasic.getUserId());
        baseInfoVO.setHeadPic(userBasic.getHeadPic());
        baseInfoVO.setNickname(userBasic.getNickname());
        baseInfoVO.setBirthDay(DateUtils.toString(userBasic.getBirthDate(), DatePattern.YMD));
        baseInfoVO.setSex(userBasic.getSex());

        baseInfoVO.setHeight(userMore.getHeight());
        baseInfoVO.setWeight(userMore.getWeight());
        baseInfoVO.setHomeTownAddr(BusiUtils.buildAddr(userInfoManager.getCityById(userMore.getHometownCityId()))); // 家乡
        baseInfoVO.setMarriageStatus(BusiUtils.buildCommonInfoByIDEnum(userMore.getMarriageStatus())); // 婚姻状态
        baseInfoVO.setEducation(BusiUtils.buildCommonInfoByIDEnum(userMore.getEducation())); // 教育
        baseInfoVO.setProfession(userInfoManager.getProfessionName(userMore)); // 职业
        baseInfoVO.setIncome(BusiUtils.buildCommonInfoByIDEnum(userMore.getIncome())); // 年收入
        baseInfoVO.setLiveTogether(BusiUtils.buildCommonInfoByIDEnum(userMore.getLiveTogether())); // 是否接受婚前同居
        baseInfoVO.setAppointment(BusiUtils.buildCommonInfoByIDEnum(userMore.getAppointment())); // 是否接受约会
        baseInfoVO.setLiveStatus(BusiUtils.buildCommonInfoByIDEnum(userMore.getLiveStatus())); // 居住状态
        baseInfoVO.setHouseStatus(BusiUtils.buildCommonInfoByIDEnum(userMore.getHouseStatus()));// 购房状态
        baseInfoVO.setCarStatus(BusiUtils.buildCommonInfoByIDEnum(userMore.getCarStatus()));// 购车状态

        baseInfoVO.setMyTags(userTagManager.getUserTagList(userId, UserTagType.Character));
        // baseInfoVO.setTaTags(userTagManager.getUserTagList(userId, UserTagType.LoveTa));
        baseInfoVO.setTogetherTags(userTagManager.getUserTagList(userId, UserTagType.Together));
        baseInfoVO.setFootprints(BusiUtils.strToList(userMore.getFootprints(), ","));
        return baseInfoVO;
    }

    /**
     * 用户等级信息
     *
     * @return
     */
    @BusiCode
    public UserLevelVO getUserLevelInfo(UserLevelForm form) {
        Long userId = form.getTargetUserId() == null ? GlobalUtils.uid() : form.getTargetUserId();
        UserBusiDTO busi = GlobalDataUtils.busi(userId);
        // 查询满级（用户等级、魅力等级）
        Integer topLevel = 20;

        // 用户当前等级（返回null代表用户已到达顶级）
        TLevel currentUserLevel = expComponent.getUserLevel(busi.getUserLevel(), LevelType.User);
        TLevel currentCharmLevel = expComponent.getUserLevel(busi.getCharmLevel(), LevelType.Charm);

        // 用户下一等级（返回null代表用户已到达顶级）
        TLevel nextUserLevel = expComponent.getNextUserLevel(busi.getUserLevel(), LevelType.User);
        TLevel nextCharmLevel = expComponent.getNextUserLevel(busi.getCharmLevel(), LevelType.Charm);

        // 封装返回值
        UserLevelVO vo = new UserLevelVO();
        // 用户信息
        vo.setUserId(busi.getUserId());
        vo.setNickname(busi.getNickname());
        vo.setHeadPic(busi.getHeadPic());

        // 财富等级
        vo.setUserLevel(busi.getUserLevel());
        vo.setUserNextLevel(busi.getUserLevel());
        vo.setUserExpSpan(0L);
        vo.setNeedUserExp(0L);
        if (!topLevel.equals(busi.getUserLevel()) && currentUserLevel != null && nextUserLevel != null) {
            vo.setUserExpSpan(nextUserLevel.getExpLowerLimit() - currentUserLevel.getExpLowerLimit());
            vo.setNeedUserExp(nextUserLevel.getExpLowerLimit() - busi.getUserExp());
            vo.setUserNextLevel(nextUserLevel.getLevelValue());
        }

        // 魅力等级
        vo.setCharmLevel(busi.getCharmLevel());
        vo.setCharmNextLevel(busi.getCharmLevel());
        vo.setCharmExpSpan(0L);
        vo.setNeedCharmExp(0L);
        if (!topLevel.equals(busi.getCharmLevel()) && currentCharmLevel != null && nextCharmLevel != null) {
            vo.setCharmExpSpan(nextCharmLevel.getExpLowerLimit() - currentCharmLevel.getExpLowerLimit());
            vo.setNeedCharmExp(nextCharmLevel.getExpLowerLimit() - busi.getCharmExp());
            vo.setCharmNextLevel(nextCharmLevel.getLevelValue());
        }

        vo.setUserLevelTop(BoolType.valueOf(topLevel.equals(busi.getUserLevel())));
        vo.setCharmLevelTop(BoolType.valueOf(topLevel.equals(busi.getCharmLevel())));
        return vo;
    }

    /**
     * 获取用户验证信息
     */
    @BusiCode
    public UserAuthInfoVO getUserAuthInfo() {
        Long userId = GlobalUtils.uid();
        userLockTemplate.acquireTransactionLock(userId);
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        UIdentityAuth identityAuth = userInfoManager.getUserIdentityAuth(userId);
        // 封装返回值
        UserAuthInfoVO vo = new UserAuthInfoVO();
        vo.setRealNameStatus(userBasic.getRealName());
        vo.setRealHeadStatus(identityAuth.getRealPeopleStatus());
        if(SexType.Female == GlobalUtils.sexType()){
            UUserWxApply wxApply = userWxManager.getUserWxApply(userId);
            vo.setRealWxStatus(Objects.isNull(wxApply) ? null : wxApply.getAuditStatus());
            vo.setWxAuthCenterUrl(HtmlUrlUtils.getWxAuthCenterUrl(GlobalUtils.packageType()));
        }
        return vo;
    }

    /**
     * 获取用户基础信息
     *
     * @param form
     * @return
     */
    @BusiCode
    public UserSimpleVO getSimpleInfo(UserSimpleQueryForm form) {
        Long userId = form.getUserId();
        UUserBasic basic = userInfoManager.getUserBasic(userId);
        if (null == basic) {
            throw new BusiException(ErrCodeType.UserNickNameNoFind);
        }
        if (null != form.getUserType() && form.getUserType() != basic.getUserType()) {
            throw new BusiException(ErrCodeType.UserNickNameNoFind);
        }
        return new UserSimpleVO(basic.getUserId(), basic.getNickname(), basic.getHeadPic());
    }

    /**
     * 检查用户是否实名认证
     */
    @BusiCode
    public void checkRealName() {
        UUserBasic userBasic = userInfoManager.getUserBasic(GlobalUtils.uid());
        if (BoolType.True == userBasic.getRealName()) {
            throw new BusiException("已实名认证！");
        }
    }

    /**
     * 检查用户某项状态
     */
    @BusiCode
    public BoolTypeVO saveCheckUserStatus(CheckUserStatusForm form) {
        Long targetUserId = Objects.nonNull(form.getUserId()) ? form.getUserId() : GlobalUtils.uid();
        if (UserCheckType.UserRealName == form.getType()) {
            return BoolTypeVO.build(checkUserRealName(targetUserId));
        }

        LOG.error("UserInfoService-saveCheckUserStatus-error form:{}", JsonUtils.seriazileAsString(form));
        throw new InternalException("用户检查状态类型不支持！");
    }

    /**
     * 检查用户是否双认证
     */
    @BusiCode
    public BoolTypeVO saveCheckUserAllAuth(){
        Long userId = GlobalUtils.uid();
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        return BoolTypeVO.build(BoolType.valueOf(BoolType.True == userBasic.getRealName() && BoolType.True == userBasic.getRealPerson()));
    }

    /**
     * 微信认证中心
     */
    @BusiCode
    public WxAuthCenterVO queryWxAuthCenter(){
        if(GlobalUtils.sexType() == SexType.Male){
            throw new BusiException("暂不支持！");
        }
        Long userId = GlobalUtils.uid();
        UUserWxApply userWxApply = userWxManager.getUserWxApply(userId);
        UMessageSetting setting = messageSettingManager.getSetting(userId);
        // 返回值封装
        WxAuthCenterVO vo = new WxAuthCenterVO();
        if(Objects.nonNull(userWxApply)){
            vo.setMyPagePic(userWxApply.getMyPagePic());
            vo.setInfoPic(userWxApply.getInfoPagePic());
            vo.setQrPic(userWxApply.getQrPic());
            vo.setStatus(userWxApply.getAuditStatus());
        }
        vo.setMyPageEg(GlobalConstant.WX_AUTH_MY_EG);
        vo.setInfoPageEg(GlobalConstant.WX_AUTH_INFO_EG);
        vo.setQrEg(GlobalConstant.WX_AUTH_QR_EG);
        vo.setUnlockNeed(setting.getLookWxFee());
        vo.setWxAuthRule(HtmlUrlUtils.getWxAuthRuleUrl());
        vo.setUnlockRule(HtmlUrlUtils.getWxUnlockRuleUrl());
        return vo;
    }

    /**
     * 提交微信认证申请
     */
    @BusiCode
    public void saveWxAuthSm(WxAuthSmForm form){
        Long userId = GlobalUtils.uid();
        userLockTemplate.acquireTransactionLock(userId);
        UUserWxApply exist = userWxManager.getUserWxApply(userId);
        // 如果在审核中则不允许更换
        if(Objects.nonNull(exist) && ReviewStatus.Wait == exist.getAuditStatus()){
            throw new BusiException("目前正在审核中，请耐心等待");
        }
        // 10天内仅允许更换一次
        if(Objects.nonNull(exist) && ReviewStatus.Pass == exist.getAuditStatus() && DateUtils.getDiffDays(exist.getApplyTime(), new Date()) < 10){
            throw new BusiException("10天内仅允许更换一次，切勿频繁更换");
        }
        // 新增 和 修改 都折合成删除+插入两步来做
        userWxManager.deleteUserWxApply(userId);
        UUserWxApply userWxApply = new UUserWxApply(userId);
        userWxApply.setMyPagePic(form.getMyPagePic());
        userWxApply.setQrPic(form.getQrPic());
        userWxApply.setAuditStatus(ReviewStatus.Wait);
        userWxApply.setApplyTime(new Date());
        userWxManager.saveUserWxApply(userWxApply);
    }

    /**
     * 查看好友微信信息
     */
    @BusiCode
    public WxInfoVO queryWxInfo(FriendIdForm form){
        if(SexType.Male != GlobalUtils.sexType()){
            throw new BusiException("暂不支持！");
        }
        if(!messageSettingManager.hasLookWx(form.getFriendId())){
            throw new BusiException("对方已关闭微信查看功能");
        }
        UUserBasic friendBasic = userInfoManager.getUserBasic(form.getFriendId());
        UUserMore friendMore = userInfoManager.getUserMore(form.getFriendId());
        UMessageSetting friendSetting = messageSettingManager.getSetting(form.getFriendId());
        Root<IntimateResponse> responseRoot = socialWebRemote.intimate(IntimateRequest.build(GlobalUtils.uid(), form.getFriendId()));
        boolean lookRight = responseRoot.getData().getGiftIntimateNum() >= NumberUtils.toLong(friendSetting.getLookWxFee());
        // 返回值封装
        WxInfoVO vo = new WxInfoVO();
        vo.setFriendId(friendBasic.getUserId());
        vo.setFriendNickname(friendBasic.getNickname());
        vo.setFriendHeadPic(friendBasic.getHeadPic());
        vo.setFriendWxQrPic(lookRight ? friendMore.getWxQr() : null);
        vo.setLookRight(BoolType.valueOf(lookRight));
        vo.setGiftIntimateNum(responseRoot.getData().getGiftIntimateNum());
        vo.setUnlockNeed(friendSetting.getLookWxFee());
        vo.setWxAuthRule(HtmlUrlUtils.getWxAuthRuleUrl());
        vo.setUnlockRule(HtmlUrlUtils.getWxUnlockRuleUrl());
        return vo;
    }

    /**
     * 获取用户在线客服专属地址
     *
     * @return
     */
    @BusiCode
    public String getCustomerUrl(CustomerUrlQueryForm form) {
        String sessionId = BusiUtils.tokToSessionId(form.getToken());
        if (StringUtils.isEmpty(sessionId)) {
            throw new BusiException("参数错误，请重新访问联系客服");
        }
        Session session = redisSessionManager.getSession(sessionId);
        if (session == null) {
            throw new BusiException("参数错误，请重新访问联系客服");
        }
        return customerManager.buildCustomerUrl(session.getUserId(), form.isExclusive());
    }


    /** ------------------------- 辅助方法 -------------------------- */

    /**
     * 获取用户首冲信息
     */
    private boolean getUserFirstRechargeStatus(Long userId, ClientType clientType) {
        try {
            Root<BoolType> result = acctWebRemote.getFirstRechargeStatus(FirstRechargeStatusRequest.build1(userId, clientType));
            if (!result.isFailure()) {
                return result.getData().boolValue();
            }
        } catch (Exception e) {
            LOG.error("UserInfoService-getUserFirstRechargeStatus-error reason:", e);
        }
        return false;
    }

    /**
     * 获取签到信息
     */
    private SignInInfoVO getSignInInfo(Long userId, SexType sexType) {
        SignInInfoVO vo = new SignInInfoVO();
        Date nowDate = DateUtils.getStartOfDay(new Date(GlobalUtils.reqTime()));
        // 给一个测试缓存
        if (!UnifiedConfig.isProdEnv()) {
            Date testTime = userSignInComponent.getSignInTextTime(userId);
            if (Objects.nonNull(testTime)) {
                nowDate = DateUtils.getStartOfDay(testTime);
            }
        }
        NowRoundSignInfoDTO roundInfo = userSignInComponent.getNowRoundSignInfo(userId, nowDate);
        Integer nowDayIndex = userSignInComponent.getNowDayIndex(nowDate, roundInfo);
        List<SignInDayInfoDTO> awardInfoList = userSignInComponent.getAwardInfo(roundInfo, nowDayIndex);
        // 获取大奖签到日数据
        List<SignInDayInfoDTO> bigAwardInfoList = awardInfoList.stream().filter(f -> f.getBigAwardSignInDay().boolValue()).filter(f -> f.getDayIndex() > roundInfo.getCtSignInDays()).collect(Collectors.toList());
        if(ListUtils.isNotEmpty(bigAwardInfoList)){
            vo.setTips(MsgUtils.format("再签到{}天，开{}金币宝箱", bigAwardInfoList.get(0).getDayIndex() - roundInfo.getCtSignInDays(), bigAwardInfoList.get(0).getCount()));
        }
        vo.setSignDetailList(awardInfoList);
        vo.setDaysNum(roundInfo.getCtSignInDays());
        vo.setNowDaySigned(roundInfo.getTodayHasSignIn());

        return vo;
    }

    /**
     * 检查用户是否实名认证
     */
    private BoolType checkUserRealName(Long userId) {
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        return BoolType.valueOf(BoolType.True == userBasic.getRealName());
    }
}
