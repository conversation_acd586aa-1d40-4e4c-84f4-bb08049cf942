package com.tuowan.yeliao.user.service.user;

import com.alibaba.nacos.common.utils.Pair;
import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.extend.SimpleMap;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.comp.change.UserPropsChangeComponent;
import com.tuowan.yeliao.commons.comp.check.ShuMeiPicComponent;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.comp.user.UserBlackListComponent;
import com.tuowan.yeliao.commons.comp.user.UserBusiComponent;
import com.tuowan.yeliao.commons.config.enums.ObjectLockType;
import com.tuowan.yeliao.commons.config.lock.ObjectLockTemplate;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.UserRedisTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.enums.busicode.BackCodeDefine;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.core.enums.redis.UserKeyDefine;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.data.dto.Session;
import com.tuowan.yeliao.commons.data.dto.common.CommonAwardNoticeDTO;
import com.tuowan.yeliao.commons.data.dto.social.ChatTipsInfoDTO;
import com.tuowan.yeliao.commons.data.dto.user.TagDTO;
import com.tuowan.yeliao.commons.data.entity.config.*;
import com.tuowan.yeliao.commons.data.entity.user.*;
import com.tuowan.yeliao.commons.data.enums.config.LevelType;
import com.tuowan.yeliao.commons.data.enums.config.NoticeSysType;
import com.tuowan.yeliao.commons.data.enums.config.ProfessionalType;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.enums.general.AuditStatus;
import com.tuowan.yeliao.commons.data.enums.general.ChatFilterType;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.enums.social.ChatTipsShowType;
import com.tuowan.yeliao.commons.data.enums.user.*;
import com.tuowan.yeliao.commons.data.manager.commons.SeqManager;
import com.tuowan.yeliao.commons.data.manager.user.MessageSettingManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.manager.user.UserKeyMarkManager;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.mq.enums.MessageTag;
import com.tuowan.yeliao.commons.mq.producer.MessageQueueProducer;
import com.tuowan.yeliao.commons.open.auth.AliCertificationSupport;
import com.tuowan.yeliao.commons.open.auth.TxCertificationSupport;
import com.tuowan.yeliao.commons.open.email.EasyPoiExcelDefine;
import com.tuowan.yeliao.commons.open.email.EmailSupport;
import com.tuowan.yeliao.commons.open.oss.AliOssSupport;
import com.tuowan.yeliao.commons.open.rongim.RongImSupport;
import com.tuowan.yeliao.commons.open.shumei.TextValidResult;
import com.tuowan.yeliao.commons.open.yidun.YidunSupport;
import com.tuowan.yeliao.commons.open.yidun.dto.YidunMediaDTO;
import com.tuowan.yeliao.commons.open.yidun.dto.YidunResultDTO;
import com.tuowan.yeliao.commons.open.yidun.enums.YidunResult;
import com.tuowan.yeliao.commons.open.yidun.enums.YidunReviewType;
import com.tuowan.yeliao.commons.web.common.form.PageForm;
import com.tuowan.yeliao.commons.web.common.form.UserIdForm;
import com.tuowan.yeliao.commons.web.common.vo.ListVO;
import com.tuowan.yeliao.commons.web.common.vo.PageVO;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.commons.web.proxy.config.NotifyMode;
import com.tuowan.yeliao.commons.web.proxy.config.RemoteInvoke;
import com.tuowan.yeliao.commons.web.session.RedisSessionManager;
import com.tuowan.yeliao.social.api.remote.SocialWebRemote;
import com.tuowan.yeliao.user.comp.UserCheckComponent;
import com.tuowan.yeliao.user.data.dto.MediaVoiceDTO;
import com.tuowan.yeliao.user.data.dto.ProfessionDTO;
import com.tuowan.yeliao.user.data.dto.MediaPicDTO;
import com.tuowan.yeliao.user.data.dto.MediaVideoDTO;
import com.tuowan.yeliao.user.data.enums.MoodAnmInfoOpt;
import com.tuowan.yeliao.user.data.manager.user.UserCommonManager;
import com.tuowan.yeliao.user.data.manager.user.UserTagManager;
import com.tuowan.yeliao.user.service.async.AsyncService;
import com.tuowan.yeliao.user.web.form.user.update.*;
import com.tuowan.yeliao.user.web.vo.user.UserMediaVO;
import com.tuowan.yeliao.user.web.vo.user.info.MySignLogVO;
import com.tuowan.yeliao.user.web.vo.user.update.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户修改信息服务实现
 */
@Service
public class UserUpdateService {
    private static final Logger LOG = LoggerFactory.getLogger(UserUpdateService.class);

    /**
     * 用户每日可修改年龄的次数限制
     */
    private static final Long USER_UPDATE_TIMES_LIMIT_DAY = 5L;

    @Autowired
    private AsyncService asyncService;
    @Autowired
    private UserTagManager userTagManager;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private UserPropsChangeComponent changeComponent;
    @Autowired
    private UserBusiComponent userBusiComponent;
    @Autowired
    private ShuMeiPicComponent shuMeiPicComponent;
    @Autowired
    private AliCertificationSupport certificationSupport;
    @Autowired
    private RedisSessionManager redisSessionManager;
    @Autowired
    private UserKeyMarkManager userKeyMarkManager;
    @Autowired
    private UserCheckComponent userCheckComponent;
    @Autowired
    private RongImSupport rongImSupport;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private NoticeComponent noticeComponent;
    @Autowired
    private MessageQueueProducer messageQueueProducer;
    @Autowired
    private UserBlackListComponent userBlackListComponent;
    @Autowired
    private TxCertificationSupport txCertificationSupport;
    @Autowired
    private AliOssSupport ossSupport;
    @Autowired
    private SocialWebRemote socialWebRemote;
    @Autowired
    private EmailSupport emailSupport;
    @Autowired
    private UserRedisTemplate userRedisTemplate;
    @Autowired
    private UserCommonManager userCommonManager;
    @Autowired
    private SeqManager seqManager;
    @Autowired
    private MessageSettingManager messageSettingManager;
    @Autowired
    private ObjectLockTemplate objectLockTemplate;

    /**
     * 修改用户归因渠道信息
     * @param channelId 用户的真实渠道
     */
    @RemoteInvoke
    public boolean updateUserChannelInfo(Long userId, SexType sexType, String channelId, String channelCode){
        // 表：u_user_ext 字段修改
        UUserExt update = new UUserExt(userId);
        update.setChannelId(channelId);
        update.setChannelCode(channelCode);
        userInfoManager.updateUserExt(update);
        // 根据新 channelId 判断该用户是否在 商店审核禁止推荐处理列表之中
        UUserBasic basic = userInfoManager.getUserBasic(userId);
        TStoreAuditShield shield = userInfoManager.getTStoreAuditShield(channelId, basic.getSex());
        if(Objects.nonNull(shield)){
            return false; // 不进行下面的处理
        }
        // 如果用户注册进来的时候 加入了商店审核禁止推荐处理队列 则将他移除
        Long removeResult = userRedisTemplate.zrem(buildStoreAuditShieldUser(), userId.toString());
        if(Objects.nonNull(removeResult) && removeResult > 0){
            UMessageSetting update1 = new UMessageSetting(userId);
            update1.setBanRecom(BoolType.False);
            messageSettingManager.updateSetting(update1);
            return true;
        }
        return false;
    }

    /**
     * 初始化职业信息
     */
    @BusiCode
    public ProfessionVO initProfession() {
        List<TProfession> professions = userInfoManager.getProfessionList();
        Map<ProfessionalType, List<TProfession>> professionMap = professions.stream().collect(Collectors.groupingBy(TProfession::getProfessionType));
        final List<InitProfessionVO> resultVO = new ArrayList<>();
        professionMap.forEach((key, value) -> {
            // 顺序排列
            value = value.stream().sorted(Comparator.comparing(TProfession::getOrderNum)).collect(Collectors.toList());
            InitProfessionVO vo = new InitProfessionVO();
            vo.setOrder(key.getOrder());
            vo.setProfession(ProfessionDTO.build1(-1, key.getDesc()));
            List<ProfessionDTO> data = new ArrayList<>();
            value.forEach(item -> {
                data.add(ProfessionDTO.build1(item.getProfessionId(), item.getProfessionName()));
            });
            vo.setData(data);
            resultVO.add(vo);
        });
        // 顺序排序
        List<InitProfessionVO> resultList = resultVO.stream().sorted(Comparator.comparing(InitProfessionVO::getOrder)).collect(Collectors.toList());
        ProfessionVO resultVo = new ProfessionVO();
        resultVo.setList(resultList);
        return resultVo;
    }

    /**
     * 初始化我的家乡
     *
     * @return
     */
    @BusiCode
    public CityVO initCity() {
        CityVO vo = new CityVO();
        /*UUserMore more = userInfoManager.getUserMore(GlobalUtils.uid());
        Integer homeCityId = more.getHometownCityId();
        if (null != homeCityId) {
            TCity city = userInfoManager.getCityById(more.getHometownCityId());
            if (null != city) {
                vo.setCityProvince(city.getProvince());
                vo.setCityName(city.getCity());
            }
            vo.setCityId(homeCityId);
        }*/
        List<TCity> cityConfigList = userInfoManager.getCityList();
        if (ListUtils.isEmpty(cityConfigList)) {
            return vo;
        }
        vo.setCityConfigList(cityConfigList);
        return vo;
    }

    /**
     * 查询标签列表
     * 备注：该接口默认每次随机返回10条数据
     */
    @BusiCode
    public ListVO initTag(InitTagForm form) {
        List<TRelationTag> tagList = null;
        if(UserTagType.Character == form.getTagType()){
            tagList = userTagManager.getTypeRelationTag(UserTagType.Character, GlobalUtils.sexType());
        }else if(UserTagType.LoveTa == form.getTagType()){
            tagList = userTagManager.getTypeRelationTag(UserTagType.Character, SexType.getTaSexType(GlobalUtils.sexType()));
        }else if(UserTagType.Together == form.getTagType()){
            tagList = userTagManager.getTypeRelationTag(UserTagType.Together, SexType.Unknown);
        }
        if(ListUtils.isEmpty(tagList)){
            return ListVO.createEmpty();
        }
        // 乱序
        Collections.shuffle(tagList);
        List<TagDTO> resultList = tagList.stream().limit(10).map(m -> TagDTO.build5(m.getTagId(), m.getTagName())).collect(Collectors.toList());
        return ListVO.create(resultList);
    }

    /**
     * 更新用户更多信息
     *
     * @param form
     */
    @BusiCode(value = BusiCodeDefine.UpdateUserMoreInfo, forward = NotifyMode.DYNAMIC)
    public void updateUserInfo(UserUpdateForm form) {
        Long userId = GlobalUtils.uid();
        userLockTemplate.acquireTransactionLock(userId);
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        UUserMore more = userInfoManager.getUserMore(userId);
        if (null != form.getHeight() && !form.getHeight().equals(more.getHeight())) {
            UUserMore update = new UUserMore(userId);
            update.setHeight(form.getHeight());
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("height", userId, more.getHeight(), form.getHeight());
        }
        if (null != form.getWeight() && !form.getWeight().equals(more.getWeight())) {
            UUserMore update = new UUserMore(userId);
            update.setWeight(form.getWeight());
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("weight", userId, more.getWeight(), form.getWeight());
        }
        // 修改居住状态
        if (form.getLiveStatus() != null && form.getLiveStatus() != more.getLiveStatus()) {
            UUserMore update = new UUserMore(userId);
            update.setLiveStatus(form.getLiveStatus());
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("liveStatus", userId, more.getLiveStatus(), form.getLiveStatus());
        }
        // 修改购房状态
        if (form.getHouseStatus() != null && form.getHouseStatus() != more.getHouseStatus()) {
            UUserMore update = new UUserMore(userId);
            update.setHouseStatus(form.getHouseStatus());
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("houseStatus", userId, more.getHouseStatus(), form.getHouseStatus());
        }
        // 修改购车状态
        if (form.getCarStatus() != null && form.getCarStatus() != more.getCarStatus()) {
            UUserMore update = new UUserMore(userId);
            update.setCarStatus(form.getCarStatus());
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("carStatus", userId, more.getCarStatus(), form.getCarStatus());
        }
        // 修改是否接受约会
        if (form.getAppointmentStatus() != null && form.getAppointmentStatus() != more.getAppointment()) {
            UUserMore update = new UUserMore(userId);
            update.setAppointment(form.getAppointmentStatus());
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("appointment", userId, more.getAppointment(), form.getAppointmentStatus());
        }
        // 修改是否接受婚前同居
        if (form.getLiveTogetherStatus() != null && form.getLiveTogetherStatus() != more.getLiveTogether()) {
            UUserMore update = new UUserMore(userId);
            update.setLiveTogether(form.getLiveTogetherStatus());
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("liveTogether", userId, more.getLiveTogether(), form.getLiveTogetherStatus());
        }
        // 修改婚恋状态
        if (form.getMarriageStatus() != null && form.getMarriageStatus() != more.getMarriageStatus()) {
            UUserMore update = new UUserMore(userId);
            update.setMarriageStatus(form.getMarriageStatus());
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("marriageStatus", userId, more.getMarriageStatus(), form.getMarriageStatus());
        }
        // 修改学历
        if (form.getEducation() != null && form.getEducation() != more.getEducation()) {
            UUserMore update = new UUserMore(userId);
            update.setEducation(form.getEducation());
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("education", userId, more.getEducation(), form.getEducation());
        }
        // 修改收入
        if (form.getIncome() != null && form.getIncome() != more.getIncome()) {
            UUserMore update = new UUserMore(userId);
            update.setIncome(form.getIncome());
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("income", userId, more.getIncome(), form.getIncome());
        }
        // 修改家乡
        if (form.getHometownCityId() != null && !form.getHometownCityId().equals(more.getHometownCityId())) {
            UUserMore update = new UUserMore(userId);
            update.setHometownCityId(form.getHometownCityId());
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("hometownCityId", userId, more.getHometownCityId(), form.getHometownCityId());
        }
        // 修改当前驻地
        /*if (form.getStationCityId() != null && !form.getStationCityId().equals(more.getStationCityId())) {
            UUserMore update = new UUserMore(userId);
            update.setStationCityId(form.getStationCityId());
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("hometownCityId", userId, more.getStationCityId(), form.getStationCityId());
        }*/
        // 修改职业
        if (form.getProfessionId() != null && !form.getProfessionId().equals(more.getProfessionId())) {
            UUserMore update = new UUserMore(userId);
            update.setProfessionId(form.getProfessionId());
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("professionId", userId, more.getProfessionId(), form.getProfessionId());
        }
        // 修改生日
        if (StringUtils.isNotEmpty(form.getBirthday()) && !form.getBirthday().equals(DateUtils.toString(userBasic.getBirthDate(), DatePattern.YMD))) {
            // 每日修改次数限制
            String today = DateUtils.toString(new Date(), DatePattern.YMD2);
            if (userInfoManager.getUserUpdateAgeTimes(today, userId) >= USER_UPDATE_TIMES_LIMIT_DAY) {
                throw new BusiException("平台提倡真实交友，今日修改已达上限，请明日再来！");
            }
            Date newBirthDay = DateUtils.parse(form.getBirthday(), DatePattern.YMD);
            UUserBasic update = new UUserBasic(userId);
            update.setBirthDate(newBirthDay);
            userBusiComponent.updateUserBasic(update);
            // 通知 social 更新
            asyncService.userBirthDayChange(userId, newBirthDay);
            // 新增属性异动记录
            changeComponent.add("birthDate", userId, userBasic.getBirthDate(), form.getBirthday());
            // 记录修改次数
            userInfoManager.recordUserUpdateAgeTimes(today, userId);
        }
        // 修改我的足迹
        if (Objects.nonNull(form.getFootprints())) {
            UUserMore update = new UUserMore(userId);
            update.setFootprints(BusiUtils.collToStr(form.getFootprints(), ","));
            userInfoManager.updateUserMore(update);
            // 新增属性异动记录
            changeComponent.add("footprints", userId, more.getFootprints(), form.getFootprints());
        }
        // 修改我的签名
        if (StringUtils.isNotEmpty(form.getMySign()) && !form.getMySign().equals(userBasic.getMySign())) {
            // 易盾验证签名合法性
            userCheckComponent.checkUserMySignValid(userBasic, form.getMySign());
            UUserBasic update = new UUserBasic(userId);
            update.setMySign(form.getMySign());
            userBusiComponent.updateUserBasic(update);
            // 新增属性异动记录
            changeComponent.add("mySign", userId, userBasic.getMySign(), form.getMySign());
        }
        if(!userCheckComponent.checkUserFinishInfo(userId)){
            GlobalUtils.extValue(BusinessDataKey.BreakNotify, true);
        }
    }

    /**
     * 修改昵称
     */
    @BusiCode
    public void updateNickname(UserUpdateForm form) {
        Long userId = GlobalUtils.uid();
        userLockTemplate.acquireTransactionLock(userId);
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        // 验证昵称合法性
        Pair<TextValidResult, Boolean> pair = userCheckComponent.checkUserNicknameValid(form.getNickname(),
                userBasic.getUserId(), userBasic.getUserType(), userBasic.getSex());
        if (pair.getFirst().getFilterType() != ChatFilterType.Pass) {
            throw new BusiException(pair.getFirst().getDescription());
        }
        // 修改昵称不允许重复
        if (userInfoManager.nicknameIsExists(userId, form.getNickname())) {
            throw new BusiException("昵称已存在，请重新输入！");
        }
        UUserBasic update = new UUserBasic(userId);
        update.setNickname(form.getNickname());
        userBusiComponent.updateUserBasic(update);
        // 通知 social 更新
        asyncService.userNicknameChange(userId, form.getNickname());
        // session修改
        redisSessionManager.updateSession(GlobalUtils.sessionId(), userId, sessions -> {
            for (Session session : sessions) {
                if (session == null) {
                    continue;
                }
                session.setNickname(form.getNickname());
            }
        });
        // 新增属性异动记录
        changeComponent.add("nickname", userId, userBasic.getNickname(), form.getNickname());
    }

    /**
     * 修改头像信息
     * 备注：如果用户是已经实名过的用户修改的头像非本人头像时 不允许修改
     */
    @BusiCode
    public UserHeadPicVO updateUserHeadPic(UserHeadPicForm form) {
        Long userId = GlobalUtils.uid();
        SexType sex = GlobalUtils.sexType();
        userLockTemplate.acquireTransactionLock(userId);
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        // 图片合规性校验
        if(!userCheckComponent.checkUserHeadPicValid(userId, sex, form.getHeadPic())){
            throw new BusiException(ErrCodeType.ImageFailed);
        }
        // 实人头像存底对比
        // boolean realPeople = getRealPeople(userId, form.getHeadPic());
        UUserBasic update = new UUserBasic(userId);
        update.setHeadPic(form.getHeadPic());
        // update.setRealPerson(BoolType.valueOf(realPeople));
        update.setHeadStatus(HeadStatus.WaitArtificial);
        userBusiComponent.updateUserBasic(update);
        // session修改
        redisSessionManager.updateSession(GlobalUtils.sessionId(), userId, sessions -> {
            for (Session session : sessions) {
                if (session == null) {
                    continue;
                }
                session.setHeadPic(form.getHeadPic());
            }
        });
        // 新增属性异动记录
        changeComponent.add("userHeadPic", userId, userBasic.getHeadPic(), form.getHeadPic());
        // 返回值填充
        UserHeadPicVO vo = new UserHeadPicVO();
        vo.setReUploadTips(BoolType.False);

        // 提交至人工复审
        //YidunSupport.submitUser(YidunReviewType.HeadPic, userBasic.getUserId(), userBasic.getUserId(), update.getHeadPic());
        return vo;
        /*if(realPeople){
            // 获取上传真人头像奖励标记（将打标记 和 分发奖励分割开，就算发奖逻辑出错有依据溯源）
            UUserKeyMark keyMark = userKeyMarkManager.getKeyMark(userId, UserBusiKeyMark.UserFirstUploadRealHead);
            if(keyMark == null){
                FixedAward award = sex == SexType.Male ? FixedAward.PerfectHeadInfo_Male : FixedAward.PerfectHeadInfo_FeMale;
                asyncService.saveUserAward(userId, AwardDTO.create(award.getAwardCode()));
                userKeyMarkManager.saveOrUpdateUserMark(userId, UserBusiKeyMark.UserFirstUploadRealHead, "T");
            }
        }*/
        // TODO 异步通知social模块用户的实人状态改变
    }

    /**
     * 修改用户别名
     * 备注：修改操作 仅修改匿名心情昵称
     */
    @BusiCode
    public UserMoodAnmInfoVO updateUserMoodAnmInfo(MoodAnmInfoForm form){
        Long userId = GlobalUtils.uid();
        UUserAnonymous userAnonymous = userInfoManager.getUserAnonymous(userId);
        if(MoodAnmInfoOpt.Get == form.getOpt()){
            return UserMoodAnmInfoVO.build1(userAnonymous.getMoodNickname(), userAnonymous.getMoodHeadPic());
        }
        String aliseName = userInfoManager.getAliseName();
        UUserAnonymous update = new UUserAnonymous(userId);
        update.setMoodNickname(aliseName);
        userInfoManager.updateUserAnonymous(update);
        return UserMoodAnmInfoVO.build1(aliseName, userAnonymous.getMoodHeadPic());
    }

    /**
     * 修改静态封面
     */
    @BusiCode
    public void updateUserStaticCover(UserUpdateForm form) {
        Long userId = GlobalUtils.uid();
        SexType sex = GlobalUtils.sexType();
        userLockTemplate.acquireTransactionLock(userId);
        // checkUserUpdateInfo(sex);
        List<String> staticCoverPic = userInfoManager.getStaticCoverPic(userId);
        for (String item : form.getStaticCovers()) {
            if (staticCoverPic.contains(item)) {
                continue;
            }
            shuMeiPicComponent.verifyCover(userId, sex, item);
        }
        userBusiComponent.updateCoverPic(userId, form.getStaticCovers());
        // 新增属性异动记录
        changeComponent.add("staticCover", userId, staticCoverPic, form.getStaticCovers());
        if (ListUtils.isNotEmpty(form.getStaticCovers()) && form.getStaticCovers().size() >= 3) {
            // 获取首次上传3张及以上的封面奖励标记（将打标记 和 分发奖励分割开，就算发奖逻辑出错有依据溯源）
            UUserKeyMark keyMark = userKeyMarkManager.getKeyMark(userId, UserBusiKeyMark.FirstCoverMoreThree);
            if (keyMark == null) {
                userKeyMarkManager.saveOrUpdateUserMark(userId, UserBusiKeyMark.FirstCoverMoreThree, "T");
                GlobalUtils.extValue(BusinessDataKey.TAwardDetailExt, buildCashAwardExtCfg(UserCashSourceType.NewTask));
                GlobalUtils.extValue(BusinessDataKey.UserBusiKeyMark, UserBusiKeyMark.FirstCoverMoreThree);
                CallbackAfterTransactionUtil.send(() -> {
                    messageQueueProducer.sendAsync(BusiCodeDefine.UpdateUserCover, MessageTag.UserTask);
                });
                /*FixedAward award = sex == SexType.Male ? FixedAward.PerfectStaticCover_Male : FixedAward.PerfectStaticCover_FeMale;
                CommonAwardNoticeDTO noticeDTO = buildAwardNotice(userId, "- 上传3张照片 -", award.getPopPic(), award.getAwardTypeSimpleName(), award.getAwardValue());
                asyncService.saveAsyncAward(GlobalUtils.getGlobalContext(), userId, award.getAwardCode(),
                        award.getAwardType(), award.getAwardValue(), award.getDesc(), noticeDTO);*/
                // 检查新人任务是否完成
                // newPeopleTaskFinish(userId, sex);
            }
        }
    }

    /**
     * 修改我的标签
     */
    @BusiCode(value = BusiCodeDefine.UpdateUserMoreInfo, forward = NotifyMode.DYNAMIC)
    public void updateMyTag(UserUpdateForm form) {
        Long userId = GlobalUtils.uid();
        SexType sex = GlobalUtils.sexType();
        userLockTemplate.acquireTransactionLock(userId);
        Object obj = userTagManager.updateTag(userId, sex, UserTagType.Character, form.getMyTags());
        // 新增属性异动记录
        changeComponent.add(UserTagType.Character.name(), userId, obj, form.getMyTags());
        if(!userCheckComponent.checkUserFinishInfo(userId)){
            GlobalUtils.extValue(BusinessDataKey.BreakNotify, true);
        }
    }

    /**
     * 修改喜欢的Ta的标签
     */
    @BusiCode(value = BusiCodeDefine.UpdateUserMoreInfo, forward = NotifyMode.DYNAMIC)
    public void updateLoveTaTag(UserUpdateForm form) {
        Long userId = GlobalUtils.uid();
        SexType sex = GlobalUtils.sexType();
        userLockTemplate.acquireTransactionLock(userId);
        Object obj = userTagManager.updateTag(userId, sex, UserTagType.LoveTa, form.getLoveTaTags());
        // 新增属性异动记录
        changeComponent.add(UserTagType.LoveTa.name(), userId, obj, form.getMyTags());
        if(!userCheckComponent.checkUserFinishInfo(userId)){
            GlobalUtils.extValue(BusinessDataKey.BreakNotify, true);
        }
    }

    /**
     * 修改一起标签
     */
    @BusiCode(value = BusiCodeDefine.UpdateUserMoreInfo, forward = NotifyMode.DYNAMIC)
    public void updateTogetherTag(UserUpdateForm form) {
        Long userId = GlobalUtils.uid();
        SexType sex = GlobalUtils.sexType();
        userLockTemplate.acquireTransactionLock(userId);
        Object obj = userTagManager.updateTag(userId, sex, UserTagType.Together, form.getTogetherTags());
        // 新增属性异动记录
        changeComponent.add(UserTagType.Together.name(), userId, obj, form.getMyTags());
        if(!userCheckComponent.checkUserFinishInfo(userId)){
            GlobalUtils.extValue(BusinessDataKey.BreakNotify, true);
        }
    }

    /**
     * 修改足迹
     */
    @BusiCode(value = BusiCodeDefine.UpdateUserMoreInfo, forward = NotifyMode.DYNAMIC)
    public void updateFootprints(UserUpdateForm form){
        Long userId = GlobalUtils.uid();
        userLockTemplate.acquireTransactionLock(userId);
        // 修改数据库
        UUserMore update = new UUserMore(userId);
        update.setFootprints(BusiUtils.collToStr(form.getFootprints(), ","));
        userInfoManager.updateUserMore(update);
        if(!userCheckComponent.checkUserFinishInfo(userId)){
            GlobalUtils.extValue(BusinessDataKey.BreakNotify, true);
        }
    }

    /**
     * 更新用户定位信息
     *
     * @param form、
     */
    @BusiCode(value = BusiCodeDefine.UpdateUserLocation)
    public void saveLocation(UserLocationForm form) {
        // 坐标错误，无需更新
        if (NumberUtils.toDouble(form.getLat()) == 0 && NumberUtils.toDouble(form.getLng()) == 0) {
            return;
        }
        if (BusiUtils.isInValidLocationData(form.getLat()) || BusiUtils.isInValidLocationData(form.getLng())) {
            return;
        }
        Long userId = GlobalUtils.uid();
        UUserLocation location = UserLocationForm.create(userId, form);
        userInfoManager.saveOrUpdateUserLocation(location);
        // 刷新搜索引擎地理位置信息
        asyncService.userLocationChange(location);
    }

    /**
     * 修改用户融云IM token
     */
    @BusiCode
    public ImTokenVO updateImToken() {
        // 获取新token
        Long userId = GlobalUtils.uid();
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        UUserMore userMore = userInfoManager.getUserMore(userId);
        String newToken = rongImSupport.getToken(userMore.getUserId(), userBasic.getNickname(), userBasic.getHeadPic());
        // 修改token
        userInfoManager.updateUserImToken(userMore.getUserId(), newToken);

        // 新增属性异动记录
        changeComponent.add("imToken", userMore.getUserId(), userMore.getImToken(), newToken);

        ImTokenVO vo = new ImTokenVO();
        vo.setImToken(newToken);
        return vo;
    }

    /**
     * 用户交友心愿历史
     */
    @BusiCode
    public PageVO mySignLog(PageForm form){
        List<UUserPropsChange> list = changeComponent.queryUserPropsChangeLog(GlobalUtils.uid(), "mySign", form.getOffset(), form.getLimit());
        List<MySignLogVO> resultList = new ArrayList<>();
        list.forEach(item -> {
            resultList.add(MySignLogVO.build(item.getNewValue(), DateUtils.toString(item.getCreateTime(), DatePattern.YMD5)));
        });
        return new PageVO(resultList, form.getOffset(), form.getLimit());
    }

    /**
     * 查看颜照库
     */
    @BusiCode
    public UserMediaVO querySelfMedia(){
        Long userId = GlobalUtils.uid();
        UserMediaVO vo = new UserMediaVO();
        vo.setMediaStatus(userInfoManager.getUserMore(userId).getMediaStatus());
        List<UUserMediaApply> mediaApplies = userCommonManager.queryUserMediaApply(userId);
        Map<MediaType, List<UUserMediaApply>> grouping = ListUtils.grouping(mediaApplies, UUserMediaApply::getType);
        grouping.forEach((k,v) -> {
            v.sort(Comparator.comparing(UUserMediaApply::getMediaIdx));
            switch (k){
                case CommonVoice:
                    UUserMediaApply voice = v.get(0);
                    vo.setVoice(MediaVoiceDTO.build2(voice.getApplyId(), voice.getMediaValue(), voice.getAuditStatus(), voice.getStatusDesc()));
                    break;
                case CommonCover:
                    UUserMediaApply cover = v.get(0);
                    vo.setCover(MediaPicDTO.build3(cover.getApplyId(), cover.getMediaValue(), cover.getAuditStatus(), cover.getStatusDesc()));
                    break;
                case CommonVideo:
                    UUserMediaApply video = v.get(0);
                    SimpleMap extMap = JsonUtils.toSimpleMap(video.getMediaExt());
                    vo.setVideo(MediaVideoDTO.build2(video.getApplyId(), extMap.getString("videoCover"), video.getMediaValue(), video.getAuditStatus(), video.getStatusDesc()));
                    break;
                case CommonPic:
                    vo.setPics(v.stream().map(m -> MediaPicDTO.build3(m.getApplyId(), m.getMediaValue(), m.getAuditStatus(), m.getStatusDesc())).collect(Collectors.toList()));
                    break;
                case ChargePic:
                    vo.setChargePics(v.stream().map(m -> MediaPicDTO.build3(m.getApplyId(), m.getMediaValue(), m.getAuditStatus(), m.getStatusDesc())).collect(Collectors.toList()));
                    break;
                default:
                    // do nothing
                    break;
            }
        });
        return vo;
    }

    /**
     * 修改颜照库
     * forward = NotifyMode.DYNAMIC
     */
    @BusiCode(value = BusiCodeDefine.UpdateMedia)
    public void saveMediaApply(MediaForm form){
        Long userId = GlobalUtils.uid();
        objectLockTemplate.acquireTransactionLock(ObjectLockType.MediaUpdate, userId);
        List<UUserMediaApply> mediaApplies = userCommonManager.queryUserMediaApply(userId);
        Map<Long, UUserMediaApply> existsMedias = mediaApplies.stream().collect(Collectors.toMap(UUserMediaApply::getApplyId, v -> v));
        // 审核照片入库
        // 先删除该用户媒资审核信息
        userInfoManager.deleteUserMediaApply(userId);
        List<UUserMediaApply> newMedias = new ArrayList<>();
        // 1、封面
        if(Objects.nonNull(form.getCover())){
            newMedias.addAll(buildMediaApplyByPic(userId, MediaType.CommonCover, Collections.singletonList(form.getCover()), existsMedias));
        }
        // 2、常规照片
        if(ListUtils.isNotEmpty(form.getPics())){
            newMedias.addAll(buildMediaApplyByPic(userId, MediaType.CommonPic, form.getPics(), existsMedias));
        }
        // 3、收费照片
        if(ListUtils.isNotEmpty(form.getChargePics())){
            newMedias.addAll(buildMediaApplyByPic(userId, MediaType.ChargePic, form.getChargePics(), existsMedias));
        }
        // 4、视频
        if(Objects.nonNull(form.getVideo())){
            newMedias.addAll(buildMediaApplyByVideo(userId, MediaType.CommonVideo, Collections.singletonList(form.getVideo()), existsMedias));
        }
        // 5、语音
        if(Objects.nonNull(form.getVoice())){
            newMedias.addAll(buildMediaApplyByVoice(userId, MediaType.CommonVoice, Collections.singletonList(form.getVoice()), existsMedias));
        }
        // 批量插入
        userInfoManager.batchSaveUserMediaApply(newMedias);
        // 提交易盾审核 备注：这里需要确保提交易盾成功
        List<Pair<String, String>> pics = new ArrayList<>();
        Pair<String, String> videoCover = null;
        Pair<String, String> video = null;
        Pair<String, String> voice = null;
        for(UUserMediaApply item : newMedias){
            // 如果媒资信息不是待审核 我们不提交易盾
            if(item.getAuditStatus() != MediaApplyStatus.WaitJq){
                continue;
            }
            if(item.getType() == MediaType.CommonCover || item.getType() == MediaType.CommonPic || item.getType() == MediaType.ChargePic){
                pics.add(Pair.with(String.valueOf(item.getApplyId()), item.getMediaValue()));
            } else if(item.getType() == MediaType.CommonVoice){
                voice = Pair.with(String.valueOf(item.getApplyId()), item.getMediaValue());
            } else if(item.getType() == MediaType.CommonVideo){
                SimpleMap extMap = JsonUtils.toSimpleMap(item.getMediaExt());
                videoCover = Pair.with(String.valueOf(item.getApplyId()), extMap.getString("videoCover"));
                video = Pair.with(String.valueOf(item.getApplyId()), item.getMediaValue());
            }
        }
        YidunMediaDTO yidunMediaDTO = YidunMediaDTO.build(pics, voice, videoCover, video);
        if(yidunMediaDTO.isEmpty()){
            // 用户没有提交新媒资
            // do nothing
        }else {
            // 修改 u_user_more 媒资审核状态
            UUserMore updateMore = new UUserMore(userId);
            updateMore.setMediaStatus(AuditStatus.Wait);
            userInfoManager.updateUserMore(updateMore);
            // 提交易盾审核
            if (!YidunSupport.submitMedia(YidunReviewType.Media, userId, userId, yidunMediaDTO)) {
                throw new BusiException("提交审核失败，请稍后重试！");
            }
        }
        // 正式环境不做转发 MQ那边的逻辑是直接通过
        /*if(UnifiedConfig.isProdEnv()){
            GlobalUtils.extValue(BusinessDataKey.BreakNotify, true);
        }*/
    }

    /**
     * 易盾审核用户真人认证上传媒资
     */
    @BusiCode
    public void saveYdAuditMedia(MediaReviewForm form){
        Long userId = form.getUserId();
        objectLockTemplate.acquireTransactionLock(ObjectLockType.MediaUpdate, userId);
        UUserMore userMore = userInfoManager.getUserMore(userId);
        if(AuditStatus.Wait != userMore.getMediaStatus()){
            return;
        }
        // 不合规的媒资信息
        List<String> invalidMedia = new ArrayList<>();
        if(ListUtils.isNotEmpty(form.getPics())){
            invalidMedia.addAll(form.getPics().stream().filter(f -> !f.pass).map(m -> m.dataId).collect(Collectors.toList()));
        }
        if(Objects.nonNull(form.getVideo()) && !form.getVideo().pass){
            invalidMedia.add(form.getVideo().dataId);
        }
        if(Objects.nonNull(form.getVoice()) && !form.getVoice().pass){
            invalidMedia.add(form.getVoice().dataId);
        }
        // 修改易盾审核结果
        List<UUserMediaApply> mediaApplies = userCommonManager.queryUserMediaApply(userId);
        mediaApplies.forEach(item -> {
            if(item.getAuditStatus() != MediaApplyStatus.WaitJq){ // 如果媒资状态不是待机器审核 我们也不做更新
                return;
            }
            UUserMediaApply updateApply = new UUserMediaApply(item.getApplyId());
            updateApply.setAuditStatus(item.getType() != MediaType.ChargePic && invalidMedia.contains(String.valueOf(item.getApplyId())) ? MediaApplyStatus.RejectJq : MediaApplyStatus.PassJq);
            updateApply.setStatusDesc(item.getType() != MediaType.ChargePic && invalidMedia.contains(String.valueOf(item.getApplyId())) ? "内容不合规" : "内容合规");
            userCommonManager.updateMediaApply(updateApply);
        });
        if(form.isPass()){ // 易盾审核通过 说明媒资里面的任何一个图片、视频都是合规的
            // 媒资同步
            List<UUserMedia> oldMedias = userInfoManager.getUserMedias(userId);
            oldMedias.forEach(item -> {
                userInfoManager.deleteUserMedia(item);
            });
            mediaApplies.forEach(item -> {
                // 插入 u_user_media 表
                UUserMedia media = new UUserMedia();
                media.setRecordId(Objects.isNull(item.getRecordId()) ? seqManager.getMediaRecordId() : item.getRecordId());
                media.setUserId(item.getUserId());
                media.setType(item.getType());
                media.setMediaValue(item.getMediaValue());
                media.setMediaExt(item.getMediaExt());
                media.setMediaIdx(item.getMediaIdx());
                media.setCreateTime(item.getCreateTime());
                userInfoManager.saveUserMedia(media);
                // 修改 u_user_media_apply 表
                UUserMediaApply updateApply = new UUserMediaApply(item.getApplyId());
                updateApply.setRecordId(media.getRecordId());
                updateApply.setAuditStatus(MediaApplyStatus.PassCms);
                updateApply.setStatusDesc("审核通过");
                userCommonManager.updateMediaApply(updateApply);
            });
            // 修改 u_user_more
            UUserMore updateMore = new UUserMore(userId);
            updateMore.setMediaStatus(AuditStatus.Pass);
            userInfoManager.updateUserMore(updateMore);
            // 系统通知
            noticeComponent.sendSystemNotice(userId, NoticeSysType.UserMediaAuditPass);
        }else{
            // 修改 u_user_more
            UUserMore updateMore = new UUserMore(userId);
            updateMore.setMediaStatus(AuditStatus.Reject);
            userInfoManager.updateUserMore(updateMore);
            // 系统通知
            noticeComponent.sendSystemNotice(userId, NoticeSysType.UserMediaAuditReject);
        }
    }

    /**
     * 删除媒资
     */
    @BusiCode
    public void deleteMedia(DeleteMediaForm form){
        // 获取媒资
        UUserMedia userMedia = userInfoManager.getUserMedia(form.getRecordId());
        if(Objects.isNull(userMedia)){
            throw new DataException(ErrCodeType.RecordNotExists);
        }
        if(!GlobalUtils.uid().equals(userMedia.getUserId())){
            throw new BusiException("无权操作！");
        }
        // u_user_media 删除
        userInfoManager.deleteUserMedia(userMedia);
        // u_user_media_apply 删除
        userCommonManager.deleteUserMediaApplyByRecordId(form.getRecordId());
    }

    /**
     * 人工审核用户资料
     *
     * @param form
     */
    @BusiCode
    public void saveReviewUser(UserReviewForm form) {
        // 只有头像审核通过/不通过都需要处理
        if (form.isPass() && YidunReviewType.HeadPic != form.getReviewType()) {
            return;
        }
        userLockTemplate.acquireTransactionLock(form.getUserId());
        UUserBasic user = userInfoManager.getUserBasic(form.getUserId());
        if (user == null) {
            return;
        }
        UUserBasic update = new UUserBasic(form.getUserId());
        if (form.isPass()) {
            // 头像审核通过
            if (YidunReviewType.HeadPic == form.getReviewType()) {
                update.setHeadStatus(HeadStatus.Pass);
                userInfoManager.updateUserBasic(update);
            }
        } else {
            NoticeSysType noticeSysType = null;
            if (YidunReviewType.HeadPic == form.getReviewType()) {
                update.setHeadPic(BusiUtils.getDefaultHeadPic(user.getSex()));
                update.setHeadStatus(HeadStatus.Reject);
                update.setRealPerson(BoolType.False);
                userBusiComponent.updateUserBasic(update);
                noticeSysType = NoticeSysType.HeadPicReviewReject;
                // 新增属性异动记录
                changeComponent.add("headPic", user.getUserId(), user.getHeadPic(), update.getHeadPic());
            } else if (YidunReviewType.MySign == form.getReviewType()) {
                update.setMySign("");
                userBusiComponent.updateUserBasic(update);
                // 新增属性异动记录
                changeComponent.add("mySign", user.getUserId(), user.getMySign(), update.getMySign());
                noticeSysType = NoticeSysType.MySignReviewReject;
            }
            if (noticeSysType != null) {
                noticeComponent.sendSystemNotice(form.getUserId(), noticeSysType);
            }
        }
    }

    /**
     * 测试事件消息
     */
    @BusiCode(BusiCodeDefine.CommonAwardMsg)
    public void saveTest(TestNoticeForm form) {
        if ("CommonAwardMsg".equals(form.getBusiCode())) {
            GlobalUtils.extValue(BusinessDataKey.OverwriteBusiCode, BusiCodeDefine.CommonAwardMsg);
            asyncService.saveAsyncAwardNotice(GlobalUtils.getGlobalContext(), "title", "awardName",
                    "user/cover/792400e0310f4b7f9a5936dca7cba598.jpg", "1", "touchName",
                    ClientTouchType.Url, form.getUserId());
        } else if ("UserUpLevel".equals(form.getBusiCode())) {
            Map<BusinessDataKey, Object> extMap = new HashMap<>();
            LevelType levelType = RandomUtils.getInt(2) == 0 ? LevelType.User : LevelType.Charm;
            extMap.put(BusinessDataKey.NewLevelValue, 100);
            extMap.put(BusinessDataKey.UserLevelType, levelType);
            messageComponent.sendMsgToUserInContext(BackCodeDefine.UserUpLevel, extMap, form.getUserId());
        } else if ("DrawRemind".equals(form.getBusiCode())) {
            Map<BusinessDataKey, Object> extMap = new HashMap<>();
            extMap.put(BusinessDataKey.WithdrawUrl, "http://www.baidu.com");
            extMap.put(BusinessDataKey.Cash, 1000);
            messageComponent.sendMsgToUserInContext(BackCodeDefine.DrawRemind, extMap, form.getUserId());
        } else if ("ChatTips".equals(form.getBusiCode())) {
            ChatTipsInfoDTO d = ChatTipsInfoDTO.createChatupTips("[飞吻][飞吻][飞吻]小红娘觉得你俩很般配，已为你${touchText}, 别错过缘分", "主动搭讪", ChatTipsShowType.Both);
            UUserBasic userBasic = userInfoManager.getUserBasic(2250749L);
            messageComponent.sendChatTipsMsg(userBasic, form.getUserId(), d);
        } else {
            throw new BusiException("测试类型未定义！！");
        }
    }

    /**
     * 测试系统通知
     */
    @BusiCode
    public void saveTest1(UserIdForm form) {
        noticeComponent.sendCommonSystemNotice(form.getUserId(), "subTitle", "content:" + System.currentTimeMillis());
    }

    /**
     * 测试事件通知
     */
    @BusiCode
    public void saveTest2(TestNoticeForm form) {
        Map<BusinessDataKey, Object> extMap = new HashMap<>();
        extMap.put(BusinessDataKey.CallId, form.getCallId());
        extMap.put(BusinessDataKey.Enough, BoolType.True);
        messageComponent.sendMsgToUserInContext(BusiCodeDefine.valueOf(form.getBusiCode()), extMap, form.getUserId());
    }

    /**
     * 测试集团黑名单
     */
    @BusiCode
    public BoolType saveTestGroupBlackList(TestGroupBlackListForm form){
        boolean groupBlack = userBlackListComponent.isGroupBlack(form.getMobile(), form.getIdNum());
        return BoolType.valueOf(groupBlack);
    }

    /**
     * 测试邮件发送
     */
    @BusiCode
    public void saveTest4(){
        String toUser = "<EMAIL>";
        List<Map<String, Object>> resultList = new ArrayList<>();
        resultList.add(MapUtils.gmap("statDateStr", "2023-12-26", "userId", "userId1", "nickname", "nickname1", "totalCashStr", 3, "socialCashStr", 1, "otherCashStr", 2));
        resultList.add(MapUtils.gmap("statDateStr", "2023-12-25", "userId", "userId2", "nickname", "nickname2", "totalCashStr", 3, "socialCashStr", 1, "otherCashStr", 2));
        emailSupport.sendAttachmentEmail(PackageType.YL, toUser, EasyPoiExcelDefine.FEMALE_INCOME, resultList);
    }

    /** -------------------------辅助方法-------------------------- */

    /**
     * 检查用户更新信息
     */
    private void checkUserUpdateInfo(SexType sexType){
        SettingsType type = SettingsType.FemaleUpdateInfoSwitch;
        if(SexType.Male == sexType){
            type = SettingsType.MaleUpdateInfoSwitch;
        }
        Date[] dateRange = SettingsConfig.getDateRange(type);
        if(Objects.isNull(dateRange)){
            return;
        }
        Date now = DateUtils.nowTime();
        if(now.after(dateRange[0]) && now.before(dateRange[1])){
            throw new BusiException("系统升级中，暂时无法使用此功能");
        }
    }

    /**
     * 新人任务完成 额外奖励
     * 备注：由于存在线程之前的一个竞争关系，发放额外奖励的时候 全局上下文我们需要将他提取出来复制一个
     */
    private void newPeopleTaskFinish(Long userId, SexType sexType) {
        if (!userInfoManager.checkUserPerfectAll(userId, sexType)) {
            return;
        }
        CallbackAfterTransactionUtil.send(() -> {
            // messageQueueProducer.sendAsync(BusiCodeDefine.FinishTask, MessageTag.UserTask);
        });
        /*GlobalContext copyContent = new GlobalContext();
        CglibUtils.copy(GlobalUtils.getGlobalContext(), copyContent);
        FixedAward extAward = SexType.Female == sexType ? FixedAward.PerfectAllInfo_FeMale : FixedAward.PerfectAllInfo_Male;
        CommonAwardNoticeDTO noticeDTO2 = buildAwardNotice(userId,"- 完成新人任务 -", extAward.getPopPic(),
                extAward.getAwardTypeSimpleName(), extAward.getAwardValue());
        asyncService.saveAsyncAward(copyContent, userId, extAward.getAwardCode(),
                extAward.getAwardType(), extAward.getAwardValue(), extAward.getDesc(), noticeDTO2);*/
    }

    /**
     * 头像二次上传人脸对比认证
     *
     * @param userId
     * @param headPic
     * @return
     */
    private boolean getRealPeople(Long userId, String headPic) {
        UIdentityAuth auth = userInfoManager.getUserIdentityAuth(userId);
        if (null == auth || StringUtils.isEmpty(auth.getRealPeopleInfo())) {
            return false;
        }
        String sourcePhotoStr = ossSupport.getPhoto2Base64(headPic);
        boolean compareResult = txCertificationSupport.compareFaces(userId, auth.getRealPeopleInfo(), sourcePhotoStr, null, GlobalUtils.packageType());
        if(!compareResult){
            throw new BusiException("请上传本人头像！");
        }
        return true;
    }

    private List<UUserMediaApply> buildMediaApplyByPic(Long userId, MediaType type, List<MediaPicDTO> dtos, Map<Long, UUserMediaApply> existsMedias){
        List<UUserMediaApply> list = new ArrayList<>();
        for(int i=1; i<=dtos.size(); i++){
            MediaPicDTO dto = dtos.get(i-1);
            if(Objects.nonNull(dto.getApplyId()) && existsMedias.containsKey(dto.getApplyId())){
                UUserMediaApply ap = existsMedias.get(dto.getApplyId());
                if(ap.getAuditStatus() == MediaApplyStatus.RejectJq || ap.getAuditStatus() == MediaApplyStatus.RejectRp || ap.getAuditStatus() == MediaApplyStatus.RejectCms){
                    throw new BusiException("请修改照片！");
                }
                ap.setMediaIdx(i);
                list.add(ap);
                continue;
            }
            UUserMediaApply apply = new UUserMediaApply();
            apply.setRecordId(dto.getRecordId());
            apply.setUserId(userId);
            apply.setType(type);
            apply.setMediaValue(dto.getPicUrl());
            apply.setMediaIdx(i);
            apply.setAuditStatus(MediaApplyStatus.WaitJq);
            apply.setStatusDesc("待审核");
            apply.setCreateTime(new Date());
            list.add(apply);
        }
        return list;
    }

    private List<UUserMediaApply> buildMediaApplyByVideo(Long userId, MediaType type, List<MediaVideoDTO> dtos, Map<Long, UUserMediaApply> existsMedias){
        List<UUserMediaApply> list = new ArrayList<>();
        for(int i=1; i<=dtos.size(); i++){
            MediaVideoDTO dto = dtos.get(i-1);
            if(Objects.nonNull(dto.getApplyId()) && existsMedias.containsKey(dto.getApplyId())){
                UUserMediaApply ap = existsMedias.get(dto.getApplyId());
                if(ap.getAuditStatus() == MediaApplyStatus.RejectJq || ap.getAuditStatus() == MediaApplyStatus.RejectRp || ap.getAuditStatus() == MediaApplyStatus.RejectCms){
                    throw new BusiException("请修改视频！");
                }
                ap.setMediaIdx(i);
                list.add(ap);
                continue;
            }
            UUserMediaApply apply = new UUserMediaApply();
            apply.setRecordId(dto.getRecordId());
            apply.setUserId(userId);
            apply.setType(type);
            apply.setMediaValue(dto.getVideoUrl());
            apply.setMediaExt(JsonUtils.seriazileAsString(MapUtils.gmap("videoCover", dto.getVideoCover())));
            apply.setMediaIdx(i);
            apply.setAuditStatus(MediaApplyStatus.WaitJq);
            apply.setStatusDesc("待审核");
            apply.setCreateTime(new Date());
            list.add(apply);
        }
        return list;
    }

    private List<UUserMediaApply> buildMediaApplyByVoice(Long userId, MediaType type, List<MediaVoiceDTO> dtos, Map<Long, UUserMediaApply> existsMedias){
        List<UUserMediaApply> list = new ArrayList<>();
        for(int i=1; i<=dtos.size(); i++){
            MediaVoiceDTO dto = dtos.get(i-1);
            if(Objects.nonNull(dto.getApplyId()) && existsMedias.containsKey(dto.getApplyId())){
                UUserMediaApply ap = existsMedias.get(dto.getApplyId());
                if(ap.getAuditStatus() == MediaApplyStatus.RejectJq || ap.getAuditStatus() == MediaApplyStatus.RejectRp || ap.getAuditStatus() == MediaApplyStatus.RejectCms){
                    throw new BusiException("请修改语音！");
                }
                ap.setMediaIdx(i);
                list.add(ap);
                continue;
            }
            UUserMediaApply apply = new UUserMediaApply();
            apply.setRecordId(dto.getRecordId());
            apply.setUserId(userId);
            apply.setType(type);
            apply.setMediaValue(dto.getVoiceUrl());
            apply.setMediaIdx(i);
            apply.setAuditStatus(MediaApplyStatus.WaitJq);
            apply.setStatusDesc("待审核");
            apply.setCreateTime(new Date());
            list.add(apply);
        }
        return list;
    }

    /**
     * 构建用户修改信息后奖励通知
     */
    private CommonAwardNoticeDTO buildAwardNotice(Long targetUserId, String title, String awardPic, String awardName, String count) {
        CommonAwardNoticeDTO dto = new CommonAwardNoticeDTO();
        dto.setTitle(title);
        dto.setAwardPic(awardPic);
        dto.setAwardName(awardName + "+" + count + "元");
        dto.setCount(count);
        dto.setTouchName("我知道了");
        dto.setTouchType(ClientTouchType.MineHomePage);
        dto.setUserId(targetUserId);
        return dto;
    }

    /**
     * 构建现金奖励扩展配置选项
     */
    private String buildCashAwardExtCfg(UserCashSourceType type) {
        return "source=" + type.getId();
    }

    /**
     * 构建商店审核禁止推荐列表用户缓存
     */
    private RedisKey buildStoreAuditShieldUser() {
        return RedisKey.create(UserKeyDefine.StoreAuditShieldUser);
    }
}
