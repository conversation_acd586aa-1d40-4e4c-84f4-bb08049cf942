package com.tuowan.yeliao.user.web.controller.app;

import com.easyooo.framework.common.util.CglibUtils;
import com.easyooo.framework.common.util.JsonUtils;
import com.tuowan.yeliao.commons.context.config.HeaderType;
import com.tuowan.yeliao.commons.context.config.Request;
import com.tuowan.yeliao.commons.context.config.SessionType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.http.ReturnUtils;
import com.tuowan.yeliao.commons.core.http.Root;
import com.tuowan.yeliao.commons.core.utils.GoogleAuthenticator;
import com.tuowan.yeliao.commons.open.oss.OSSTokenSupport;
import com.tuowan.yeliao.commons.web.common.util.WebUtils;
import com.tuowan.yeliao.commons.web.common.vo.ListVO;
import com.tuowan.yeliao.user.web.form.app.BroadcastForm;
import com.tuowan.yeliao.user.web.form.app.OfficialNoticeContentForm;
import com.tuowan.yeliao.user.web.vo.acct.common.BeautyAuthVO;
import com.tuowan.yeliao.user.service.app.AppService;
import com.tuowan.yeliao.user.web.form.app.NewsForm;
import com.tuowan.yeliao.user.web.form.app.UserAliveForm;
import com.tuowan.yeliao.user.web.vo.acct.common.OssAuthVO;
import com.tuowan.yeliao.user.web.vo.app.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @title APP控制器
 * 备注： APP启动调用接口顺序应该为 /start -> /config
 */
@RestController
@RequestMapping("/usr/app")
public class AppController {

    @Autowired
    private AppService appService;
    @Autowired
    private OSSTokenSupport ossTokenSupport;

    /**
     * 用于统计app激活数据，app首次下载并启动的时候调用该接口
     * 调用完成之后，客户端标记已经激活，后续不再调用
     *
     * @title 激活APP接口
     * 备注：恐后期在APP启动的时候还需要做其他操作，所以将这个接口放在user模块
     */
    @Request(SessionType.None)
    @RequestMapping("/start")
    public Root<Void> start() {
        appService.saveStartApp();
        return ReturnUtils.empty();
    }

    /**
     * @return
     * @title 获取应用配置
     */
    @Request(SessionType.None)
    @RequestMapping("/config")
    public Root<AppConfigVO> config() {
        return ReturnUtils.root(appService.getAppConfig());
    }

    /**
     * 只有应用进程已结束的情况，打开App才会调用
     *
     * @title app信息更新
     */
    @Request(SessionType.None)
    @RequestMapping("/findNews")
    public Root<NewsVO> findNews(@RequestBody NewsForm form) {
        return ReturnUtils.root(appService.saveFindNews(form));
    }

    /**
     * 调用方式：
     * 1，用户登录之后
     * 2，用户每次打开App
     * <p>
     * 调用频率：1次
     * 该接口返回一个onlineId，在调用心跳接口的时候需要传递此参数
     * <p>
     * 生命周期：用户本次上线 到 用户离线（登出 或者 心跳超时自动离线）
     *
     * @title 用户上线接口
     * 备注：在调用该接口时，客户端需要保证用户已经登录
     */
    @Request
    @RequestMapping("/online")
    public Root<OnlineVO> online() {
        return ReturnUtils.root(appService.saveOnline());
    }

    /**
     * 调用频率：1次/30秒
     *
     * @title 用户在线心跳接口
     */
    @Request
    @RequestMapping("/alive")
    public Root<UserAliveVO> alive(@RequestBody UserAliveForm form) {
        return ReturnUtils.root(appService.saveUserAlive(form));
    }

    /**
     * @return
     * @title 安卓oaid证书信息
     */
    @Request(session = SessionType.None, header = HeaderType.None)
    @RequestMapping("/oaid")
    public Root<OAIDCertVO> oaid() {
        return ReturnUtils.root(OAIDCertVO.build());
    }

    /**
     * 指定URL初始化OSS SDK调用该接口
     * 一般用户移动客户端调用
     * 备注：Android客户端上传使用
     * @title OSS认证
     */
    @Request(session = SessionType.None, header = HeaderType.None)
    @RequestMapping("/ossAuth")
    public Root<OssAuthVO> ossAuth() {
        OssAuthVO vo = new OssAuthVO();
        CglibUtils.copy(ossTokenSupport.assumeRole(), vo);
        return ReturnUtils.root(vo);
    }

    /**
     * 指定URL初始化OSS SDK调用该接口
     * 一般用户移动客户端调用
     * 备注：ios客户端上传使用
     * @title OSS认证
     */
    @Request(session = SessionType.None, header = HeaderType.None)
    @RequestMapping("/iosOssAuth")
    public String iosOssAuth(HttpServletResponse response) {
        return WebUtils.writeString(ossTokenSupport.iosAssumeRole(), response);
    }

    /**
     * 宇宙美颜授权 SDK调用该接口
     * 一般用户移动客户端调用
     *
     * @title OSS认证
     */
    @Request(session = SessionType.None, header = HeaderType.None)
    @RequestMapping("/beautyAuth")
    public Root<BeautyAuthVO> beautyAuth() {
        return ReturnUtils.root(appService.beautyAuth());
    }
    /**
     * 手动初始化OSS SDK调用该接口
     * <p>
     * 用于CMS和前端调用
     *
     * @title OSS认证
     */
    @Request(session = SessionType.None, header = HeaderType.None)
    @RequestMapping({"/ossAuthV1", "/cmsOssAuth"})
    public Root<OSSAuthVO> cmsOssAuth() {
        return ReturnUtils.root(appService.getOssAuth());
    }

    /**
     * 测试
     */
    @Request(session = SessionType.None, header = HeaderType.None)
    @RequestMapping("/test")
    public Root<Void> test() {
        appService.saveTest1();
        return ReturnUtils.empty();
    }

    /**
     * 指定URL初始化OSS SDK调用该接口
     * 一般用户移动客户端调用
     *
     * @title OSS认证
     */
    @Request(session = SessionType.None, header = HeaderType.None)
    @RequestMapping("/totp")
    public String totp(@RequestParam("s") String secret) {
        return GoogleAuthenticator.getCode(secret);
    }

    /**
     * 客服系统所携带信息
     */
    @Request
    @RequestMapping("/kfUserInfo")
    public Root<String> kfUserInfo() {
        return ReturnUtils.root(appService.queryKfUserInfo());
    }

    /**
     * 客服地址
     */
    @Request(session = SessionType.None)
    @RequestMapping("/kfUrl")
    public Root<String> kfUrl() {
        return ReturnUtils.root(appService.kfUrl());
    }

    /**
     * 平台公告
     */
    @Request(clients = ClientType.H5)
    @RequestMapping("/officialNotice")
    public Root<ListVO> officialNotice() {
        return ReturnUtils.root(appService.officialNotice());
    }

    /**
     * 公告内容
     */
    @Request(clients = ClientType.H5)
    @RequestMapping("/officialNoticeContent")
    public Root<Object> officialNoticeContent(@RequestBody OfficialNoticeContentForm form) {
        return ReturnUtils.root(appService.officialNoticeContent(form));
    }

    /**
     * 全站通知
     */
    @Request
    @RequestMapping("/broadcast")
    public Root<BroadcastVO> broadcast(@RequestBody BroadcastForm form) {
        return ReturnUtils.root(appService.broadcast(form));
    }
}
