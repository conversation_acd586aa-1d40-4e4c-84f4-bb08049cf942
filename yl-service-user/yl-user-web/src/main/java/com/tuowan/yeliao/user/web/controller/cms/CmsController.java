package com.tuowan.yeliao.user.web.controller.cms;

import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.ListUtils;
import com.easyooo.framework.common.util.MsgUtils;
import com.tuowan.yeliao.commons.context.config.HeaderType;
import com.tuowan.yeliao.commons.context.config.Request;
import com.tuowan.yeliao.commons.context.request.UserIdRequest;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.core.exception.GeneralException;
import com.tuowan.yeliao.commons.core.exception.WebException;
import com.tuowan.yeliao.commons.core.http.ReturnUtils;
import com.tuowan.yeliao.commons.core.http.Root;
import com.tuowan.yeliao.commons.data.enums.config.AwardType;
import com.tuowan.yeliao.commons.data.enums.general.ReviewResultType;
import com.tuowan.yeliao.commons.data.enums.user.UserCashSourceType;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.web.common.form.UserIdForm;
import com.tuowan.yeliao.commons.web.proxy.config.RemoteInvoke;
import com.tuowan.yeliao.user.data.entity.UUserReport;
import com.tuowan.yeliao.user.service.acct.AuthService;
import com.tuowan.yeliao.user.service.cms.CmsService;
import com.tuowan.yeliao.user.web.form.acct.auth.RpMediaReviewForm;
import com.tuowan.yeliao.user.web.form.cms.*;
import com.tuowan.yeliao.user.web.form.opt.LogIdForm;
import com.tuowan.yeliao.user.web.vo.cms.ExamineAwardVO;
import com.tuowan.yeliao.user.web.vo.cms.SendSystemNoticeVO;
import com.tuowan.yeliao.user.web.vo.cms.UserRightVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * CMS服务集合
 */
@RemoteInvoke
@RestController
@RequestMapping("/usr/cms")
public class CmsController {
    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CmsService cmsService;
    @Autowired
    private AuthService authService;

    /**
     * CMS账号注册绑定
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/rgCmsAccount")
    public Root<Void> rgCmsAccount(@RequestBody RgCmsAccountForm form) {
        cmsService.saveCmsAccountRg(form);
        return ReturnUtils.empty();
    }

    /**
     * CMS账号注册绑定
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/updateCmsAccountPw")
    public Root<Void> updateCmsAccountPw(@RequestBody CmsAccountPwForm form) {
        cmsService.updateCmsAccountPw(form);
        return ReturnUtils.empty();
    }

    /**
     * 修改APP内部客服昵称
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/updateAppCustomer")
    public Root<Void> updateAppCustomer(@RequestBody UpdateAppCustomerForm form) {
        cmsService.updateAppCustomer(form);
        return ReturnUtils.empty();
    }

    /**
     * 新增-修改 礼物追回记录
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/addOrUpdateGiftRecover")
    public Root<Void> updateUserBasic(@RequestBody AddOrUpdateGiftRecoverForm form) {
        if (Objects.isNull(form.getLogId())) {
            cmsService.addGiftRecover(form);
        } else {
            cmsService.updateGiftRecover(form);
        }
        return ReturnUtils.empty();
    }

    /**
     * 修改用户基本信息
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/updateUserBasic")
    public Root<Void> updateUserBasic(@RequestBody UpdateUserBasicForm form) {
        cmsService.updateUserBasic(form);
        return ReturnUtils.empty();
    }

    /**
     * 修改用户基本信息
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/updateUserInvitor")
    public Root<Void> updateUserInvitor(@RequestBody UpdateUserInvitorForm form) {
        cmsService.updateUserInvitor(form);
        return ReturnUtils.empty();
    }

    /**
     * 解绑邀请人
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/releaseInvitor")
    public Root<Void> releaseInvitor(@RequestBody UserIdForm form) {
        cmsService.saveReleaseInvitor(form);
        return ReturnUtils.empty();
    }

    /**
     * 修改用户基本信息
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/updateUserCity")
    public Root<Void> updateUserCity(@RequestBody UpdateUserCityForm form) {
        cmsService.updateUserCity(form);
        return ReturnUtils.empty();
    }

    /**
     * 封禁用户
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/blockUser")
    public Root<Void> blockUser(@RequestBody BlockUserForm form) {
        cmsService.saveBlockUser(form);
        return ReturnUtils.empty();
    }

    /**
     * 解封用户
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/unblockUser")
    public Root<Void> unblockUser(@RequestBody UserIdForm form) {
        cmsService.saveUnBanUser(form);
        return ReturnUtils.empty();
    }

    /**
     * 注销用户账号
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/detroyAcct")
    public Root<Void> detroyAcct(@RequestBody UserIdForm form) {
        cmsService.saveDetroyAcct(form);
        return ReturnUtils.empty();
    }

    /**
     * 发送系统通知
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/sendSystemNotice")
    public Root<SendSystemNoticeVO> sendSystemNotice(@RequestBody SendSystemNoticeForm form) {
        return ReturnUtils.root(cmsService.saveSendSystemNotice(form));
    }

    /**
     * 实名认证解绑
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/untieRealName")
    public Root<Void> saveUntieRealName(@RequestBody OperateUserForm form) {
        cmsService.saveUntieRealName(form);
        return ReturnUtils.empty();
    }

    /**
     * 手机号解绑
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/untieMobile")
    public Root<Void> saveUntieMobile(@RequestBody OperateUserForm form) {
        cmsService.saveUntieMobile(form);
        return ReturnUtils.empty();
    }

    /**
     * 微信解绑
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/untieWechat")
    public Root<Void> saveUntieWechat(@RequestBody OperateUserForm form) {
        cmsService.saveUntieWeChat(form);
        return ReturnUtils.empty();
    }

    /**
     * 支付宝解绑
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/untieAlipay")
    public Root<Void> saveUntieAlipay(@RequestBody OperateUserForm form) {
        cmsService.saveUntieAlipay(form);
        return ReturnUtils.empty();
    }

    /**
     * 批量审核待发将
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/examineAwards")
    public Root<ExamineAwardVO> examineAwards(@RequestBody AwardReviewForm form) {
        List<String> unawardIds = Arrays.asList(form.getUnawardIds());
        List<String> userIds = Arrays.asList(form.getUserIds());
        if (ListUtils.isEmpty(unawardIds) || ListUtils.isEmpty(userIds) || unawardIds.size() != userIds.size()) {
            throw new DataException(ErrCodeType.InvalidArguments);
        }
        if (ReviewResultType.Pass != form.getStatus() && ReviewResultType.Reject != form.getStatus()) {
            throw new BusiException(MsgUtils.format("{} 操作方式未定义！！", form.getStatus().getDesc()));
        }
        List<AwardReviewItemForm> itemForms = new ArrayList<>();
        // 要求 unAwardIds 和 userIds 里面的顺序一一对应
        for (int i = 0; i < form.getUnawardIds().length; i++) {
            itemForms.add(new AwardReviewItemForm(form.getUnawardIds()[i], form.getUserIds()[i], form.getRemark()));
        }
        // 按照 unAwardId 的升序排列
        itemForms = itemForms.stream().sorted(Comparator.comparing(AwardReviewItemForm::getUnawardId)).collect(Collectors.toList());
        int suc = 0, fai = 0;
        List<String> failReason = new ArrayList<>();
        for (AwardReviewItemForm item : itemForms) {
            try {
                if (ReviewResultType.Pass == form.getStatus()) {
                    cmsService.savePassUnAward(item);
                } else if (ReviewResultType.Reject == form.getStatus()) {
                    cmsService.saveRejectUnAward(item);
                }
                suc++;
            } catch (GeneralException e) {
                fai++;
                failReason.add(MsgUtils.format("记录id：{}_失败原因：{}", item.getUnawardId(), e.getMessage()));
            } catch (Exception E) {
                LOG.error("CmsController-examineAwards-error item:{}, reason:", JsonUtils.seriazileAsString(item), E);
                fai++;
                failReason.add(MsgUtils.format("记录id：{}_失败原因：{}", item.getUnawardId(), "未知原因"));
            }
        }
        // 填充返回值
        ExamineAwardVO vo = new ExamineAwardVO();
        vo.setSucNum(suc);
        vo.setFailNUm(fai);
        vo.setFailReason(failReason);
        return ReturnUtils.root(vo);
    }

    /**
     * @title 奖励金币(充值金币或者赠送金币)
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/awardBeans")
    public Root<Void> awardBeans(@RequestBody AwardCountForm form) {
        if (AwardType.Beans != form.getAwardType() && AwardType.PlatFormBeans != form.getAwardType()) {
            throw new BusiException("奖励类型异常");
        }
        AwardCreateForm aform = new AwardCreateForm(form.getAwardType(), form.getUserId(), form.getRemark());
        aform.setCount(form.getCount());
        cmsService.saveCreateUnAward(aform);
        return ReturnUtils.empty();
    }

    /**
     * @title 扣除金币(充值金币或者赠送金币)
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/deductBeans")
    public Root<Void> deductBeans(@RequestBody AwardCountForm form) {
        AwardType awardType = form.getAwardType();
        if (AwardType.Beans != awardType && AwardType.PlatFormBeans != awardType) {
            throw new WebException("扣除奖励类型异常");
        }
        AwardCreateForm aform = new AwardCreateForm(awardType, form.getUserId(), form.getRemark());
        Integer count = form.getCount() > 0 ? -form.getCount() : form.getCount();
        aform.setCount(count);
        cmsService.saveCreateUnAward(aform);
        return ReturnUtils.empty();
    }

    /**
     * @title 奖励零钱
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/awardCash")
    public Root<Void> awardCash(@RequestBody AwardCountForm form) {
        AwardCreateForm aform = new AwardCreateForm(AwardType.Cash, form.getUserId(), form.getRemark());
        aform.setCount(BusiUtils.yuanToCash(form.getCount()).intValue());
        aform.setTypeDetail(form.getTypeDetail());
        cmsService.saveCreateUnAward(aform);
        return ReturnUtils.empty();
    }

    /**
     * @title 扣除零钱
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/deductCash")
    public Root<Void> deductCash(@RequestBody AwardCountForm form) {
        AwardCreateForm aform = new AwardCreateForm(AwardType.Cash, form.getUserId(), form.getRemark());
        Integer count = form.getCount() > 0 ? -form.getCount() : form.getCount();
        aform.setCount(BusiUtils.yuanToCash(count).intValue());
        aform.setTypeDetail(UserCashSourceType.Punish.name());
        cmsService.saveCreateUnAward(aform);
        return ReturnUtils.empty();
    }

    /**
     * @title 奖励社交物品
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/awardSocialGoods")
    public Root<Void> socialGoods(@RequestBody AwardGoodsForm form) {
        AwardCreateForm aform = createAwardForm(form, false, AwardType.SocialGoods);
        cmsService.saveCreateUnAward(aform);
        return ReturnUtils.empty();
    }

    /**
     * @title 奖励社交礼物
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/awardSocialGift")
    public Root<Void> socialGift(@RequestBody AwardGoodsForm form) {
        if (form.getCount() <= 0) {
            throw new WebException("奖励礼物数量必须大于0");
        }
        AwardCreateForm aform = createAwardForm(form, false, AwardType.SocialGift);
        cmsService.saveCreateUnAward(aform);
        return ReturnUtils.empty();
    }

    /**
     * @title 更新嘉宾（聊主）权限开关
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/updateMasterAuth")
    public Root<Void> updateMasterAuth(@RequestBody MasterAuthForm form) {
        cmsService.updateMasterAuth(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 设备封禁操作
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/deviceBlockOpt")
    public Root<Void> unBlockDevice(@RequestBody DeviceBlockOptForm form) {
        if (BoolType.True == form.getOptType()) {
            // 封禁设备
            cmsService.saveBanDevice(form);
        } else {
            // 解封设备
            cmsService.saveUnBanDevice(form);
        }
        return ReturnUtils.empty();
    }

    /**
     * @title 用户提现封禁操作
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/withDrawBlockOpt")
    public Root<Void> withDrawBlockOpt(@RequestBody UserWithDrawBanForm form) {
        if (BoolType.True == form.getOptType()) {
            // 封禁提现
            cmsService.saveBanWithDraw(form);
        } else {
            // 解除封禁
            cmsService.saveUnBanWithDraw(form);
        }
        return ReturnUtils.empty();
    }

    /**
     * 举报用户
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/reportUser")
    public Root<Void> reportUser(@RequestBody ReportUserForm form) {
        cmsService.saveReportUser(form);
        return ReturnUtils.empty();
    }

    /**
     * 处理举报记录
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/dealReport")
    public Root<Void> dealReport(@RequestBody ReportDealForm form) {
        UUserReport report = cmsService.saveDealReport(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 添加白名单
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/joinWhitelist")
    public Root<Void> joinWhitelist(@RequestBody JoinWhitelistForm form) {
        cmsService.saveJoinWhitelist(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 移除指定的白名单
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/removeWhitelist")
    public Root<Void> removeWhitelist(@RequestBody JoinWhitelistForm form) {
        cmsService.saveRemoveWhitelist(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 广告预警 审核无效操作
     */
    @Request(header = HeaderType.Whitelist, clients = ClientType.Cms)
    @RequestMapping("/releaseForAdInvalid")
    public Root<Void> releaseForAdInvalid(@RequestBody UserIdForm form) {
        cmsService.releaseForAdInvalid(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 发送配置数据变化同步消息
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/refreshConfig")
    public Root<Void> refreshConfig(@RequestBody RefreshConfigForm form) {
        cmsService.saveRefreshConfig(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 删除奖池配置
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/deletePoolBox")
    public Root<Void> deletePoolBox(@RequestBody DeletePoolBoxForm form) {
        cmsService.deletePoolBox(form);
        return ReturnUtils.empty();
    }

    /**
     * 账号审核
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/account/review")
    public Root<Void> accountReview(@RequestBody AccountReviewForm form) {
        cmsService.saveAccountReview(form);
        return ReturnUtils.empty();
    }

    /**
     * 微信审核
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/wx/review")
    public Root<Void> wxReview(@RequestBody WxReviewForm form) {
        cmsService.saveWxReview(form);
        return ReturnUtils.empty();
    }

    /**
     * 微信删除
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/wx/delete")
    public Root<Void> wxDelete(@RequestBody UserIdForm form) {
        cmsService.saveWxDelete(form);
        return ReturnUtils.empty();
    }

    /**
     * 设置固定邀请提成比例
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/inviteFixScale")
    public Root<Void> inviteFixScale(@RequestBody InviteFixScaleForm form) {
        cmsService.saveInviteFixScale(form);
        return ReturnUtils.empty();
    }

    /**
     * 设置分成模式
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/presentType")
    public Root<Void> presentType(@RequestBody PresentTypeForm form) {
        cmsService.savePresentType(form);
        return ReturnUtils.empty();
    }

    /**
     * 设置工会提成模式
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/invitePsType")
    public Root<Void> invitePsType(@RequestBody InvitePsTypeForm form) {
        cmsService.saveInvitePsType(form);
        return ReturnUtils.empty();
    }

    /**
     * 设置是否有效邀请人
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/validInvitor")
    public Root<Void> validInvitor(@RequestBody ValidInvitorForm form) {
        cmsService.saveValidInvitor(form);
        return ReturnUtils.empty();
    }

    /**
     * 设置聊天室查看类型
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/lookChatRoomType")
    public Root<Void> lookChatRoomType(@RequestBody LookChatRoomTypeForm form) {
        cmsService.saveLookChatRoomType(form);
        return ReturnUtils.empty();
    }

    /**
     * 设置固定邀
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/deleteUserCover")
    public Root<Void> deleteUserCover(@RequestBody LogIdForm form) {
        cmsService.deleteUserCover(form);
        return ReturnUtils.empty();
    }

    /**
     * 审核真人认证媒资
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/auditRpMedia")
    public Root<Void> auditRpMedias(@RequestBody RpMediaReviewForm form){
        form.setPass(form.getResult().boolValue());
        authService.saveCmsAuditRpMedia(form);
        return ReturnUtils.empty();
    }

    /**
     * 删除用户媒资
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/delUserMedia")
    public Root<Void> delUserMedia(@RequestBody DelUserMediaForm form){
        authService.deleteUserMedia(form);
        return ReturnUtils.empty();
    }

    /**
     * 查询用户权限
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/queryUserRight")
    public Root<UserRightVO> queryUserRight(@RequestBody UserIdForm form){
        return ReturnUtils.root(cmsService.queryUserRight(form));
    }

    /**
     * CMS操作用户权限
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/operateUserSetting")
    public Root<Void> operateUserSetting(@RequestBody OperateUserSettingForm form){
        cmsService.saveOperateUserSetting(form);
        return ReturnUtils.empty();
    }

    /**
     * --------------------- 辅助方法 ----------------------
     */

    private AwardCreateForm createAwardForm(AwardGoodsForm form, boolean isDeduct, AwardType type) {
        AwardCreateForm aform = new AwardCreateForm(type, form.getUserId(), form.getRemark());
        aform.setCount(isDeduct ? -form.getCount() : form.getCount());
        aform.setGoodsId(form.getGoodsId());
        aform.setExpDays(form.getExpDays());
        return aform;
    }
}
