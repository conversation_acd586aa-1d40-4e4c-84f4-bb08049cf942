package com.tuowan.yeliao.user.web.form.cms;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.web.common.form.Form;

public class ValidInvitorForm implements Form {
    @LMNotNull
    private Long userId;
    @LMNotNull
    private BoolType type;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public BoolType getType() {
        return type;
    }

    public void setType(BoolType type) {
        this.type = type;
    }
}
