package com.tuowan.yeliao.user.web.form.opt;

import com.easyooo.framework.validate.config.LMLength;
import com.easyooo.framework.validate.config.LMNotEmpty;
import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.web.common.form.Form;

public class RecoverAppealForm implements Form {
    /**
     * 追回订单唯一标识
     */
    @LMNotNull
    private Long logId;
    /**
     * 申诉理由
     */
    @LMNotEmpty
    @LMLength(max = 200)
    private String appealReason;
    /**
     * 申诉预留手机号
     */
    @LMNotEmpty
    @LMLength(min = 11, max = 11)
    private String mobile;


    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public String getAppealReason() {
        return appealReason;
    }

    public void setAppealReason(String appealReason) {
        this.appealReason = appealReason;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
