package com.tuowan.yeliao.user.web.form.user.info;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.data.enums.user.UserCheckType;
import com.tuowan.yeliao.commons.web.common.form.Form;

public class CheckUserStatusForm implements Form {
    /**
     * 目标用户
     */
    private Long userId;
    /**
     * 检查类型
     */
    @LMNotNull
    private UserCheckType type;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public UserCheckType getType() {
        return type;
    }

    public void setType(UserCheckType type) {
        this.type = type;
    }
}
