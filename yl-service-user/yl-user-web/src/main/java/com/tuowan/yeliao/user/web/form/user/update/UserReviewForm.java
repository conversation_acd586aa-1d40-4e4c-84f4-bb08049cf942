package com.tuowan.yeliao.user.web.form.user.update;

import com.tuowan.yeliao.commons.open.yidun.enums.YidunReviewType;
import com.tuowan.yeliao.commons.web.common.form.Form;

/**
 * 用户审核表单
 *
 * <AUTHOR>
 * @date 2022/7/1 18:57
 */
public class UserReviewForm implements Form {

    /**
     * 审核类型
     */
    private YidunReviewType reviewType;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 是否通过
     */
    private boolean pass;
    /**
     * 不通过原因
     */
    private String reason;
    /**
     * 对象ID
     */
    private Long objectId;

    public YidunReviewType getReviewType() {
        return reviewType;
    }

    public void setReviewType(YidunReviewType reviewType) {
        this.reviewType = reviewType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public boolean isPass() {
        return pass;
    }

    public void setPass(boolean pass) {
        this.pass = pass;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }
}
